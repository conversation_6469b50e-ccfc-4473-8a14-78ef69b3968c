"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

exports.__esModule = true;
exports.default = parents;

var _collectElements = _interopRequireDefault(require("./collectElements"));

/**
 * Collects all parent elements of a given element.
 * 
 * @param node the element
 */
function parents(node) {
  return (0, _collectElements.default)(node, 'parentElement');
}

module.exports = exports["default"];