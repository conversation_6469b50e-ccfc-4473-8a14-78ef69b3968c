export declare const GeoJSON = "scalar GeoJSON";
export declare const CountryName = "scalar CountryName";
export declare const BigInt = "scalar BigInt";
export declare const Byte = "scalar Byte";
export declare const Date = "scalar Date";
export declare const Time = "scalar Time";
export declare const Timestamp = "scalar Timestamp";
export declare const TimeZone = "scalar TimeZone";
export declare const DateTime = "scalar DateTime";
export declare const DateTimeISO = "scalar DateTimeISO";
export declare const UtcOffset = "scalar UtcOffset";
export declare const Duration = "scalar Duration";
export declare const ISO8601Duration = "scalar ISO8601Duration";
export declare const LocalDate = "scalar LocalDate";
export declare const LocalTime = "scalar LocalTime";
export declare const LocalDateTime = "scalar LocalDateTime";
export declare const LocalEndTime = "scalar LocalEndTime";
export declare const EmailAddress = "scalar EmailAddress";
export declare const UUID = "scalar UUID";
export declare const Hexadecimal = "scalar Hexadecimal";
export declare const HexColorCode = "scalar HexColorCode";
export declare const HSL = "scalar HSL";
export declare const HSLA = "scalar HSLA";
export declare const IBAN = "scalar IBAN";
export declare const IP = "scalar IP";
export declare const IPv4 = "scalar IPv4";
export declare const IPv6 = "scalar IPv6";
export declare const ISBN = "scalar ISBN";
export declare const JWT = "scalar JWT";
export declare const Latitude = "scalar Latitude";
export declare const Longitude = "scalar Longitude";
export declare const JSON = "scalar JSON";
export declare const JSONObject = "scalar JSONObject";
export declare const MAC = "scalar MAC";
export declare const NegativeFloat = "scalar NegativeFloat";
export declare const NegativeInt = "scalar NegativeInt";
export declare const NonEmptyString = "scalar NonEmptyString";
export declare const NonNegativeFloat = "scalar NonNegativeFloat";
export declare const NonNegativeInt = "scalar NonNegativeInt";
export declare const NonPositiveFloat = "scalar NonPositiveFloat";
export declare const NonPositiveInt = "scalar NonPositiveInt";
export declare const PhoneNumber = "scalar PhoneNumber";
export declare const Port = "scalar Port";
export declare const PositiveFloat = "scalar PositiveFloat";
export declare const PositiveInt = "scalar PositiveInt";
export declare const PostalCode = "scalar PostalCode";
export declare const RGB = "scalar RGB";
export declare const RGBA = "scalar RGBA";
export declare const SafeInt = "scalar SafeInt";
export declare const URL = "scalar URL";
export declare const USCurrency = "scalar USCurrency";
export declare const Currency = "scalar Currency";
export declare const RoutingNumber = "scalar RoutingNumber";
export declare const AccountNumber = "scalar AccountNumber";
export declare const Cuid = "scalar Cuid";
export declare const SemVer = "scalar SemVer";
export declare const SESSN = "scalar SESSN";
export declare const UnsignedFloat = "scalar UnsignedFloat";
export declare const UnsignedInt = "scalar UnsignedInt";
export declare const GUID = "scalar GUID";
export declare const Long = "scalar Long";
export declare const ObjectID = "scalar ObjectID";
export declare const Void = "scalar Void";
export declare const DID = "scalar DID";
export declare const CountryCode = "scalar CountryCode";
export declare const Locale = "scalar Locale";
export declare const DeweyDecimal = "scalar DeweyDecimal";
export declare const LCCSubclass = "scalar LCCSubclass";
export declare const IPCPatent = "scalar IPCPatent";
export declare const typeDefs: string[];
