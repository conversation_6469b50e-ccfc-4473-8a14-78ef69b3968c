'use strict';
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/batch.proto
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.BatchObjectsReply_BatchError =
  exports.BatchObjectsReply =
  exports.BatchObject_MultiTargetRefProps =
  exports.BatchObject_SingleTargetRefProps =
  exports.BatchObject_Properties =
  exports.BatchObject =
  exports.BatchObjectsRequest =
  exports.protobufPackage =
    void 0;
/* eslint-disable */
const minimal_js_1 = __importDefault(require('protobufjs/minimal.js'));
const struct_js_1 = require('../google/protobuf/struct.js');
const base_js_1 = require('./base.js');
exports.protobufPackage = 'weaviate.v1';
function createBaseBatchObjectsRequest() {
  return { objects: [], consistencyLevel: undefined };
}
exports.BatchObjectsRequest = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.objects) {
      exports.BatchObject.encode(v, writer.uint32(10).fork()).ldelim();
    }
    if (message.consistencyLevel !== undefined) {
      writer.uint32(16).int32(message.consistencyLevel);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchObjectsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.objects.push(exports.BatchObject.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.consistencyLevel = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      objects: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.objects)
        ? object.objects.map((e) => exports.BatchObject.fromJSON(e))
        : [],
      consistencyLevel: isSet(object.consistencyLevel)
        ? (0, base_js_1.consistencyLevelFromJSON)(object.consistencyLevel)
        : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.objects) === null || _a === void 0 ? void 0 : _a.length) {
      obj.objects = message.objects.map((e) => exports.BatchObject.toJSON(e));
    }
    if (message.consistencyLevel !== undefined) {
      obj.consistencyLevel = (0, base_js_1.consistencyLevelToJSON)(message.consistencyLevel);
    }
    return obj;
  },
  create(base) {
    return exports.BatchObjectsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseBatchObjectsRequest();
    message.objects =
      ((_a = object.objects) === null || _a === void 0
        ? void 0
        : _a.map((e) => exports.BatchObject.fromPartial(e))) || [];
    message.consistencyLevel = (_b = object.consistencyLevel) !== null && _b !== void 0 ? _b : undefined;
    return message;
  },
};
function createBaseBatchObject() {
  return {
    uuid: '',
    vector: [],
    properties: undefined,
    collection: '',
    tenant: '',
    vectorBytes: new Uint8Array(0),
    vectors: [],
  };
}
exports.BatchObject = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.uuid !== '') {
      writer.uint32(10).string(message.uuid);
    }
    writer.uint32(18).fork();
    for (const v of message.vector) {
      writer.float(v);
    }
    writer.ldelim();
    if (message.properties !== undefined) {
      exports.BatchObject_Properties.encode(message.properties, writer.uint32(26).fork()).ldelim();
    }
    if (message.collection !== '') {
      writer.uint32(34).string(message.collection);
    }
    if (message.tenant !== '') {
      writer.uint32(42).string(message.tenant);
    }
    if (message.vectorBytes.length !== 0) {
      writer.uint32(50).bytes(message.vectorBytes);
    }
    for (const v of message.vectors) {
      base_js_1.Vectors.encode(v, writer.uint32(186).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchObject();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.uuid = reader.string();
          continue;
        case 2:
          if (tag === 21) {
            message.vector.push(reader.float());
            continue;
          }
          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.vector.push(reader.float());
            }
            continue;
          }
          break;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.properties = exports.BatchObject_Properties.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.collection = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.tenant = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.vectorBytes = reader.bytes();
          continue;
        case 23:
          if (tag !== 186) {
            break;
          }
          message.vectors.push(base_js_1.Vectors.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : '',
      vector: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vector)
        ? object.vector.map((e) => globalThis.Number(e))
        : [],
      properties: isSet(object.properties)
        ? exports.BatchObject_Properties.fromJSON(object.properties)
        : undefined,
      collection: isSet(object.collection) ? globalThis.String(object.collection) : '',
      tenant: isSet(object.tenant) ? globalThis.String(object.tenant) : '',
      vectorBytes: isSet(object.vectorBytes) ? bytesFromBase64(object.vectorBytes) : new Uint8Array(0),
      vectors: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vectors)
        ? object.vectors.map((e) => base_js_1.Vectors.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b;
    const obj = {};
    if (message.uuid !== '') {
      obj.uuid = message.uuid;
    }
    if ((_a = message.vector) === null || _a === void 0 ? void 0 : _a.length) {
      obj.vector = message.vector;
    }
    if (message.properties !== undefined) {
      obj.properties = exports.BatchObject_Properties.toJSON(message.properties);
    }
    if (message.collection !== '') {
      obj.collection = message.collection;
    }
    if (message.tenant !== '') {
      obj.tenant = message.tenant;
    }
    if (message.vectorBytes.length !== 0) {
      obj.vectorBytes = base64FromBytes(message.vectorBytes);
    }
    if ((_b = message.vectors) === null || _b === void 0 ? void 0 : _b.length) {
      obj.vectors = message.vectors.map((e) => base_js_1.Vectors.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return exports.BatchObject.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseBatchObject();
    message.uuid = (_a = object.uuid) !== null && _a !== void 0 ? _a : '';
    message.vector = ((_b = object.vector) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    message.properties =
      object.properties !== undefined && object.properties !== null
        ? exports.BatchObject_Properties.fromPartial(object.properties)
        : undefined;
    message.collection = (_c = object.collection) !== null && _c !== void 0 ? _c : '';
    message.tenant = (_d = object.tenant) !== null && _d !== void 0 ? _d : '';
    message.vectorBytes = (_e = object.vectorBytes) !== null && _e !== void 0 ? _e : new Uint8Array(0);
    message.vectors =
      ((_f = object.vectors) === null || _f === void 0
        ? void 0
        : _f.map((e) => base_js_1.Vectors.fromPartial(e))) || [];
    return message;
  },
};
function createBaseBatchObject_Properties() {
  return {
    nonRefProperties: undefined,
    singleTargetRefProps: [],
    multiTargetRefProps: [],
    numberArrayProperties: [],
    intArrayProperties: [],
    textArrayProperties: [],
    booleanArrayProperties: [],
    objectProperties: [],
    objectArrayProperties: [],
    emptyListProps: [],
  };
}
exports.BatchObject_Properties = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.nonRefProperties !== undefined) {
      struct_js_1.Struct.encode(
        struct_js_1.Struct.wrap(message.nonRefProperties),
        writer.uint32(10).fork()
      ).ldelim();
    }
    for (const v of message.singleTargetRefProps) {
      exports.BatchObject_SingleTargetRefProps.encode(v, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.multiTargetRefProps) {
      exports.BatchObject_MultiTargetRefProps.encode(v, writer.uint32(26).fork()).ldelim();
    }
    for (const v of message.numberArrayProperties) {
      base_js_1.NumberArrayProperties.encode(v, writer.uint32(34).fork()).ldelim();
    }
    for (const v of message.intArrayProperties) {
      base_js_1.IntArrayProperties.encode(v, writer.uint32(42).fork()).ldelim();
    }
    for (const v of message.textArrayProperties) {
      base_js_1.TextArrayProperties.encode(v, writer.uint32(50).fork()).ldelim();
    }
    for (const v of message.booleanArrayProperties) {
      base_js_1.BooleanArrayProperties.encode(v, writer.uint32(58).fork()).ldelim();
    }
    for (const v of message.objectProperties) {
      base_js_1.ObjectProperties.encode(v, writer.uint32(66).fork()).ldelim();
    }
    for (const v of message.objectArrayProperties) {
      base_js_1.ObjectArrayProperties.encode(v, writer.uint32(74).fork()).ldelim();
    }
    for (const v of message.emptyListProps) {
      writer.uint32(82).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchObject_Properties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.nonRefProperties = struct_js_1.Struct.unwrap(
            struct_js_1.Struct.decode(reader, reader.uint32())
          );
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.singleTargetRefProps.push(
            exports.BatchObject_SingleTargetRefProps.decode(reader, reader.uint32())
          );
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.multiTargetRefProps.push(
            exports.BatchObject_MultiTargetRefProps.decode(reader, reader.uint32())
          );
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.numberArrayProperties.push(base_js_1.NumberArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.intArrayProperties.push(base_js_1.IntArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.textArrayProperties.push(base_js_1.TextArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.booleanArrayProperties.push(
            base_js_1.BooleanArrayProperties.decode(reader, reader.uint32())
          );
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.objectProperties.push(base_js_1.ObjectProperties.decode(reader, reader.uint32()));
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.objectArrayProperties.push(base_js_1.ObjectArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.emptyListProps.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      nonRefProperties: isObject(object.nonRefProperties) ? object.nonRefProperties : undefined,
      singleTargetRefProps: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.singleTargetRefProps
      )
        ? object.singleTargetRefProps.map((e) => exports.BatchObject_SingleTargetRefProps.fromJSON(e))
        : [],
      multiTargetRefProps: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.multiTargetRefProps
      )
        ? object.multiTargetRefProps.map((e) => exports.BatchObject_MultiTargetRefProps.fromJSON(e))
        : [],
      numberArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.numberArrayProperties
      )
        ? object.numberArrayProperties.map((e) => base_js_1.NumberArrayProperties.fromJSON(e))
        : [],
      intArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.intArrayProperties
      )
        ? object.intArrayProperties.map((e) => base_js_1.IntArrayProperties.fromJSON(e))
        : [],
      textArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.textArrayProperties
      )
        ? object.textArrayProperties.map((e) => base_js_1.TextArrayProperties.fromJSON(e))
        : [],
      booleanArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.booleanArrayProperties
      )
        ? object.booleanArrayProperties.map((e) => base_js_1.BooleanArrayProperties.fromJSON(e))
        : [],
      objectProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.objectProperties
      )
        ? object.objectProperties.map((e) => base_js_1.ObjectProperties.fromJSON(e))
        : [],
      objectArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.objectArrayProperties
      )
        ? object.objectArrayProperties.map((e) => base_js_1.ObjectArrayProperties.fromJSON(e))
        : [],
      emptyListProps: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.emptyListProps
      )
        ? object.emptyListProps.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j;
    const obj = {};
    if (message.nonRefProperties !== undefined) {
      obj.nonRefProperties = message.nonRefProperties;
    }
    if ((_a = message.singleTargetRefProps) === null || _a === void 0 ? void 0 : _a.length) {
      obj.singleTargetRefProps = message.singleTargetRefProps.map((e) =>
        exports.BatchObject_SingleTargetRefProps.toJSON(e)
      );
    }
    if ((_b = message.multiTargetRefProps) === null || _b === void 0 ? void 0 : _b.length) {
      obj.multiTargetRefProps = message.multiTargetRefProps.map((e) =>
        exports.BatchObject_MultiTargetRefProps.toJSON(e)
      );
    }
    if ((_c = message.numberArrayProperties) === null || _c === void 0 ? void 0 : _c.length) {
      obj.numberArrayProperties = message.numberArrayProperties.map((e) =>
        base_js_1.NumberArrayProperties.toJSON(e)
      );
    }
    if ((_d = message.intArrayProperties) === null || _d === void 0 ? void 0 : _d.length) {
      obj.intArrayProperties = message.intArrayProperties.map((e) => base_js_1.IntArrayProperties.toJSON(e));
    }
    if ((_e = message.textArrayProperties) === null || _e === void 0 ? void 0 : _e.length) {
      obj.textArrayProperties = message.textArrayProperties.map((e) =>
        base_js_1.TextArrayProperties.toJSON(e)
      );
    }
    if ((_f = message.booleanArrayProperties) === null || _f === void 0 ? void 0 : _f.length) {
      obj.booleanArrayProperties = message.booleanArrayProperties.map((e) =>
        base_js_1.BooleanArrayProperties.toJSON(e)
      );
    }
    if ((_g = message.objectProperties) === null || _g === void 0 ? void 0 : _g.length) {
      obj.objectProperties = message.objectProperties.map((e) => base_js_1.ObjectProperties.toJSON(e));
    }
    if ((_h = message.objectArrayProperties) === null || _h === void 0 ? void 0 : _h.length) {
      obj.objectArrayProperties = message.objectArrayProperties.map((e) =>
        base_js_1.ObjectArrayProperties.toJSON(e)
      );
    }
    if ((_j = message.emptyListProps) === null || _j === void 0 ? void 0 : _j.length) {
      obj.emptyListProps = message.emptyListProps;
    }
    return obj;
  },
  create(base) {
    return exports.BatchObject_Properties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    const message = createBaseBatchObject_Properties();
    message.nonRefProperties = (_a = object.nonRefProperties) !== null && _a !== void 0 ? _a : undefined;
    message.singleTargetRefProps =
      ((_b = object.singleTargetRefProps) === null || _b === void 0
        ? void 0
        : _b.map((e) => exports.BatchObject_SingleTargetRefProps.fromPartial(e))) || [];
    message.multiTargetRefProps =
      ((_c = object.multiTargetRefProps) === null || _c === void 0
        ? void 0
        : _c.map((e) => exports.BatchObject_MultiTargetRefProps.fromPartial(e))) || [];
    message.numberArrayProperties =
      ((_d = object.numberArrayProperties) === null || _d === void 0
        ? void 0
        : _d.map((e) => base_js_1.NumberArrayProperties.fromPartial(e))) || [];
    message.intArrayProperties =
      ((_e = object.intArrayProperties) === null || _e === void 0
        ? void 0
        : _e.map((e) => base_js_1.IntArrayProperties.fromPartial(e))) || [];
    message.textArrayProperties =
      ((_f = object.textArrayProperties) === null || _f === void 0
        ? void 0
        : _f.map((e) => base_js_1.TextArrayProperties.fromPartial(e))) || [];
    message.booleanArrayProperties =
      ((_g = object.booleanArrayProperties) === null || _g === void 0
        ? void 0
        : _g.map((e) => base_js_1.BooleanArrayProperties.fromPartial(e))) || [];
    message.objectProperties =
      ((_h = object.objectProperties) === null || _h === void 0
        ? void 0
        : _h.map((e) => base_js_1.ObjectProperties.fromPartial(e))) || [];
    message.objectArrayProperties =
      ((_j = object.objectArrayProperties) === null || _j === void 0
        ? void 0
        : _j.map((e) => base_js_1.ObjectArrayProperties.fromPartial(e))) || [];
    message.emptyListProps =
      ((_k = object.emptyListProps) === null || _k === void 0 ? void 0 : _k.map((e) => e)) || [];
    return message;
  },
};
function createBaseBatchObject_SingleTargetRefProps() {
  return { uuids: [], propName: '' };
}
exports.BatchObject_SingleTargetRefProps = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.uuids) {
      writer.uint32(10).string(v);
    }
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchObject_SingleTargetRefProps();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.uuids.push(reader.string());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      uuids: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.uuids)
        ? object.uuids.map((e) => globalThis.String(e))
        : [],
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.uuids) === null || _a === void 0 ? void 0 : _a.length) {
      obj.uuids = message.uuids;
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    return obj;
  },
  create(base) {
    return exports.BatchObject_SingleTargetRefProps.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseBatchObject_SingleTargetRefProps();
    message.uuids = ((_a = object.uuids) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.propName = (_b = object.propName) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function createBaseBatchObject_MultiTargetRefProps() {
  return { uuids: [], propName: '', targetCollection: '' };
}
exports.BatchObject_MultiTargetRefProps = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.uuids) {
      writer.uint32(10).string(v);
    }
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    if (message.targetCollection !== '') {
      writer.uint32(26).string(message.targetCollection);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchObject_MultiTargetRefProps();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.uuids.push(reader.string());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.targetCollection = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      uuids: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.uuids)
        ? object.uuids.map((e) => globalThis.String(e))
        : [],
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
      targetCollection: isSet(object.targetCollection) ? globalThis.String(object.targetCollection) : '',
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.uuids) === null || _a === void 0 ? void 0 : _a.length) {
      obj.uuids = message.uuids;
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    if (message.targetCollection !== '') {
      obj.targetCollection = message.targetCollection;
    }
    return obj;
  },
  create(base) {
    return exports.BatchObject_MultiTargetRefProps.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseBatchObject_MultiTargetRefProps();
    message.uuids = ((_a = object.uuids) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.propName = (_b = object.propName) !== null && _b !== void 0 ? _b : '';
    message.targetCollection = (_c = object.targetCollection) !== null && _c !== void 0 ? _c : '';
    return message;
  },
};
function createBaseBatchObjectsReply() {
  return { took: 0, errors: [] };
}
exports.BatchObjectsReply = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.took !== 0) {
      writer.uint32(13).float(message.took);
    }
    for (const v of message.errors) {
      exports.BatchObjectsReply_BatchError.encode(v, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchObjectsReply();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.took = reader.float();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.errors.push(exports.BatchObjectsReply_BatchError.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      took: isSet(object.took) ? globalThis.Number(object.took) : 0,
      errors: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.errors)
        ? object.errors.map((e) => exports.BatchObjectsReply_BatchError.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.took !== 0) {
      obj.took = message.took;
    }
    if ((_a = message.errors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.errors = message.errors.map((e) => exports.BatchObjectsReply_BatchError.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return exports.BatchObjectsReply.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseBatchObjectsReply();
    message.took = (_a = object.took) !== null && _a !== void 0 ? _a : 0;
    message.errors =
      ((_b = object.errors) === null || _b === void 0
        ? void 0
        : _b.map((e) => exports.BatchObjectsReply_BatchError.fromPartial(e))) || [];
    return message;
  },
};
function createBaseBatchObjectsReply_BatchError() {
  return { index: 0, error: '' };
}
exports.BatchObjectsReply_BatchError = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.index !== 0) {
      writer.uint32(8).int32(message.index);
    }
    if (message.error !== '') {
      writer.uint32(18).string(message.error);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchObjectsReply_BatchError();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.index = reader.int32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.error = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      index: isSet(object.index) ? globalThis.Number(object.index) : 0,
      error: isSet(object.error) ? globalThis.String(object.error) : '',
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.index !== 0) {
      obj.index = Math.round(message.index);
    }
    if (message.error !== '') {
      obj.error = message.error;
    }
    return obj;
  },
  create(base) {
    return exports.BatchObjectsReply_BatchError.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseBatchObjectsReply_BatchError();
    message.index = (_a = object.index) !== null && _a !== void 0 ? _a : 0;
    message.error = (_b = object.error) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function bytesFromBase64(b64) {
  if (globalThis.Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}
function base64FromBytes(arr) {
  if (globalThis.Buffer) {
    return globalThis.Buffer.from(arr).toString('base64');
  } else {
    const bin = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(''));
  }
}
function isObject(value) {
  return typeof value === 'object' && value !== null;
}
function isSet(value) {
  return value !== null && value !== undefined;
}
