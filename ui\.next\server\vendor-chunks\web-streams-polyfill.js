/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-streams-polyfill";
exports.ids = ["vendor-chunks/web-streams-polyfill"];
exports.modules = {

/***/ "(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.js":
/*!************************************************************!*\
  !*** ./node_modules/web-streams-polyfill/dist/ponyfill.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("/**\n * @license\n * web-streams-polyfill v4.0.0-beta.3\n * Copyright 2021 Mattias Buelens, Diwank Singh Tomer and other contributors.\n * This code is released under the MIT license.\n * SPDX-License-Identifier: MIT\n */\n!function(e,t){ true?t(exports):0}(this,(function(e){\"use strict\";const t=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?Symbol:e=>`Symbol(${e})`;function r(){}function o(e){return\"object\"==typeof e&&null!==e||\"function\"==typeof e}const n=r;function a(e,t){try{Object.defineProperty(e,\"name\",{value:t,configurable:!0})}catch(e){}}const i=Promise,l=Promise.prototype.then,s=Promise.resolve.bind(i),u=Promise.reject.bind(i);function c(e){return new i(e)}function d(e){return s(e)}function f(e){return u(e)}function b(e,t,r){return l.call(e,t,r)}function h(e,t,r){b(b(e,t,r),void 0,n)}function _(e,t){h(e,t)}function p(e,t){h(e,void 0,t)}function m(e,t,r){return b(e,t,r)}function y(e){b(e,void 0,n)}let g=e=>{if(\"function\"==typeof queueMicrotask)g=queueMicrotask;else{const e=d(void 0);g=t=>b(e,t)}return g(e)};function S(e,t,r){if(\"function\"!=typeof e)throw new TypeError(\"Argument is not a function\");return Function.prototype.apply.call(e,t,r)}function w(e,t,r){try{return d(S(e,t,r))}catch(e){return f(e)}}class v{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,a=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,a}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const R=t(\"[[AbortSteps]]\"),T=t(\"[[ErrorSteps]]\"),q=t(\"[[CancelSteps]]\"),C=t(\"[[PullSteps]]\"),P=t(\"[[ReleaseSteps]]\");function E(e,t){e._ownerReadableStream=t,t._reader=e,\"readable\"===t._state?B(e):\"closed\"===t._state?function(e){B(e),z(e)}(e):A(e,t._storedError)}function W(e,t){return Xt(e._ownerReadableStream,t)}function O(e){const t=e._ownerReadableStream;\"readable\"===t._state?j(e,new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")):function(e,t){A(e,t)}(e,new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")),t._readableStreamController[P](),t._reader=void 0,e._ownerReadableStream=void 0}function k(e){return new TypeError(\"Cannot \"+e+\" a stream using a released reader\")}function B(e){e._closedPromise=c(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function A(e,t){B(e),j(e,t)}function j(e,t){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function z(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const L=Number.isFinite||function(e){return\"number\"==typeof e&&isFinite(e)},F=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function D(e,t){if(void 0!==e&&(\"object\"!=typeof(r=e)&&\"function\"!=typeof r))throw new TypeError(`${t} is not an object.`);var r}function I(e,t){if(\"function\"!=typeof e)throw new TypeError(`${t} is not a function.`)}function $(e,t){if(!function(e){return\"object\"==typeof e&&null!==e||\"function\"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function M(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function Y(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function Q(e){return Number(e)}function N(e){return 0===e?0:e}function x(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=N(o),!L(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return N(F(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return L(o)&&0!==o?o:0}function H(e){if(!o(e))return!1;if(\"function\"!=typeof e.getReader)return!1;try{return\"boolean\"==typeof e.locked}catch(e){return!1}}function V(e){if(!o(e))return!1;if(\"function\"!=typeof e.getWriter)return!1;try{return\"boolean\"==typeof e.locked}catch(e){return!1}}function U(e,t){if(!Ut(e))throw new TypeError(`${t} is not a ReadableStream.`)}function G(e,t){e._reader._readRequests.push(t)}function X(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function J(e){return e._reader._readRequests.length}function K(e){const t=e._reader;return void 0!==t&&!!Z(t)}class ReadableStreamDefaultReader{constructor(e){if(M(e,1,\"ReadableStreamDefaultReader\"),U(e,\"First parameter\"),Gt(e))throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");E(this,e),this._readRequests=new v}get closed(){return Z(this)?this._closedPromise:f(te(\"closed\"))}cancel(e){return Z(this)?void 0===this._ownerReadableStream?f(k(\"cancel\")):W(this,e):f(te(\"cancel\"))}read(){if(!Z(this))return f(te(\"read\"));if(void 0===this._ownerReadableStream)return f(k(\"read from\"));let e,t;const r=c(((r,o)=>{e=r,t=o}));return function(e,t){const r=e._ownerReadableStream;r._disturbed=!0,\"closed\"===r._state?t._closeSteps():\"errored\"===r._state?t._errorSteps(r._storedError):r._readableStreamController[C](t)}(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!Z(this))throw te(\"releaseLock\");void 0!==this._ownerReadableStream&&function(e){O(e);const t=new TypeError(\"Reader was released\");ee(e,t)}(this)}}function Z(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readRequests\")&&e instanceof ReadableStreamDefaultReader)}function ee(e,t){const r=e._readRequests;e._readRequests=new v,r.forEach((e=>{e._errorSteps(t)}))}function te(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(ReadableStreamDefaultReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(ReadableStreamDefaultReader.prototype.cancel,\"cancel\"),a(ReadableStreamDefaultReader.prototype.read,\"read\"),a(ReadableStreamDefaultReader.prototype.releaseLock,\"releaseLock\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(ReadableStreamDefaultReader.prototype,t.toStringTag,{value:\"ReadableStreamDefaultReader\",configurable:!0});class re{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?m(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?m(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;return void 0===e?f(k(\"iterate\")):b(e.read(),(e=>{var t;return this._ongoingPromise=void 0,e.done&&(this._isFinished=!0,null===(t=this._reader)||void 0===t||t.releaseLock(),this._reader=void 0),e}),(e=>{var t;throw this._ongoingPromise=void 0,this._isFinished=!0,null===(t=this._reader)||void 0===t||t.releaseLock(),this._reader=void 0,e}))}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(void 0===t)return f(k(\"finish iterating\"));if(this._reader=void 0,!this._preventCancel){const r=t.cancel(e);return t.releaseLock(),m(r,(()=>({value:e,done:!0})))}return t.releaseLock(),d({value:e,done:!0})}}const oe={next(){return ne(this)?this._asyncIteratorImpl.next():f(ae(\"next\"))},return(e){return ne(this)?this._asyncIteratorImpl.return(e):f(ae(\"return\"))}};function ne(e){if(!o(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,\"_asyncIteratorImpl\"))return!1;try{return e._asyncIteratorImpl instanceof re}catch(e){return!1}}function ae(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}\"symbol\"==typeof t.asyncIterator&&Object.defineProperty(oe,t.asyncIterator,{value(){return this},writable:!0,configurable:!0});const ie=Number.isNaN||function(e){return e!=e};function le(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}function se(e){const t=function(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return le(n,0,e,t,o),n}(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function ue(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ce(e,t,r){if(\"number\"!=typeof(o=r)||ie(o)||o<0||r===1/0)throw new RangeError(\"Size must be a finite, non-NaN, non-negative number.\");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function de(e){e._queue=new v,e._queueTotalSize=0}class ReadableStreamBYOBRequest{constructor(){throw new TypeError(\"Illegal constructor\")}get view(){if(!be(this))throw Ae(\"view\");return this._view}respond(e){if(!be(this))throw Ae(\"respond\");if(M(e,1,\"respond\"),e=x(e,\"First parameter\"),void 0===this._associatedReadableByteStreamController)throw new TypeError(\"This BYOB request has been invalidated\");this._view.buffer,function(e,t){const r=e._pendingPullIntos.peek();if(\"closed\"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError(\"bytesWritten must be 0 when calling respond() on a closed stream\")}else{if(0===t)throw new TypeError(\"bytesWritten must be greater than 0 when calling respond() on a readable stream\");if(r.bytesFilled+t>r.byteLength)throw new RangeError(\"bytesWritten out of range\")}r.buffer=r.buffer,Ce(e,t)}(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!be(this))throw Ae(\"respondWithNewView\");if(M(e,1,\"respondWithNewView\"),!ArrayBuffer.isView(e))throw new TypeError(\"You can only respond with array buffer views\");if(void 0===this._associatedReadableByteStreamController)throw new TypeError(\"This BYOB request has been invalidated\");e.buffer,function(e,t){const r=e._pendingPullIntos.peek();if(\"closed\"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError(\"The view's length must be 0 when calling respondWithNewView() on a closed stream\")}else if(0===t.byteLength)throw new TypeError(\"The view's length must be greater than 0 when calling respondWithNewView() on a readable stream\");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError(\"The region specified by view does not match byobRequest\");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError(\"The buffer of view has different capacity than byobRequest\");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError(\"The region specified by view is larger than byobRequest\");const o=t.byteLength;r.buffer=t.buffer,Ce(e,o)}(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ReadableStreamBYOBRequest.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),a(ReadableStreamBYOBRequest.prototype.respond,\"respond\"),a(ReadableStreamBYOBRequest.prototype.respondWithNewView,\"respondWithNewView\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(ReadableStreamBYOBRequest.prototype,t.toStringTag,{value:\"ReadableStreamBYOBRequest\",configurable:!0});class ReadableByteStreamController{constructor(){throw new TypeError(\"Illegal constructor\")}get byobRequest(){if(!fe(this))throw je(\"byobRequest\");return function(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ReadableStreamBYOBRequest.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}(this)}get desiredSize(){if(!fe(this))throw je(\"desiredSize\");return ke(this)}close(){if(!fe(this))throw je(\"close\");if(this._closeRequested)throw new TypeError(\"The stream has already been closed; do not close it again!\");const e=this._controlledReadableByteStream._state;if(\"readable\"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);!function(e){const t=e._controlledReadableByteStream;if(e._closeRequested||\"readable\"!==t._state)return;if(e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0){if(e._pendingPullIntos.peek().bytesFilled>0){const t=new TypeError(\"Insufficient bytes to fill elements in the given buffer\");throw We(e,t),t}}Ee(e),Jt(t)}(this)}enqueue(e){if(!fe(this))throw je(\"enqueue\");if(M(e,1,\"enqueue\"),!ArrayBuffer.isView(e))throw new TypeError(\"chunk must be an array buffer view\");if(0===e.byteLength)throw new TypeError(\"chunk must have non-zero byteLength\");if(0===e.buffer.byteLength)throw new TypeError(\"chunk's buffer must have non-zero byteLength\");if(this._closeRequested)throw new TypeError(\"stream is closed or draining\");const t=this._controlledReadableByteStream._state;if(\"readable\"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);!function(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||\"readable\"!==r._state)return;const o=t.buffer,n=t.byteOffset,a=t.byteLength,i=o;if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();t.buffer,0,Te(e),t.buffer=t.buffer,\"none\"===t.readerType&&Se(e,t)}if(K(r))if(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;Oe(e,t._readRequests.shift())}}(e),0===J(r))ye(e,i,n,a);else{e._pendingPullIntos.length>0&&Pe(e);X(r,new Uint8Array(i,n,a),!1)}else Fe(r)?(ye(e,i,n,a),qe(e)):ye(e,i,n,a);he(e)}(this,e)}error(e){if(!fe(this))throw je(\"error\");We(this,e)}[q](e){_e(this),de(this);const t=this._cancelAlgorithm(e);return Ee(this),t}[C](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void Oe(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:\"default\"};this._pendingPullIntos.push(o)}G(t,e),he(this)}[P](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType=\"none\",this._pendingPullIntos=new v,this._pendingPullIntos.push(e)}}}function fe(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledReadableByteStream\")&&e instanceof ReadableByteStreamController)}function be(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_associatedReadableByteStreamController\")&&e instanceof ReadableStreamBYOBRequest)}function he(e){const t=function(e){const t=e._controlledReadableByteStream;if(\"readable\"!==t._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(K(t)&&J(t)>0)return!0;if(Fe(t)&&Le(t)>0)return!0;if(ke(e)>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;h(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,he(e)),null)),(t=>(We(e,t),null)))}function _e(e){Te(e),e._pendingPullIntos=new v}function pe(e,t){let r=!1;\"closed\"===e._state&&(r=!0);const o=me(t);\"default\"===t.readerType?X(e,o,r):function(e,t,r){const o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function me(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function ye(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function ge(e,t,r,o){let n;try{n=t.slice(r,r+o)}catch(t){throw We(e,t),t}ye(e,n,0,o)}function Se(e,t){t.bytesFilled>0&&ge(e,t.buffer,t.byteOffset,t.bytesFilled),Pe(e)}function we(e,t){const r=t.elementSize,o=t.bytesFilled-t.bytesFilled%r,n=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+n,i=a-a%r;let l=n,s=!1;i>o&&(l=i-t.bytesFilled,s=!0);const u=e._queue;for(;l>0;){const r=u.peek(),o=Math.min(l,r.byteLength),n=t.byteOffset+t.bytesFilled;le(t.buffer,n,r.buffer,r.byteOffset,o),r.byteLength===o?u.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,ve(e,o,t),l-=o}return s}function ve(e,t,r){r.bytesFilled+=t}function Re(e){0===e._queueTotalSize&&e._closeRequested?(Ee(e),Jt(e._controlledReadableByteStream)):he(e)}function Te(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function qe(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();we(e,t)&&(Pe(e),pe(e._controlledReadableByteStream,t))}}function Ce(e,t){const r=e._pendingPullIntos.peek();Te(e);\"closed\"===e._controlledReadableByteStream._state?function(e,t){\"none\"===t.readerType&&Pe(e);const r=e._controlledReadableByteStream;if(Fe(r))for(;Le(r)>0;)pe(r,Pe(e))}(e,r):function(e,t,r){if(ve(0,t,r),\"none\"===r.readerType)return Se(e,r),void qe(e);if(r.bytesFilled<r.elementSize)return;Pe(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled;ge(e,r.buffer,t-o,o)}r.bytesFilled-=o,pe(e._controlledReadableByteStream,r),qe(e)}(e,t,r),he(e)}function Pe(e){return e._pendingPullIntos.shift()}function Ee(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function We(e,t){const r=e._controlledReadableByteStream;\"readable\"===r._state&&(_e(e),de(e),Ee(e),Kt(r,t))}function Oe(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,Re(e);const o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function ke(e){const t=e._controlledReadableByteStream._state;return\"errored\"===t?null:\"closed\"===t?0:e._strategyHWM-e._queueTotalSize}function Be(e,t,r){const o=Object.create(ReadableByteStreamController.prototype);let n,a,i;n=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.pull?()=>t.pull(o):()=>d(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0);const l=t.autoAllocateChunkSize;if(0===l)throw new TypeError(\"autoAllocateChunkSize must be greater than 0\");!function(e,t,r,o,n,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,de(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=i,t._pendingPullIntos=new v,e._readableStreamController=t,h(d(r()),(()=>(t._started=!0,he(t),null)),(e=>(We(t,e),null)))}(e,o,n,a,i,r,l)}function Ae(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function je(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function ze(e,t){e._reader._readIntoRequests.push(t)}function Le(e){return e._reader._readIntoRequests.length}function Fe(e){const t=e._reader;return void 0!==t&&!!De(t)}Object.defineProperties(ReadableByteStreamController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),a(ReadableByteStreamController.prototype.close,\"close\"),a(ReadableByteStreamController.prototype.enqueue,\"enqueue\"),a(ReadableByteStreamController.prototype.error,\"error\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(ReadableByteStreamController.prototype,t.toStringTag,{value:\"ReadableByteStreamController\",configurable:!0});class ReadableStreamBYOBReader{constructor(e){if(M(e,1,\"ReadableStreamBYOBReader\"),U(e,\"First parameter\"),Gt(e))throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");if(!fe(e._readableStreamController))throw new TypeError(\"Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source\");E(this,e),this._readIntoRequests=new v}get closed(){return De(this)?this._closedPromise:f($e(\"closed\"))}cancel(e){return De(this)?void 0===this._ownerReadableStream?f(k(\"cancel\")):W(this,e):f($e(\"cancel\"))}read(e){if(!De(this))return f($e(\"read\"));if(!ArrayBuffer.isView(e))return f(new TypeError(\"view must be an array buffer view\"));if(0===e.byteLength)return f(new TypeError(\"view must have non-zero byteLength\"));if(0===e.buffer.byteLength)return f(new TypeError(\"view's buffer must have non-zero byteLength\"));if(e.buffer,void 0===this._ownerReadableStream)return f(k(\"read from\"));let t,r;const o=c(((e,o)=>{t=e,r=o}));return function(e,t,r){const o=e._ownerReadableStream;o._disturbed=!0,\"errored\"===o._state?r._errorSteps(o._storedError):function(e,t,r){const o=e._controlledReadableByteStream;let n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);const a=t.constructor,i=t.buffer,l={buffer:i,bufferByteLength:i.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,viewConstructor:a,readerType:\"byob\"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void ze(o,r);if(\"closed\"!==o._state){if(e._queueTotalSize>0){if(we(e,l)){const t=me(l);return Re(e),void r._chunkSteps(t)}if(e._closeRequested){const t=new TypeError(\"Insufficient bytes to fill elements in the given buffer\");return We(e,t),void r._errorSteps(t)}}e._pendingPullIntos.push(l),ze(o,r),he(e)}else{const e=new a(l.buffer,l.byteOffset,0);r._closeSteps(e)}}(o._readableStreamController,t,r)}(this,e,{_chunkSteps:e=>t({value:e,done:!1}),_closeSteps:e=>t({value:e,done:!0}),_errorSteps:e=>r(e)}),o}releaseLock(){if(!De(this))throw $e(\"releaseLock\");void 0!==this._ownerReadableStream&&function(e){O(e);const t=new TypeError(\"Reader was released\");Ie(e,t)}(this)}}function De(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readIntoRequests\")&&e instanceof ReadableStreamBYOBReader)}function Ie(e,t){const r=e._readIntoRequests;e._readIntoRequests=new v,r.forEach((e=>{e._errorSteps(t)}))}function $e(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function Me(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(ie(r)||r<0)throw new RangeError(\"Invalid highWaterMark\");return r}function Ye(e){const{size:t}=e;return t||(()=>1)}function Qe(e,t){D(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:Q(r),size:void 0===o?void 0:Ne(o,`${t} has member 'size' that`)}}function Ne(e,t){return I(e,t),t=>Q(e(t))}function xe(e,t,r){return I(e,r),r=>w(e,t,[r])}function He(e,t,r){return I(e,r),()=>w(e,t,[])}function Ve(e,t,r){return I(e,r),r=>S(e,t,[r])}function Ue(e,t,r){return I(e,r),(r,o)=>w(e,t,[r,o])}Object.defineProperties(ReadableStreamBYOBReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(ReadableStreamBYOBReader.prototype.cancel,\"cancel\"),a(ReadableStreamBYOBReader.prototype.read,\"read\"),a(ReadableStreamBYOBReader.prototype.releaseLock,\"releaseLock\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(ReadableStreamBYOBReader.prototype,t.toStringTag,{value:\"ReadableStreamBYOBReader\",configurable:!0});const Ge=\"function\"==typeof AbortController;class WritableStream{constructor(e={},t={}){void 0===e?e=null:$(e,\"First parameter\");const r=Qe(t,\"Second parameter\"),o=function(e,t){D(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===r?void 0:xe(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:He(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:Ve(n,e,`${t} has member 'start' that`),write:void 0===i?void 0:Ue(i,e,`${t} has member 'write' that`),type:a}}(e,\"First parameter\");var n;(n=this)._state=\"writable\",n._storedError=void 0,n._writer=void 0,n._writableStreamController=void 0,n._writeRequests=new v,n._inFlightWriteRequest=void 0,n._closeRequest=void 0,n._inFlightCloseRequest=void 0,n._pendingAbortRequest=void 0,n._backpressure=!1;if(void 0!==o.type)throw new RangeError(\"Invalid type is specified\");const a=Ye(r);!function(e,t,r,o){const n=Object.create(WritableStreamDefaultController.prototype);let a,i,l,s;a=void 0!==t.start?()=>t.start(n):()=>{};i=void 0!==t.write?e=>t.write(e,n):()=>d(void 0);l=void 0!==t.close?()=>t.close():()=>d(void 0);s=void 0!==t.abort?e=>t.abort(e):()=>d(void 0);!function(e,t,r,o,n,a,i,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,de(t),t._abortReason=void 0,t._abortController=function(){if(Ge)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=i,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=a;const s=ht(t);at(e,s);const u=r();h(d(u),(()=>(t._started=!0,ft(t),null)),(r=>(t._started=!0,et(e,r),null)))}(e,n,a,i,l,s,r,o)}(this,o,Me(r,1),a)}get locked(){if(!Xe(this))throw pt(\"locked\");return Je(this)}abort(e){return Xe(this)?Je(this)?f(new TypeError(\"Cannot abort a stream that already has a writer\")):Ke(this,e):f(pt(\"abort\"))}close(){return Xe(this)?Je(this)?f(new TypeError(\"Cannot close a stream that already has a writer\")):ot(this)?f(new TypeError(\"Cannot close an already-closing stream\")):Ze(this):f(pt(\"close\"))}getWriter(){if(!Xe(this))throw pt(\"getWriter\");return new WritableStreamDefaultWriter(this)}}function Xe(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_writableStreamController\")&&e instanceof WritableStream)}function Je(e){return void 0!==e._writer}function Ke(e,t){var r;if(\"closed\"===e._state||\"errored\"===e._state)return d(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const o=e._state;if(\"closed\"===o||\"errored\"===o)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;\"erroring\"===o&&(n=!0,t=void 0);const a=c(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||tt(e,t),a}function Ze(e){const t=e._state;if(\"closed\"===t||\"errored\"===t)return f(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=c(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&\"writable\"===t&&Et(o),ce(n=e._writableStreamController,st,0),ft(n),r}function et(e,t){\"writable\"!==e._state?rt(e):tt(e,t)}function tt(e,t){const r=e._writableStreamController;e._state=\"erroring\",e._storedError=t;const o=e._writer;void 0!==o&&lt(o,t),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&r._started&&rt(e)}function rt(e){e._state=\"errored\",e._writableStreamController[T]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new v,void 0===e._pendingAbortRequest)return void nt(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void nt(e);h(e._writableStreamController[R](r._reason),(()=>(r._resolve(),nt(e),null)),(t=>(r._reject(t),nt(e),null)))}function ot(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function nt(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&vt(t,e._storedError)}function at(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){Tt(e)}(r):Et(r)),e._backpressure=t}Object.defineProperties(WritableStream.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),a(WritableStream.prototype.abort,\"abort\"),a(WritableStream.prototype.close,\"close\"),a(WritableStream.prototype.getWriter,\"getWriter\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(WritableStream.prototype,t.toStringTag,{value:\"WritableStream\",configurable:!0});class WritableStreamDefaultWriter{constructor(e){if(M(e,1,\"WritableStreamDefaultWriter\"),function(e,t){if(!Xe(e))throw new TypeError(`${t} is not a WritableStream.`)}(e,\"First parameter\"),Je(e))throw new TypeError(\"This stream has already been locked for exclusive writing by another writer\");this._ownerWritableStream=e,e._writer=this;const t=e._state;if(\"writable\"===t)!ot(e)&&e._backpressure?Tt(this):Ct(this),St(this);else if(\"erroring\"===t)qt(this,e._storedError),St(this);else if(\"closed\"===t)Ct(this),St(r=this),Rt(r);else{const t=e._storedError;qt(this,t),wt(this,t)}var r}get closed(){return it(this)?this._closedPromise:f(yt(\"closed\"))}get desiredSize(){if(!it(this))throw yt(\"desiredSize\");if(void 0===this._ownerWritableStream)throw gt(\"desiredSize\");return function(e){const t=e._ownerWritableStream,r=t._state;if(\"errored\"===r||\"erroring\"===r)return null;if(\"closed\"===r)return 0;return dt(t._writableStreamController)}(this)}get ready(){return it(this)?this._readyPromise:f(yt(\"ready\"))}abort(e){return it(this)?void 0===this._ownerWritableStream?f(gt(\"abort\")):function(e,t){return Ke(e._ownerWritableStream,t)}(this,e):f(yt(\"abort\"))}close(){if(!it(this))return f(yt(\"close\"));const e=this._ownerWritableStream;return void 0===e?f(gt(\"close\")):ot(e)?f(new TypeError(\"Cannot close an already-closing stream\")):Ze(this._ownerWritableStream)}releaseLock(){if(!it(this))throw yt(\"releaseLock\");void 0!==this._ownerWritableStream&&function(e){const t=e._ownerWritableStream,r=new TypeError(\"Writer was released and can no longer be used to monitor the stream's closedness\");lt(e,r),function(e,t){\"pending\"===e._closedPromiseState?vt(e,t):function(e,t){wt(e,t)}(e,t)}(e,r),t._writer=void 0,e._ownerWritableStream=void 0}(this)}write(e){return it(this)?void 0===this._ownerWritableStream?f(gt(\"write to\")):function(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return bt(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return f(gt(\"write to\"));const a=r._state;if(\"errored\"===a)return f(r._storedError);if(ot(r)||\"closed\"===a)return f(new TypeError(\"The stream is closing or closed and cannot be written to\"));if(\"erroring\"===a)return f(r._storedError);const i=function(e){return c(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{ce(e,t,r)}catch(t){return void bt(e,t)}const o=e._controlledWritableStream;if(!ot(o)&&\"writable\"===o._state){at(o,ht(e))}ft(e)}(o,t,n),i}(this,e):f(yt(\"write\"))}}function it(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_ownerWritableStream\")&&e instanceof WritableStreamDefaultWriter)}function lt(e,t){\"pending\"===e._readyPromiseState?Pt(e,t):function(e,t){qt(e,t)}(e,t)}Object.defineProperties(WritableStreamDefaultWriter.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),a(WritableStreamDefaultWriter.prototype.abort,\"abort\"),a(WritableStreamDefaultWriter.prototype.close,\"close\"),a(WritableStreamDefaultWriter.prototype.releaseLock,\"releaseLock\"),a(WritableStreamDefaultWriter.prototype.write,\"write\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(WritableStreamDefaultWriter.prototype,t.toStringTag,{value:\"WritableStreamDefaultWriter\",configurable:!0});const st={};class WritableStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get abortReason(){if(!ut(this))throw mt(\"abortReason\");return this._abortReason}get signal(){if(!ut(this))throw mt(\"signal\");if(void 0===this._abortController)throw new TypeError(\"WritableStreamDefaultController.prototype.signal is not supported\");return this._abortController.signal}error(e){if(!ut(this))throw mt(\"error\");\"writable\"===this._controlledWritableStream._state&&_t(this,e)}[R](e){const t=this._abortAlgorithm(e);return ct(this),t}[T](){de(this)}}function ut(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledWritableStream\")&&e instanceof WritableStreamDefaultController)}function ct(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function dt(e){return e._strategyHWM-e._queueTotalSize}function ft(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if(\"erroring\"===t._state)return void rt(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===st?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),ue(e);const r=e._closeAlgorithm();ct(e),h(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,\"erroring\"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state=\"closed\";const t=e._writer;void 0!==t&&Rt(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),et(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r);h(e._writeAlgorithm(t),(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(ue(e),!ot(r)&&\"writable\"===t){const t=ht(e);at(r,t)}return ft(e),null}),(t=>(\"writable\"===r._state&&ct(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,et(e,t)}(r,t),null)))}(e,r)}function bt(e,t){\"writable\"===e._controlledWritableStream._state&&_t(e,t)}function ht(e){return dt(e)<=0}function _t(e,t){const r=e._controlledWritableStream;ct(e),tt(r,t)}function pt(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function mt(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function yt(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function gt(e){return new TypeError(\"Cannot \"+e+\" a stream using a released writer\")}function St(e){e._closedPromise=c(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState=\"pending\"}))}function wt(e,t){St(e),vt(e,t)}function vt(e,t){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState=\"rejected\")}function Rt(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState=\"resolved\")}function Tt(e){e._readyPromise=c(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState=\"pending\"}function qt(e,t){Tt(e),Pt(e,t)}function Ct(e){Tt(e),Et(e)}function Pt(e,t){void 0!==e._readyPromise_reject&&(y(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState=\"rejected\")}function Et(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState=\"fulfilled\")}Object.defineProperties(WritableStreamDefaultController.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(WritableStreamDefaultController.prototype,t.toStringTag,{value:\"WritableStreamDefaultController\",configurable:!0});const Wt=\"undefined\"!=typeof DOMException?DOMException:void 0;const Ot=function(e){if(\"function\"!=typeof e&&\"object\"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(Wt)?Wt:function(){const e=function(e,t){this.message=e||\"\",this.name=t||\"Error\",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,\"constructor\",{value:e,writable:!0,configurable:!0}),e}();function kt(e,t,r,o,n,a){const i=e.getReader(),l=t.getWriter();Ut(e)&&(e._disturbed=!0);let s,u,p,S=!1,w=!1,v=\"readable\",R=\"writable\",T=!1,q=!1;const C=c((e=>{p=e}));let P=Promise.resolve(void 0);return c(((E,W)=>{let O;function k(){if(S)return;const e=c(((e,t)=>{!function r(o){o?e():b(function(){if(S)return d(!0);return b(l.ready,(()=>b(i.read(),(e=>!!e.done||(P=l.write(e.value),y(P),!1)))))}(),r,t)}(!1)}));y(e)}function B(){return v=\"closed\",r?L():z((()=>(Xe(t)&&(T=ot(t),R=t._state),T||\"closed\"===R?d(void 0):\"erroring\"===R||\"errored\"===R?f(u):(T=!0,l.close()))),!1,void 0),null}function A(e){return S||(v=\"errored\",s=e,o?L(!0,e):z((()=>l.abort(e)),!0,e)),null}function j(e){return w||(R=\"errored\",u=e,n?L(!0,e):z((()=>i.cancel(e)),!0,e)),null}if(void 0!==a&&(O=()=>{const e=void 0!==a.reason?a.reason:new Ot(\"Aborted\",\"AbortError\"),t=[];o||t.push((()=>\"writable\"===R?l.abort(e):d(void 0))),n||t.push((()=>\"readable\"===v?i.cancel(e):d(void 0))),z((()=>Promise.all(t.map((e=>e())))),!0,e)},a.aborted?O():a.addEventListener(\"abort\",O)),Ut(e)&&(v=e._state,s=e._storedError),Xe(t)&&(R=t._state,u=t._storedError,T=ot(t)),Ut(e)&&Xe(t)&&(q=!0,p()),\"errored\"===v)A(s);else if(\"erroring\"===R||\"errored\"===R)j(u);else if(\"closed\"===v)B();else if(T||\"closed\"===R){const e=new TypeError(\"the destination writable stream closed before all data could be piped to it\");n?L(!0,e):z((()=>i.cancel(e)),!0,e)}function z(e,t,r){function o(){return\"writable\"!==R||T?n():_(function(){let e;return d(function t(){if(e!==P)return e=P,m(P,t,t)}())}(),n),null}function n(){return e?h(e(),(()=>F(t,r)),(e=>F(!0,e))):F(t,r),null}S||(S=!0,q?o():_(C,o))}function L(e,t){z(void 0,e,t)}function F(e,t){return w=!0,l.releaseLock(),i.releaseLock(),void 0!==a&&a.removeEventListener(\"abort\",O),e?W(t):E(void 0),null}S||(h(i.closed,B,A),h(l.closed,(function(){return w||(R=\"closed\"),null}),j)),q?k():g((()=>{q=!0,p(),k()}))}))}function Bt(e,t){return function(e){try{return e.getReader({mode:\"byob\"}).releaseLock(),!0}catch(e){return!1}}(e)?function(e){let t,r,o,n,a,i=e.getReader(),l=!1,s=!1,u=!1,f=!1,b=!1,_=!1;const m=c((e=>{a=e}));function y(e){p(e.closed,(t=>(e!==i||(o.error(t),n.error(t),b&&_||a(void 0)),null)))}function g(){l&&(i.releaseLock(),i=e.getReader(),y(i),l=!1),h(i.read(),(e=>{var t,r;if(u=!1,f=!1,e.done)return b||o.close(),_||n.close(),null===(t=o.byobRequest)||void 0===t||t.respond(0),null===(r=n.byobRequest)||void 0===r||r.respond(0),b&&_||a(void 0),null;const l=e.value,c=l;let d=l;if(!b&&!_)try{d=se(l)}catch(e){return o.error(e),n.error(e),a(i.cancel(e)),null}return b||o.enqueue(c),_||n.enqueue(d),s=!1,u?w():f&&v(),null}),(()=>(s=!1,null)))}function S(t,r){l||(i.releaseLock(),i=e.getReader({mode:\"byob\"}),y(i),l=!0);const c=r?n:o,d=r?o:n;h(i.read(t),(e=>{var t;u=!1,f=!1;const o=r?_:b,n=r?b:_;if(e.done){o||c.close(),n||d.close();const r=e.value;return void 0!==r&&(o||c.byobRequest.respondWithNewView(r),n||null===(t=d.byobRequest)||void 0===t||t.respond(0)),o&&n||a(void 0),null}const l=e.value;if(n)o||c.byobRequest.respondWithNewView(l);else{let e;try{e=se(l)}catch(e){return c.error(e),d.error(e),a(i.cancel(e)),null}o||c.byobRequest.respondWithNewView(l),d.enqueue(e)}return s=!1,u?w():f&&v(),null}),(()=>(s=!1,null)))}function w(){if(s)return u=!0,d(void 0);s=!0;const e=o.byobRequest;return null===e?g():S(e.view,!1),d(void 0)}function v(){if(s)return f=!0,d(void 0);s=!0;const e=n.byobRequest;return null===e?g():S(e.view,!0),d(void 0)}function R(e){if(b=!0,t=e,_){const e=[t,r],o=i.cancel(e);a(o)}return m}function T(e){if(_=!0,r=e,b){const e=[t,r],o=i.cancel(e);a(o)}return m}const q=new ReadableStream({type:\"bytes\",start(e){o=e},pull:w,cancel:R}),C=new ReadableStream({type:\"bytes\",start(e){n=e},pull:v,cancel:T});return y(i),[q,C]}(e):function(e,t){const r=e.getReader();let o,n,a,i,l,s=!1,u=!1,f=!1,b=!1;const _=c((e=>{l=e}));function m(){return s?(u=!0,d(void 0)):(s=!0,h(r.read(),(e=>{if(u=!1,e.done)return f||a.close(),b||i.close(),f&&b||l(void 0),null;const t=e.value,r=t,o=t;return f||a.enqueue(r),b||i.enqueue(o),s=!1,u&&m(),null}),(()=>(s=!1,null))),d(void 0))}function y(e){if(f=!0,o=e,b){const e=[o,n],t=r.cancel(e);l(t)}return _}function g(e){if(b=!0,n=e,f){const e=[o,n],t=r.cancel(e);l(t)}return _}const S=new ReadableStream({start(e){a=e},pull:m,cancel:y}),w=new ReadableStream({start(e){i=e},pull:m,cancel:g});return p(r.closed,(e=>(a.error(e),i.error(e),f&&b||l(void 0),null))),[S,w]}(e)}class ReadableStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get desiredSize(){if(!At(this))throw $t(\"desiredSize\");return Ft(this)}close(){if(!At(this))throw $t(\"close\");if(!Dt(this))throw new TypeError(\"The stream is not in a state that permits close\");!function(e){if(!Dt(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(zt(e),Jt(t))}(this)}enqueue(e){if(!At(this))throw $t(\"enqueue\");if(!Dt(this))throw new TypeError(\"The stream is not in a state that permits enqueue\");return function(e,t){if(!Dt(e))return;const r=e._controlledReadableStream;if(Gt(r)&&J(r)>0)X(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw Lt(e,t),t}try{ce(e,t,r)}catch(t){throw Lt(e,t),t}}jt(e)}(this,e)}error(e){if(!At(this))throw $t(\"error\");Lt(this,e)}[q](e){de(this);const t=this._cancelAlgorithm(e);return zt(this),t}[C](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=ue(this);this._closeRequested&&0===this._queue.length?(zt(this),Jt(t)):jt(this),e._chunkSteps(r)}else G(t,e),jt(this)}[P](){}}function At(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledReadableStream\")&&e instanceof ReadableStreamDefaultController)}function jt(e){const t=function(e){const t=e._controlledReadableStream;if(!Dt(e))return!1;if(!e._started)return!1;if(Gt(t)&&J(t)>0)return!0;if(Ft(e)>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;h(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,jt(e)),null)),(t=>(Lt(e,t),null)))}function zt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Lt(e,t){const r=e._controlledReadableStream;\"readable\"===r._state&&(de(e),zt(e),Kt(r,t))}function Ft(e){const t=e._controlledReadableStream._state;return\"errored\"===t?null:\"closed\"===t?0:e._strategyHWM-e._queueTotalSize}function Dt(e){return!e._closeRequested&&\"readable\"===e._controlledReadableStream._state}function It(e,t,r,o){const n=Object.create(ReadableStreamDefaultController.prototype);let a,i,l;a=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>d(void 0),l=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0),function(e,t,r,o,n,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,de(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t,h(d(r()),(()=>(t._started=!0,jt(t),null)),(e=>(Lt(t,e),null)))}(e,n,a,i,l,r,o)}function $t(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function Mt(e,t,r){return I(e,r),r=>w(e,t,[r])}function Yt(e,t,r){return I(e,r),r=>w(e,t,[r])}function Qt(e,t,r){return I(e,r),r=>S(e,t,[r])}function Nt(e,t){if(\"bytes\"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function xt(e,t){if(\"byob\"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function Ht(e,t){D(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,t){if(!function(e){if(\"object\"!=typeof e||null===e)return!1;try{return\"boolean\"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(a,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}function Vt(e,t){D(e,t);const r=null==e?void 0:e.readable;Y(r,\"readable\",\"ReadableWritablePair\"),function(e,t){if(!H(e))throw new TypeError(`${t} is not a ReadableStream.`)}(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return Y(o,\"writable\",\"ReadableWritablePair\"),function(e,t){if(!V(e))throw new TypeError(`${t} is not a WritableStream.`)}(o,`${t} has member 'writable' that`),{readable:r,writable:o}}Object.defineProperties(ReadableStreamDefaultController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),a(ReadableStreamDefaultController.prototype.close,\"close\"),a(ReadableStreamDefaultController.prototype.enqueue,\"enqueue\"),a(ReadableStreamDefaultController.prototype.error,\"error\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(ReadableStreamDefaultController.prototype,t.toStringTag,{value:\"ReadableStreamDefaultController\",configurable:!0});class ReadableStream{constructor(e={},t={}){void 0===e?e=null:$(e,\"First parameter\");const r=Qe(t,\"Second parameter\"),o=function(e,t){D(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,a=null==r?void 0:r.pull,i=null==r?void 0:r.start,l=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:x(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:Mt(n,r,`${t} has member 'cancel' that`),pull:void 0===a?void 0:Yt(a,r,`${t} has member 'pull' that`),start:void 0===i?void 0:Qt(i,r,`${t} has member 'start' that`),type:void 0===l?void 0:Nt(l,`${t} has member 'type' that`)}}(e,\"First parameter\");var n;if((n=this)._state=\"readable\",n._reader=void 0,n._storedError=void 0,n._disturbed=!1,\"bytes\"===o.type){if(void 0!==r.size)throw new RangeError(\"The strategy for a byte stream cannot have a size function\");Be(this,o,Me(r,0))}else{const e=Ye(r);It(this,o,Me(r,1),e)}}get locked(){if(!Ut(this))throw Zt(\"locked\");return Gt(this)}cancel(e){return Ut(this)?Gt(this)?f(new TypeError(\"Cannot cancel a stream that already has a reader\")):Xt(this,e):f(Zt(\"cancel\"))}getReader(e){if(!Ut(this))throw Zt(\"getReader\");return void 0===function(e,t){D(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:xt(r,`${t} has member 'mode' that`)}}(e,\"First parameter\").mode?new ReadableStreamDefaultReader(this):function(e){return new ReadableStreamBYOBReader(e)}(this)}pipeThrough(e,t={}){if(!H(this))throw Zt(\"pipeThrough\");M(e,1,\"pipeThrough\");const r=Vt(e,\"First parameter\"),o=Ht(t,\"Second parameter\");if(this.locked)throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream\");if(r.writable.locked)throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream\");return y(kt(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!H(this))return f(Zt(\"pipeTo\"));if(void 0===e)return f(\"Parameter 1 is required in 'pipeTo'.\");if(!V(e))return f(new TypeError(\"ReadableStream.prototype.pipeTo's first argument must be a WritableStream\"));let r;try{r=Ht(t,\"Second parameter\")}catch(e){return f(e)}return this.locked?f(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream\")):e.locked?f(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream\")):kt(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!H(this))throw Zt(\"tee\");if(this.locked)throw new TypeError(\"Cannot tee a stream that already has a reader\");return Bt(this)}values(e){if(!H(this))throw Zt(\"values\");return function(e,t){const r=e.getReader(),o=new re(r,t),n=Object.create(oe);return n._asyncIteratorImpl=o,n}(this,function(e,t){D(e,t);const r=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(r)}}(e,\"First parameter\").preventCancel)}}function Ut(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readableStreamController\")&&e instanceof ReadableStream)}function Gt(e){return void 0!==e._reader}function Xt(e,t){if(e._disturbed=!0,\"closed\"===e._state)return d(void 0);if(\"errored\"===e._state)return f(e._storedError);Jt(e);const o=e._reader;if(void 0!==o&&De(o)){const e=o._readIntoRequests;o._readIntoRequests=new v,e.forEach((e=>{e._closeSteps(void 0)}))}return m(e._readableStreamController[q](t),r)}function Jt(e){e._state=\"closed\";const t=e._reader;if(void 0!==t&&(z(t),Z(t))){const e=t._readRequests;t._readRequests=new v,e.forEach((e=>{e._closeSteps()}))}}function Kt(e,t){e._state=\"errored\",e._storedError=t;const r=e._reader;void 0!==r&&(j(r,t),Z(r)?ee(r,t):Ie(r,t))}function Zt(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function er(e,t){D(e,t);const r=null==e?void 0:e.highWaterMark;return Y(r,\"highWaterMark\",\"QueuingStrategyInit\"),{highWaterMark:Q(r)}}Object.defineProperties(ReadableStream.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),a(ReadableStream.prototype.cancel,\"cancel\"),a(ReadableStream.prototype.getReader,\"getReader\"),a(ReadableStream.prototype.pipeThrough,\"pipeThrough\"),a(ReadableStream.prototype.pipeTo,\"pipeTo\"),a(ReadableStream.prototype.tee,\"tee\"),a(ReadableStream.prototype.values,\"values\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(ReadableStream.prototype,t.toStringTag,{value:\"ReadableStream\",configurable:!0}),\"symbol\"==typeof t.asyncIterator&&Object.defineProperty(ReadableStream.prototype,t.asyncIterator,{value:ReadableStream.prototype.values,writable:!0,configurable:!0});const tr=e=>e.byteLength;a(tr,\"size\");class ByteLengthQueuingStrategy{constructor(e){M(e,1,\"ByteLengthQueuingStrategy\"),e=er(e,\"First parameter\"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!or(this))throw rr(\"highWaterMark\");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!or(this))throw rr(\"size\");return tr}}function rr(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function or(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_byteLengthQueuingStrategyHighWaterMark\")&&e instanceof ByteLengthQueuingStrategy)}Object.defineProperties(ByteLengthQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(ByteLengthQueuingStrategy.prototype,t.toStringTag,{value:\"ByteLengthQueuingStrategy\",configurable:!0});const nr=()=>1;a(nr,\"size\");class CountQueuingStrategy{constructor(e){M(e,1,\"CountQueuingStrategy\"),e=er(e,\"First parameter\"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!ir(this))throw ar(\"highWaterMark\");return this._countQueuingStrategyHighWaterMark}get size(){if(!ir(this))throw ar(\"size\");return nr}}function ar(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function ir(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_countQueuingStrategyHighWaterMark\")&&e instanceof CountQueuingStrategy)}function lr(e,t,r){return I(e,r),r=>w(e,t,[r])}function sr(e,t,r){return I(e,r),r=>S(e,t,[r])}function ur(e,t,r){return I(e,r),(r,o)=>w(e,t,[r,o])}Object.defineProperties(CountQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(CountQueuingStrategy.prototype,t.toStringTag,{value:\"CountQueuingStrategy\",configurable:!0});class TransformStream{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=Qe(t,\"Second parameter\"),n=Qe(r,\"Third parameter\"),a=function(e,t){D(e,t);const r=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,n=null==e?void 0:e.start,a=null==e?void 0:e.transform,i=null==e?void 0:e.writableType;return{flush:void 0===r?void 0:lr(r,e,`${t} has member 'flush' that`),readableType:o,start:void 0===n?void 0:sr(n,e,`${t} has member 'start' that`),transform:void 0===a?void 0:ur(a,e,`${t} has member 'transform' that`),writableType:i}}(e,\"First parameter\");if(void 0!==a.readableType)throw new RangeError(\"Invalid readableType specified\");if(void 0!==a.writableType)throw new RangeError(\"Invalid writableType specified\");const i=Me(n,0),l=Ye(n),s=Me(o,1),u=Ye(o);let b;!function(e,t,r,o,n,a){function i(){return t}function l(t){return function(e,t){const r=e._transformStreamController;if(e._backpressure){return m(e._backpressureChangePromise,(()=>{if(\"erroring\"===(Xe(e._writable)?e._writable._state:e._writableState))throw Xe(e._writable)?e._writable._storedError:e._writableStoredError;return mr(r,t)}))}return mr(r,t)}(e,t)}function s(t){return function(e,t){return dr(e,t),d(void 0)}(e,t)}function u(){return function(e){const t=e._transformStreamController,r=t._flushAlgorithm();return _r(t),m(r,(()=>{if(\"errored\"===e._readableState)throw e._readableStoredError;Sr(e)&&wr(e)}),(t=>{throw dr(e,t),e._readableStoredError}))}(e)}function c(){return function(e){return br(e,!1),e._backpressureChangePromise}(e)}function f(t){return fr(e,t),d(void 0)}e._writableState=\"writable\",e._writableStoredError=void 0,e._writableHasInFlightOperation=!1,e._writableStarted=!1,e._writable=function(e,t,r,o,n,a,i){return new WritableStream({start(r){e._writableController=r;try{const t=r.signal;void 0!==t&&t.addEventListener(\"abort\",(()=>{\"writable\"===e._writableState&&(e._writableState=\"erroring\",t.reason&&(e._writableStoredError=t.reason))}))}catch(e){}return m(t(),(()=>(e._writableStarted=!0,Pr(e),null)),(t=>{throw e._writableStarted=!0,Tr(e,t),t}))},write:t=>(function(e){e._writableHasInFlightOperation=!0}(e),m(r(t),(()=>(function(e){e._writableHasInFlightOperation=!1}(e),Pr(e),null)),(t=>{throw function(e,t){e._writableHasInFlightOperation=!1,Tr(e,t)}(e,t),t}))),close:()=>(function(e){e._writableHasInFlightOperation=!0}(e),m(o(),(()=>(function(e){e._writableHasInFlightOperation=!1;\"erroring\"===e._writableState&&(e._writableStoredError=void 0);e._writableState=\"closed\"}(e),null)),(t=>{throw function(e,t){e._writableHasInFlightOperation=!1,e._writableState,Tr(e,t)}(e,t),t}))),abort:t=>(e._writableState=\"errored\",e._writableStoredError=t,n(t))},{highWaterMark:a,size:i})}(e,i,l,u,s,r,o),e._readableState=\"readable\",e._readableStoredError=void 0,e._readableCloseRequested=!1,e._readablePulling=!1,e._readable=function(e,t,r,o,n,a){return new ReadableStream({start:r=>(e._readableController=r,t().catch((t=>{vr(e,t)}))),pull:()=>(e._readablePulling=!0,r().catch((t=>{vr(e,t)}))),cancel:t=>(e._readableState=\"closed\",o(t))},{highWaterMark:n,size:a})}(e,i,c,f,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,br(e,!0),e._transformStreamController=void 0}(this,c((e=>{b=e})),s,u,i,l),function(e,t){const r=Object.create(TransformStreamDefaultController.prototype);let o,n;o=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return pr(r,e),d(void 0)}catch(e){return f(e)}};n=void 0!==t.flush?()=>t.flush(r):()=>d(void 0);!function(e,t,r,o){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o}(e,r,o,n)}(this,a),void 0!==a.start?b(a.start(this._transformStreamController)):b(void 0)}get readable(){if(!cr(this))throw gr(\"readable\");return this._readable}get writable(){if(!cr(this))throw gr(\"writable\");return this._writable}}function cr(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_transformStreamController\")&&e instanceof TransformStream)}function dr(e,t){vr(e,t),fr(e,t)}function fr(e,t){_r(e._transformStreamController),function(e,t){e._writableController.error(t);\"writable\"===e._writableState&&qr(e,t)}(e,t),e._backpressure&&br(e,!1)}function br(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(TransformStream.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(TransformStream.prototype,t.toStringTag,{value:\"TransformStream\",configurable:!0});class TransformStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get desiredSize(){if(!hr(this))throw yr(\"desiredSize\");return Rr(this._controlledTransformStream)}enqueue(e){if(!hr(this))throw yr(\"enqueue\");pr(this,e)}error(e){if(!hr(this))throw yr(\"error\");var t;t=e,dr(this._controlledTransformStream,t)}terminate(){if(!hr(this))throw yr(\"terminate\");!function(e){const t=e._controlledTransformStream;Sr(t)&&wr(t);const r=new TypeError(\"TransformStream terminated\");fr(t,r)}(this)}}function hr(e){return!!o(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledTransformStream\")&&e instanceof TransformStreamDefaultController)}function _r(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function pr(e,t){const r=e._controlledTransformStream;if(!Sr(r))throw new TypeError(\"Readable side is not in a state that permits enqueue\");try{!function(e,t){e._readablePulling=!1;try{e._readableController.enqueue(t)}catch(t){throw vr(e,t),t}}(r,t)}catch(e){throw fr(r,e),r._readableStoredError}const o=function(e){return!function(e){if(!Sr(e))return!1;if(e._readablePulling)return!0;if(Rr(e)>0)return!0;return!1}(e)}(r);o!==r._backpressure&&br(r,!0)}function mr(e,t){return m(e._transformAlgorithm(t),void 0,(t=>{throw dr(e._controlledTransformStream,t),t}))}function yr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function gr(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}function Sr(e){return!e._readableCloseRequested&&\"readable\"===e._readableState}function wr(e){e._readableState=\"closed\",e._readableCloseRequested=!0,e._readableController.close()}function vr(e,t){\"readable\"===e._readableState&&(e._readableState=\"errored\",e._readableStoredError=t),e._readableController.error(t)}function Rr(e){return e._readableController.desiredSize}function Tr(e,t){\"writable\"!==e._writableState?Cr(e):qr(e,t)}function qr(e,t){e._writableState=\"erroring\",e._writableStoredError=t,!function(e){return e._writableHasInFlightOperation}(e)&&e._writableStarted&&Cr(e)}function Cr(e){e._writableState=\"errored\"}function Pr(e){\"erroring\"===e._writableState&&Cr(e)}Object.defineProperties(TransformStreamDefaultController.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),a(TransformStreamDefaultController.prototype.enqueue,\"enqueue\"),a(TransformStreamDefaultController.prototype.error,\"error\"),a(TransformStreamDefaultController.prototype.terminate,\"terminate\"),\"symbol\"==typeof t.toStringTag&&Object.defineProperty(TransformStreamDefaultController.prototype,t.toStringTag,{value:\"TransformStreamDefaultController\",configurable:!0}),e.ByteLengthQueuingStrategy=ByteLengthQueuingStrategy,e.CountQueuingStrategy=CountQueuingStrategy,e.ReadableByteStreamController=ReadableByteStreamController,e.ReadableStream=ReadableStream,e.ReadableStreamBYOBReader=ReadableStreamBYOBReader,e.ReadableStreamBYOBRequest=ReadableStreamBYOBRequest,e.ReadableStreamDefaultController=ReadableStreamDefaultController,e.ReadableStreamDefaultReader=ReadableStreamDefaultReader,e.TransformStream=TransformStream,e.TransformStreamDefaultController=TransformStreamDefaultController,e.WritableStream=WritableStream,e.WritableStreamDefaultController=WritableStreamDefaultController,e.WritableStreamDefaultWriter=WritableStreamDefaultWriter,Object.defineProperty(e,\"__esModule\",{value:!0})}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvd2ViLXN0cmVhbXMtcG9seWZpbGwvZGlzdC9wb255ZmlsbC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsS0FBb0QsWUFBWSxDQUEwSSxDQUFDLG1CQUFtQixhQUFhLHdGQUF3RixFQUFFLEdBQUcsY0FBYyxjQUFjLHlEQUF5RCxVQUFVLGdCQUFnQixJQUFJLGdDQUFnQyx3QkFBd0IsRUFBRSxXQUFXLDRGQUE0RixjQUFjLGdCQUFnQixjQUFjLFlBQVksY0FBYyxZQUFZLGtCQUFrQixxQkFBcUIsa0JBQWtCLHFCQUFxQixnQkFBZ0IsT0FBTyxnQkFBZ0IsY0FBYyxrQkFBa0IsZ0JBQWdCLGNBQWMsY0FBYyxVQUFVLHNEQUFzRCxLQUFLLGtCQUFrQixZQUFZLGFBQWEsa0JBQWtCLDBFQUEwRSw0Q0FBNEMsa0JBQWtCLElBQUksbUJBQW1CLFNBQVMsYUFBYSxRQUFRLGNBQWMseUNBQXlDLDBCQUEwQixvREFBb0QsYUFBYSxrQkFBa0IsUUFBUSxtQkFBbUIsUUFBUSxnQ0FBZ0MsMEJBQTBCLG1FQUFtRSxRQUFRLG9CQUFvQixRQUFRLHFCQUFxQixVQUFVLDJCQUEyQixtR0FBbUcsV0FBVywrQ0FBK0MsS0FBSyw0RkFBNEYsYUFBYSxPQUFPLG1DQUFtQyx1QkFBdUIsc0hBQXNILGdCQUFnQixnR0FBZ0csVUFBVSx3QkFBd0IsZ0JBQWdCLG9DQUFvQyxjQUFjLCtCQUErQiwySUFBMkksT0FBTyxzTEFBc0wsY0FBYyxzRUFBc0UsY0FBYyw0QkFBNEIscURBQXFELEdBQUcsZ0JBQWdCLFlBQVksZ0JBQWdCLGtKQUFrSixjQUFjLHFJQUFxSSxxQ0FBcUMsc0NBQXNDLDJCQUEyQix1Q0FBdUMsZ0JBQWdCLG9GQUFvRixHQUFHLG9CQUFvQixNQUFNLGdCQUFnQiwrQ0FBK0MsR0FBRyxxQkFBcUIsZ0JBQWdCLGdCQUFnQix5REFBeUQsMkJBQTJCLEdBQUcsb0JBQW9CLGtCQUFrQiwrQ0FBK0MsR0FBRyxrQkFBa0IsRUFBRSxLQUFLLGtCQUFrQixxQ0FBcUMsR0FBRyxrQkFBa0IsRUFBRSxLQUFLLGNBQWMsaUJBQWlCLGNBQWMsaUJBQWlCLGdCQUFnQixnQ0FBZ0MsZ0JBQWdCLHVDQUF1QyxHQUFHLHlCQUF5QixpQkFBaUIsZUFBZSxvQ0FBb0MsR0FBRyx3Q0FBd0MsRUFBRSxjQUFjLHVCQUF1QixjQUFjLGtCQUFrQiwyQ0FBMkMsSUFBSSxpQ0FBaUMsU0FBUyxVQUFVLGNBQWMsa0JBQWtCLDJDQUEyQyxJQUFJLGlDQUFpQyxTQUFTLFVBQVUsZ0JBQWdCLGlDQUFpQyxHQUFHLDJCQUEyQixnQkFBZ0IsZ0NBQWdDLGtCQUFrQix3Q0FBd0MsbUNBQW1DLGNBQWMsc0NBQXNDLGNBQWMsa0JBQWtCLDBCQUEwQixrQ0FBa0MsZUFBZSx3S0FBd0ssbUNBQW1DLGFBQWEsbURBQW1ELFVBQVUsMkZBQTJGLE9BQU8saUNBQWlDLCtEQUErRCxRQUFRLG1CQUFtQixRQUFRLEdBQUcscUJBQXFCLCtCQUErQix5SUFBeUksT0FBTyxrQkFBa0IsZ0JBQWdCLHFCQUFxQixxQkFBcUIsc0JBQXNCLElBQUksY0FBYyxvQ0FBb0MsZ0RBQWdELEtBQUssNkNBQTZDLFFBQVEsUUFBUSxjQUFjLG9IQUFvSCxpQkFBaUIsd0JBQXdCLHFDQUFxQyxpQkFBaUIsR0FBRyxlQUFlLDhEQUE4RCxHQUFHLG9EQUFvRCwrREFBK0QsUUFBUSxjQUFjLE9BQU8sY0FBYyxjQUFjLGNBQWMsU0FBUyxlQUFlLDhSQUE4UixvREFBb0QsRUFBRSxTQUFTLGlCQUFpQixxRkFBcUYsT0FBTyw4QkFBOEIsc0dBQXNHLFVBQVUsaUNBQWlDLDREQUE0RCxhQUFhLDRDQUE0QyxxQkFBcUIsRUFBRSxxQkFBcUIsa0RBQWtELE1BQU0sNElBQTRJLE9BQU8sTUFBTSxpSUFBaUksR0FBRyxnQkFBZ0IsNENBQTRDLGdCQUFnQixFQUFFLG9CQUFvQixxQkFBcUIsOENBQThDLDZDQUE2QyxvQkFBb0Isa0NBQWtDLGdCQUFnQixJQUFJLDBCQUEwQixnQkFBZ0IsR0FBRyxVQUFVLE9BQU8sNkRBQTZELFdBQVcsb0VBQW9FLGVBQWUsa0JBQWtCLDBFQUEwRSxJQUFJLDBDQUEwQyxTQUFTLFVBQVUsZUFBZSxvREFBb0QsR0FBRyxtREFBbUQsNEVBQTRFLFFBQVEsWUFBWSw2QkFBNkIsRUFBRSxtQ0FBbUMsYUFBYSx1QkFBdUIsK0NBQStDLGVBQWUsd0JBQXdCLCtCQUErQixpQ0FBaUMsdUJBQXVCLGtEQUFrRCx5QkFBeUIsZUFBZSx5QkFBeUIsb0ZBQW9GLG1CQUFtQiwySEFBMkgsTUFBTSxlQUFlLGVBQWUsdUJBQXVCLGVBQWUsbUNBQW1DLGdDQUFnQyxjQUFjLDJDQUEyQyxXQUFXLDhCQUE4QixrQkFBa0IsV0FBVyxpQ0FBaUMsaUtBQWlLLGdDQUFnQyxtQ0FBbUMsc0RBQXNELGlHQUFpRyxLQUFLLGdIQUFnSCxrRkFBa0YsMEJBQTBCLGlEQUFpRCxzQkFBc0IsNENBQTRDLDBIQUEwSCx1SEFBdUgsdUJBQXVCLG1DQUFtQyxzREFBc0QsNEhBQTRILGdKQUFnSiw2SEFBNkgsK0hBQStILDJIQUEySCxxQkFBcUIsMEJBQTBCLGtEQUFrRCw2REFBNkQsU0FBUyxjQUFjLHFCQUFxQixjQUFjLE9BQU8sZUFBZSxtUEFBbVAsa0RBQWtELEVBQUUsbUNBQW1DLGNBQWMsMkNBQTJDLGtCQUFrQixxQ0FBcUMsbUJBQW1CLHdEQUF3RCx5S0FBeUssaUJBQWlCLHNEQUFzRCx5QkFBeUIsc0JBQXNCLE9BQU8sa0JBQWtCLHFDQUFxQyxnQkFBZ0IsUUFBUSwrQkFBK0IsaUZBQWlGLHlCQUF5QixrREFBa0Qsd0RBQXdELEdBQUcsMkRBQTJELGFBQWEsd0NBQXdDLG1EQUFtRCx5REFBeUQsaUNBQWlDLDZDQUE2QyxpRkFBaUYsaUJBQWlCLFlBQVksT0FBTyxXQUFXLGlDQUFpQyxxR0FBcUcsK0VBQStFLCtGQUErRiw0RUFBNEUsa0RBQWtELHdEQUF3RCxHQUFHLGdFQUFnRSxlQUFlLHdDQUF3QyxtREFBbUQsbURBQW1ELGlDQUFpQyxtQ0FBbUMsa0VBQWtFLHVCQUF1QixnREFBZ0QsS0FBSyx5QkFBeUIsRUFBRSxnQ0FBZ0MsK0JBQStCLHlCQUF5QixLQUFLLG9DQUFvQyw4QkFBOEIsMkNBQTJDLE1BQU0sU0FBUyxTQUFTLCtCQUErQixXQUFXLE9BQU8sa0JBQWtCLGlDQUFpQyxrQkFBa0IsT0FBTywyQ0FBMkMsaURBQWlELG9DQUFvQyxlQUFlLE1BQU0sSUFBSSxxQkFBcUIsU0FBUyw2QkFBNkIsU0FBUyxtSUFBbUksK0JBQStCLGdCQUFnQixNQUFNLG9DQUFvQyxzQ0FBc0Msa0ZBQWtGLGVBQWUscUlBQXFJLGVBQWUsNElBQTRJLGVBQWUsb0JBQW9CLHdDQUF3QyxrQ0FBa0MsOEJBQThCLHdCQUF3Qix5QkFBeUIsMkJBQTJCLG9CQUFvQixTQUFTLElBQUksYUFBYSwyQ0FBMkMsY0FBYywyR0FBMkcsZUFBZSxnQ0FBZ0MsaUJBQWlCLFNBQVMsNEJBQTRCLGNBQWMsa0RBQWtELDRDQUE0QyxvQ0FBb0MsUUFBUSxlQUFlLHNDQUFzQyx3REFBd0QscUJBQXFCLGVBQWUsbUNBQW1DLHVCQUF1QixxQkFBcUIsTUFBTSxJQUFJLGlCQUFpQixTQUFTLGdCQUFnQixZQUFZLGlCQUFpQixpRUFBaUUsaUJBQWlCLHlJQUF5SSxhQUFhLDhCQUE4QixpQkFBaUIsS0FBSyxJQUFJLEVBQUUseUVBQXlFLHdJQUF3SSxTQUFTLG1CQUFtQixpQkFBaUIsZUFBZSwyRkFBMkYsZUFBZSxxSUFBcUksZUFBZSxLQUFLLDZCQUE2QixFQUFFLGdDQUFnQyxtQ0FBbUMsd0RBQXdELGlCQUFpQixtQ0FBbUMsTUFBTSxnRUFBZ0UsNkJBQTZCLHdDQUF3QyxjQUFjLFFBQVEsYUFBYSxzQkFBc0IsNkRBQTZELHNDQUFzQyxNQUFNLG9DQUFvQyxRQUFRLG1DQUFtQyxxQkFBcUIsNkRBQTZELGNBQWMsZUFBZSxtQ0FBbUMsZUFBZSxrREFBa0QsaUJBQWlCLHdDQUF3QyxtREFBbUQsaUJBQWlCLHlCQUF5QixzQ0FBc0MsMkRBQTJELGlCQUFpQixlQUFlLCtDQUErQyx5RUFBeUUsbUJBQW1CLDhEQUE4RCxVQUFVLHdDQUF3QyxnR0FBZ0csZ0NBQWdDLDZFQUE2RSx5QkFBeUIsMFdBQTBXLGdCQUFnQixlQUFlLDREQUE0RCxHQUFHLGtEQUFrRCxlQUFlLCtEQUErRCxHQUFHLHFEQUFxRCxpQkFBaUIsb0NBQW9DLGVBQWUsMENBQTBDLGVBQWUsa0JBQWtCLDJCQUEyQixnRUFBZ0UsT0FBTyxjQUFjLFVBQVUsY0FBYyxRQUFRLGNBQWMsY0FBYyxjQUFjLGNBQWMsZUFBZSwwUkFBMFIscURBQXFELEVBQUUsK0JBQStCLGVBQWUscUtBQXFLLHVKQUF1Six1Q0FBdUMsYUFBYSxvREFBb0QsVUFBVSw0RkFBNEYsUUFBUSxrQ0FBa0MsdUZBQXVGLGtGQUFrRixrR0FBa0csd0VBQXdFLFFBQVEsbUJBQW1CLFFBQVEsR0FBRyx1QkFBdUIsK0JBQStCLG1GQUFtRix3Q0FBd0MsUUFBUSw4REFBOEQsb0NBQW9DLHdKQUF3SixnRkFBZ0Ysd0JBQXdCLHdCQUF3QixZQUFZLGNBQWMsbUNBQW1DLHNCQUFzQixpRkFBaUYsc0NBQXNDLDBDQUEwQyxLQUFLLHVDQUF1QyxrQkFBa0Isa0NBQWtDLFNBQVMsa0JBQWtCLGdCQUFnQixvQkFBb0IsZ0JBQWdCLHNCQUFzQixJQUFJLGNBQWMscUNBQXFDLGdEQUFnRCxLQUFLLDZDQUE2QyxRQUFRLFFBQVEsZUFBZSxxSEFBcUgsaUJBQWlCLDRCQUE0Qix5Q0FBeUMsaUJBQWlCLEdBQUcsZUFBZSwyREFBMkQsR0FBRyxpREFBaUQsaUJBQWlCLE1BQU0sZ0JBQWdCLEdBQUcsdUJBQXVCLDREQUE0RCxTQUFTLGVBQWUsTUFBTSxPQUFPLEdBQUcsa0JBQWtCLGlCQUFpQixPQUFPLCtEQUErRCxPQUFPLG9FQUFvRSxHQUFHLDBCQUEwQixpQkFBaUIseUJBQXlCLG1CQUFtQiw0QkFBNEIsbUJBQW1CLDRCQUE0QixtQkFBbUIsNEJBQTRCLG1CQUFtQixrQ0FBa0MsNERBQTRELFFBQVEsY0FBYyxPQUFPLGNBQWMsY0FBYyxjQUFjLFNBQVMsZUFBZSxrUkFBa1IsaURBQWlELEVBQUUsNENBQTRDLHFCQUFxQixnQkFBZ0IsS0FBSyxFQUFFLHlDQUF5QyxpREFBaUQsT0FBTyxrSUFBa0ksT0FBTyxrQ0FBa0MsR0FBRyw0REFBNEQsR0FBRyw0REFBNEQsR0FBRyw0REFBNEQsR0FBRyxrQ0FBa0Msc0JBQXNCLE1BQU0sa1FBQWtRLHFFQUFxRSxjQUFjLG1CQUFtQixpRUFBaUUsWUFBWSx5Q0FBeUMsaURBQWlELCtDQUErQywrQ0FBK0MsMkJBQTJCLCtKQUErSixpQ0FBaUMseUhBQXlILGNBQWMsUUFBUSxZQUFZLDJFQUEyRSxrQkFBa0IsbUJBQW1CLGFBQWEsZ0NBQWdDLGdCQUFnQixTQUFTLHVIQUF1SCxRQUFRLHlMQUF5TCxZQUFZLG1DQUFtQyw4Q0FBOEMsZUFBZSxtSEFBbUgsZUFBZSwwQkFBMEIsaUJBQWlCLE1BQU0sOERBQThELDJIQUEySCxpQkFBaUIsZ0RBQWdELDBFQUEwRSxTQUFTLGdDQUFnQyxtQkFBbUIsd0JBQXdCLHNFQUFzRSxHQUFHLHNEQUFzRCxlQUFlLGlCQUFpQix3RUFBd0UsR0FBRyw0REFBNEQsbUJBQW1CLFNBQVMsc0JBQXNCLGtCQUFrQixlQUFlLE1BQU0seUdBQXlHLGlCQUFpQixvQ0FBb0MsaUJBQWlCLG9DQUFvQyxxQ0FBcUMsa0JBQWtCLGlDQUFpQywrRUFBK0UsU0FBUyx1QkFBdUIsZUFBZSxvREFBb0QsdUJBQXVCLGlDQUFpQyxhQUFhLDRFQUE0RSwrQkFBK0Isc0ZBQXNGLDRHQUE0RyxlQUFlLGtFQUFrRSxlQUFlLDJGQUEyRixrQkFBa0IsaUNBQWlDLGlCQUFpQixrQkFBa0IsZ0RBQWdELE1BQU0sNkJBQTZCLGtEQUFrRCxPQUFPLGNBQWMsUUFBUSxjQUFjLFlBQVksY0FBYyxTQUFTLGVBQWUsc09BQXNPLHVDQUF1QyxFQUFFLGtDQUFrQyxlQUFlLHNEQUFzRCxpQ0FBaUMsR0FBRywyQkFBMkIsK0hBQStILDJDQUEyQyxpQkFBaUIscUVBQXFFLHdEQUF3RCwrQ0FBK0MsS0FBSyx1QkFBdUIsc0JBQXNCLE1BQU0sYUFBYSxvREFBb0Qsa0JBQWtCLHFDQUFxQyw4REFBOEQsbUJBQW1CLDBDQUEwQyw2Q0FBNkMseUJBQXlCLHVDQUF1QyxPQUFPLFlBQVksa0RBQWtELFNBQVMsZ0ZBQWdGLG9DQUFvQyx3QkFBd0IsUUFBUSxtQ0FBbUMsa0NBQWtDLGdJQUFnSSxjQUFjLHFDQUFxQyxnREFBZ0QsbUlBQW1JLHNCQUFzQix3REFBd0QsUUFBUSxNQUFNLHFEQUFxRCxPQUFPLFNBQVMsbUZBQW1GLDZFQUE2RSxJQUFJLG1DQUFtQyxTQUFTLGtCQUFrQixNQUFNLHVEQUF1RCxpQkFBaUIsMENBQTBDLDJHQUEyRywyQ0FBMkMsb0JBQW9CLGtCQUFrQixTQUFTLHNCQUFzQix5QkFBeUIsR0FBRyxJQUFJLHVCQUF1QixJQUFJLFVBQVUsU0FBUyxvQkFBb0Isb0NBQW9DLGtDQUFrQyxZQUFZLE1BQU0sVUFBVSx5QkFBeUIsZUFBZSwySEFBMkgsaUJBQWlCLHVEQUF1RCxRQUFRLE1BQU0sK0RBQStELE9BQU8sY0FBYyxRQUFRLGNBQWMsY0FBYyxjQUFjLFFBQVEsY0FBYyxTQUFTLGNBQWMsY0FBYyxjQUFjLFFBQVEsZUFBZSxxVkFBcVYsb0RBQW9ELEVBQUUsWUFBWSxzQ0FBc0MsY0FBYywyQ0FBMkMsa0JBQWtCLHFDQUFxQyx5QkFBeUIsYUFBYSxnQ0FBZ0MsMkhBQTJILG9DQUFvQyxTQUFTLCtCQUErQiwrREFBK0QsT0FBTyxnQ0FBZ0Msa0JBQWtCLE1BQU0sVUFBVSxlQUFlLG9JQUFvSSxlQUFlLDJHQUEyRyxlQUFlLHdDQUF3QyxlQUFlLG9DQUFvQyxzQkFBc0IsMkNBQTJDLDJDQUEyQyw4QkFBOEIsOEJBQThCLG1CQUFtQixvQ0FBb0MsYUFBYSwrREFBK0QsV0FBVyw0QkFBNEIsNEJBQTRCLDRPQUE0TyxrQkFBa0Isa0JBQWtCLDhCQUE4Qiw2S0FBNkssY0FBYyxrQkFBa0Isb0NBQW9DLGFBQWEsaURBQWlELElBQUksNkJBQTZCLGFBQWEsd0VBQXdFLElBQUksaUJBQWlCLGlDQUFpQyxjQUFjLFFBQVEsa0JBQWtCLGtEQUFrRCwwRUFBMEUsY0FBYyxNQUFNLGlCQUFpQix5REFBeUQsZUFBZSxnQkFBZ0IsaUJBQWlCLG9DQUFvQyxjQUFjLGVBQWUsaURBQWlELEdBQUcsdUNBQXVDLGVBQWUsa0VBQWtFLEdBQUcsd0RBQXdELGVBQWUsOERBQThELEdBQUcsb0RBQW9ELGVBQWUsc0VBQXNFLGVBQWUsNEJBQTRCLHFGQUFxRixHQUFHLGlCQUFpQixjQUFjLGlCQUFpQixtTEFBbUwsZUFBZSxzS0FBc0ssZUFBZSwyQkFBMkIsbURBQW1ELGtDQUFrQyxpQkFBaUIsY0FBYyxlQUFlLFlBQVksaUJBQWlCLDZLQUE2SyxlQUFlLGtLQUFrSyxtRUFBbUUsYUFBYSxjQUFjLFNBQVMsY0FBYyxRQUFRLGVBQWUsaUhBQWlILHdEQUF3RCxFQUFFLDhEQUE4RCxxQkFBcUIscURBQXFELElBQUksZ0JBQWdCLFNBQVMsVUFBVSxtQkFBbUIsc0JBQXNCLGlIQUFpSCxtR0FBbUcsb0NBQW9DLElBQUksR0FBRyx5QkFBeUIsc0NBQXNDLHlCQUF5Qix3REFBd0QsZUFBZSxJQUFJLEdBQUcsOEJBQThCLGtCQUFrQixNQUFNLGFBQWEsWUFBWSxtQkFBbUIsZUFBZSxtQkFBbUIsa0JBQWtCLGdGQUFnRixRQUFRLEtBQUssR0FBRyxLQUFLLGFBQWEsNEpBQTRKLGNBQWMsb0VBQW9FLGNBQWMscUVBQXFFLHVCQUF1Qix1RUFBdUUsc0pBQXNKLDRLQUE0SywyQ0FBMkMseUJBQXlCLHlCQUF5QixxR0FBcUcsb0NBQW9DLGtCQUFrQixhQUFhLHlDQUF5QyxNQUFNLHNCQUFzQiw2QkFBNkIsSUFBSSxXQUFXLGFBQWEsc0RBQXNELHVCQUF1QixnQkFBZ0IsY0FBYyxnQkFBZ0IsK0dBQStHLDJDQUEyQyw0QkFBNEIsb0JBQW9CLGFBQWEsR0FBRyxHQUFHLGlCQUFpQixtQkFBbUIsSUFBSSxvQkFBb0IsWUFBWSxtQkFBbUIsU0FBUyxVQUFVLGdCQUFnQiw0REFBNEQsZUFBZSxJQUFJLEdBQUcsY0FBYyx1RUFBdUUsYUFBYSwrREFBK0QsUUFBUSxnTEFBZ0wsb0JBQW9CLFFBQVEsY0FBYyxRQUFRLFNBQVMsaURBQWlELDhEQUE4RCxxQkFBcUIsZ0JBQWdCLG1DQUFtQyxZQUFZLGFBQWEsc0JBQXNCLGlCQUFpQixNQUFNLFVBQVUsc0JBQXNCLFdBQVcsMEJBQTBCLGdCQUFnQix1SUFBdUksZ0JBQWdCLDRDQUE0QyxLQUFLLE1BQU0sSUFBSSxRQUFRLFNBQVMsaURBQWlELG9EQUFvRCw4QkFBOEIscUJBQXFCLGFBQWEsMkJBQTJCLEtBQUssc0JBQXNCLDJDQUEyQyxhQUFhLDJCQUEyQixLQUFLLHNCQUFzQiwyQ0FBMkMsY0FBYyxlQUFlLDRCQUE0QixLQUFLLFNBQVMsY0FBYyxlQUFlLDRCQUE0QixLQUFLLFNBQVMsNEJBQTRCLHNCQUFzQixJQUFJLGlCQUFpQix3QkFBd0Isc0JBQXNCLElBQUksaUJBQWlCLEVBQUUsa0JBQWtCLGtCQUFrQixzQkFBc0Isa0NBQWtDLGVBQWUsSUFBSSxHQUFHLGFBQWEsZ0RBQWdELHFFQUFxRSx3QkFBd0Isd0RBQXdELGdDQUFnQyxjQUFjLGVBQWUsNEJBQTRCLEtBQUssU0FBUyxjQUFjLGVBQWUsNEJBQTRCLEtBQUssU0FBUyw0QkFBNEIsU0FBUyxJQUFJLGlCQUFpQix3QkFBd0IsU0FBUyxJQUFJLGlCQUFpQixFQUFFLDJFQUEyRSxJQUFJLHNDQUFzQyxjQUFjLDJDQUEyQyxrQkFBa0IscUNBQXFDLGdCQUFnQixRQUFRLCtCQUErQixvRkFBb0YsYUFBYSxpQkFBaUIsb0NBQW9DLHdEQUF3RCxPQUFPLFdBQVcsaUNBQWlDLHNGQUFzRixxQkFBcUIsaUJBQWlCLG9DQUFvQywyQkFBMkIsS0FBSyxNQUFNLElBQUksOEJBQThCLFNBQVMsZ0JBQWdCLElBQUksVUFBVSxTQUFTLGlCQUFpQixNQUFNLFNBQVMsU0FBUywrQkFBK0IsV0FBVyxPQUFPLFNBQVMsaUNBQWlDLGtCQUFrQixPQUFPLHVDQUF1Qyx5QkFBeUIsaUJBQWlCLHdGQUF3RixxQkFBcUIsUUFBUSxlQUFlLG9JQUFvSSxlQUFlLG9CQUFvQixvQ0FBb0MsbUJBQW1CLHdCQUF3QiwwQkFBMEIsb0JBQW9CLFNBQVMsSUFBSSxhQUFhLDJDQUEyQyxjQUFjLDJHQUEyRyxlQUFlLGtGQUFrRixpQkFBaUIsb0NBQW9DLDZDQUE2QyxlQUFlLDJDQUEyQyx5RUFBeUUsZUFBZSwwRUFBMEUscUJBQXFCLGlFQUFpRSxVQUFVLHdDQUF3Qyx3SEFBd0gsK1RBQStULGdCQUFnQixlQUFlLGtFQUFrRSxHQUFHLHdEQUF3RCxtQkFBbUIsNEJBQTRCLG1CQUFtQiw0QkFBNEIsbUJBQW1CLDRCQUE0QixpQkFBaUIsbUJBQW1CLEVBQUUsMEJBQTBCLEdBQUcsR0FBRyxFQUFFLDREQUE0RCxTQUFTLGlCQUFpQixrQkFBa0IsRUFBRSwwQkFBMEIsR0FBRyxHQUFHLEVBQUUsa0VBQWtFLFNBQVMsaUJBQWlCLE9BQU8saUlBQWlJLGlDQUFpQyxnQkFBZ0IseUNBQXlDLElBQUksa0NBQWtDLFNBQVMsVUFBVSwyQkFBMkIsR0FBRyx5QkFBeUIsTUFBTSxHQUFHLDRCQUE0QixtRkFBbUYsaUJBQWlCLE9BQU8sa0NBQWtDLHFEQUFxRCxnQ0FBZ0MsR0FBRywyQkFBMkIsTUFBTSxHQUFHLDZCQUE2QixrQ0FBa0MsNERBQTRELGdDQUFnQyxHQUFHLDJCQUEyQixNQUFNLEdBQUcsOEJBQThCLHVCQUF1QixtRUFBbUUsT0FBTyxjQUFjLFVBQVUsY0FBYyxRQUFRLGNBQWMsY0FBYyxlQUFlLHNTQUFzUyx3REFBd0QsRUFBRSxxQkFBcUIsZ0JBQWdCLEtBQUssRUFBRSx5Q0FBeUMsaURBQWlELE9BQU8sc0pBQXNKLE9BQU8sK0NBQStDLEdBQUcsNkVBQTZFLEdBQUcsNERBQTRELEdBQUcsMkRBQTJELEdBQUcseURBQXlELEdBQUcsMEJBQTBCLHNCQUFzQixNQUFNLHVHQUF1RyxzR0FBc0csbUJBQW1CLEtBQUssY0FBYyxzQkFBc0IsYUFBYSxnQ0FBZ0MsZ0JBQWdCLFVBQVUseUhBQXlILGFBQWEsbUNBQW1DLDhCQUE4QixPQUFPLDhCQUE4QixPQUFPLCtCQUErQixHQUFHLDBCQUEwQiw2RUFBNkUsdUNBQXVDLE9BQU8sa0JBQWtCLEVBQUUsb0NBQW9DLHFCQUFxQiwyREFBMkQscUhBQXFILDJIQUEySCxnR0FBZ0csYUFBYSxFQUFFLG1DQUFtQywrREFBK0QsOEdBQThHLE1BQU0sSUFBSSwyQkFBMkIsU0FBUyxZQUFZLDBSQUEwUixNQUFNLDRCQUE0QixvRkFBb0YsZ0JBQWdCLFVBQVUsK0JBQStCLHFCQUFxQix3REFBd0QsZ0NBQWdDLG9CQUFvQixPQUFPLHVDQUF1QyxPQUFPLDBCQUEwQixzQ0FBc0MsZUFBZSxtSEFBbUgsZUFBZSwwQkFBMEIsaUJBQWlCLHdEQUF3RCxpREFBaUQsTUFBTSxrQkFBa0Isc0JBQXNCLDRCQUE0Qix5Q0FBeUMsc0JBQXNCLEdBQUcsOENBQThDLGVBQWUsa0JBQWtCLGtCQUFrQiw0QkFBNEIsd0JBQXdCLHFDQUFxQyxnQkFBZ0IsSUFBSSxpQkFBaUIsb0NBQW9DLGtCQUFrQiwwQ0FBMEMsZUFBZSxpREFBaUQsR0FBRyx1Q0FBdUMsaUJBQWlCLE9BQU8sdUNBQXVDLG1EQUFtRCxvQkFBb0Isa0RBQWtELFFBQVEsY0FBYyxZQUFZLGNBQWMsY0FBYyxjQUFjLFNBQVMsY0FBYyxNQUFNLGNBQWMsU0FBUyxjQUFjLFNBQVMsZUFBZSxrWEFBa1gsdUNBQXVDLG9HQUFvRyxrRUFBa0UsRUFBRSx5QkFBeUIsYUFBYSxnQ0FBZ0MsZUFBZSwwSEFBMEgsb0JBQW9CLHVDQUF1QyxvREFBb0QsV0FBVyw4QkFBOEIsV0FBVyxlQUFlLDREQUE0RCxHQUFHLGtEQUFrRCxlQUFlLDRJQUE0SSw2REFBNkQsZUFBZSxjQUFjLE9BQU8sZUFBZSwyR0FBMkcsa0RBQWtELEVBQUUsZUFBZSxhQUFhLDJCQUEyQixlQUFlLGdIQUFnSCxvQkFBb0IsdUNBQXVDLCtDQUErQyxXQUFXLDhCQUE4QixXQUFXLGVBQWUsdURBQXVELEdBQUcsNkNBQTZDLGVBQWUsa0lBQWtJLG1CQUFtQiw0QkFBNEIsbUJBQW1CLDRCQUE0QixtQkFBbUIsa0NBQWtDLHdEQUF3RCxlQUFlLGNBQWMsT0FBTyxlQUFlLHNHQUFzRyw2Q0FBNkMsRUFBRSxzQkFBc0IsZ0JBQWdCLEtBQUssS0FBSyxFQUFFLHFCQUFxQiwyRUFBMkUsT0FBTyxxSkFBcUosT0FBTyxrQ0FBa0MsR0FBRywyRUFBMkUsR0FBRyxnRUFBZ0UsR0FBRyw4Q0FBOEMsc0JBQXNCLGtGQUFrRixrRkFBa0YsMENBQTBDLE1BQU0sdUJBQXVCLGFBQWEsU0FBUyxjQUFjLHFCQUFxQixxQ0FBcUMsb0JBQW9CLDRDQUE0Qyw0SUFBNEksZUFBZSxHQUFHLGVBQWUsTUFBTSxjQUFjLHFCQUFxQix5QkFBeUIsTUFBTSxhQUFhLG1CQUFtQiwyREFBMkQsdUJBQXVCLDZEQUE2RCxhQUFhLE9BQU8scUNBQXFDLEdBQUcsSUFBSSxhQUFhLG1CQUFtQiw2Q0FBNkMsSUFBSSxjQUFjLHlCQUF5Qix1SkFBdUosMkJBQTJCLFNBQVMsd0JBQXdCLElBQUksaUJBQWlCLDZDQUE2Qyx5R0FBeUcsR0FBRyxVQUFVLDJEQUEyRCxzQ0FBc0MsR0FBRyx1QkFBdUIsbUNBQW1DLDZCQUE2QixtQ0FBbUMsc0JBQXNCLG9CQUFvQiwyQ0FBMkMsUUFBUSwyQkFBMkIsbUNBQW1DLDRCQUE0QixtQ0FBbUMsK0RBQStELDBCQUEwQixnQkFBZ0Isb0JBQW9CLDREQUE0RCxRQUFRLHdFQUF3RSxFQUFFLHVCQUF1QixFQUFFLCtKQUErSiwyQkFBMkIsaURBQWlELFFBQVEsbURBQW1ELFFBQVEsK0NBQStDLEVBQUUsdUJBQXVCLEVBQUUsa0tBQWtLLGFBQWEsSUFBSSwwQkFBMEIsa0VBQWtFLFFBQVEsK0NBQStDLElBQUkseUJBQXlCLFNBQVMsY0FBYyxnREFBZ0QsbUJBQW1CLDBHQUEwRyxVQUFVLGdGQUFnRixlQUFlLGtDQUFrQyxzQkFBc0IsZUFBZSxrQ0FBa0MsdUJBQXVCLGVBQWUscUhBQXFILGlCQUFpQixnQkFBZ0IsaUJBQWlCLCtDQUErQywrQkFBK0IsdUNBQXVDLGdDQUFnQyxpQkFBaUIsa0hBQWtILHVDQUF1QyxxQkFBcUIsbURBQW1ELFVBQVUsY0FBYyxXQUFXLGVBQWUsaUdBQWlHLHdDQUF3QyxFQUFFLHVDQUF1QyxjQUFjLDJDQUEyQyxrQkFBa0IscUNBQXFDLDJDQUEyQyxXQUFXLGlDQUFpQyxXQUFXLFNBQVMsK0JBQStCLE1BQU0sMENBQTBDLFlBQVksbUNBQW1DLGFBQWEscUNBQXFDLGFBQWEsb0RBQW9ELFFBQVEsUUFBUSxlQUFlLHNJQUFzSSxlQUFlLHNEQUFzRCxpQkFBaUIscUNBQXFDLHNGQUFzRixJQUFJLGVBQWUsc0JBQXNCLElBQUksaUNBQWlDLFNBQVMsaUJBQWlCLE1BQU0sU0FBUyxxQ0FBcUMsb0JBQW9CLG1CQUFtQixtQkFBbUIsK0JBQStCLG9CQUFvQixTQUFTLElBQUksSUFBSSw4QkFBOEIsaUJBQWlCLDhDQUE4QywyQ0FBMkMsR0FBRyxlQUFlLG1FQUFtRSxHQUFHLHlEQUF5RCxlQUFlLGtEQUFrRCxHQUFHLHdDQUF3QyxlQUFlLGdFQUFnRSxlQUFlLHFGQUFxRixpQkFBaUIsb0hBQW9ILGVBQWUseUNBQXlDLGlCQUFpQiw0Q0FBNEMsaUJBQWlCLGtFQUFrRSx1Q0FBdUMsK0JBQStCLGVBQWUsMkJBQTJCLGVBQWUscUNBQXFDLG9FQUFvRSxTQUFTLGNBQWMsUUFBUSxjQUFjLFlBQVksY0FBYyxjQUFjLGVBQWUsa1RBQWtULHlEQUF5RCw4c0JBQThzQixTQUFTLEVBQUUiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcd2ViLXN0cmVhbXMtcG9seWZpbGxcXGRpc3RcXHBvbnlmaWxsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIHdlYi1zdHJlYW1zLXBvbHlmaWxsIHY0LjAuMC1iZXRhLjNcbiAqIENvcHlyaWdodCAyMDIxIE1hdHRpYXMgQnVlbGVucywgRGl3YW5rIFNpbmdoIFRvbWVyIGFuZCBvdGhlciBjb250cmlidXRvcnMuXG4gKiBUaGlzIGNvZGUgaXMgcmVsZWFzZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxuICogU1BEWC1MaWNlbnNlLUlkZW50aWZpZXI6IE1JVFxuICovXG4hZnVuY3Rpb24oZSx0KXtcIm9iamVjdFwiPT10eXBlb2YgZXhwb3J0cyYmXCJ1bmRlZmluZWRcIiE9dHlwZW9mIG1vZHVsZT90KGV4cG9ydHMpOlwiZnVuY3Rpb25cIj09dHlwZW9mIGRlZmluZSYmZGVmaW5lLmFtZD9kZWZpbmUoW1wiZXhwb3J0c1wiXSx0KTp0KChlPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6ZXx8c2VsZikuV2ViU3RyZWFtc1BvbHlmaWxsPXt9KX0odGhpcywoZnVuY3Rpb24oZSl7XCJ1c2Ugc3RyaWN0XCI7Y29uc3QgdD1cImZ1bmN0aW9uXCI9PXR5cGVvZiBTeW1ib2wmJlwic3ltYm9sXCI9PXR5cGVvZiBTeW1ib2wuaXRlcmF0b3I/U3ltYm9sOmU9PmBTeW1ib2woJHtlfSlgO2Z1bmN0aW9uIHIoKXt9ZnVuY3Rpb24gbyhlKXtyZXR1cm5cIm9iamVjdFwiPT10eXBlb2YgZSYmbnVsbCE9PWV8fFwiZnVuY3Rpb25cIj09dHlwZW9mIGV9Y29uc3Qgbj1yO2Z1bmN0aW9uIGEoZSx0KXt0cnl7T2JqZWN0LmRlZmluZVByb3BlcnR5KGUsXCJuYW1lXCIse3ZhbHVlOnQsY29uZmlndXJhYmxlOiEwfSl9Y2F0Y2goZSl7fX1jb25zdCBpPVByb21pc2UsbD1Qcm9taXNlLnByb3RvdHlwZS50aGVuLHM9UHJvbWlzZS5yZXNvbHZlLmJpbmQoaSksdT1Qcm9taXNlLnJlamVjdC5iaW5kKGkpO2Z1bmN0aW9uIGMoZSl7cmV0dXJuIG5ldyBpKGUpfWZ1bmN0aW9uIGQoZSl7cmV0dXJuIHMoZSl9ZnVuY3Rpb24gZihlKXtyZXR1cm4gdShlKX1mdW5jdGlvbiBiKGUsdCxyKXtyZXR1cm4gbC5jYWxsKGUsdCxyKX1mdW5jdGlvbiBoKGUsdCxyKXtiKGIoZSx0LHIpLHZvaWQgMCxuKX1mdW5jdGlvbiBfKGUsdCl7aChlLHQpfWZ1bmN0aW9uIHAoZSx0KXtoKGUsdm9pZCAwLHQpfWZ1bmN0aW9uIG0oZSx0LHIpe3JldHVybiBiKGUsdCxyKX1mdW5jdGlvbiB5KGUpe2IoZSx2b2lkIDAsbil9bGV0IGc9ZT0+e2lmKFwiZnVuY3Rpb25cIj09dHlwZW9mIHF1ZXVlTWljcm90YXNrKWc9cXVldWVNaWNyb3Rhc2s7ZWxzZXtjb25zdCBlPWQodm9pZCAwKTtnPXQ9PmIoZSx0KX1yZXR1cm4gZyhlKX07ZnVuY3Rpb24gUyhlLHQscil7aWYoXCJmdW5jdGlvblwiIT10eXBlb2YgZSl0aHJvdyBuZXcgVHlwZUVycm9yKFwiQXJndW1lbnQgaXMgbm90IGEgZnVuY3Rpb25cIik7cmV0dXJuIEZ1bmN0aW9uLnByb3RvdHlwZS5hcHBseS5jYWxsKGUsdCxyKX1mdW5jdGlvbiB3KGUsdCxyKXt0cnl7cmV0dXJuIGQoUyhlLHQscikpfWNhdGNoKGUpe3JldHVybiBmKGUpfX1jbGFzcyB2e2NvbnN0cnVjdG9yKCl7dGhpcy5fY3Vyc29yPTAsdGhpcy5fc2l6ZT0wLHRoaXMuX2Zyb250PXtfZWxlbWVudHM6W10sX25leHQ6dm9pZCAwfSx0aGlzLl9iYWNrPXRoaXMuX2Zyb250LHRoaXMuX2N1cnNvcj0wLHRoaXMuX3NpemU9MH1nZXQgbGVuZ3RoKCl7cmV0dXJuIHRoaXMuX3NpemV9cHVzaChlKXtjb25zdCB0PXRoaXMuX2JhY2s7bGV0IHI9dDsxNjM4Mz09PXQuX2VsZW1lbnRzLmxlbmd0aCYmKHI9e19lbGVtZW50czpbXSxfbmV4dDp2b2lkIDB9KSx0Ll9lbGVtZW50cy5wdXNoKGUpLHIhPT10JiYodGhpcy5fYmFjaz1yLHQuX25leHQ9ciksKyt0aGlzLl9zaXplfXNoaWZ0KCl7Y29uc3QgZT10aGlzLl9mcm9udDtsZXQgdD1lO2NvbnN0IHI9dGhpcy5fY3Vyc29yO2xldCBvPXIrMTtjb25zdCBuPWUuX2VsZW1lbnRzLGE9bltyXTtyZXR1cm4gMTYzODQ9PT1vJiYodD1lLl9uZXh0LG89MCksLS10aGlzLl9zaXplLHRoaXMuX2N1cnNvcj1vLGUhPT10JiYodGhpcy5fZnJvbnQ9dCksbltyXT12b2lkIDAsYX1mb3JFYWNoKGUpe2xldCB0PXRoaXMuX2N1cnNvcixyPXRoaXMuX2Zyb250LG89ci5fZWxlbWVudHM7Zm9yKDshKHQ9PT1vLmxlbmd0aCYmdm9pZCAwPT09ci5fbmV4dHx8dD09PW8ubGVuZ3RoJiYocj1yLl9uZXh0LG89ci5fZWxlbWVudHMsdD0wLDA9PT1vLmxlbmd0aCkpOyllKG9bdF0pLCsrdH1wZWVrKCl7Y29uc3QgZT10aGlzLl9mcm9udCx0PXRoaXMuX2N1cnNvcjtyZXR1cm4gZS5fZWxlbWVudHNbdF19fWNvbnN0IFI9dChcIltbQWJvcnRTdGVwc11dXCIpLFQ9dChcIltbRXJyb3JTdGVwc11dXCIpLHE9dChcIltbQ2FuY2VsU3RlcHNdXVwiKSxDPXQoXCJbW1B1bGxTdGVwc11dXCIpLFA9dChcIltbUmVsZWFzZVN0ZXBzXV1cIik7ZnVuY3Rpb24gRShlLHQpe2UuX293bmVyUmVhZGFibGVTdHJlYW09dCx0Ll9yZWFkZXI9ZSxcInJlYWRhYmxlXCI9PT10Ll9zdGF0ZT9CKGUpOlwiY2xvc2VkXCI9PT10Ll9zdGF0ZT9mdW5jdGlvbihlKXtCKGUpLHooZSl9KGUpOkEoZSx0Ll9zdG9yZWRFcnJvcil9ZnVuY3Rpb24gVyhlLHQpe3JldHVybiBYdChlLl9vd25lclJlYWRhYmxlU3RyZWFtLHQpfWZ1bmN0aW9uIE8oZSl7Y29uc3QgdD1lLl9vd25lclJlYWRhYmxlU3RyZWFtO1wicmVhZGFibGVcIj09PXQuX3N0YXRlP2ooZSxuZXcgVHlwZUVycm9yKFwiUmVhZGVyIHdhcyByZWxlYXNlZCBhbmQgY2FuIG5vIGxvbmdlciBiZSB1c2VkIHRvIG1vbml0b3IgdGhlIHN0cmVhbSdzIGNsb3NlZG5lc3NcIikpOmZ1bmN0aW9uKGUsdCl7QShlLHQpfShlLG5ldyBUeXBlRXJyb3IoXCJSZWFkZXIgd2FzIHJlbGVhc2VkIGFuZCBjYW4gbm8gbG9uZ2VyIGJlIHVzZWQgdG8gbW9uaXRvciB0aGUgc3RyZWFtJ3MgY2xvc2VkbmVzc1wiKSksdC5fcmVhZGFibGVTdHJlYW1Db250cm9sbGVyW1BdKCksdC5fcmVhZGVyPXZvaWQgMCxlLl9vd25lclJlYWRhYmxlU3RyZWFtPXZvaWQgMH1mdW5jdGlvbiBrKGUpe3JldHVybiBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IFwiK2UrXCIgYSBzdHJlYW0gdXNpbmcgYSByZWxlYXNlZCByZWFkZXJcIil9ZnVuY3Rpb24gQihlKXtlLl9jbG9zZWRQcm9taXNlPWMoKCh0LHIpPT57ZS5fY2xvc2VkUHJvbWlzZV9yZXNvbHZlPXQsZS5fY2xvc2VkUHJvbWlzZV9yZWplY3Q9cn0pKX1mdW5jdGlvbiBBKGUsdCl7QihlKSxqKGUsdCl9ZnVuY3Rpb24gaihlLHQpe3ZvaWQgMCE9PWUuX2Nsb3NlZFByb21pc2VfcmVqZWN0JiYoeShlLl9jbG9zZWRQcm9taXNlKSxlLl9jbG9zZWRQcm9taXNlX3JlamVjdCh0KSxlLl9jbG9zZWRQcm9taXNlX3Jlc29sdmU9dm9pZCAwLGUuX2Nsb3NlZFByb21pc2VfcmVqZWN0PXZvaWQgMCl9ZnVuY3Rpb24geihlKXt2b2lkIDAhPT1lLl9jbG9zZWRQcm9taXNlX3Jlc29sdmUmJihlLl9jbG9zZWRQcm9taXNlX3Jlc29sdmUodm9pZCAwKSxlLl9jbG9zZWRQcm9taXNlX3Jlc29sdmU9dm9pZCAwLGUuX2Nsb3NlZFByb21pc2VfcmVqZWN0PXZvaWQgMCl9Y29uc3QgTD1OdW1iZXIuaXNGaW5pdGV8fGZ1bmN0aW9uKGUpe3JldHVyblwibnVtYmVyXCI9PXR5cGVvZiBlJiZpc0Zpbml0ZShlKX0sRj1NYXRoLnRydW5jfHxmdW5jdGlvbihlKXtyZXR1cm4gZTwwP01hdGguY2VpbChlKTpNYXRoLmZsb29yKGUpfTtmdW5jdGlvbiBEKGUsdCl7aWYodm9pZCAwIT09ZSYmKFwib2JqZWN0XCIhPXR5cGVvZihyPWUpJiZcImZ1bmN0aW9uXCIhPXR5cGVvZiByKSl0aHJvdyBuZXcgVHlwZUVycm9yKGAke3R9IGlzIG5vdCBhbiBvYmplY3QuYCk7dmFyIHJ9ZnVuY3Rpb24gSShlLHQpe2lmKFwiZnVuY3Rpb25cIiE9dHlwZW9mIGUpdGhyb3cgbmV3IFR5cGVFcnJvcihgJHt0fSBpcyBub3QgYSBmdW5jdGlvbi5gKX1mdW5jdGlvbiAkKGUsdCl7aWYoIWZ1bmN0aW9uKGUpe3JldHVyblwib2JqZWN0XCI9PXR5cGVvZiBlJiZudWxsIT09ZXx8XCJmdW5jdGlvblwiPT10eXBlb2YgZX0oZSkpdGhyb3cgbmV3IFR5cGVFcnJvcihgJHt0fSBpcyBub3QgYW4gb2JqZWN0LmApfWZ1bmN0aW9uIE0oZSx0LHIpe2lmKHZvaWQgMD09PWUpdGhyb3cgbmV3IFR5cGVFcnJvcihgUGFyYW1ldGVyICR7dH0gaXMgcmVxdWlyZWQgaW4gJyR7cn0nLmApfWZ1bmN0aW9uIFkoZSx0LHIpe2lmKHZvaWQgMD09PWUpdGhyb3cgbmV3IFR5cGVFcnJvcihgJHt0fSBpcyByZXF1aXJlZCBpbiAnJHtyfScuYCl9ZnVuY3Rpb24gUShlKXtyZXR1cm4gTnVtYmVyKGUpfWZ1bmN0aW9uIE4oZSl7cmV0dXJuIDA9PT1lPzA6ZX1mdW5jdGlvbiB4KGUsdCl7Y29uc3Qgcj1OdW1iZXIuTUFYX1NBRkVfSU5URUdFUjtsZXQgbz1OdW1iZXIoZSk7aWYobz1OKG8pLCFMKG8pKXRocm93IG5ldyBUeXBlRXJyb3IoYCR7dH0gaXMgbm90IGEgZmluaXRlIG51bWJlcmApO2lmKG89ZnVuY3Rpb24oZSl7cmV0dXJuIE4oRihlKSl9KG8pLG88MHx8bz5yKXRocm93IG5ldyBUeXBlRXJyb3IoYCR7dH0gaXMgb3V0c2lkZSB0aGUgYWNjZXB0ZWQgcmFuZ2Ugb2YgMCB0byAke3J9LCBpbmNsdXNpdmVgKTtyZXR1cm4gTChvKSYmMCE9PW8/bzowfWZ1bmN0aW9uIEgoZSl7aWYoIW8oZSkpcmV0dXJuITE7aWYoXCJmdW5jdGlvblwiIT10eXBlb2YgZS5nZXRSZWFkZXIpcmV0dXJuITE7dHJ5e3JldHVyblwiYm9vbGVhblwiPT10eXBlb2YgZS5sb2NrZWR9Y2F0Y2goZSl7cmV0dXJuITF9fWZ1bmN0aW9uIFYoZSl7aWYoIW8oZSkpcmV0dXJuITE7aWYoXCJmdW5jdGlvblwiIT10eXBlb2YgZS5nZXRXcml0ZXIpcmV0dXJuITE7dHJ5e3JldHVyblwiYm9vbGVhblwiPT10eXBlb2YgZS5sb2NrZWR9Y2F0Y2goZSl7cmV0dXJuITF9fWZ1bmN0aW9uIFUoZSx0KXtpZighVXQoZSkpdGhyb3cgbmV3IFR5cGVFcnJvcihgJHt0fSBpcyBub3QgYSBSZWFkYWJsZVN0cmVhbS5gKX1mdW5jdGlvbiBHKGUsdCl7ZS5fcmVhZGVyLl9yZWFkUmVxdWVzdHMucHVzaCh0KX1mdW5jdGlvbiBYKGUsdCxyKXtjb25zdCBvPWUuX3JlYWRlci5fcmVhZFJlcXVlc3RzLnNoaWZ0KCk7cj9vLl9jbG9zZVN0ZXBzKCk6by5fY2h1bmtTdGVwcyh0KX1mdW5jdGlvbiBKKGUpe3JldHVybiBlLl9yZWFkZXIuX3JlYWRSZXF1ZXN0cy5sZW5ndGh9ZnVuY3Rpb24gSyhlKXtjb25zdCB0PWUuX3JlYWRlcjtyZXR1cm4gdm9pZCAwIT09dCYmISFaKHQpfWNsYXNzIFJlYWRhYmxlU3RyZWFtRGVmYXVsdFJlYWRlcntjb25zdHJ1Y3RvcihlKXtpZihNKGUsMSxcIlJlYWRhYmxlU3RyZWFtRGVmYXVsdFJlYWRlclwiKSxVKGUsXCJGaXJzdCBwYXJhbWV0ZXJcIiksR3QoZSkpdGhyb3cgbmV3IFR5cGVFcnJvcihcIlRoaXMgc3RyZWFtIGhhcyBhbHJlYWR5IGJlZW4gbG9ja2VkIGZvciBleGNsdXNpdmUgcmVhZGluZyBieSBhbm90aGVyIHJlYWRlclwiKTtFKHRoaXMsZSksdGhpcy5fcmVhZFJlcXVlc3RzPW5ldyB2fWdldCBjbG9zZWQoKXtyZXR1cm4gWih0aGlzKT90aGlzLl9jbG9zZWRQcm9taXNlOmYodGUoXCJjbG9zZWRcIikpfWNhbmNlbChlKXtyZXR1cm4gWih0aGlzKT92b2lkIDA9PT10aGlzLl9vd25lclJlYWRhYmxlU3RyZWFtP2YoayhcImNhbmNlbFwiKSk6Vyh0aGlzLGUpOmYodGUoXCJjYW5jZWxcIikpfXJlYWQoKXtpZighWih0aGlzKSlyZXR1cm4gZih0ZShcInJlYWRcIikpO2lmKHZvaWQgMD09PXRoaXMuX293bmVyUmVhZGFibGVTdHJlYW0pcmV0dXJuIGYoayhcInJlYWQgZnJvbVwiKSk7bGV0IGUsdDtjb25zdCByPWMoKChyLG8pPT57ZT1yLHQ9b30pKTtyZXR1cm4gZnVuY3Rpb24oZSx0KXtjb25zdCByPWUuX293bmVyUmVhZGFibGVTdHJlYW07ci5fZGlzdHVyYmVkPSEwLFwiY2xvc2VkXCI9PT1yLl9zdGF0ZT90Ll9jbG9zZVN0ZXBzKCk6XCJlcnJvcmVkXCI9PT1yLl9zdGF0ZT90Ll9lcnJvclN0ZXBzKHIuX3N0b3JlZEVycm9yKTpyLl9yZWFkYWJsZVN0cmVhbUNvbnRyb2xsZXJbQ10odCl9KHRoaXMse19jaHVua1N0ZXBzOnQ9PmUoe3ZhbHVlOnQsZG9uZTohMX0pLF9jbG9zZVN0ZXBzOigpPT5lKHt2YWx1ZTp2b2lkIDAsZG9uZTohMH0pLF9lcnJvclN0ZXBzOmU9PnQoZSl9KSxyfXJlbGVhc2VMb2NrKCl7aWYoIVoodGhpcykpdGhyb3cgdGUoXCJyZWxlYXNlTG9ja1wiKTt2b2lkIDAhPT10aGlzLl9vd25lclJlYWRhYmxlU3RyZWFtJiZmdW5jdGlvbihlKXtPKGUpO2NvbnN0IHQ9bmV3IFR5cGVFcnJvcihcIlJlYWRlciB3YXMgcmVsZWFzZWRcIik7ZWUoZSx0KX0odGhpcyl9fWZ1bmN0aW9uIFooZSl7cmV0dXJuISFvKGUpJiYoISFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZSxcIl9yZWFkUmVxdWVzdHNcIikmJmUgaW5zdGFuY2VvZiBSZWFkYWJsZVN0cmVhbURlZmF1bHRSZWFkZXIpfWZ1bmN0aW9uIGVlKGUsdCl7Y29uc3Qgcj1lLl9yZWFkUmVxdWVzdHM7ZS5fcmVhZFJlcXVlc3RzPW5ldyB2LHIuZm9yRWFjaCgoZT0+e2UuX2Vycm9yU3RlcHModCl9KSl9ZnVuY3Rpb24gdGUoZSl7cmV0dXJuIG5ldyBUeXBlRXJyb3IoYFJlYWRhYmxlU3RyZWFtRGVmYXVsdFJlYWRlci5wcm90b3R5cGUuJHtlfSBjYW4gb25seSBiZSB1c2VkIG9uIGEgUmVhZGFibGVTdHJlYW1EZWZhdWx0UmVhZGVyYCl9T2JqZWN0LmRlZmluZVByb3BlcnRpZXMoUmVhZGFibGVTdHJlYW1EZWZhdWx0UmVhZGVyLnByb3RvdHlwZSx7Y2FuY2VsOntlbnVtZXJhYmxlOiEwfSxyZWFkOntlbnVtZXJhYmxlOiEwfSxyZWxlYXNlTG9jazp7ZW51bWVyYWJsZTohMH0sY2xvc2VkOntlbnVtZXJhYmxlOiEwfX0pLGEoUmVhZGFibGVTdHJlYW1EZWZhdWx0UmVhZGVyLnByb3RvdHlwZS5jYW5jZWwsXCJjYW5jZWxcIiksYShSZWFkYWJsZVN0cmVhbURlZmF1bHRSZWFkZXIucHJvdG90eXBlLnJlYWQsXCJyZWFkXCIpLGEoUmVhZGFibGVTdHJlYW1EZWZhdWx0UmVhZGVyLnByb3RvdHlwZS5yZWxlYXNlTG9jayxcInJlbGVhc2VMb2NrXCIpLFwic3ltYm9sXCI9PXR5cGVvZiB0LnRvU3RyaW5nVGFnJiZPYmplY3QuZGVmaW5lUHJvcGVydHkoUmVhZGFibGVTdHJlYW1EZWZhdWx0UmVhZGVyLnByb3RvdHlwZSx0LnRvU3RyaW5nVGFnLHt2YWx1ZTpcIlJlYWRhYmxlU3RyZWFtRGVmYXVsdFJlYWRlclwiLGNvbmZpZ3VyYWJsZTohMH0pO2NsYXNzIHJle2NvbnN0cnVjdG9yKGUsdCl7dGhpcy5fb25nb2luZ1Byb21pc2U9dm9pZCAwLHRoaXMuX2lzRmluaXNoZWQ9ITEsdGhpcy5fcmVhZGVyPWUsdGhpcy5fcHJldmVudENhbmNlbD10fW5leHQoKXtjb25zdCBlPSgpPT50aGlzLl9uZXh0U3RlcHMoKTtyZXR1cm4gdGhpcy5fb25nb2luZ1Byb21pc2U9dGhpcy5fb25nb2luZ1Byb21pc2U/bSh0aGlzLl9vbmdvaW5nUHJvbWlzZSxlLGUpOmUoKSx0aGlzLl9vbmdvaW5nUHJvbWlzZX1yZXR1cm4oZSl7Y29uc3QgdD0oKT0+dGhpcy5fcmV0dXJuU3RlcHMoZSk7cmV0dXJuIHRoaXMuX29uZ29pbmdQcm9taXNlP20odGhpcy5fb25nb2luZ1Byb21pc2UsdCx0KTp0KCl9X25leHRTdGVwcygpe2lmKHRoaXMuX2lzRmluaXNoZWQpcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7dmFsdWU6dm9pZCAwLGRvbmU6ITB9KTtjb25zdCBlPXRoaXMuX3JlYWRlcjtyZXR1cm4gdm9pZCAwPT09ZT9mKGsoXCJpdGVyYXRlXCIpKTpiKGUucmVhZCgpLChlPT57dmFyIHQ7cmV0dXJuIHRoaXMuX29uZ29pbmdQcm9taXNlPXZvaWQgMCxlLmRvbmUmJih0aGlzLl9pc0ZpbmlzaGVkPSEwLG51bGw9PT0odD10aGlzLl9yZWFkZXIpfHx2b2lkIDA9PT10fHx0LnJlbGVhc2VMb2NrKCksdGhpcy5fcmVhZGVyPXZvaWQgMCksZX0pLChlPT57dmFyIHQ7dGhyb3cgdGhpcy5fb25nb2luZ1Byb21pc2U9dm9pZCAwLHRoaXMuX2lzRmluaXNoZWQ9ITAsbnVsbD09PSh0PXRoaXMuX3JlYWRlcil8fHZvaWQgMD09PXR8fHQucmVsZWFzZUxvY2soKSx0aGlzLl9yZWFkZXI9dm9pZCAwLGV9KSl9X3JldHVyblN0ZXBzKGUpe2lmKHRoaXMuX2lzRmluaXNoZWQpcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7dmFsdWU6ZSxkb25lOiEwfSk7dGhpcy5faXNGaW5pc2hlZD0hMDtjb25zdCB0PXRoaXMuX3JlYWRlcjtpZih2b2lkIDA9PT10KXJldHVybiBmKGsoXCJmaW5pc2ggaXRlcmF0aW5nXCIpKTtpZih0aGlzLl9yZWFkZXI9dm9pZCAwLCF0aGlzLl9wcmV2ZW50Q2FuY2VsKXtjb25zdCByPXQuY2FuY2VsKGUpO3JldHVybiB0LnJlbGVhc2VMb2NrKCksbShyLCgoKT0+KHt2YWx1ZTplLGRvbmU6ITB9KSkpfXJldHVybiB0LnJlbGVhc2VMb2NrKCksZCh7dmFsdWU6ZSxkb25lOiEwfSl9fWNvbnN0IG9lPXtuZXh0KCl7cmV0dXJuIG5lKHRoaXMpP3RoaXMuX2FzeW5jSXRlcmF0b3JJbXBsLm5leHQoKTpmKGFlKFwibmV4dFwiKSl9LHJldHVybihlKXtyZXR1cm4gbmUodGhpcyk/dGhpcy5fYXN5bmNJdGVyYXRvckltcGwucmV0dXJuKGUpOmYoYWUoXCJyZXR1cm5cIikpfX07ZnVuY3Rpb24gbmUoZSl7aWYoIW8oZSkpcmV0dXJuITE7aWYoIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiX2FzeW5jSXRlcmF0b3JJbXBsXCIpKXJldHVybiExO3RyeXtyZXR1cm4gZS5fYXN5bmNJdGVyYXRvckltcGwgaW5zdGFuY2VvZiByZX1jYXRjaChlKXtyZXR1cm4hMX19ZnVuY3Rpb24gYWUoZSl7cmV0dXJuIG5ldyBUeXBlRXJyb3IoYFJlYWRhYmxlU3RyZWFtQXN5bmNJdGVyYXRvci4ke2V9IGNhbiBvbmx5IGJlIHVzZWQgb24gYSBSZWFkYWJsZVN0ZWFtQXN5bmNJdGVyYXRvcmApfVwic3ltYm9sXCI9PXR5cGVvZiB0LmFzeW5jSXRlcmF0b3ImJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShvZSx0LmFzeW5jSXRlcmF0b3Ise3ZhbHVlKCl7cmV0dXJuIHRoaXN9LHdyaXRhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMH0pO2NvbnN0IGllPU51bWJlci5pc05hTnx8ZnVuY3Rpb24oZSl7cmV0dXJuIGUhPWV9O2Z1bmN0aW9uIGxlKGUsdCxyLG8sbil7bmV3IFVpbnQ4QXJyYXkoZSkuc2V0KG5ldyBVaW50OEFycmF5KHIsbyxuKSx0KX1mdW5jdGlvbiBzZShlKXtjb25zdCB0PWZ1bmN0aW9uKGUsdCxyKXtpZihlLnNsaWNlKXJldHVybiBlLnNsaWNlKHQscik7Y29uc3Qgbz1yLXQsbj1uZXcgQXJyYXlCdWZmZXIobyk7cmV0dXJuIGxlKG4sMCxlLHQsbyksbn0oZS5idWZmZXIsZS5ieXRlT2Zmc2V0LGUuYnl0ZU9mZnNldCtlLmJ5dGVMZW5ndGgpO3JldHVybiBuZXcgVWludDhBcnJheSh0KX1mdW5jdGlvbiB1ZShlKXtjb25zdCB0PWUuX3F1ZXVlLnNoaWZ0KCk7cmV0dXJuIGUuX3F1ZXVlVG90YWxTaXplLT10LnNpemUsZS5fcXVldWVUb3RhbFNpemU8MCYmKGUuX3F1ZXVlVG90YWxTaXplPTApLHQudmFsdWV9ZnVuY3Rpb24gY2UoZSx0LHIpe2lmKFwibnVtYmVyXCIhPXR5cGVvZihvPXIpfHxpZShvKXx8bzwwfHxyPT09MS8wKXRocm93IG5ldyBSYW5nZUVycm9yKFwiU2l6ZSBtdXN0IGJlIGEgZmluaXRlLCBub24tTmFOLCBub24tbmVnYXRpdmUgbnVtYmVyLlwiKTt2YXIgbztlLl9xdWV1ZS5wdXNoKHt2YWx1ZTp0LHNpemU6cn0pLGUuX3F1ZXVlVG90YWxTaXplKz1yfWZ1bmN0aW9uIGRlKGUpe2UuX3F1ZXVlPW5ldyB2LGUuX3F1ZXVlVG90YWxTaXplPTB9Y2xhc3MgUmVhZGFibGVTdHJlYW1CWU9CUmVxdWVzdHtjb25zdHJ1Y3Rvcigpe3Rocm93IG5ldyBUeXBlRXJyb3IoXCJJbGxlZ2FsIGNvbnN0cnVjdG9yXCIpfWdldCB2aWV3KCl7aWYoIWJlKHRoaXMpKXRocm93IEFlKFwidmlld1wiKTtyZXR1cm4gdGhpcy5fdmlld31yZXNwb25kKGUpe2lmKCFiZSh0aGlzKSl0aHJvdyBBZShcInJlc3BvbmRcIik7aWYoTShlLDEsXCJyZXNwb25kXCIpLGU9eChlLFwiRmlyc3QgcGFyYW1ldGVyXCIpLHZvaWQgMD09PXRoaXMuX2Fzc29jaWF0ZWRSZWFkYWJsZUJ5dGVTdHJlYW1Db250cm9sbGVyKXRocm93IG5ldyBUeXBlRXJyb3IoXCJUaGlzIEJZT0IgcmVxdWVzdCBoYXMgYmVlbiBpbnZhbGlkYXRlZFwiKTt0aGlzLl92aWV3LmJ1ZmZlcixmdW5jdGlvbihlLHQpe2NvbnN0IHI9ZS5fcGVuZGluZ1B1bGxJbnRvcy5wZWVrKCk7aWYoXCJjbG9zZWRcIj09PWUuX2NvbnRyb2xsZWRSZWFkYWJsZUJ5dGVTdHJlYW0uX3N0YXRlKXtpZigwIT09dCl0aHJvdyBuZXcgVHlwZUVycm9yKFwiYnl0ZXNXcml0dGVuIG11c3QgYmUgMCB3aGVuIGNhbGxpbmcgcmVzcG9uZCgpIG9uIGEgY2xvc2VkIHN0cmVhbVwiKX1lbHNle2lmKDA9PT10KXRocm93IG5ldyBUeXBlRXJyb3IoXCJieXRlc1dyaXR0ZW4gbXVzdCBiZSBncmVhdGVyIHRoYW4gMCB3aGVuIGNhbGxpbmcgcmVzcG9uZCgpIG9uIGEgcmVhZGFibGUgc3RyZWFtXCIpO2lmKHIuYnl0ZXNGaWxsZWQrdD5yLmJ5dGVMZW5ndGgpdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJieXRlc1dyaXR0ZW4gb3V0IG9mIHJhbmdlXCIpfXIuYnVmZmVyPXIuYnVmZmVyLENlKGUsdCl9KHRoaXMuX2Fzc29jaWF0ZWRSZWFkYWJsZUJ5dGVTdHJlYW1Db250cm9sbGVyLGUpfXJlc3BvbmRXaXRoTmV3VmlldyhlKXtpZighYmUodGhpcykpdGhyb3cgQWUoXCJyZXNwb25kV2l0aE5ld1ZpZXdcIik7aWYoTShlLDEsXCJyZXNwb25kV2l0aE5ld1ZpZXdcIiksIUFycmF5QnVmZmVyLmlzVmlldyhlKSl0aHJvdyBuZXcgVHlwZUVycm9yKFwiWW91IGNhbiBvbmx5IHJlc3BvbmQgd2l0aCBhcnJheSBidWZmZXIgdmlld3NcIik7aWYodm9pZCAwPT09dGhpcy5fYXNzb2NpYXRlZFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXIpdGhyb3cgbmV3IFR5cGVFcnJvcihcIlRoaXMgQllPQiByZXF1ZXN0IGhhcyBiZWVuIGludmFsaWRhdGVkXCIpO2UuYnVmZmVyLGZ1bmN0aW9uKGUsdCl7Y29uc3Qgcj1lLl9wZW5kaW5nUHVsbEludG9zLnBlZWsoKTtpZihcImNsb3NlZFwiPT09ZS5fY29udHJvbGxlZFJlYWRhYmxlQnl0ZVN0cmVhbS5fc3RhdGUpe2lmKDAhPT10LmJ5dGVMZW5ndGgpdGhyb3cgbmV3IFR5cGVFcnJvcihcIlRoZSB2aWV3J3MgbGVuZ3RoIG11c3QgYmUgMCB3aGVuIGNhbGxpbmcgcmVzcG9uZFdpdGhOZXdWaWV3KCkgb24gYSBjbG9zZWQgc3RyZWFtXCIpfWVsc2UgaWYoMD09PXQuYnl0ZUxlbmd0aCl0aHJvdyBuZXcgVHlwZUVycm9yKFwiVGhlIHZpZXcncyBsZW5ndGggbXVzdCBiZSBncmVhdGVyIHRoYW4gMCB3aGVuIGNhbGxpbmcgcmVzcG9uZFdpdGhOZXdWaWV3KCkgb24gYSByZWFkYWJsZSBzdHJlYW1cIik7aWYoci5ieXRlT2Zmc2V0K3IuYnl0ZXNGaWxsZWQhPT10LmJ5dGVPZmZzZXQpdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJUaGUgcmVnaW9uIHNwZWNpZmllZCBieSB2aWV3IGRvZXMgbm90IG1hdGNoIGJ5b2JSZXF1ZXN0XCIpO2lmKHIuYnVmZmVyQnl0ZUxlbmd0aCE9PXQuYnVmZmVyLmJ5dGVMZW5ndGgpdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJUaGUgYnVmZmVyIG9mIHZpZXcgaGFzIGRpZmZlcmVudCBjYXBhY2l0eSB0aGFuIGJ5b2JSZXF1ZXN0XCIpO2lmKHIuYnl0ZXNGaWxsZWQrdC5ieXRlTGVuZ3RoPnIuYnl0ZUxlbmd0aCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIlRoZSByZWdpb24gc3BlY2lmaWVkIGJ5IHZpZXcgaXMgbGFyZ2VyIHRoYW4gYnlvYlJlcXVlc3RcIik7Y29uc3Qgbz10LmJ5dGVMZW5ndGg7ci5idWZmZXI9dC5idWZmZXIsQ2UoZSxvKX0odGhpcy5fYXNzb2NpYXRlZFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXIsZSl9fU9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKFJlYWRhYmxlU3RyZWFtQllPQlJlcXVlc3QucHJvdG90eXBlLHtyZXNwb25kOntlbnVtZXJhYmxlOiEwfSxyZXNwb25kV2l0aE5ld1ZpZXc6e2VudW1lcmFibGU6ITB9LHZpZXc6e2VudW1lcmFibGU6ITB9fSksYShSZWFkYWJsZVN0cmVhbUJZT0JSZXF1ZXN0LnByb3RvdHlwZS5yZXNwb25kLFwicmVzcG9uZFwiKSxhKFJlYWRhYmxlU3RyZWFtQllPQlJlcXVlc3QucHJvdG90eXBlLnJlc3BvbmRXaXRoTmV3VmlldyxcInJlc3BvbmRXaXRoTmV3Vmlld1wiKSxcInN5bWJvbFwiPT10eXBlb2YgdC50b1N0cmluZ1RhZyYmT2JqZWN0LmRlZmluZVByb3BlcnR5KFJlYWRhYmxlU3RyZWFtQllPQlJlcXVlc3QucHJvdG90eXBlLHQudG9TdHJpbmdUYWcse3ZhbHVlOlwiUmVhZGFibGVTdHJlYW1CWU9CUmVxdWVzdFwiLGNvbmZpZ3VyYWJsZTohMH0pO2NsYXNzIFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXJ7Y29uc3RydWN0b3IoKXt0aHJvdyBuZXcgVHlwZUVycm9yKFwiSWxsZWdhbCBjb25zdHJ1Y3RvclwiKX1nZXQgYnlvYlJlcXVlc3QoKXtpZighZmUodGhpcykpdGhyb3cgamUoXCJieW9iUmVxdWVzdFwiKTtyZXR1cm4gZnVuY3Rpb24oZSl7aWYobnVsbD09PWUuX2J5b2JSZXF1ZXN0JiZlLl9wZW5kaW5nUHVsbEludG9zLmxlbmd0aD4wKXtjb25zdCB0PWUuX3BlbmRpbmdQdWxsSW50b3MucGVlaygpLHI9bmV3IFVpbnQ4QXJyYXkodC5idWZmZXIsdC5ieXRlT2Zmc2V0K3QuYnl0ZXNGaWxsZWQsdC5ieXRlTGVuZ3RoLXQuYnl0ZXNGaWxsZWQpLG89T2JqZWN0LmNyZWF0ZShSZWFkYWJsZVN0cmVhbUJZT0JSZXF1ZXN0LnByb3RvdHlwZSk7IWZ1bmN0aW9uKGUsdCxyKXtlLl9hc3NvY2lhdGVkUmVhZGFibGVCeXRlU3RyZWFtQ29udHJvbGxlcj10LGUuX3ZpZXc9cn0obyxlLHIpLGUuX2J5b2JSZXF1ZXN0PW99cmV0dXJuIGUuX2J5b2JSZXF1ZXN0fSh0aGlzKX1nZXQgZGVzaXJlZFNpemUoKXtpZighZmUodGhpcykpdGhyb3cgamUoXCJkZXNpcmVkU2l6ZVwiKTtyZXR1cm4ga2UodGhpcyl9Y2xvc2UoKXtpZighZmUodGhpcykpdGhyb3cgamUoXCJjbG9zZVwiKTtpZih0aGlzLl9jbG9zZVJlcXVlc3RlZCl0aHJvdyBuZXcgVHlwZUVycm9yKFwiVGhlIHN0cmVhbSBoYXMgYWxyZWFkeSBiZWVuIGNsb3NlZDsgZG8gbm90IGNsb3NlIGl0IGFnYWluIVwiKTtjb25zdCBlPXRoaXMuX2NvbnRyb2xsZWRSZWFkYWJsZUJ5dGVTdHJlYW0uX3N0YXRlO2lmKFwicmVhZGFibGVcIiE9PWUpdGhyb3cgbmV3IFR5cGVFcnJvcihgVGhlIHN0cmVhbSAoaW4gJHtlfSBzdGF0ZSkgaXMgbm90IGluIHRoZSByZWFkYWJsZSBzdGF0ZSBhbmQgY2Fubm90IGJlIGNsb3NlZGApOyFmdW5jdGlvbihlKXtjb25zdCB0PWUuX2NvbnRyb2xsZWRSZWFkYWJsZUJ5dGVTdHJlYW07aWYoZS5fY2xvc2VSZXF1ZXN0ZWR8fFwicmVhZGFibGVcIiE9PXQuX3N0YXRlKXJldHVybjtpZihlLl9xdWV1ZVRvdGFsU2l6ZT4wKXJldHVybiB2b2lkKGUuX2Nsb3NlUmVxdWVzdGVkPSEwKTtpZihlLl9wZW5kaW5nUHVsbEludG9zLmxlbmd0aD4wKXtpZihlLl9wZW5kaW5nUHVsbEludG9zLnBlZWsoKS5ieXRlc0ZpbGxlZD4wKXtjb25zdCB0PW5ldyBUeXBlRXJyb3IoXCJJbnN1ZmZpY2llbnQgYnl0ZXMgdG8gZmlsbCBlbGVtZW50cyBpbiB0aGUgZ2l2ZW4gYnVmZmVyXCIpO3Rocm93IFdlKGUsdCksdH19RWUoZSksSnQodCl9KHRoaXMpfWVucXVldWUoZSl7aWYoIWZlKHRoaXMpKXRocm93IGplKFwiZW5xdWV1ZVwiKTtpZihNKGUsMSxcImVucXVldWVcIiksIUFycmF5QnVmZmVyLmlzVmlldyhlKSl0aHJvdyBuZXcgVHlwZUVycm9yKFwiY2h1bmsgbXVzdCBiZSBhbiBhcnJheSBidWZmZXIgdmlld1wiKTtpZigwPT09ZS5ieXRlTGVuZ3RoKXRocm93IG5ldyBUeXBlRXJyb3IoXCJjaHVuayBtdXN0IGhhdmUgbm9uLXplcm8gYnl0ZUxlbmd0aFwiKTtpZigwPT09ZS5idWZmZXIuYnl0ZUxlbmd0aCl0aHJvdyBuZXcgVHlwZUVycm9yKFwiY2h1bmsncyBidWZmZXIgbXVzdCBoYXZlIG5vbi16ZXJvIGJ5dGVMZW5ndGhcIik7aWYodGhpcy5fY2xvc2VSZXF1ZXN0ZWQpdGhyb3cgbmV3IFR5cGVFcnJvcihcInN0cmVhbSBpcyBjbG9zZWQgb3IgZHJhaW5pbmdcIik7Y29uc3QgdD10aGlzLl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtLl9zdGF0ZTtpZihcInJlYWRhYmxlXCIhPT10KXRocm93IG5ldyBUeXBlRXJyb3IoYFRoZSBzdHJlYW0gKGluICR7dH0gc3RhdGUpIGlzIG5vdCBpbiB0aGUgcmVhZGFibGUgc3RhdGUgYW5kIGNhbm5vdCBiZSBlbnF1ZXVlZCB0b2ApOyFmdW5jdGlvbihlLHQpe2NvbnN0IHI9ZS5fY29udHJvbGxlZFJlYWRhYmxlQnl0ZVN0cmVhbTtpZihlLl9jbG9zZVJlcXVlc3RlZHx8XCJyZWFkYWJsZVwiIT09ci5fc3RhdGUpcmV0dXJuO2NvbnN0IG89dC5idWZmZXIsbj10LmJ5dGVPZmZzZXQsYT10LmJ5dGVMZW5ndGgsaT1vO2lmKGUuX3BlbmRpbmdQdWxsSW50b3MubGVuZ3RoPjApe2NvbnN0IHQ9ZS5fcGVuZGluZ1B1bGxJbnRvcy5wZWVrKCk7dC5idWZmZXIsMCxUZShlKSx0LmJ1ZmZlcj10LmJ1ZmZlcixcIm5vbmVcIj09PXQucmVhZGVyVHlwZSYmU2UoZSx0KX1pZihLKHIpKWlmKGZ1bmN0aW9uKGUpe2NvbnN0IHQ9ZS5fY29udHJvbGxlZFJlYWRhYmxlQnl0ZVN0cmVhbS5fcmVhZGVyO2Zvcig7dC5fcmVhZFJlcXVlc3RzLmxlbmd0aD4wOyl7aWYoMD09PWUuX3F1ZXVlVG90YWxTaXplKXJldHVybjtPZShlLHQuX3JlYWRSZXF1ZXN0cy5zaGlmdCgpKX19KGUpLDA9PT1KKHIpKXllKGUsaSxuLGEpO2Vsc2V7ZS5fcGVuZGluZ1B1bGxJbnRvcy5sZW5ndGg+MCYmUGUoZSk7WChyLG5ldyBVaW50OEFycmF5KGksbixhKSwhMSl9ZWxzZSBGZShyKT8oeWUoZSxpLG4sYSkscWUoZSkpOnllKGUsaSxuLGEpO2hlKGUpfSh0aGlzLGUpfWVycm9yKGUpe2lmKCFmZSh0aGlzKSl0aHJvdyBqZShcImVycm9yXCIpO1dlKHRoaXMsZSl9W3FdKGUpe19lKHRoaXMpLGRlKHRoaXMpO2NvbnN0IHQ9dGhpcy5fY2FuY2VsQWxnb3JpdGhtKGUpO3JldHVybiBFZSh0aGlzKSx0fVtDXShlKXtjb25zdCB0PXRoaXMuX2NvbnRyb2xsZWRSZWFkYWJsZUJ5dGVTdHJlYW07aWYodGhpcy5fcXVldWVUb3RhbFNpemU+MClyZXR1cm4gdm9pZCBPZSh0aGlzLGUpO2NvbnN0IHI9dGhpcy5fYXV0b0FsbG9jYXRlQ2h1bmtTaXplO2lmKHZvaWQgMCE9PXIpe2xldCB0O3RyeXt0PW5ldyBBcnJheUJ1ZmZlcihyKX1jYXRjaCh0KXtyZXR1cm4gdm9pZCBlLl9lcnJvclN0ZXBzKHQpfWNvbnN0IG89e2J1ZmZlcjp0LGJ1ZmZlckJ5dGVMZW5ndGg6cixieXRlT2Zmc2V0OjAsYnl0ZUxlbmd0aDpyLGJ5dGVzRmlsbGVkOjAsZWxlbWVudFNpemU6MSx2aWV3Q29uc3RydWN0b3I6VWludDhBcnJheSxyZWFkZXJUeXBlOlwiZGVmYXVsdFwifTt0aGlzLl9wZW5kaW5nUHVsbEludG9zLnB1c2gobyl9Ryh0LGUpLGhlKHRoaXMpfVtQXSgpe2lmKHRoaXMuX3BlbmRpbmdQdWxsSW50b3MubGVuZ3RoPjApe2NvbnN0IGU9dGhpcy5fcGVuZGluZ1B1bGxJbnRvcy5wZWVrKCk7ZS5yZWFkZXJUeXBlPVwibm9uZVwiLHRoaXMuX3BlbmRpbmdQdWxsSW50b3M9bmV3IHYsdGhpcy5fcGVuZGluZ1B1bGxJbnRvcy5wdXNoKGUpfX19ZnVuY3Rpb24gZmUoZSl7cmV0dXJuISFvKGUpJiYoISFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZSxcIl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtXCIpJiZlIGluc3RhbmNlb2YgUmVhZGFibGVCeXRlU3RyZWFtQ29udHJvbGxlcil9ZnVuY3Rpb24gYmUoZSl7cmV0dXJuISFvKGUpJiYoISFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZSxcIl9hc3NvY2lhdGVkUmVhZGFibGVCeXRlU3RyZWFtQ29udHJvbGxlclwiKSYmZSBpbnN0YW5jZW9mIFJlYWRhYmxlU3RyZWFtQllPQlJlcXVlc3QpfWZ1bmN0aW9uIGhlKGUpe2NvbnN0IHQ9ZnVuY3Rpb24oZSl7Y29uc3QgdD1lLl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtO2lmKFwicmVhZGFibGVcIiE9PXQuX3N0YXRlKXJldHVybiExO2lmKGUuX2Nsb3NlUmVxdWVzdGVkKXJldHVybiExO2lmKCFlLl9zdGFydGVkKXJldHVybiExO2lmKEsodCkmJkoodCk+MClyZXR1cm4hMDtpZihGZSh0KSYmTGUodCk+MClyZXR1cm4hMDtpZihrZShlKT4wKXJldHVybiEwO3JldHVybiExfShlKTtpZighdClyZXR1cm47aWYoZS5fcHVsbGluZylyZXR1cm4gdm9pZChlLl9wdWxsQWdhaW49ITApO2UuX3B1bGxpbmc9ITA7aChlLl9wdWxsQWxnb3JpdGhtKCksKCgpPT4oZS5fcHVsbGluZz0hMSxlLl9wdWxsQWdhaW4mJihlLl9wdWxsQWdhaW49ITEsaGUoZSkpLG51bGwpKSwodD0+KFdlKGUsdCksbnVsbCkpKX1mdW5jdGlvbiBfZShlKXtUZShlKSxlLl9wZW5kaW5nUHVsbEludG9zPW5ldyB2fWZ1bmN0aW9uIHBlKGUsdCl7bGV0IHI9ITE7XCJjbG9zZWRcIj09PWUuX3N0YXRlJiYocj0hMCk7Y29uc3Qgbz1tZSh0KTtcImRlZmF1bHRcIj09PXQucmVhZGVyVHlwZT9YKGUsbyxyKTpmdW5jdGlvbihlLHQscil7Y29uc3Qgbz1lLl9yZWFkZXIuX3JlYWRJbnRvUmVxdWVzdHMuc2hpZnQoKTtyP28uX2Nsb3NlU3RlcHModCk6by5fY2h1bmtTdGVwcyh0KX0oZSxvLHIpfWZ1bmN0aW9uIG1lKGUpe2NvbnN0IHQ9ZS5ieXRlc0ZpbGxlZCxyPWUuZWxlbWVudFNpemU7cmV0dXJuIG5ldyBlLnZpZXdDb25zdHJ1Y3RvcihlLmJ1ZmZlcixlLmJ5dGVPZmZzZXQsdC9yKX1mdW5jdGlvbiB5ZShlLHQscixvKXtlLl9xdWV1ZS5wdXNoKHtidWZmZXI6dCxieXRlT2Zmc2V0OnIsYnl0ZUxlbmd0aDpvfSksZS5fcXVldWVUb3RhbFNpemUrPW99ZnVuY3Rpb24gZ2UoZSx0LHIsbyl7bGV0IG47dHJ5e249dC5zbGljZShyLHIrbyl9Y2F0Y2godCl7dGhyb3cgV2UoZSx0KSx0fXllKGUsbiwwLG8pfWZ1bmN0aW9uIFNlKGUsdCl7dC5ieXRlc0ZpbGxlZD4wJiZnZShlLHQuYnVmZmVyLHQuYnl0ZU9mZnNldCx0LmJ5dGVzRmlsbGVkKSxQZShlKX1mdW5jdGlvbiB3ZShlLHQpe2NvbnN0IHI9dC5lbGVtZW50U2l6ZSxvPXQuYnl0ZXNGaWxsZWQtdC5ieXRlc0ZpbGxlZCVyLG49TWF0aC5taW4oZS5fcXVldWVUb3RhbFNpemUsdC5ieXRlTGVuZ3RoLXQuYnl0ZXNGaWxsZWQpLGE9dC5ieXRlc0ZpbGxlZCtuLGk9YS1hJXI7bGV0IGw9bixzPSExO2k+byYmKGw9aS10LmJ5dGVzRmlsbGVkLHM9ITApO2NvbnN0IHU9ZS5fcXVldWU7Zm9yKDtsPjA7KXtjb25zdCByPXUucGVlaygpLG89TWF0aC5taW4obCxyLmJ5dGVMZW5ndGgpLG49dC5ieXRlT2Zmc2V0K3QuYnl0ZXNGaWxsZWQ7bGUodC5idWZmZXIsbixyLmJ1ZmZlcixyLmJ5dGVPZmZzZXQsbyksci5ieXRlTGVuZ3RoPT09bz91LnNoaWZ0KCk6KHIuYnl0ZU9mZnNldCs9byxyLmJ5dGVMZW5ndGgtPW8pLGUuX3F1ZXVlVG90YWxTaXplLT1vLHZlKGUsbyx0KSxsLT1vfXJldHVybiBzfWZ1bmN0aW9uIHZlKGUsdCxyKXtyLmJ5dGVzRmlsbGVkKz10fWZ1bmN0aW9uIFJlKGUpezA9PT1lLl9xdWV1ZVRvdGFsU2l6ZSYmZS5fY2xvc2VSZXF1ZXN0ZWQ/KEVlKGUpLEp0KGUuX2NvbnRyb2xsZWRSZWFkYWJsZUJ5dGVTdHJlYW0pKTpoZShlKX1mdW5jdGlvbiBUZShlKXtudWxsIT09ZS5fYnlvYlJlcXVlc3QmJihlLl9ieW9iUmVxdWVzdC5fYXNzb2NpYXRlZFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXI9dm9pZCAwLGUuX2J5b2JSZXF1ZXN0Ll92aWV3PW51bGwsZS5fYnlvYlJlcXVlc3Q9bnVsbCl9ZnVuY3Rpb24gcWUoZSl7Zm9yKDtlLl9wZW5kaW5nUHVsbEludG9zLmxlbmd0aD4wOyl7aWYoMD09PWUuX3F1ZXVlVG90YWxTaXplKXJldHVybjtjb25zdCB0PWUuX3BlbmRpbmdQdWxsSW50b3MucGVlaygpO3dlKGUsdCkmJihQZShlKSxwZShlLl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtLHQpKX19ZnVuY3Rpb24gQ2UoZSx0KXtjb25zdCByPWUuX3BlbmRpbmdQdWxsSW50b3MucGVlaygpO1RlKGUpO1wiY2xvc2VkXCI9PT1lLl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtLl9zdGF0ZT9mdW5jdGlvbihlLHQpe1wibm9uZVwiPT09dC5yZWFkZXJUeXBlJiZQZShlKTtjb25zdCByPWUuX2NvbnRyb2xsZWRSZWFkYWJsZUJ5dGVTdHJlYW07aWYoRmUocikpZm9yKDtMZShyKT4wOylwZShyLFBlKGUpKX0oZSxyKTpmdW5jdGlvbihlLHQscil7aWYodmUoMCx0LHIpLFwibm9uZVwiPT09ci5yZWFkZXJUeXBlKXJldHVybiBTZShlLHIpLHZvaWQgcWUoZSk7aWYoci5ieXRlc0ZpbGxlZDxyLmVsZW1lbnRTaXplKXJldHVybjtQZShlKTtjb25zdCBvPXIuYnl0ZXNGaWxsZWQlci5lbGVtZW50U2l6ZTtpZihvPjApe2NvbnN0IHQ9ci5ieXRlT2Zmc2V0K3IuYnl0ZXNGaWxsZWQ7Z2UoZSxyLmJ1ZmZlcix0LW8sbyl9ci5ieXRlc0ZpbGxlZC09byxwZShlLl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtLHIpLHFlKGUpfShlLHQsciksaGUoZSl9ZnVuY3Rpb24gUGUoZSl7cmV0dXJuIGUuX3BlbmRpbmdQdWxsSW50b3Muc2hpZnQoKX1mdW5jdGlvbiBFZShlKXtlLl9wdWxsQWxnb3JpdGhtPXZvaWQgMCxlLl9jYW5jZWxBbGdvcml0aG09dm9pZCAwfWZ1bmN0aW9uIFdlKGUsdCl7Y29uc3Qgcj1lLl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtO1wicmVhZGFibGVcIj09PXIuX3N0YXRlJiYoX2UoZSksZGUoZSksRWUoZSksS3Qocix0KSl9ZnVuY3Rpb24gT2UoZSx0KXtjb25zdCByPWUuX3F1ZXVlLnNoaWZ0KCk7ZS5fcXVldWVUb3RhbFNpemUtPXIuYnl0ZUxlbmd0aCxSZShlKTtjb25zdCBvPW5ldyBVaW50OEFycmF5KHIuYnVmZmVyLHIuYnl0ZU9mZnNldCxyLmJ5dGVMZW5ndGgpO3QuX2NodW5rU3RlcHMobyl9ZnVuY3Rpb24ga2UoZSl7Y29uc3QgdD1lLl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtLl9zdGF0ZTtyZXR1cm5cImVycm9yZWRcIj09PXQ/bnVsbDpcImNsb3NlZFwiPT09dD8wOmUuX3N0cmF0ZWd5SFdNLWUuX3F1ZXVlVG90YWxTaXplfWZ1bmN0aW9uIEJlKGUsdCxyKXtjb25zdCBvPU9iamVjdC5jcmVhdGUoUmVhZGFibGVCeXRlU3RyZWFtQ29udHJvbGxlci5wcm90b3R5cGUpO2xldCBuLGEsaTtuPXZvaWQgMCE9PXQuc3RhcnQ/KCk9PnQuc3RhcnQobyk6KCk9Pnt9LGE9dm9pZCAwIT09dC5wdWxsPygpPT50LnB1bGwobyk6KCk9PmQodm9pZCAwKSxpPXZvaWQgMCE9PXQuY2FuY2VsP2U9PnQuY2FuY2VsKGUpOigpPT5kKHZvaWQgMCk7Y29uc3QgbD10LmF1dG9BbGxvY2F0ZUNodW5rU2l6ZTtpZigwPT09bCl0aHJvdyBuZXcgVHlwZUVycm9yKFwiYXV0b0FsbG9jYXRlQ2h1bmtTaXplIG11c3QgYmUgZ3JlYXRlciB0aGFuIDBcIik7IWZ1bmN0aW9uKGUsdCxyLG8sbixhLGkpe3QuX2NvbnRyb2xsZWRSZWFkYWJsZUJ5dGVTdHJlYW09ZSx0Ll9wdWxsQWdhaW49ITEsdC5fcHVsbGluZz0hMSx0Ll9ieW9iUmVxdWVzdD1udWxsLHQuX3F1ZXVlPXQuX3F1ZXVlVG90YWxTaXplPXZvaWQgMCxkZSh0KSx0Ll9jbG9zZVJlcXVlc3RlZD0hMSx0Ll9zdGFydGVkPSExLHQuX3N0cmF0ZWd5SFdNPWEsdC5fcHVsbEFsZ29yaXRobT1vLHQuX2NhbmNlbEFsZ29yaXRobT1uLHQuX2F1dG9BbGxvY2F0ZUNodW5rU2l6ZT1pLHQuX3BlbmRpbmdQdWxsSW50b3M9bmV3IHYsZS5fcmVhZGFibGVTdHJlYW1Db250cm9sbGVyPXQsaChkKHIoKSksKCgpPT4odC5fc3RhcnRlZD0hMCxoZSh0KSxudWxsKSksKGU9PihXZSh0LGUpLG51bGwpKSl9KGUsbyxuLGEsaSxyLGwpfWZ1bmN0aW9uIEFlKGUpe3JldHVybiBuZXcgVHlwZUVycm9yKGBSZWFkYWJsZVN0cmVhbUJZT0JSZXF1ZXN0LnByb3RvdHlwZS4ke2V9IGNhbiBvbmx5IGJlIHVzZWQgb24gYSBSZWFkYWJsZVN0cmVhbUJZT0JSZXF1ZXN0YCl9ZnVuY3Rpb24gamUoZSl7cmV0dXJuIG5ldyBUeXBlRXJyb3IoYFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXIucHJvdG90eXBlLiR7ZX0gY2FuIG9ubHkgYmUgdXNlZCBvbiBhIFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXJgKX1mdW5jdGlvbiB6ZShlLHQpe2UuX3JlYWRlci5fcmVhZEludG9SZXF1ZXN0cy5wdXNoKHQpfWZ1bmN0aW9uIExlKGUpe3JldHVybiBlLl9yZWFkZXIuX3JlYWRJbnRvUmVxdWVzdHMubGVuZ3RofWZ1bmN0aW9uIEZlKGUpe2NvbnN0IHQ9ZS5fcmVhZGVyO3JldHVybiB2b2lkIDAhPT10JiYhIURlKHQpfU9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXIucHJvdG90eXBlLHtjbG9zZTp7ZW51bWVyYWJsZTohMH0sZW5xdWV1ZTp7ZW51bWVyYWJsZTohMH0sZXJyb3I6e2VudW1lcmFibGU6ITB9LGJ5b2JSZXF1ZXN0OntlbnVtZXJhYmxlOiEwfSxkZXNpcmVkU2l6ZTp7ZW51bWVyYWJsZTohMH19KSxhKFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXIucHJvdG90eXBlLmNsb3NlLFwiY2xvc2VcIiksYShSZWFkYWJsZUJ5dGVTdHJlYW1Db250cm9sbGVyLnByb3RvdHlwZS5lbnF1ZXVlLFwiZW5xdWV1ZVwiKSxhKFJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXIucHJvdG90eXBlLmVycm9yLFwiZXJyb3JcIiksXCJzeW1ib2xcIj09dHlwZW9mIHQudG9TdHJpbmdUYWcmJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShSZWFkYWJsZUJ5dGVTdHJlYW1Db250cm9sbGVyLnByb3RvdHlwZSx0LnRvU3RyaW5nVGFnLHt2YWx1ZTpcIlJlYWRhYmxlQnl0ZVN0cmVhbUNvbnRyb2xsZXJcIixjb25maWd1cmFibGU6ITB9KTtjbGFzcyBSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXJ7Y29uc3RydWN0b3IoZSl7aWYoTShlLDEsXCJSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXJcIiksVShlLFwiRmlyc3QgcGFyYW1ldGVyXCIpLEd0KGUpKXRocm93IG5ldyBUeXBlRXJyb3IoXCJUaGlzIHN0cmVhbSBoYXMgYWxyZWFkeSBiZWVuIGxvY2tlZCBmb3IgZXhjbHVzaXZlIHJlYWRpbmcgYnkgYW5vdGhlciByZWFkZXJcIik7aWYoIWZlKGUuX3JlYWRhYmxlU3RyZWFtQ29udHJvbGxlcikpdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNhbm5vdCBjb25zdHJ1Y3QgYSBSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXIgZm9yIGEgc3RyZWFtIG5vdCBjb25zdHJ1Y3RlZCB3aXRoIGEgYnl0ZSBzb3VyY2VcIik7RSh0aGlzLGUpLHRoaXMuX3JlYWRJbnRvUmVxdWVzdHM9bmV3IHZ9Z2V0IGNsb3NlZCgpe3JldHVybiBEZSh0aGlzKT90aGlzLl9jbG9zZWRQcm9taXNlOmYoJGUoXCJjbG9zZWRcIikpfWNhbmNlbChlKXtyZXR1cm4gRGUodGhpcyk/dm9pZCAwPT09dGhpcy5fb3duZXJSZWFkYWJsZVN0cmVhbT9mKGsoXCJjYW5jZWxcIikpOlcodGhpcyxlKTpmKCRlKFwiY2FuY2VsXCIpKX1yZWFkKGUpe2lmKCFEZSh0aGlzKSlyZXR1cm4gZigkZShcInJlYWRcIikpO2lmKCFBcnJheUJ1ZmZlci5pc1ZpZXcoZSkpcmV0dXJuIGYobmV3IFR5cGVFcnJvcihcInZpZXcgbXVzdCBiZSBhbiBhcnJheSBidWZmZXIgdmlld1wiKSk7aWYoMD09PWUuYnl0ZUxlbmd0aClyZXR1cm4gZihuZXcgVHlwZUVycm9yKFwidmlldyBtdXN0IGhhdmUgbm9uLXplcm8gYnl0ZUxlbmd0aFwiKSk7aWYoMD09PWUuYnVmZmVyLmJ5dGVMZW5ndGgpcmV0dXJuIGYobmV3IFR5cGVFcnJvcihcInZpZXcncyBidWZmZXIgbXVzdCBoYXZlIG5vbi16ZXJvIGJ5dGVMZW5ndGhcIikpO2lmKGUuYnVmZmVyLHZvaWQgMD09PXRoaXMuX293bmVyUmVhZGFibGVTdHJlYW0pcmV0dXJuIGYoayhcInJlYWQgZnJvbVwiKSk7bGV0IHQscjtjb25zdCBvPWMoKChlLG8pPT57dD1lLHI9b30pKTtyZXR1cm4gZnVuY3Rpb24oZSx0LHIpe2NvbnN0IG89ZS5fb3duZXJSZWFkYWJsZVN0cmVhbTtvLl9kaXN0dXJiZWQ9ITAsXCJlcnJvcmVkXCI9PT1vLl9zdGF0ZT9yLl9lcnJvclN0ZXBzKG8uX3N0b3JlZEVycm9yKTpmdW5jdGlvbihlLHQscil7Y29uc3Qgbz1lLl9jb250cm9sbGVkUmVhZGFibGVCeXRlU3RyZWFtO2xldCBuPTE7dC5jb25zdHJ1Y3RvciE9PURhdGFWaWV3JiYobj10LmNvbnN0cnVjdG9yLkJZVEVTX1BFUl9FTEVNRU5UKTtjb25zdCBhPXQuY29uc3RydWN0b3IsaT10LmJ1ZmZlcixsPXtidWZmZXI6aSxidWZmZXJCeXRlTGVuZ3RoOmkuYnl0ZUxlbmd0aCxieXRlT2Zmc2V0OnQuYnl0ZU9mZnNldCxieXRlTGVuZ3RoOnQuYnl0ZUxlbmd0aCxieXRlc0ZpbGxlZDowLGVsZW1lbnRTaXplOm4sdmlld0NvbnN0cnVjdG9yOmEscmVhZGVyVHlwZTpcImJ5b2JcIn07aWYoZS5fcGVuZGluZ1B1bGxJbnRvcy5sZW5ndGg+MClyZXR1cm4gZS5fcGVuZGluZ1B1bGxJbnRvcy5wdXNoKGwpLHZvaWQgemUobyxyKTtpZihcImNsb3NlZFwiIT09by5fc3RhdGUpe2lmKGUuX3F1ZXVlVG90YWxTaXplPjApe2lmKHdlKGUsbCkpe2NvbnN0IHQ9bWUobCk7cmV0dXJuIFJlKGUpLHZvaWQgci5fY2h1bmtTdGVwcyh0KX1pZihlLl9jbG9zZVJlcXVlc3RlZCl7Y29uc3QgdD1uZXcgVHlwZUVycm9yKFwiSW5zdWZmaWNpZW50IGJ5dGVzIHRvIGZpbGwgZWxlbWVudHMgaW4gdGhlIGdpdmVuIGJ1ZmZlclwiKTtyZXR1cm4gV2UoZSx0KSx2b2lkIHIuX2Vycm9yU3RlcHModCl9fWUuX3BlbmRpbmdQdWxsSW50b3MucHVzaChsKSx6ZShvLHIpLGhlKGUpfWVsc2V7Y29uc3QgZT1uZXcgYShsLmJ1ZmZlcixsLmJ5dGVPZmZzZXQsMCk7ci5fY2xvc2VTdGVwcyhlKX19KG8uX3JlYWRhYmxlU3RyZWFtQ29udHJvbGxlcix0LHIpfSh0aGlzLGUse19jaHVua1N0ZXBzOmU9PnQoe3ZhbHVlOmUsZG9uZTohMX0pLF9jbG9zZVN0ZXBzOmU9PnQoe3ZhbHVlOmUsZG9uZTohMH0pLF9lcnJvclN0ZXBzOmU9PnIoZSl9KSxvfXJlbGVhc2VMb2NrKCl7aWYoIURlKHRoaXMpKXRocm93ICRlKFwicmVsZWFzZUxvY2tcIik7dm9pZCAwIT09dGhpcy5fb3duZXJSZWFkYWJsZVN0cmVhbSYmZnVuY3Rpb24oZSl7TyhlKTtjb25zdCB0PW5ldyBUeXBlRXJyb3IoXCJSZWFkZXIgd2FzIHJlbGVhc2VkXCIpO0llKGUsdCl9KHRoaXMpfX1mdW5jdGlvbiBEZShlKXtyZXR1cm4hIW8oZSkmJighIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiX3JlYWRJbnRvUmVxdWVzdHNcIikmJmUgaW5zdGFuY2VvZiBSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXIpfWZ1bmN0aW9uIEllKGUsdCl7Y29uc3Qgcj1lLl9yZWFkSW50b1JlcXVlc3RzO2UuX3JlYWRJbnRvUmVxdWVzdHM9bmV3IHYsci5mb3JFYWNoKChlPT57ZS5fZXJyb3JTdGVwcyh0KX0pKX1mdW5jdGlvbiAkZShlKXtyZXR1cm4gbmV3IFR5cGVFcnJvcihgUmVhZGFibGVTdHJlYW1CWU9CUmVhZGVyLnByb3RvdHlwZS4ke2V9IGNhbiBvbmx5IGJlIHVzZWQgb24gYSBSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXJgKX1mdW5jdGlvbiBNZShlLHQpe2NvbnN0e2hpZ2hXYXRlck1hcms6cn09ZTtpZih2b2lkIDA9PT1yKXJldHVybiB0O2lmKGllKHIpfHxyPDApdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJJbnZhbGlkIGhpZ2hXYXRlck1hcmtcIik7cmV0dXJuIHJ9ZnVuY3Rpb24gWWUoZSl7Y29uc3R7c2l6ZTp0fT1lO3JldHVybiB0fHwoKCk9PjEpfWZ1bmN0aW9uIFFlKGUsdCl7RChlLHQpO2NvbnN0IHI9bnVsbD09ZT92b2lkIDA6ZS5oaWdoV2F0ZXJNYXJrLG89bnVsbD09ZT92b2lkIDA6ZS5zaXplO3JldHVybntoaWdoV2F0ZXJNYXJrOnZvaWQgMD09PXI/dm9pZCAwOlEociksc2l6ZTp2b2lkIDA9PT1vP3ZvaWQgMDpOZShvLGAke3R9IGhhcyBtZW1iZXIgJ3NpemUnIHRoYXRgKX19ZnVuY3Rpb24gTmUoZSx0KXtyZXR1cm4gSShlLHQpLHQ9PlEoZSh0KSl9ZnVuY3Rpb24geGUoZSx0LHIpe3JldHVybiBJKGUscikscj0+dyhlLHQsW3JdKX1mdW5jdGlvbiBIZShlLHQscil7cmV0dXJuIEkoZSxyKSwoKT0+dyhlLHQsW10pfWZ1bmN0aW9uIFZlKGUsdCxyKXtyZXR1cm4gSShlLHIpLHI9PlMoZSx0LFtyXSl9ZnVuY3Rpb24gVWUoZSx0LHIpe3JldHVybiBJKGUsciksKHIsbyk9PncoZSx0LFtyLG9dKX1PYmplY3QuZGVmaW5lUHJvcGVydGllcyhSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXIucHJvdG90eXBlLHtjYW5jZWw6e2VudW1lcmFibGU6ITB9LHJlYWQ6e2VudW1lcmFibGU6ITB9LHJlbGVhc2VMb2NrOntlbnVtZXJhYmxlOiEwfSxjbG9zZWQ6e2VudW1lcmFibGU6ITB9fSksYShSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXIucHJvdG90eXBlLmNhbmNlbCxcImNhbmNlbFwiKSxhKFJlYWRhYmxlU3RyZWFtQllPQlJlYWRlci5wcm90b3R5cGUucmVhZCxcInJlYWRcIiksYShSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXIucHJvdG90eXBlLnJlbGVhc2VMb2NrLFwicmVsZWFzZUxvY2tcIiksXCJzeW1ib2xcIj09dHlwZW9mIHQudG9TdHJpbmdUYWcmJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShSZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXIucHJvdG90eXBlLHQudG9TdHJpbmdUYWcse3ZhbHVlOlwiUmVhZGFibGVTdHJlYW1CWU9CUmVhZGVyXCIsY29uZmlndXJhYmxlOiEwfSk7Y29uc3QgR2U9XCJmdW5jdGlvblwiPT10eXBlb2YgQWJvcnRDb250cm9sbGVyO2NsYXNzIFdyaXRhYmxlU3RyZWFte2NvbnN0cnVjdG9yKGU9e30sdD17fSl7dm9pZCAwPT09ZT9lPW51bGw6JChlLFwiRmlyc3QgcGFyYW1ldGVyXCIpO2NvbnN0IHI9UWUodCxcIlNlY29uZCBwYXJhbWV0ZXJcIiksbz1mdW5jdGlvbihlLHQpe0QoZSx0KTtjb25zdCByPW51bGw9PWU/dm9pZCAwOmUuYWJvcnQsbz1udWxsPT1lP3ZvaWQgMDplLmNsb3NlLG49bnVsbD09ZT92b2lkIDA6ZS5zdGFydCxhPW51bGw9PWU/dm9pZCAwOmUudHlwZSxpPW51bGw9PWU/dm9pZCAwOmUud3JpdGU7cmV0dXJue2Fib3J0OnZvaWQgMD09PXI/dm9pZCAwOnhlKHIsZSxgJHt0fSBoYXMgbWVtYmVyICdhYm9ydCcgdGhhdGApLGNsb3NlOnZvaWQgMD09PW8/dm9pZCAwOkhlKG8sZSxgJHt0fSBoYXMgbWVtYmVyICdjbG9zZScgdGhhdGApLHN0YXJ0OnZvaWQgMD09PW4/dm9pZCAwOlZlKG4sZSxgJHt0fSBoYXMgbWVtYmVyICdzdGFydCcgdGhhdGApLHdyaXRlOnZvaWQgMD09PWk/dm9pZCAwOlVlKGksZSxgJHt0fSBoYXMgbWVtYmVyICd3cml0ZScgdGhhdGApLHR5cGU6YX19KGUsXCJGaXJzdCBwYXJhbWV0ZXJcIik7dmFyIG47KG49dGhpcykuX3N0YXRlPVwid3JpdGFibGVcIixuLl9zdG9yZWRFcnJvcj12b2lkIDAsbi5fd3JpdGVyPXZvaWQgMCxuLl93cml0YWJsZVN0cmVhbUNvbnRyb2xsZXI9dm9pZCAwLG4uX3dyaXRlUmVxdWVzdHM9bmV3IHYsbi5faW5GbGlnaHRXcml0ZVJlcXVlc3Q9dm9pZCAwLG4uX2Nsb3NlUmVxdWVzdD12b2lkIDAsbi5faW5GbGlnaHRDbG9zZVJlcXVlc3Q9dm9pZCAwLG4uX3BlbmRpbmdBYm9ydFJlcXVlc3Q9dm9pZCAwLG4uX2JhY2twcmVzc3VyZT0hMTtpZih2b2lkIDAhPT1vLnR5cGUpdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJJbnZhbGlkIHR5cGUgaXMgc3BlY2lmaWVkXCIpO2NvbnN0IGE9WWUocik7IWZ1bmN0aW9uKGUsdCxyLG8pe2NvbnN0IG49T2JqZWN0LmNyZWF0ZShXcml0YWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyLnByb3RvdHlwZSk7bGV0IGEsaSxsLHM7YT12b2lkIDAhPT10LnN0YXJ0PygpPT50LnN0YXJ0KG4pOigpPT57fTtpPXZvaWQgMCE9PXQud3JpdGU/ZT0+dC53cml0ZShlLG4pOigpPT5kKHZvaWQgMCk7bD12b2lkIDAhPT10LmNsb3NlPygpPT50LmNsb3NlKCk6KCk9PmQodm9pZCAwKTtzPXZvaWQgMCE9PXQuYWJvcnQ/ZT0+dC5hYm9ydChlKTooKT0+ZCh2b2lkIDApOyFmdW5jdGlvbihlLHQscixvLG4sYSxpLGwpe3QuX2NvbnRyb2xsZWRXcml0YWJsZVN0cmVhbT1lLGUuX3dyaXRhYmxlU3RyZWFtQ29udHJvbGxlcj10LHQuX3F1ZXVlPXZvaWQgMCx0Ll9xdWV1ZVRvdGFsU2l6ZT12b2lkIDAsZGUodCksdC5fYWJvcnRSZWFzb249dm9pZCAwLHQuX2Fib3J0Q29udHJvbGxlcj1mdW5jdGlvbigpe2lmKEdlKXJldHVybiBuZXcgQWJvcnRDb250cm9sbGVyfSgpLHQuX3N0YXJ0ZWQ9ITEsdC5fc3RyYXRlZ3lTaXplQWxnb3JpdGhtPWwsdC5fc3RyYXRlZ3lIV009aSx0Ll93cml0ZUFsZ29yaXRobT1vLHQuX2Nsb3NlQWxnb3JpdGhtPW4sdC5fYWJvcnRBbGdvcml0aG09YTtjb25zdCBzPWh0KHQpO2F0KGUscyk7Y29uc3QgdT1yKCk7aChkKHUpLCgoKT0+KHQuX3N0YXJ0ZWQ9ITAsZnQodCksbnVsbCkpLChyPT4odC5fc3RhcnRlZD0hMCxldChlLHIpLG51bGwpKSl9KGUsbixhLGksbCxzLHIsbyl9KHRoaXMsbyxNZShyLDEpLGEpfWdldCBsb2NrZWQoKXtpZighWGUodGhpcykpdGhyb3cgcHQoXCJsb2NrZWRcIik7cmV0dXJuIEplKHRoaXMpfWFib3J0KGUpe3JldHVybiBYZSh0aGlzKT9KZSh0aGlzKT9mKG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgYWJvcnQgYSBzdHJlYW0gdGhhdCBhbHJlYWR5IGhhcyBhIHdyaXRlclwiKSk6S2UodGhpcyxlKTpmKHB0KFwiYWJvcnRcIikpfWNsb3NlKCl7cmV0dXJuIFhlKHRoaXMpP0plKHRoaXMpP2YobmV3IFR5cGVFcnJvcihcIkNhbm5vdCBjbG9zZSBhIHN0cmVhbSB0aGF0IGFscmVhZHkgaGFzIGEgd3JpdGVyXCIpKTpvdCh0aGlzKT9mKG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgY2xvc2UgYW4gYWxyZWFkeS1jbG9zaW5nIHN0cmVhbVwiKSk6WmUodGhpcyk6ZihwdChcImNsb3NlXCIpKX1nZXRXcml0ZXIoKXtpZighWGUodGhpcykpdGhyb3cgcHQoXCJnZXRXcml0ZXJcIik7cmV0dXJuIG5ldyBXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIodGhpcyl9fWZ1bmN0aW9uIFhlKGUpe3JldHVybiEhbyhlKSYmKCEhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGUsXCJfd3JpdGFibGVTdHJlYW1Db250cm9sbGVyXCIpJiZlIGluc3RhbmNlb2YgV3JpdGFibGVTdHJlYW0pfWZ1bmN0aW9uIEplKGUpe3JldHVybiB2b2lkIDAhPT1lLl93cml0ZXJ9ZnVuY3Rpb24gS2UoZSx0KXt2YXIgcjtpZihcImNsb3NlZFwiPT09ZS5fc3RhdGV8fFwiZXJyb3JlZFwiPT09ZS5fc3RhdGUpcmV0dXJuIGQodm9pZCAwKTtlLl93cml0YWJsZVN0cmVhbUNvbnRyb2xsZXIuX2Fib3J0UmVhc29uPXQsbnVsbD09PShyPWUuX3dyaXRhYmxlU3RyZWFtQ29udHJvbGxlci5fYWJvcnRDb250cm9sbGVyKXx8dm9pZCAwPT09cnx8ci5hYm9ydCh0KTtjb25zdCBvPWUuX3N0YXRlO2lmKFwiY2xvc2VkXCI9PT1vfHxcImVycm9yZWRcIj09PW8pcmV0dXJuIGQodm9pZCAwKTtpZih2b2lkIDAhPT1lLl9wZW5kaW5nQWJvcnRSZXF1ZXN0KXJldHVybiBlLl9wZW5kaW5nQWJvcnRSZXF1ZXN0Ll9wcm9taXNlO2xldCBuPSExO1wiZXJyb3JpbmdcIj09PW8mJihuPSEwLHQ9dm9pZCAwKTtjb25zdCBhPWMoKChyLG8pPT57ZS5fcGVuZGluZ0Fib3J0UmVxdWVzdD17X3Byb21pc2U6dm9pZCAwLF9yZXNvbHZlOnIsX3JlamVjdDpvLF9yZWFzb246dCxfd2FzQWxyZWFkeUVycm9yaW5nOm59fSkpO3JldHVybiBlLl9wZW5kaW5nQWJvcnRSZXF1ZXN0Ll9wcm9taXNlPWEsbnx8dHQoZSx0KSxhfWZ1bmN0aW9uIFplKGUpe2NvbnN0IHQ9ZS5fc3RhdGU7aWYoXCJjbG9zZWRcIj09PXR8fFwiZXJyb3JlZFwiPT09dClyZXR1cm4gZihuZXcgVHlwZUVycm9yKGBUaGUgc3RyZWFtIChpbiAke3R9IHN0YXRlKSBpcyBub3QgaW4gdGhlIHdyaXRhYmxlIHN0YXRlIGFuZCBjYW5ub3QgYmUgY2xvc2VkYCkpO2NvbnN0IHI9YygoKHQscik9Pntjb25zdCBvPXtfcmVzb2x2ZTp0LF9yZWplY3Q6cn07ZS5fY2xvc2VSZXF1ZXN0PW99KSksbz1lLl93cml0ZXI7dmFyIG47cmV0dXJuIHZvaWQgMCE9PW8mJmUuX2JhY2twcmVzc3VyZSYmXCJ3cml0YWJsZVwiPT09dCYmRXQobyksY2Uobj1lLl93cml0YWJsZVN0cmVhbUNvbnRyb2xsZXIsc3QsMCksZnQobikscn1mdW5jdGlvbiBldChlLHQpe1wid3JpdGFibGVcIiE9PWUuX3N0YXRlP3J0KGUpOnR0KGUsdCl9ZnVuY3Rpb24gdHQoZSx0KXtjb25zdCByPWUuX3dyaXRhYmxlU3RyZWFtQ29udHJvbGxlcjtlLl9zdGF0ZT1cImVycm9yaW5nXCIsZS5fc3RvcmVkRXJyb3I9dDtjb25zdCBvPWUuX3dyaXRlcjt2b2lkIDAhPT1vJiZsdChvLHQpLCFmdW5jdGlvbihlKXtpZih2b2lkIDA9PT1lLl9pbkZsaWdodFdyaXRlUmVxdWVzdCYmdm9pZCAwPT09ZS5faW5GbGlnaHRDbG9zZVJlcXVlc3QpcmV0dXJuITE7cmV0dXJuITB9KGUpJiZyLl9zdGFydGVkJiZydChlKX1mdW5jdGlvbiBydChlKXtlLl9zdGF0ZT1cImVycm9yZWRcIixlLl93cml0YWJsZVN0cmVhbUNvbnRyb2xsZXJbVF0oKTtjb25zdCB0PWUuX3N0b3JlZEVycm9yO2lmKGUuX3dyaXRlUmVxdWVzdHMuZm9yRWFjaCgoZT0+e2UuX3JlamVjdCh0KX0pKSxlLl93cml0ZVJlcXVlc3RzPW5ldyB2LHZvaWQgMD09PWUuX3BlbmRpbmdBYm9ydFJlcXVlc3QpcmV0dXJuIHZvaWQgbnQoZSk7Y29uc3Qgcj1lLl9wZW5kaW5nQWJvcnRSZXF1ZXN0O2lmKGUuX3BlbmRpbmdBYm9ydFJlcXVlc3Q9dm9pZCAwLHIuX3dhc0FscmVhZHlFcnJvcmluZylyZXR1cm4gci5fcmVqZWN0KHQpLHZvaWQgbnQoZSk7aChlLl93cml0YWJsZVN0cmVhbUNvbnRyb2xsZXJbUl0oci5fcmVhc29uKSwoKCk9PihyLl9yZXNvbHZlKCksbnQoZSksbnVsbCkpLCh0PT4oci5fcmVqZWN0KHQpLG50KGUpLG51bGwpKSl9ZnVuY3Rpb24gb3QoZSl7cmV0dXJuIHZvaWQgMCE9PWUuX2Nsb3NlUmVxdWVzdHx8dm9pZCAwIT09ZS5faW5GbGlnaHRDbG9zZVJlcXVlc3R9ZnVuY3Rpb24gbnQoZSl7dm9pZCAwIT09ZS5fY2xvc2VSZXF1ZXN0JiYoZS5fY2xvc2VSZXF1ZXN0Ll9yZWplY3QoZS5fc3RvcmVkRXJyb3IpLGUuX2Nsb3NlUmVxdWVzdD12b2lkIDApO2NvbnN0IHQ9ZS5fd3JpdGVyO3ZvaWQgMCE9PXQmJnZ0KHQsZS5fc3RvcmVkRXJyb3IpfWZ1bmN0aW9uIGF0KGUsdCl7Y29uc3Qgcj1lLl93cml0ZXI7dm9pZCAwIT09ciYmdCE9PWUuX2JhY2twcmVzc3VyZSYmKHQ/ZnVuY3Rpb24oZSl7VHQoZSl9KHIpOkV0KHIpKSxlLl9iYWNrcHJlc3N1cmU9dH1PYmplY3QuZGVmaW5lUHJvcGVydGllcyhXcml0YWJsZVN0cmVhbS5wcm90b3R5cGUse2Fib3J0OntlbnVtZXJhYmxlOiEwfSxjbG9zZTp7ZW51bWVyYWJsZTohMH0sZ2V0V3JpdGVyOntlbnVtZXJhYmxlOiEwfSxsb2NrZWQ6e2VudW1lcmFibGU6ITB9fSksYShXcml0YWJsZVN0cmVhbS5wcm90b3R5cGUuYWJvcnQsXCJhYm9ydFwiKSxhKFdyaXRhYmxlU3RyZWFtLnByb3RvdHlwZS5jbG9zZSxcImNsb3NlXCIpLGEoV3JpdGFibGVTdHJlYW0ucHJvdG90eXBlLmdldFdyaXRlcixcImdldFdyaXRlclwiKSxcInN5bWJvbFwiPT10eXBlb2YgdC50b1N0cmluZ1RhZyYmT2JqZWN0LmRlZmluZVByb3BlcnR5KFdyaXRhYmxlU3RyZWFtLnByb3RvdHlwZSx0LnRvU3RyaW5nVGFnLHt2YWx1ZTpcIldyaXRhYmxlU3RyZWFtXCIsY29uZmlndXJhYmxlOiEwfSk7Y2xhc3MgV3JpdGFibGVTdHJlYW1EZWZhdWx0V3JpdGVye2NvbnN0cnVjdG9yKGUpe2lmKE0oZSwxLFwiV3JpdGFibGVTdHJlYW1EZWZhdWx0V3JpdGVyXCIpLGZ1bmN0aW9uKGUsdCl7aWYoIVhlKGUpKXRocm93IG5ldyBUeXBlRXJyb3IoYCR7dH0gaXMgbm90IGEgV3JpdGFibGVTdHJlYW0uYCl9KGUsXCJGaXJzdCBwYXJhbWV0ZXJcIiksSmUoZSkpdGhyb3cgbmV3IFR5cGVFcnJvcihcIlRoaXMgc3RyZWFtIGhhcyBhbHJlYWR5IGJlZW4gbG9ja2VkIGZvciBleGNsdXNpdmUgd3JpdGluZyBieSBhbm90aGVyIHdyaXRlclwiKTt0aGlzLl9vd25lcldyaXRhYmxlU3RyZWFtPWUsZS5fd3JpdGVyPXRoaXM7Y29uc3QgdD1lLl9zdGF0ZTtpZihcIndyaXRhYmxlXCI9PT10KSFvdChlKSYmZS5fYmFja3ByZXNzdXJlP1R0KHRoaXMpOkN0KHRoaXMpLFN0KHRoaXMpO2Vsc2UgaWYoXCJlcnJvcmluZ1wiPT09dClxdCh0aGlzLGUuX3N0b3JlZEVycm9yKSxTdCh0aGlzKTtlbHNlIGlmKFwiY2xvc2VkXCI9PT10KUN0KHRoaXMpLFN0KHI9dGhpcyksUnQocik7ZWxzZXtjb25zdCB0PWUuX3N0b3JlZEVycm9yO3F0KHRoaXMsdCksd3QodGhpcyx0KX12YXIgcn1nZXQgY2xvc2VkKCl7cmV0dXJuIGl0KHRoaXMpP3RoaXMuX2Nsb3NlZFByb21pc2U6Zih5dChcImNsb3NlZFwiKSl9Z2V0IGRlc2lyZWRTaXplKCl7aWYoIWl0KHRoaXMpKXRocm93IHl0KFwiZGVzaXJlZFNpemVcIik7aWYodm9pZCAwPT09dGhpcy5fb3duZXJXcml0YWJsZVN0cmVhbSl0aHJvdyBndChcImRlc2lyZWRTaXplXCIpO3JldHVybiBmdW5jdGlvbihlKXtjb25zdCB0PWUuX293bmVyV3JpdGFibGVTdHJlYW0scj10Ll9zdGF0ZTtpZihcImVycm9yZWRcIj09PXJ8fFwiZXJyb3JpbmdcIj09PXIpcmV0dXJuIG51bGw7aWYoXCJjbG9zZWRcIj09PXIpcmV0dXJuIDA7cmV0dXJuIGR0KHQuX3dyaXRhYmxlU3RyZWFtQ29udHJvbGxlcil9KHRoaXMpfWdldCByZWFkeSgpe3JldHVybiBpdCh0aGlzKT90aGlzLl9yZWFkeVByb21pc2U6Zih5dChcInJlYWR5XCIpKX1hYm9ydChlKXtyZXR1cm4gaXQodGhpcyk/dm9pZCAwPT09dGhpcy5fb3duZXJXcml0YWJsZVN0cmVhbT9mKGd0KFwiYWJvcnRcIikpOmZ1bmN0aW9uKGUsdCl7cmV0dXJuIEtlKGUuX293bmVyV3JpdGFibGVTdHJlYW0sdCl9KHRoaXMsZSk6Zih5dChcImFib3J0XCIpKX1jbG9zZSgpe2lmKCFpdCh0aGlzKSlyZXR1cm4gZih5dChcImNsb3NlXCIpKTtjb25zdCBlPXRoaXMuX293bmVyV3JpdGFibGVTdHJlYW07cmV0dXJuIHZvaWQgMD09PWU/ZihndChcImNsb3NlXCIpKTpvdChlKT9mKG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgY2xvc2UgYW4gYWxyZWFkeS1jbG9zaW5nIHN0cmVhbVwiKSk6WmUodGhpcy5fb3duZXJXcml0YWJsZVN0cmVhbSl9cmVsZWFzZUxvY2soKXtpZighaXQodGhpcykpdGhyb3cgeXQoXCJyZWxlYXNlTG9ja1wiKTt2b2lkIDAhPT10aGlzLl9vd25lcldyaXRhYmxlU3RyZWFtJiZmdW5jdGlvbihlKXtjb25zdCB0PWUuX293bmVyV3JpdGFibGVTdHJlYW0scj1uZXcgVHlwZUVycm9yKFwiV3JpdGVyIHdhcyByZWxlYXNlZCBhbmQgY2FuIG5vIGxvbmdlciBiZSB1c2VkIHRvIG1vbml0b3IgdGhlIHN0cmVhbSdzIGNsb3NlZG5lc3NcIik7bHQoZSxyKSxmdW5jdGlvbihlLHQpe1wicGVuZGluZ1wiPT09ZS5fY2xvc2VkUHJvbWlzZVN0YXRlP3Z0KGUsdCk6ZnVuY3Rpb24oZSx0KXt3dChlLHQpfShlLHQpfShlLHIpLHQuX3dyaXRlcj12b2lkIDAsZS5fb3duZXJXcml0YWJsZVN0cmVhbT12b2lkIDB9KHRoaXMpfXdyaXRlKGUpe3JldHVybiBpdCh0aGlzKT92b2lkIDA9PT10aGlzLl9vd25lcldyaXRhYmxlU3RyZWFtP2YoZ3QoXCJ3cml0ZSB0b1wiKSk6ZnVuY3Rpb24oZSx0KXtjb25zdCByPWUuX293bmVyV3JpdGFibGVTdHJlYW0sbz1yLl93cml0YWJsZVN0cmVhbUNvbnRyb2xsZXIsbj1mdW5jdGlvbihlLHQpe3RyeXtyZXR1cm4gZS5fc3RyYXRlZ3lTaXplQWxnb3JpdGhtKHQpfWNhdGNoKHQpe3JldHVybiBidChlLHQpLDF9fShvLHQpO2lmKHIhPT1lLl9vd25lcldyaXRhYmxlU3RyZWFtKXJldHVybiBmKGd0KFwid3JpdGUgdG9cIikpO2NvbnN0IGE9ci5fc3RhdGU7aWYoXCJlcnJvcmVkXCI9PT1hKXJldHVybiBmKHIuX3N0b3JlZEVycm9yKTtpZihvdChyKXx8XCJjbG9zZWRcIj09PWEpcmV0dXJuIGYobmV3IFR5cGVFcnJvcihcIlRoZSBzdHJlYW0gaXMgY2xvc2luZyBvciBjbG9zZWQgYW5kIGNhbm5vdCBiZSB3cml0dGVuIHRvXCIpKTtpZihcImVycm9yaW5nXCI9PT1hKXJldHVybiBmKHIuX3N0b3JlZEVycm9yKTtjb25zdCBpPWZ1bmN0aW9uKGUpe3JldHVybiBjKCgodCxyKT0+e2NvbnN0IG89e19yZXNvbHZlOnQsX3JlamVjdDpyfTtlLl93cml0ZVJlcXVlc3RzLnB1c2gobyl9KSl9KHIpO3JldHVybiBmdW5jdGlvbihlLHQscil7dHJ5e2NlKGUsdCxyKX1jYXRjaCh0KXtyZXR1cm4gdm9pZCBidChlLHQpfWNvbnN0IG89ZS5fY29udHJvbGxlZFdyaXRhYmxlU3RyZWFtO2lmKCFvdChvKSYmXCJ3cml0YWJsZVwiPT09by5fc3RhdGUpe2F0KG8saHQoZSkpfWZ0KGUpfShvLHQsbiksaX0odGhpcyxlKTpmKHl0KFwid3JpdGVcIikpfX1mdW5jdGlvbiBpdChlKXtyZXR1cm4hIW8oZSkmJighIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiX293bmVyV3JpdGFibGVTdHJlYW1cIikmJmUgaW5zdGFuY2VvZiBXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIpfWZ1bmN0aW9uIGx0KGUsdCl7XCJwZW5kaW5nXCI9PT1lLl9yZWFkeVByb21pc2VTdGF0ZT9QdChlLHQpOmZ1bmN0aW9uKGUsdCl7cXQoZSx0KX0oZSx0KX1PYmplY3QuZGVmaW5lUHJvcGVydGllcyhXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIucHJvdG90eXBlLHthYm9ydDp7ZW51bWVyYWJsZTohMH0sY2xvc2U6e2VudW1lcmFibGU6ITB9LHJlbGVhc2VMb2NrOntlbnVtZXJhYmxlOiEwfSx3cml0ZTp7ZW51bWVyYWJsZTohMH0sY2xvc2VkOntlbnVtZXJhYmxlOiEwfSxkZXNpcmVkU2l6ZTp7ZW51bWVyYWJsZTohMH0scmVhZHk6e2VudW1lcmFibGU6ITB9fSksYShXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIucHJvdG90eXBlLmFib3J0LFwiYWJvcnRcIiksYShXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIucHJvdG90eXBlLmNsb3NlLFwiY2xvc2VcIiksYShXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIucHJvdG90eXBlLnJlbGVhc2VMb2NrLFwicmVsZWFzZUxvY2tcIiksYShXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIucHJvdG90eXBlLndyaXRlLFwid3JpdGVcIiksXCJzeW1ib2xcIj09dHlwZW9mIHQudG9TdHJpbmdUYWcmJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIucHJvdG90eXBlLHQudG9TdHJpbmdUYWcse3ZhbHVlOlwiV3JpdGFibGVTdHJlYW1EZWZhdWx0V3JpdGVyXCIsY29uZmlndXJhYmxlOiEwfSk7Y29uc3Qgc3Q9e307Y2xhc3MgV3JpdGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlcntjb25zdHJ1Y3Rvcigpe3Rocm93IG5ldyBUeXBlRXJyb3IoXCJJbGxlZ2FsIGNvbnN0cnVjdG9yXCIpfWdldCBhYm9ydFJlYXNvbigpe2lmKCF1dCh0aGlzKSl0aHJvdyBtdChcImFib3J0UmVhc29uXCIpO3JldHVybiB0aGlzLl9hYm9ydFJlYXNvbn1nZXQgc2lnbmFsKCl7aWYoIXV0KHRoaXMpKXRocm93IG10KFwic2lnbmFsXCIpO2lmKHZvaWQgMD09PXRoaXMuX2Fib3J0Q29udHJvbGxlcil0aHJvdyBuZXcgVHlwZUVycm9yKFwiV3JpdGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlci5wcm90b3R5cGUuc2lnbmFsIGlzIG5vdCBzdXBwb3J0ZWRcIik7cmV0dXJuIHRoaXMuX2Fib3J0Q29udHJvbGxlci5zaWduYWx9ZXJyb3IoZSl7aWYoIXV0KHRoaXMpKXRocm93IG10KFwiZXJyb3JcIik7XCJ3cml0YWJsZVwiPT09dGhpcy5fY29udHJvbGxlZFdyaXRhYmxlU3RyZWFtLl9zdGF0ZSYmX3QodGhpcyxlKX1bUl0oZSl7Y29uc3QgdD10aGlzLl9hYm9ydEFsZ29yaXRobShlKTtyZXR1cm4gY3QodGhpcyksdH1bVF0oKXtkZSh0aGlzKX19ZnVuY3Rpb24gdXQoZSl7cmV0dXJuISFvKGUpJiYoISFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZSxcIl9jb250cm9sbGVkV3JpdGFibGVTdHJlYW1cIikmJmUgaW5zdGFuY2VvZiBXcml0YWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyKX1mdW5jdGlvbiBjdChlKXtlLl93cml0ZUFsZ29yaXRobT12b2lkIDAsZS5fY2xvc2VBbGdvcml0aG09dm9pZCAwLGUuX2Fib3J0QWxnb3JpdGhtPXZvaWQgMCxlLl9zdHJhdGVneVNpemVBbGdvcml0aG09dm9pZCAwfWZ1bmN0aW9uIGR0KGUpe3JldHVybiBlLl9zdHJhdGVneUhXTS1lLl9xdWV1ZVRvdGFsU2l6ZX1mdW5jdGlvbiBmdChlKXtjb25zdCB0PWUuX2NvbnRyb2xsZWRXcml0YWJsZVN0cmVhbTtpZighZS5fc3RhcnRlZClyZXR1cm47aWYodm9pZCAwIT09dC5faW5GbGlnaHRXcml0ZVJlcXVlc3QpcmV0dXJuO2lmKFwiZXJyb3JpbmdcIj09PXQuX3N0YXRlKXJldHVybiB2b2lkIHJ0KHQpO2lmKDA9PT1lLl9xdWV1ZS5sZW5ndGgpcmV0dXJuO2NvbnN0IHI9ZS5fcXVldWUucGVlaygpLnZhbHVlO3I9PT1zdD9mdW5jdGlvbihlKXtjb25zdCB0PWUuX2NvbnRyb2xsZWRXcml0YWJsZVN0cmVhbTsoZnVuY3Rpb24oZSl7ZS5faW5GbGlnaHRDbG9zZVJlcXVlc3Q9ZS5fY2xvc2VSZXF1ZXN0LGUuX2Nsb3NlUmVxdWVzdD12b2lkIDB9KSh0KSx1ZShlKTtjb25zdCByPWUuX2Nsb3NlQWxnb3JpdGhtKCk7Y3QoZSksaChyLCgoKT0+KGZ1bmN0aW9uKGUpe2UuX2luRmxpZ2h0Q2xvc2VSZXF1ZXN0Ll9yZXNvbHZlKHZvaWQgMCksZS5faW5GbGlnaHRDbG9zZVJlcXVlc3Q9dm9pZCAwLFwiZXJyb3JpbmdcIj09PWUuX3N0YXRlJiYoZS5fc3RvcmVkRXJyb3I9dm9pZCAwLHZvaWQgMCE9PWUuX3BlbmRpbmdBYm9ydFJlcXVlc3QmJihlLl9wZW5kaW5nQWJvcnRSZXF1ZXN0Ll9yZXNvbHZlKCksZS5fcGVuZGluZ0Fib3J0UmVxdWVzdD12b2lkIDApKSxlLl9zdGF0ZT1cImNsb3NlZFwiO2NvbnN0IHQ9ZS5fd3JpdGVyO3ZvaWQgMCE9PXQmJlJ0KHQpfSh0KSxudWxsKSksKGU9PihmdW5jdGlvbihlLHQpe2UuX2luRmxpZ2h0Q2xvc2VSZXF1ZXN0Ll9yZWplY3QodCksZS5faW5GbGlnaHRDbG9zZVJlcXVlc3Q9dm9pZCAwLHZvaWQgMCE9PWUuX3BlbmRpbmdBYm9ydFJlcXVlc3QmJihlLl9wZW5kaW5nQWJvcnRSZXF1ZXN0Ll9yZWplY3QodCksZS5fcGVuZGluZ0Fib3J0UmVxdWVzdD12b2lkIDApLGV0KGUsdCl9KHQsZSksbnVsbCkpKX0oZSk6ZnVuY3Rpb24oZSx0KXtjb25zdCByPWUuX2NvbnRyb2xsZWRXcml0YWJsZVN0cmVhbTshZnVuY3Rpb24oZSl7ZS5faW5GbGlnaHRXcml0ZVJlcXVlc3Q9ZS5fd3JpdGVSZXF1ZXN0cy5zaGlmdCgpfShyKTtoKGUuX3dyaXRlQWxnb3JpdGhtKHQpLCgoKT0+eyFmdW5jdGlvbihlKXtlLl9pbkZsaWdodFdyaXRlUmVxdWVzdC5fcmVzb2x2ZSh2b2lkIDApLGUuX2luRmxpZ2h0V3JpdGVSZXF1ZXN0PXZvaWQgMH0ocik7Y29uc3QgdD1yLl9zdGF0ZTtpZih1ZShlKSwhb3QocikmJlwid3JpdGFibGVcIj09PXQpe2NvbnN0IHQ9aHQoZSk7YXQocix0KX1yZXR1cm4gZnQoZSksbnVsbH0pLCh0PT4oXCJ3cml0YWJsZVwiPT09ci5fc3RhdGUmJmN0KGUpLGZ1bmN0aW9uKGUsdCl7ZS5faW5GbGlnaHRXcml0ZVJlcXVlc3QuX3JlamVjdCh0KSxlLl9pbkZsaWdodFdyaXRlUmVxdWVzdD12b2lkIDAsZXQoZSx0KX0ocix0KSxudWxsKSkpfShlLHIpfWZ1bmN0aW9uIGJ0KGUsdCl7XCJ3cml0YWJsZVwiPT09ZS5fY29udHJvbGxlZFdyaXRhYmxlU3RyZWFtLl9zdGF0ZSYmX3QoZSx0KX1mdW5jdGlvbiBodChlKXtyZXR1cm4gZHQoZSk8PTB9ZnVuY3Rpb24gX3QoZSx0KXtjb25zdCByPWUuX2NvbnRyb2xsZWRXcml0YWJsZVN0cmVhbTtjdChlKSx0dChyLHQpfWZ1bmN0aW9uIHB0KGUpe3JldHVybiBuZXcgVHlwZUVycm9yKGBXcml0YWJsZVN0cmVhbS5wcm90b3R5cGUuJHtlfSBjYW4gb25seSBiZSB1c2VkIG9uIGEgV3JpdGFibGVTdHJlYW1gKX1mdW5jdGlvbiBtdChlKXtyZXR1cm4gbmV3IFR5cGVFcnJvcihgV3JpdGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlci5wcm90b3R5cGUuJHtlfSBjYW4gb25seSBiZSB1c2VkIG9uIGEgV3JpdGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlcmApfWZ1bmN0aW9uIHl0KGUpe3JldHVybiBuZXcgVHlwZUVycm9yKGBXcml0YWJsZVN0cmVhbURlZmF1bHRXcml0ZXIucHJvdG90eXBlLiR7ZX0gY2FuIG9ubHkgYmUgdXNlZCBvbiBhIFdyaXRhYmxlU3RyZWFtRGVmYXVsdFdyaXRlcmApfWZ1bmN0aW9uIGd0KGUpe3JldHVybiBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IFwiK2UrXCIgYSBzdHJlYW0gdXNpbmcgYSByZWxlYXNlZCB3cml0ZXJcIil9ZnVuY3Rpb24gU3QoZSl7ZS5fY2xvc2VkUHJvbWlzZT1jKCgodCxyKT0+e2UuX2Nsb3NlZFByb21pc2VfcmVzb2x2ZT10LGUuX2Nsb3NlZFByb21pc2VfcmVqZWN0PXIsZS5fY2xvc2VkUHJvbWlzZVN0YXRlPVwicGVuZGluZ1wifSkpfWZ1bmN0aW9uIHd0KGUsdCl7U3QoZSksdnQoZSx0KX1mdW5jdGlvbiB2dChlLHQpe3ZvaWQgMCE9PWUuX2Nsb3NlZFByb21pc2VfcmVqZWN0JiYoeShlLl9jbG9zZWRQcm9taXNlKSxlLl9jbG9zZWRQcm9taXNlX3JlamVjdCh0KSxlLl9jbG9zZWRQcm9taXNlX3Jlc29sdmU9dm9pZCAwLGUuX2Nsb3NlZFByb21pc2VfcmVqZWN0PXZvaWQgMCxlLl9jbG9zZWRQcm9taXNlU3RhdGU9XCJyZWplY3RlZFwiKX1mdW5jdGlvbiBSdChlKXt2b2lkIDAhPT1lLl9jbG9zZWRQcm9taXNlX3Jlc29sdmUmJihlLl9jbG9zZWRQcm9taXNlX3Jlc29sdmUodm9pZCAwKSxlLl9jbG9zZWRQcm9taXNlX3Jlc29sdmU9dm9pZCAwLGUuX2Nsb3NlZFByb21pc2VfcmVqZWN0PXZvaWQgMCxlLl9jbG9zZWRQcm9taXNlU3RhdGU9XCJyZXNvbHZlZFwiKX1mdW5jdGlvbiBUdChlKXtlLl9yZWFkeVByb21pc2U9YygoKHQscik9PntlLl9yZWFkeVByb21pc2VfcmVzb2x2ZT10LGUuX3JlYWR5UHJvbWlzZV9yZWplY3Q9cn0pKSxlLl9yZWFkeVByb21pc2VTdGF0ZT1cInBlbmRpbmdcIn1mdW5jdGlvbiBxdChlLHQpe1R0KGUpLFB0KGUsdCl9ZnVuY3Rpb24gQ3QoZSl7VHQoZSksRXQoZSl9ZnVuY3Rpb24gUHQoZSx0KXt2b2lkIDAhPT1lLl9yZWFkeVByb21pc2VfcmVqZWN0JiYoeShlLl9yZWFkeVByb21pc2UpLGUuX3JlYWR5UHJvbWlzZV9yZWplY3QodCksZS5fcmVhZHlQcm9taXNlX3Jlc29sdmU9dm9pZCAwLGUuX3JlYWR5UHJvbWlzZV9yZWplY3Q9dm9pZCAwLGUuX3JlYWR5UHJvbWlzZVN0YXRlPVwicmVqZWN0ZWRcIil9ZnVuY3Rpb24gRXQoZSl7dm9pZCAwIT09ZS5fcmVhZHlQcm9taXNlX3Jlc29sdmUmJihlLl9yZWFkeVByb21pc2VfcmVzb2x2ZSh2b2lkIDApLGUuX3JlYWR5UHJvbWlzZV9yZXNvbHZlPXZvaWQgMCxlLl9yZWFkeVByb21pc2VfcmVqZWN0PXZvaWQgMCxlLl9yZWFkeVByb21pc2VTdGF0ZT1cImZ1bGZpbGxlZFwiKX1PYmplY3QuZGVmaW5lUHJvcGVydGllcyhXcml0YWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyLnByb3RvdHlwZSx7YWJvcnRSZWFzb246e2VudW1lcmFibGU6ITB9LHNpZ25hbDp7ZW51bWVyYWJsZTohMH0sZXJyb3I6e2VudW1lcmFibGU6ITB9fSksXCJzeW1ib2xcIj09dHlwZW9mIHQudG9TdHJpbmdUYWcmJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShXcml0YWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyLnByb3RvdHlwZSx0LnRvU3RyaW5nVGFnLHt2YWx1ZTpcIldyaXRhYmxlU3RyZWFtRGVmYXVsdENvbnRyb2xsZXJcIixjb25maWd1cmFibGU6ITB9KTtjb25zdCBXdD1cInVuZGVmaW5lZFwiIT10eXBlb2YgRE9NRXhjZXB0aW9uP0RPTUV4Y2VwdGlvbjp2b2lkIDA7Y29uc3QgT3Q9ZnVuY3Rpb24oZSl7aWYoXCJmdW5jdGlvblwiIT10eXBlb2YgZSYmXCJvYmplY3RcIiE9dHlwZW9mIGUpcmV0dXJuITE7dHJ5e3JldHVybiBuZXcgZSwhMH1jYXRjaChlKXtyZXR1cm4hMX19KFd0KT9XdDpmdW5jdGlvbigpe2NvbnN0IGU9ZnVuY3Rpb24oZSx0KXt0aGlzLm1lc3NhZ2U9ZXx8XCJcIix0aGlzLm5hbWU9dHx8XCJFcnJvclwiLEVycm9yLmNhcHR1cmVTdGFja1RyYWNlJiZFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSh0aGlzLHRoaXMuY29uc3RydWN0b3IpfTtyZXR1cm4gZS5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZShFcnJvci5wcm90b3R5cGUpLE9iamVjdC5kZWZpbmVQcm9wZXJ0eShlLnByb3RvdHlwZSxcImNvbnN0cnVjdG9yXCIse3ZhbHVlOmUsd3JpdGFibGU6ITAsY29uZmlndXJhYmxlOiEwfSksZX0oKTtmdW5jdGlvbiBrdChlLHQscixvLG4sYSl7Y29uc3QgaT1lLmdldFJlYWRlcigpLGw9dC5nZXRXcml0ZXIoKTtVdChlKSYmKGUuX2Rpc3R1cmJlZD0hMCk7bGV0IHMsdSxwLFM9ITEsdz0hMSx2PVwicmVhZGFibGVcIixSPVwid3JpdGFibGVcIixUPSExLHE9ITE7Y29uc3QgQz1jKChlPT57cD1lfSkpO2xldCBQPVByb21pc2UucmVzb2x2ZSh2b2lkIDApO3JldHVybiBjKCgoRSxXKT0+e2xldCBPO2Z1bmN0aW9uIGsoKXtpZihTKXJldHVybjtjb25zdCBlPWMoKChlLHQpPT57IWZ1bmN0aW9uIHIobyl7bz9lKCk6YihmdW5jdGlvbigpe2lmKFMpcmV0dXJuIGQoITApO3JldHVybiBiKGwucmVhZHksKCgpPT5iKGkucmVhZCgpLChlPT4hIWUuZG9uZXx8KFA9bC53cml0ZShlLnZhbHVlKSx5KFApLCExKSkpKSl9KCkscix0KX0oITEpfSkpO3koZSl9ZnVuY3Rpb24gQigpe3JldHVybiB2PVwiY2xvc2VkXCIscj9MKCk6eigoKCk9PihYZSh0KSYmKFQ9b3QodCksUj10Ll9zdGF0ZSksVHx8XCJjbG9zZWRcIj09PVI/ZCh2b2lkIDApOlwiZXJyb3JpbmdcIj09PVJ8fFwiZXJyb3JlZFwiPT09Uj9mKHUpOihUPSEwLGwuY2xvc2UoKSkpKSwhMSx2b2lkIDApLG51bGx9ZnVuY3Rpb24gQShlKXtyZXR1cm4gU3x8KHY9XCJlcnJvcmVkXCIscz1lLG8/TCghMCxlKTp6KCgoKT0+bC5hYm9ydChlKSksITAsZSkpLG51bGx9ZnVuY3Rpb24gaihlKXtyZXR1cm4gd3x8KFI9XCJlcnJvcmVkXCIsdT1lLG4/TCghMCxlKTp6KCgoKT0+aS5jYW5jZWwoZSkpLCEwLGUpKSxudWxsfWlmKHZvaWQgMCE9PWEmJihPPSgpPT57Y29uc3QgZT12b2lkIDAhPT1hLnJlYXNvbj9hLnJlYXNvbjpuZXcgT3QoXCJBYm9ydGVkXCIsXCJBYm9ydEVycm9yXCIpLHQ9W107b3x8dC5wdXNoKCgoKT0+XCJ3cml0YWJsZVwiPT09Uj9sLmFib3J0KGUpOmQodm9pZCAwKSkpLG58fHQucHVzaCgoKCk9PlwicmVhZGFibGVcIj09PXY/aS5jYW5jZWwoZSk6ZCh2b2lkIDApKSkseigoKCk9PlByb21pc2UuYWxsKHQubWFwKChlPT5lKCkpKSkpLCEwLGUpfSxhLmFib3J0ZWQ/TygpOmEuYWRkRXZlbnRMaXN0ZW5lcihcImFib3J0XCIsTykpLFV0KGUpJiYodj1lLl9zdGF0ZSxzPWUuX3N0b3JlZEVycm9yKSxYZSh0KSYmKFI9dC5fc3RhdGUsdT10Ll9zdG9yZWRFcnJvcixUPW90KHQpKSxVdChlKSYmWGUodCkmJihxPSEwLHAoKSksXCJlcnJvcmVkXCI9PT12KUEocyk7ZWxzZSBpZihcImVycm9yaW5nXCI9PT1SfHxcImVycm9yZWRcIj09PVIpaih1KTtlbHNlIGlmKFwiY2xvc2VkXCI9PT12KUIoKTtlbHNlIGlmKFR8fFwiY2xvc2VkXCI9PT1SKXtjb25zdCBlPW5ldyBUeXBlRXJyb3IoXCJ0aGUgZGVzdGluYXRpb24gd3JpdGFibGUgc3RyZWFtIGNsb3NlZCBiZWZvcmUgYWxsIGRhdGEgY291bGQgYmUgcGlwZWQgdG8gaXRcIik7bj9MKCEwLGUpOnooKCgpPT5pLmNhbmNlbChlKSksITAsZSl9ZnVuY3Rpb24geihlLHQscil7ZnVuY3Rpb24gbygpe3JldHVyblwid3JpdGFibGVcIiE9PVJ8fFQ/bigpOl8oZnVuY3Rpb24oKXtsZXQgZTtyZXR1cm4gZChmdW5jdGlvbiB0KCl7aWYoZSE9PVApcmV0dXJuIGU9UCxtKFAsdCx0KX0oKSl9KCksbiksbnVsbH1mdW5jdGlvbiBuKCl7cmV0dXJuIGU/aChlKCksKCgpPT5GKHQscikpLChlPT5GKCEwLGUpKSk6Rih0LHIpLG51bGx9U3x8KFM9ITAscT9vKCk6XyhDLG8pKX1mdW5jdGlvbiBMKGUsdCl7eih2b2lkIDAsZSx0KX1mdW5jdGlvbiBGKGUsdCl7cmV0dXJuIHc9ITAsbC5yZWxlYXNlTG9jaygpLGkucmVsZWFzZUxvY2soKSx2b2lkIDAhPT1hJiZhLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJhYm9ydFwiLE8pLGU/Vyh0KTpFKHZvaWQgMCksbnVsbH1TfHwoaChpLmNsb3NlZCxCLEEpLGgobC5jbG9zZWQsKGZ1bmN0aW9uKCl7cmV0dXJuIHd8fChSPVwiY2xvc2VkXCIpLG51bGx9KSxqKSkscT9rKCk6ZygoKCk9PntxPSEwLHAoKSxrKCl9KSl9KSl9ZnVuY3Rpb24gQnQoZSx0KXtyZXR1cm4gZnVuY3Rpb24oZSl7dHJ5e3JldHVybiBlLmdldFJlYWRlcih7bW9kZTpcImJ5b2JcIn0pLnJlbGVhc2VMb2NrKCksITB9Y2F0Y2goZSl7cmV0dXJuITF9fShlKT9mdW5jdGlvbihlKXtsZXQgdCxyLG8sbixhLGk9ZS5nZXRSZWFkZXIoKSxsPSExLHM9ITEsdT0hMSxmPSExLGI9ITEsXz0hMTtjb25zdCBtPWMoKGU9PnthPWV9KSk7ZnVuY3Rpb24geShlKXtwKGUuY2xvc2VkLCh0PT4oZSE9PWl8fChvLmVycm9yKHQpLG4uZXJyb3IodCksYiYmX3x8YSh2b2lkIDApKSxudWxsKSkpfWZ1bmN0aW9uIGcoKXtsJiYoaS5yZWxlYXNlTG9jaygpLGk9ZS5nZXRSZWFkZXIoKSx5KGkpLGw9ITEpLGgoaS5yZWFkKCksKGU9Pnt2YXIgdCxyO2lmKHU9ITEsZj0hMSxlLmRvbmUpcmV0dXJuIGJ8fG8uY2xvc2UoKSxffHxuLmNsb3NlKCksbnVsbD09PSh0PW8uYnlvYlJlcXVlc3QpfHx2b2lkIDA9PT10fHx0LnJlc3BvbmQoMCksbnVsbD09PShyPW4uYnlvYlJlcXVlc3QpfHx2b2lkIDA9PT1yfHxyLnJlc3BvbmQoMCksYiYmX3x8YSh2b2lkIDApLG51bGw7Y29uc3QgbD1lLnZhbHVlLGM9bDtsZXQgZD1sO2lmKCFiJiYhXyl0cnl7ZD1zZShsKX1jYXRjaChlKXtyZXR1cm4gby5lcnJvcihlKSxuLmVycm9yKGUpLGEoaS5jYW5jZWwoZSkpLG51bGx9cmV0dXJuIGJ8fG8uZW5xdWV1ZShjKSxffHxuLmVucXVldWUoZCkscz0hMSx1P3coKTpmJiZ2KCksbnVsbH0pLCgoKT0+KHM9ITEsbnVsbCkpKX1mdW5jdGlvbiBTKHQscil7bHx8KGkucmVsZWFzZUxvY2soKSxpPWUuZ2V0UmVhZGVyKHttb2RlOlwiYnlvYlwifSkseShpKSxsPSEwKTtjb25zdCBjPXI/bjpvLGQ9cj9vOm47aChpLnJlYWQodCksKGU9Pnt2YXIgdDt1PSExLGY9ITE7Y29uc3Qgbz1yP186YixuPXI/YjpfO2lmKGUuZG9uZSl7b3x8Yy5jbG9zZSgpLG58fGQuY2xvc2UoKTtjb25zdCByPWUudmFsdWU7cmV0dXJuIHZvaWQgMCE9PXImJihvfHxjLmJ5b2JSZXF1ZXN0LnJlc3BvbmRXaXRoTmV3VmlldyhyKSxufHxudWxsPT09KHQ9ZC5ieW9iUmVxdWVzdCl8fHZvaWQgMD09PXR8fHQucmVzcG9uZCgwKSksbyYmbnx8YSh2b2lkIDApLG51bGx9Y29uc3QgbD1lLnZhbHVlO2lmKG4pb3x8Yy5ieW9iUmVxdWVzdC5yZXNwb25kV2l0aE5ld1ZpZXcobCk7ZWxzZXtsZXQgZTt0cnl7ZT1zZShsKX1jYXRjaChlKXtyZXR1cm4gYy5lcnJvcihlKSxkLmVycm9yKGUpLGEoaS5jYW5jZWwoZSkpLG51bGx9b3x8Yy5ieW9iUmVxdWVzdC5yZXNwb25kV2l0aE5ld1ZpZXcobCksZC5lbnF1ZXVlKGUpfXJldHVybiBzPSExLHU/dygpOmYmJnYoKSxudWxsfSksKCgpPT4ocz0hMSxudWxsKSkpfWZ1bmN0aW9uIHcoKXtpZihzKXJldHVybiB1PSEwLGQodm9pZCAwKTtzPSEwO2NvbnN0IGU9by5ieW9iUmVxdWVzdDtyZXR1cm4gbnVsbD09PWU/ZygpOlMoZS52aWV3LCExKSxkKHZvaWQgMCl9ZnVuY3Rpb24gdigpe2lmKHMpcmV0dXJuIGY9ITAsZCh2b2lkIDApO3M9ITA7Y29uc3QgZT1uLmJ5b2JSZXF1ZXN0O3JldHVybiBudWxsPT09ZT9nKCk6UyhlLnZpZXcsITApLGQodm9pZCAwKX1mdW5jdGlvbiBSKGUpe2lmKGI9ITAsdD1lLF8pe2NvbnN0IGU9W3Qscl0sbz1pLmNhbmNlbChlKTthKG8pfXJldHVybiBtfWZ1bmN0aW9uIFQoZSl7aWYoXz0hMCxyPWUsYil7Y29uc3QgZT1bdCxyXSxvPWkuY2FuY2VsKGUpO2Eobyl9cmV0dXJuIG19Y29uc3QgcT1uZXcgUmVhZGFibGVTdHJlYW0oe3R5cGU6XCJieXRlc1wiLHN0YXJ0KGUpe289ZX0scHVsbDp3LGNhbmNlbDpSfSksQz1uZXcgUmVhZGFibGVTdHJlYW0oe3R5cGU6XCJieXRlc1wiLHN0YXJ0KGUpe249ZX0scHVsbDp2LGNhbmNlbDpUfSk7cmV0dXJuIHkoaSksW3EsQ119KGUpOmZ1bmN0aW9uKGUsdCl7Y29uc3Qgcj1lLmdldFJlYWRlcigpO2xldCBvLG4sYSxpLGwscz0hMSx1PSExLGY9ITEsYj0hMTtjb25zdCBfPWMoKGU9PntsPWV9KSk7ZnVuY3Rpb24gbSgpe3JldHVybiBzPyh1PSEwLGQodm9pZCAwKSk6KHM9ITAsaChyLnJlYWQoKSwoZT0+e2lmKHU9ITEsZS5kb25lKXJldHVybiBmfHxhLmNsb3NlKCksYnx8aS5jbG9zZSgpLGYmJmJ8fGwodm9pZCAwKSxudWxsO2NvbnN0IHQ9ZS52YWx1ZSxyPXQsbz10O3JldHVybiBmfHxhLmVucXVldWUociksYnx8aS5lbnF1ZXVlKG8pLHM9ITEsdSYmbSgpLG51bGx9KSwoKCk9PihzPSExLG51bGwpKSksZCh2b2lkIDApKX1mdW5jdGlvbiB5KGUpe2lmKGY9ITAsbz1lLGIpe2NvbnN0IGU9W28sbl0sdD1yLmNhbmNlbChlKTtsKHQpfXJldHVybiBffWZ1bmN0aW9uIGcoZSl7aWYoYj0hMCxuPWUsZil7Y29uc3QgZT1bbyxuXSx0PXIuY2FuY2VsKGUpO2wodCl9cmV0dXJuIF99Y29uc3QgUz1uZXcgUmVhZGFibGVTdHJlYW0oe3N0YXJ0KGUpe2E9ZX0scHVsbDptLGNhbmNlbDp5fSksdz1uZXcgUmVhZGFibGVTdHJlYW0oe3N0YXJ0KGUpe2k9ZX0scHVsbDptLGNhbmNlbDpnfSk7cmV0dXJuIHAoci5jbG9zZWQsKGU9PihhLmVycm9yKGUpLGkuZXJyb3IoZSksZiYmYnx8bCh2b2lkIDApLG51bGwpKSksW1Msd119KGUpfWNsYXNzIFJlYWRhYmxlU3RyZWFtRGVmYXVsdENvbnRyb2xsZXJ7Y29uc3RydWN0b3IoKXt0aHJvdyBuZXcgVHlwZUVycm9yKFwiSWxsZWdhbCBjb25zdHJ1Y3RvclwiKX1nZXQgZGVzaXJlZFNpemUoKXtpZighQXQodGhpcykpdGhyb3cgJHQoXCJkZXNpcmVkU2l6ZVwiKTtyZXR1cm4gRnQodGhpcyl9Y2xvc2UoKXtpZighQXQodGhpcykpdGhyb3cgJHQoXCJjbG9zZVwiKTtpZighRHQodGhpcykpdGhyb3cgbmV3IFR5cGVFcnJvcihcIlRoZSBzdHJlYW0gaXMgbm90IGluIGEgc3RhdGUgdGhhdCBwZXJtaXRzIGNsb3NlXCIpOyFmdW5jdGlvbihlKXtpZighRHQoZSkpcmV0dXJuO2NvbnN0IHQ9ZS5fY29udHJvbGxlZFJlYWRhYmxlU3RyZWFtO2UuX2Nsb3NlUmVxdWVzdGVkPSEwLDA9PT1lLl9xdWV1ZS5sZW5ndGgmJih6dChlKSxKdCh0KSl9KHRoaXMpfWVucXVldWUoZSl7aWYoIUF0KHRoaXMpKXRocm93ICR0KFwiZW5xdWV1ZVwiKTtpZighRHQodGhpcykpdGhyb3cgbmV3IFR5cGVFcnJvcihcIlRoZSBzdHJlYW0gaXMgbm90IGluIGEgc3RhdGUgdGhhdCBwZXJtaXRzIGVucXVldWVcIik7cmV0dXJuIGZ1bmN0aW9uKGUsdCl7aWYoIUR0KGUpKXJldHVybjtjb25zdCByPWUuX2NvbnRyb2xsZWRSZWFkYWJsZVN0cmVhbTtpZihHdChyKSYmSihyKT4wKVgocix0LCExKTtlbHNle2xldCByO3RyeXtyPWUuX3N0cmF0ZWd5U2l6ZUFsZ29yaXRobSh0KX1jYXRjaCh0KXt0aHJvdyBMdChlLHQpLHR9dHJ5e2NlKGUsdCxyKX1jYXRjaCh0KXt0aHJvdyBMdChlLHQpLHR9fWp0KGUpfSh0aGlzLGUpfWVycm9yKGUpe2lmKCFBdCh0aGlzKSl0aHJvdyAkdChcImVycm9yXCIpO0x0KHRoaXMsZSl9W3FdKGUpe2RlKHRoaXMpO2NvbnN0IHQ9dGhpcy5fY2FuY2VsQWxnb3JpdGhtKGUpO3JldHVybiB6dCh0aGlzKSx0fVtDXShlKXtjb25zdCB0PXRoaXMuX2NvbnRyb2xsZWRSZWFkYWJsZVN0cmVhbTtpZih0aGlzLl9xdWV1ZS5sZW5ndGg+MCl7Y29uc3Qgcj11ZSh0aGlzKTt0aGlzLl9jbG9zZVJlcXVlc3RlZCYmMD09PXRoaXMuX3F1ZXVlLmxlbmd0aD8oenQodGhpcyksSnQodCkpOmp0KHRoaXMpLGUuX2NodW5rU3RlcHMocil9ZWxzZSBHKHQsZSksanQodGhpcyl9W1BdKCl7fX1mdW5jdGlvbiBBdChlKXtyZXR1cm4hIW8oZSkmJighIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiX2NvbnRyb2xsZWRSZWFkYWJsZVN0cmVhbVwiKSYmZSBpbnN0YW5jZW9mIFJlYWRhYmxlU3RyZWFtRGVmYXVsdENvbnRyb2xsZXIpfWZ1bmN0aW9uIGp0KGUpe2NvbnN0IHQ9ZnVuY3Rpb24oZSl7Y29uc3QgdD1lLl9jb250cm9sbGVkUmVhZGFibGVTdHJlYW07aWYoIUR0KGUpKXJldHVybiExO2lmKCFlLl9zdGFydGVkKXJldHVybiExO2lmKEd0KHQpJiZKKHQpPjApcmV0dXJuITA7aWYoRnQoZSk+MClyZXR1cm4hMDtyZXR1cm4hMX0oZSk7aWYoIXQpcmV0dXJuO2lmKGUuX3B1bGxpbmcpcmV0dXJuIHZvaWQoZS5fcHVsbEFnYWluPSEwKTtlLl9wdWxsaW5nPSEwO2goZS5fcHVsbEFsZ29yaXRobSgpLCgoKT0+KGUuX3B1bGxpbmc9ITEsZS5fcHVsbEFnYWluJiYoZS5fcHVsbEFnYWluPSExLGp0KGUpKSxudWxsKSksKHQ9PihMdChlLHQpLG51bGwpKSl9ZnVuY3Rpb24genQoZSl7ZS5fcHVsbEFsZ29yaXRobT12b2lkIDAsZS5fY2FuY2VsQWxnb3JpdGhtPXZvaWQgMCxlLl9zdHJhdGVneVNpemVBbGdvcml0aG09dm9pZCAwfWZ1bmN0aW9uIEx0KGUsdCl7Y29uc3Qgcj1lLl9jb250cm9sbGVkUmVhZGFibGVTdHJlYW07XCJyZWFkYWJsZVwiPT09ci5fc3RhdGUmJihkZShlKSx6dChlKSxLdChyLHQpKX1mdW5jdGlvbiBGdChlKXtjb25zdCB0PWUuX2NvbnRyb2xsZWRSZWFkYWJsZVN0cmVhbS5fc3RhdGU7cmV0dXJuXCJlcnJvcmVkXCI9PT10P251bGw6XCJjbG9zZWRcIj09PXQ/MDplLl9zdHJhdGVneUhXTS1lLl9xdWV1ZVRvdGFsU2l6ZX1mdW5jdGlvbiBEdChlKXtyZXR1cm4hZS5fY2xvc2VSZXF1ZXN0ZWQmJlwicmVhZGFibGVcIj09PWUuX2NvbnRyb2xsZWRSZWFkYWJsZVN0cmVhbS5fc3RhdGV9ZnVuY3Rpb24gSXQoZSx0LHIsbyl7Y29uc3Qgbj1PYmplY3QuY3JlYXRlKFJlYWRhYmxlU3RyZWFtRGVmYXVsdENvbnRyb2xsZXIucHJvdG90eXBlKTtsZXQgYSxpLGw7YT12b2lkIDAhPT10LnN0YXJ0PygpPT50LnN0YXJ0KG4pOigpPT57fSxpPXZvaWQgMCE9PXQucHVsbD8oKT0+dC5wdWxsKG4pOigpPT5kKHZvaWQgMCksbD12b2lkIDAhPT10LmNhbmNlbD9lPT50LmNhbmNlbChlKTooKT0+ZCh2b2lkIDApLGZ1bmN0aW9uKGUsdCxyLG8sbixhLGkpe3QuX2NvbnRyb2xsZWRSZWFkYWJsZVN0cmVhbT1lLHQuX3F1ZXVlPXZvaWQgMCx0Ll9xdWV1ZVRvdGFsU2l6ZT12b2lkIDAsZGUodCksdC5fc3RhcnRlZD0hMSx0Ll9jbG9zZVJlcXVlc3RlZD0hMSx0Ll9wdWxsQWdhaW49ITEsdC5fcHVsbGluZz0hMSx0Ll9zdHJhdGVneVNpemVBbGdvcml0aG09aSx0Ll9zdHJhdGVneUhXTT1hLHQuX3B1bGxBbGdvcml0aG09byx0Ll9jYW5jZWxBbGdvcml0aG09bixlLl9yZWFkYWJsZVN0cmVhbUNvbnRyb2xsZXI9dCxoKGQocigpKSwoKCk9Pih0Ll9zdGFydGVkPSEwLGp0KHQpLG51bGwpKSwoZT0+KEx0KHQsZSksbnVsbCkpKX0oZSxuLGEsaSxsLHIsbyl9ZnVuY3Rpb24gJHQoZSl7cmV0dXJuIG5ldyBUeXBlRXJyb3IoYFJlYWRhYmxlU3RyZWFtRGVmYXVsdENvbnRyb2xsZXIucHJvdG90eXBlLiR7ZX0gY2FuIG9ubHkgYmUgdXNlZCBvbiBhIFJlYWRhYmxlU3RyZWFtRGVmYXVsdENvbnRyb2xsZXJgKX1mdW5jdGlvbiBNdChlLHQscil7cmV0dXJuIEkoZSxyKSxyPT53KGUsdCxbcl0pfWZ1bmN0aW9uIFl0KGUsdCxyKXtyZXR1cm4gSShlLHIpLHI9PncoZSx0LFtyXSl9ZnVuY3Rpb24gUXQoZSx0LHIpe3JldHVybiBJKGUscikscj0+UyhlLHQsW3JdKX1mdW5jdGlvbiBOdChlLHQpe2lmKFwiYnl0ZXNcIiE9PShlPWAke2V9YCkpdGhyb3cgbmV3IFR5cGVFcnJvcihgJHt0fSAnJHtlfScgaXMgbm90IGEgdmFsaWQgZW51bWVyYXRpb24gdmFsdWUgZm9yIFJlYWRhYmxlU3RyZWFtVHlwZWApO3JldHVybiBlfWZ1bmN0aW9uIHh0KGUsdCl7aWYoXCJieW9iXCIhPT0oZT1gJHtlfWApKXRocm93IG5ldyBUeXBlRXJyb3IoYCR7dH0gJyR7ZX0nIGlzIG5vdCBhIHZhbGlkIGVudW1lcmF0aW9uIHZhbHVlIGZvciBSZWFkYWJsZVN0cmVhbVJlYWRlck1vZGVgKTtyZXR1cm4gZX1mdW5jdGlvbiBIdChlLHQpe0QoZSx0KTtjb25zdCByPW51bGw9PWU/dm9pZCAwOmUucHJldmVudEFib3J0LG89bnVsbD09ZT92b2lkIDA6ZS5wcmV2ZW50Q2FuY2VsLG49bnVsbD09ZT92b2lkIDA6ZS5wcmV2ZW50Q2xvc2UsYT1udWxsPT1lP3ZvaWQgMDplLnNpZ25hbDtyZXR1cm4gdm9pZCAwIT09YSYmZnVuY3Rpb24oZSx0KXtpZighZnVuY3Rpb24oZSl7aWYoXCJvYmplY3RcIiE9dHlwZW9mIGV8fG51bGw9PT1lKXJldHVybiExO3RyeXtyZXR1cm5cImJvb2xlYW5cIj09dHlwZW9mIGUuYWJvcnRlZH1jYXRjaChlKXtyZXR1cm4hMX19KGUpKXRocm93IG5ldyBUeXBlRXJyb3IoYCR7dH0gaXMgbm90IGFuIEFib3J0U2lnbmFsLmApfShhLGAke3R9IGhhcyBtZW1iZXIgJ3NpZ25hbCcgdGhhdGApLHtwcmV2ZW50QWJvcnQ6Qm9vbGVhbihyKSxwcmV2ZW50Q2FuY2VsOkJvb2xlYW4obykscHJldmVudENsb3NlOkJvb2xlYW4obiksc2lnbmFsOmF9fWZ1bmN0aW9uIFZ0KGUsdCl7RChlLHQpO2NvbnN0IHI9bnVsbD09ZT92b2lkIDA6ZS5yZWFkYWJsZTtZKHIsXCJyZWFkYWJsZVwiLFwiUmVhZGFibGVXcml0YWJsZVBhaXJcIiksZnVuY3Rpb24oZSx0KXtpZighSChlKSl0aHJvdyBuZXcgVHlwZUVycm9yKGAke3R9IGlzIG5vdCBhIFJlYWRhYmxlU3RyZWFtLmApfShyLGAke3R9IGhhcyBtZW1iZXIgJ3JlYWRhYmxlJyB0aGF0YCk7Y29uc3Qgbz1udWxsPT1lP3ZvaWQgMDplLndyaXRhYmxlO3JldHVybiBZKG8sXCJ3cml0YWJsZVwiLFwiUmVhZGFibGVXcml0YWJsZVBhaXJcIiksZnVuY3Rpb24oZSx0KXtpZighVihlKSl0aHJvdyBuZXcgVHlwZUVycm9yKGAke3R9IGlzIG5vdCBhIFdyaXRhYmxlU3RyZWFtLmApfShvLGAke3R9IGhhcyBtZW1iZXIgJ3dyaXRhYmxlJyB0aGF0YCkse3JlYWRhYmxlOnIsd3JpdGFibGU6b319T2JqZWN0LmRlZmluZVByb3BlcnRpZXMoUmVhZGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlci5wcm90b3R5cGUse2Nsb3NlOntlbnVtZXJhYmxlOiEwfSxlbnF1ZXVlOntlbnVtZXJhYmxlOiEwfSxlcnJvcjp7ZW51bWVyYWJsZTohMH0sZGVzaXJlZFNpemU6e2VudW1lcmFibGU6ITB9fSksYShSZWFkYWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyLnByb3RvdHlwZS5jbG9zZSxcImNsb3NlXCIpLGEoUmVhZGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlci5wcm90b3R5cGUuZW5xdWV1ZSxcImVucXVldWVcIiksYShSZWFkYWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyLnByb3RvdHlwZS5lcnJvcixcImVycm9yXCIpLFwic3ltYm9sXCI9PXR5cGVvZiB0LnRvU3RyaW5nVGFnJiZPYmplY3QuZGVmaW5lUHJvcGVydHkoUmVhZGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlci5wcm90b3R5cGUsdC50b1N0cmluZ1RhZyx7dmFsdWU6XCJSZWFkYWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyXCIsY29uZmlndXJhYmxlOiEwfSk7Y2xhc3MgUmVhZGFibGVTdHJlYW17Y29uc3RydWN0b3IoZT17fSx0PXt9KXt2b2lkIDA9PT1lP2U9bnVsbDokKGUsXCJGaXJzdCBwYXJhbWV0ZXJcIik7Y29uc3Qgcj1RZSh0LFwiU2Vjb25kIHBhcmFtZXRlclwiKSxvPWZ1bmN0aW9uKGUsdCl7RChlLHQpO2NvbnN0IHI9ZSxvPW51bGw9PXI/dm9pZCAwOnIuYXV0b0FsbG9jYXRlQ2h1bmtTaXplLG49bnVsbD09cj92b2lkIDA6ci5jYW5jZWwsYT1udWxsPT1yP3ZvaWQgMDpyLnB1bGwsaT1udWxsPT1yP3ZvaWQgMDpyLnN0YXJ0LGw9bnVsbD09cj92b2lkIDA6ci50eXBlO3JldHVybnthdXRvQWxsb2NhdGVDaHVua1NpemU6dm9pZCAwPT09bz92b2lkIDA6eChvLGAke3R9IGhhcyBtZW1iZXIgJ2F1dG9BbGxvY2F0ZUNodW5rU2l6ZScgdGhhdGApLGNhbmNlbDp2b2lkIDA9PT1uP3ZvaWQgMDpNdChuLHIsYCR7dH0gaGFzIG1lbWJlciAnY2FuY2VsJyB0aGF0YCkscHVsbDp2b2lkIDA9PT1hP3ZvaWQgMDpZdChhLHIsYCR7dH0gaGFzIG1lbWJlciAncHVsbCcgdGhhdGApLHN0YXJ0OnZvaWQgMD09PWk/dm9pZCAwOlF0KGkscixgJHt0fSBoYXMgbWVtYmVyICdzdGFydCcgdGhhdGApLHR5cGU6dm9pZCAwPT09bD92b2lkIDA6TnQobCxgJHt0fSBoYXMgbWVtYmVyICd0eXBlJyB0aGF0YCl9fShlLFwiRmlyc3QgcGFyYW1ldGVyXCIpO3ZhciBuO2lmKChuPXRoaXMpLl9zdGF0ZT1cInJlYWRhYmxlXCIsbi5fcmVhZGVyPXZvaWQgMCxuLl9zdG9yZWRFcnJvcj12b2lkIDAsbi5fZGlzdHVyYmVkPSExLFwiYnl0ZXNcIj09PW8udHlwZSl7aWYodm9pZCAwIT09ci5zaXplKXRocm93IG5ldyBSYW5nZUVycm9yKFwiVGhlIHN0cmF0ZWd5IGZvciBhIGJ5dGUgc3RyZWFtIGNhbm5vdCBoYXZlIGEgc2l6ZSBmdW5jdGlvblwiKTtCZSh0aGlzLG8sTWUociwwKSl9ZWxzZXtjb25zdCBlPVllKHIpO0l0KHRoaXMsbyxNZShyLDEpLGUpfX1nZXQgbG9ja2VkKCl7aWYoIVV0KHRoaXMpKXRocm93IFp0KFwibG9ja2VkXCIpO3JldHVybiBHdCh0aGlzKX1jYW5jZWwoZSl7cmV0dXJuIFV0KHRoaXMpP0d0KHRoaXMpP2YobmV3IFR5cGVFcnJvcihcIkNhbm5vdCBjYW5jZWwgYSBzdHJlYW0gdGhhdCBhbHJlYWR5IGhhcyBhIHJlYWRlclwiKSk6WHQodGhpcyxlKTpmKFp0KFwiY2FuY2VsXCIpKX1nZXRSZWFkZXIoZSl7aWYoIVV0KHRoaXMpKXRocm93IFp0KFwiZ2V0UmVhZGVyXCIpO3JldHVybiB2b2lkIDA9PT1mdW5jdGlvbihlLHQpe0QoZSx0KTtjb25zdCByPW51bGw9PWU/dm9pZCAwOmUubW9kZTtyZXR1cm57bW9kZTp2b2lkIDA9PT1yP3ZvaWQgMDp4dChyLGAke3R9IGhhcyBtZW1iZXIgJ21vZGUnIHRoYXRgKX19KGUsXCJGaXJzdCBwYXJhbWV0ZXJcIikubW9kZT9uZXcgUmVhZGFibGVTdHJlYW1EZWZhdWx0UmVhZGVyKHRoaXMpOmZ1bmN0aW9uKGUpe3JldHVybiBuZXcgUmVhZGFibGVTdHJlYW1CWU9CUmVhZGVyKGUpfSh0aGlzKX1waXBlVGhyb3VnaChlLHQ9e30pe2lmKCFIKHRoaXMpKXRocm93IFp0KFwicGlwZVRocm91Z2hcIik7TShlLDEsXCJwaXBlVGhyb3VnaFwiKTtjb25zdCByPVZ0KGUsXCJGaXJzdCBwYXJhbWV0ZXJcIiksbz1IdCh0LFwiU2Vjb25kIHBhcmFtZXRlclwiKTtpZih0aGlzLmxvY2tlZCl0aHJvdyBuZXcgVHlwZUVycm9yKFwiUmVhZGFibGVTdHJlYW0ucHJvdG90eXBlLnBpcGVUaHJvdWdoIGNhbm5vdCBiZSB1c2VkIG9uIGEgbG9ja2VkIFJlYWRhYmxlU3RyZWFtXCIpO2lmKHIud3JpdGFibGUubG9ja2VkKXRocm93IG5ldyBUeXBlRXJyb3IoXCJSZWFkYWJsZVN0cmVhbS5wcm90b3R5cGUucGlwZVRocm91Z2ggY2Fubm90IGJlIHVzZWQgb24gYSBsb2NrZWQgV3JpdGFibGVTdHJlYW1cIik7cmV0dXJuIHkoa3QodGhpcyxyLndyaXRhYmxlLG8ucHJldmVudENsb3NlLG8ucHJldmVudEFib3J0LG8ucHJldmVudENhbmNlbCxvLnNpZ25hbCkpLHIucmVhZGFibGV9cGlwZVRvKGUsdD17fSl7aWYoIUgodGhpcykpcmV0dXJuIGYoWnQoXCJwaXBlVG9cIikpO2lmKHZvaWQgMD09PWUpcmV0dXJuIGYoXCJQYXJhbWV0ZXIgMSBpcyByZXF1aXJlZCBpbiAncGlwZVRvJy5cIik7aWYoIVYoZSkpcmV0dXJuIGYobmV3IFR5cGVFcnJvcihcIlJlYWRhYmxlU3RyZWFtLnByb3RvdHlwZS5waXBlVG8ncyBmaXJzdCBhcmd1bWVudCBtdXN0IGJlIGEgV3JpdGFibGVTdHJlYW1cIikpO2xldCByO3RyeXtyPUh0KHQsXCJTZWNvbmQgcGFyYW1ldGVyXCIpfWNhdGNoKGUpe3JldHVybiBmKGUpfXJldHVybiB0aGlzLmxvY2tlZD9mKG5ldyBUeXBlRXJyb3IoXCJSZWFkYWJsZVN0cmVhbS5wcm90b3R5cGUucGlwZVRvIGNhbm5vdCBiZSB1c2VkIG9uIGEgbG9ja2VkIFJlYWRhYmxlU3RyZWFtXCIpKTplLmxvY2tlZD9mKG5ldyBUeXBlRXJyb3IoXCJSZWFkYWJsZVN0cmVhbS5wcm90b3R5cGUucGlwZVRvIGNhbm5vdCBiZSB1c2VkIG9uIGEgbG9ja2VkIFdyaXRhYmxlU3RyZWFtXCIpKTprdCh0aGlzLGUsci5wcmV2ZW50Q2xvc2Usci5wcmV2ZW50QWJvcnQsci5wcmV2ZW50Q2FuY2VsLHIuc2lnbmFsKX10ZWUoKXtpZighSCh0aGlzKSl0aHJvdyBadChcInRlZVwiKTtpZih0aGlzLmxvY2tlZCl0aHJvdyBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IHRlZSBhIHN0cmVhbSB0aGF0IGFscmVhZHkgaGFzIGEgcmVhZGVyXCIpO3JldHVybiBCdCh0aGlzKX12YWx1ZXMoZSl7aWYoIUgodGhpcykpdGhyb3cgWnQoXCJ2YWx1ZXNcIik7cmV0dXJuIGZ1bmN0aW9uKGUsdCl7Y29uc3Qgcj1lLmdldFJlYWRlcigpLG89bmV3IHJlKHIsdCksbj1PYmplY3QuY3JlYXRlKG9lKTtyZXR1cm4gbi5fYXN5bmNJdGVyYXRvckltcGw9byxufSh0aGlzLGZ1bmN0aW9uKGUsdCl7RChlLHQpO2NvbnN0IHI9bnVsbD09ZT92b2lkIDA6ZS5wcmV2ZW50Q2FuY2VsO3JldHVybntwcmV2ZW50Q2FuY2VsOkJvb2xlYW4ocil9fShlLFwiRmlyc3QgcGFyYW1ldGVyXCIpLnByZXZlbnRDYW5jZWwpfX1mdW5jdGlvbiBVdChlKXtyZXR1cm4hIW8oZSkmJighIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiX3JlYWRhYmxlU3RyZWFtQ29udHJvbGxlclwiKSYmZSBpbnN0YW5jZW9mIFJlYWRhYmxlU3RyZWFtKX1mdW5jdGlvbiBHdChlKXtyZXR1cm4gdm9pZCAwIT09ZS5fcmVhZGVyfWZ1bmN0aW9uIFh0KGUsdCl7aWYoZS5fZGlzdHVyYmVkPSEwLFwiY2xvc2VkXCI9PT1lLl9zdGF0ZSlyZXR1cm4gZCh2b2lkIDApO2lmKFwiZXJyb3JlZFwiPT09ZS5fc3RhdGUpcmV0dXJuIGYoZS5fc3RvcmVkRXJyb3IpO0p0KGUpO2NvbnN0IG89ZS5fcmVhZGVyO2lmKHZvaWQgMCE9PW8mJkRlKG8pKXtjb25zdCBlPW8uX3JlYWRJbnRvUmVxdWVzdHM7by5fcmVhZEludG9SZXF1ZXN0cz1uZXcgdixlLmZvckVhY2goKGU9PntlLl9jbG9zZVN0ZXBzKHZvaWQgMCl9KSl9cmV0dXJuIG0oZS5fcmVhZGFibGVTdHJlYW1Db250cm9sbGVyW3FdKHQpLHIpfWZ1bmN0aW9uIEp0KGUpe2UuX3N0YXRlPVwiY2xvc2VkXCI7Y29uc3QgdD1lLl9yZWFkZXI7aWYodm9pZCAwIT09dCYmKHoodCksWih0KSkpe2NvbnN0IGU9dC5fcmVhZFJlcXVlc3RzO3QuX3JlYWRSZXF1ZXN0cz1uZXcgdixlLmZvckVhY2goKGU9PntlLl9jbG9zZVN0ZXBzKCl9KSl9fWZ1bmN0aW9uIEt0KGUsdCl7ZS5fc3RhdGU9XCJlcnJvcmVkXCIsZS5fc3RvcmVkRXJyb3I9dDtjb25zdCByPWUuX3JlYWRlcjt2b2lkIDAhPT1yJiYoaihyLHQpLFoocik/ZWUocix0KTpJZShyLHQpKX1mdW5jdGlvbiBadChlKXtyZXR1cm4gbmV3IFR5cGVFcnJvcihgUmVhZGFibGVTdHJlYW0ucHJvdG90eXBlLiR7ZX0gY2FuIG9ubHkgYmUgdXNlZCBvbiBhIFJlYWRhYmxlU3RyZWFtYCl9ZnVuY3Rpb24gZXIoZSx0KXtEKGUsdCk7Y29uc3Qgcj1udWxsPT1lP3ZvaWQgMDplLmhpZ2hXYXRlck1hcms7cmV0dXJuIFkocixcImhpZ2hXYXRlck1hcmtcIixcIlF1ZXVpbmdTdHJhdGVneUluaXRcIikse2hpZ2hXYXRlck1hcms6UShyKX19T2JqZWN0LmRlZmluZVByb3BlcnRpZXMoUmVhZGFibGVTdHJlYW0ucHJvdG90eXBlLHtjYW5jZWw6e2VudW1lcmFibGU6ITB9LGdldFJlYWRlcjp7ZW51bWVyYWJsZTohMH0scGlwZVRocm91Z2g6e2VudW1lcmFibGU6ITB9LHBpcGVUbzp7ZW51bWVyYWJsZTohMH0sdGVlOntlbnVtZXJhYmxlOiEwfSx2YWx1ZXM6e2VudW1lcmFibGU6ITB9LGxvY2tlZDp7ZW51bWVyYWJsZTohMH19KSxhKFJlYWRhYmxlU3RyZWFtLnByb3RvdHlwZS5jYW5jZWwsXCJjYW5jZWxcIiksYShSZWFkYWJsZVN0cmVhbS5wcm90b3R5cGUuZ2V0UmVhZGVyLFwiZ2V0UmVhZGVyXCIpLGEoUmVhZGFibGVTdHJlYW0ucHJvdG90eXBlLnBpcGVUaHJvdWdoLFwicGlwZVRocm91Z2hcIiksYShSZWFkYWJsZVN0cmVhbS5wcm90b3R5cGUucGlwZVRvLFwicGlwZVRvXCIpLGEoUmVhZGFibGVTdHJlYW0ucHJvdG90eXBlLnRlZSxcInRlZVwiKSxhKFJlYWRhYmxlU3RyZWFtLnByb3RvdHlwZS52YWx1ZXMsXCJ2YWx1ZXNcIiksXCJzeW1ib2xcIj09dHlwZW9mIHQudG9TdHJpbmdUYWcmJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShSZWFkYWJsZVN0cmVhbS5wcm90b3R5cGUsdC50b1N0cmluZ1RhZyx7dmFsdWU6XCJSZWFkYWJsZVN0cmVhbVwiLGNvbmZpZ3VyYWJsZTohMH0pLFwic3ltYm9sXCI9PXR5cGVvZiB0LmFzeW5jSXRlcmF0b3ImJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShSZWFkYWJsZVN0cmVhbS5wcm90b3R5cGUsdC5hc3luY0l0ZXJhdG9yLHt2YWx1ZTpSZWFkYWJsZVN0cmVhbS5wcm90b3R5cGUudmFsdWVzLHdyaXRhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMH0pO2NvbnN0IHRyPWU9PmUuYnl0ZUxlbmd0aDthKHRyLFwic2l6ZVwiKTtjbGFzcyBCeXRlTGVuZ3RoUXVldWluZ1N0cmF0ZWd5e2NvbnN0cnVjdG9yKGUpe00oZSwxLFwiQnl0ZUxlbmd0aFF1ZXVpbmdTdHJhdGVneVwiKSxlPWVyKGUsXCJGaXJzdCBwYXJhbWV0ZXJcIiksdGhpcy5fYnl0ZUxlbmd0aFF1ZXVpbmdTdHJhdGVneUhpZ2hXYXRlck1hcms9ZS5oaWdoV2F0ZXJNYXJrfWdldCBoaWdoV2F0ZXJNYXJrKCl7aWYoIW9yKHRoaXMpKXRocm93IHJyKFwiaGlnaFdhdGVyTWFya1wiKTtyZXR1cm4gdGhpcy5fYnl0ZUxlbmd0aFF1ZXVpbmdTdHJhdGVneUhpZ2hXYXRlck1hcmt9Z2V0IHNpemUoKXtpZighb3IodGhpcykpdGhyb3cgcnIoXCJzaXplXCIpO3JldHVybiB0cn19ZnVuY3Rpb24gcnIoZSl7cmV0dXJuIG5ldyBUeXBlRXJyb3IoYEJ5dGVMZW5ndGhRdWV1aW5nU3RyYXRlZ3kucHJvdG90eXBlLiR7ZX0gY2FuIG9ubHkgYmUgdXNlZCBvbiBhIEJ5dGVMZW5ndGhRdWV1aW5nU3RyYXRlZ3lgKX1mdW5jdGlvbiBvcihlKXtyZXR1cm4hIW8oZSkmJighIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiX2J5dGVMZW5ndGhRdWV1aW5nU3RyYXRlZ3lIaWdoV2F0ZXJNYXJrXCIpJiZlIGluc3RhbmNlb2YgQnl0ZUxlbmd0aFF1ZXVpbmdTdHJhdGVneSl9T2JqZWN0LmRlZmluZVByb3BlcnRpZXMoQnl0ZUxlbmd0aFF1ZXVpbmdTdHJhdGVneS5wcm90b3R5cGUse2hpZ2hXYXRlck1hcms6e2VudW1lcmFibGU6ITB9LHNpemU6e2VudW1lcmFibGU6ITB9fSksXCJzeW1ib2xcIj09dHlwZW9mIHQudG9TdHJpbmdUYWcmJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShCeXRlTGVuZ3RoUXVldWluZ1N0cmF0ZWd5LnByb3RvdHlwZSx0LnRvU3RyaW5nVGFnLHt2YWx1ZTpcIkJ5dGVMZW5ndGhRdWV1aW5nU3RyYXRlZ3lcIixjb25maWd1cmFibGU6ITB9KTtjb25zdCBucj0oKT0+MTthKG5yLFwic2l6ZVwiKTtjbGFzcyBDb3VudFF1ZXVpbmdTdHJhdGVneXtjb25zdHJ1Y3RvcihlKXtNKGUsMSxcIkNvdW50UXVldWluZ1N0cmF0ZWd5XCIpLGU9ZXIoZSxcIkZpcnN0IHBhcmFtZXRlclwiKSx0aGlzLl9jb3VudFF1ZXVpbmdTdHJhdGVneUhpZ2hXYXRlck1hcms9ZS5oaWdoV2F0ZXJNYXJrfWdldCBoaWdoV2F0ZXJNYXJrKCl7aWYoIWlyKHRoaXMpKXRocm93IGFyKFwiaGlnaFdhdGVyTWFya1wiKTtyZXR1cm4gdGhpcy5fY291bnRRdWV1aW5nU3RyYXRlZ3lIaWdoV2F0ZXJNYXJrfWdldCBzaXplKCl7aWYoIWlyKHRoaXMpKXRocm93IGFyKFwic2l6ZVwiKTtyZXR1cm4gbnJ9fWZ1bmN0aW9uIGFyKGUpe3JldHVybiBuZXcgVHlwZUVycm9yKGBDb3VudFF1ZXVpbmdTdHJhdGVneS5wcm90b3R5cGUuJHtlfSBjYW4gb25seSBiZSB1c2VkIG9uIGEgQ291bnRRdWV1aW5nU3RyYXRlZ3lgKX1mdW5jdGlvbiBpcihlKXtyZXR1cm4hIW8oZSkmJighIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiX2NvdW50UXVldWluZ1N0cmF0ZWd5SGlnaFdhdGVyTWFya1wiKSYmZSBpbnN0YW5jZW9mIENvdW50UXVldWluZ1N0cmF0ZWd5KX1mdW5jdGlvbiBscihlLHQscil7cmV0dXJuIEkoZSxyKSxyPT53KGUsdCxbcl0pfWZ1bmN0aW9uIHNyKGUsdCxyKXtyZXR1cm4gSShlLHIpLHI9PlMoZSx0LFtyXSl9ZnVuY3Rpb24gdXIoZSx0LHIpe3JldHVybiBJKGUsciksKHIsbyk9PncoZSx0LFtyLG9dKX1PYmplY3QuZGVmaW5lUHJvcGVydGllcyhDb3VudFF1ZXVpbmdTdHJhdGVneS5wcm90b3R5cGUse2hpZ2hXYXRlck1hcms6e2VudW1lcmFibGU6ITB9LHNpemU6e2VudW1lcmFibGU6ITB9fSksXCJzeW1ib2xcIj09dHlwZW9mIHQudG9TdHJpbmdUYWcmJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShDb3VudFF1ZXVpbmdTdHJhdGVneS5wcm90b3R5cGUsdC50b1N0cmluZ1RhZyx7dmFsdWU6XCJDb3VudFF1ZXVpbmdTdHJhdGVneVwiLGNvbmZpZ3VyYWJsZTohMH0pO2NsYXNzIFRyYW5zZm9ybVN0cmVhbXtjb25zdHJ1Y3RvcihlPXt9LHQ9e30scj17fSl7dm9pZCAwPT09ZSYmKGU9bnVsbCk7Y29uc3Qgbz1RZSh0LFwiU2Vjb25kIHBhcmFtZXRlclwiKSxuPVFlKHIsXCJUaGlyZCBwYXJhbWV0ZXJcIiksYT1mdW5jdGlvbihlLHQpe0QoZSx0KTtjb25zdCByPW51bGw9PWU/dm9pZCAwOmUuZmx1c2gsbz1udWxsPT1lP3ZvaWQgMDplLnJlYWRhYmxlVHlwZSxuPW51bGw9PWU/dm9pZCAwOmUuc3RhcnQsYT1udWxsPT1lP3ZvaWQgMDplLnRyYW5zZm9ybSxpPW51bGw9PWU/dm9pZCAwOmUud3JpdGFibGVUeXBlO3JldHVybntmbHVzaDp2b2lkIDA9PT1yP3ZvaWQgMDpscihyLGUsYCR7dH0gaGFzIG1lbWJlciAnZmx1c2gnIHRoYXRgKSxyZWFkYWJsZVR5cGU6byxzdGFydDp2b2lkIDA9PT1uP3ZvaWQgMDpzcihuLGUsYCR7dH0gaGFzIG1lbWJlciAnc3RhcnQnIHRoYXRgKSx0cmFuc2Zvcm06dm9pZCAwPT09YT92b2lkIDA6dXIoYSxlLGAke3R9IGhhcyBtZW1iZXIgJ3RyYW5zZm9ybScgdGhhdGApLHdyaXRhYmxlVHlwZTppfX0oZSxcIkZpcnN0IHBhcmFtZXRlclwiKTtpZih2b2lkIDAhPT1hLnJlYWRhYmxlVHlwZSl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIkludmFsaWQgcmVhZGFibGVUeXBlIHNwZWNpZmllZFwiKTtpZih2b2lkIDAhPT1hLndyaXRhYmxlVHlwZSl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIkludmFsaWQgd3JpdGFibGVUeXBlIHNwZWNpZmllZFwiKTtjb25zdCBpPU1lKG4sMCksbD1ZZShuKSxzPU1lKG8sMSksdT1ZZShvKTtsZXQgYjshZnVuY3Rpb24oZSx0LHIsbyxuLGEpe2Z1bmN0aW9uIGkoKXtyZXR1cm4gdH1mdW5jdGlvbiBsKHQpe3JldHVybiBmdW5jdGlvbihlLHQpe2NvbnN0IHI9ZS5fdHJhbnNmb3JtU3RyZWFtQ29udHJvbGxlcjtpZihlLl9iYWNrcHJlc3N1cmUpe3JldHVybiBtKGUuX2JhY2twcmVzc3VyZUNoYW5nZVByb21pc2UsKCgpPT57aWYoXCJlcnJvcmluZ1wiPT09KFhlKGUuX3dyaXRhYmxlKT9lLl93cml0YWJsZS5fc3RhdGU6ZS5fd3JpdGFibGVTdGF0ZSkpdGhyb3cgWGUoZS5fd3JpdGFibGUpP2UuX3dyaXRhYmxlLl9zdG9yZWRFcnJvcjplLl93cml0YWJsZVN0b3JlZEVycm9yO3JldHVybiBtcihyLHQpfSkpfXJldHVybiBtcihyLHQpfShlLHQpfWZ1bmN0aW9uIHModCl7cmV0dXJuIGZ1bmN0aW9uKGUsdCl7cmV0dXJuIGRyKGUsdCksZCh2b2lkIDApfShlLHQpfWZ1bmN0aW9uIHUoKXtyZXR1cm4gZnVuY3Rpb24oZSl7Y29uc3QgdD1lLl90cmFuc2Zvcm1TdHJlYW1Db250cm9sbGVyLHI9dC5fZmx1c2hBbGdvcml0aG0oKTtyZXR1cm4gX3IodCksbShyLCgoKT0+e2lmKFwiZXJyb3JlZFwiPT09ZS5fcmVhZGFibGVTdGF0ZSl0aHJvdyBlLl9yZWFkYWJsZVN0b3JlZEVycm9yO1NyKGUpJiZ3cihlKX0pLCh0PT57dGhyb3cgZHIoZSx0KSxlLl9yZWFkYWJsZVN0b3JlZEVycm9yfSkpfShlKX1mdW5jdGlvbiBjKCl7cmV0dXJuIGZ1bmN0aW9uKGUpe3JldHVybiBicihlLCExKSxlLl9iYWNrcHJlc3N1cmVDaGFuZ2VQcm9taXNlfShlKX1mdW5jdGlvbiBmKHQpe3JldHVybiBmcihlLHQpLGQodm9pZCAwKX1lLl93cml0YWJsZVN0YXRlPVwid3JpdGFibGVcIixlLl93cml0YWJsZVN0b3JlZEVycm9yPXZvaWQgMCxlLl93cml0YWJsZUhhc0luRmxpZ2h0T3BlcmF0aW9uPSExLGUuX3dyaXRhYmxlU3RhcnRlZD0hMSxlLl93cml0YWJsZT1mdW5jdGlvbihlLHQscixvLG4sYSxpKXtyZXR1cm4gbmV3IFdyaXRhYmxlU3RyZWFtKHtzdGFydChyKXtlLl93cml0YWJsZUNvbnRyb2xsZXI9cjt0cnl7Y29uc3QgdD1yLnNpZ25hbDt2b2lkIDAhPT10JiZ0LmFkZEV2ZW50TGlzdGVuZXIoXCJhYm9ydFwiLCgoKT0+e1wid3JpdGFibGVcIj09PWUuX3dyaXRhYmxlU3RhdGUmJihlLl93cml0YWJsZVN0YXRlPVwiZXJyb3JpbmdcIix0LnJlYXNvbiYmKGUuX3dyaXRhYmxlU3RvcmVkRXJyb3I9dC5yZWFzb24pKX0pKX1jYXRjaChlKXt9cmV0dXJuIG0odCgpLCgoKT0+KGUuX3dyaXRhYmxlU3RhcnRlZD0hMCxQcihlKSxudWxsKSksKHQ9Pnt0aHJvdyBlLl93cml0YWJsZVN0YXJ0ZWQ9ITAsVHIoZSx0KSx0fSkpfSx3cml0ZTp0PT4oZnVuY3Rpb24oZSl7ZS5fd3JpdGFibGVIYXNJbkZsaWdodE9wZXJhdGlvbj0hMH0oZSksbShyKHQpLCgoKT0+KGZ1bmN0aW9uKGUpe2UuX3dyaXRhYmxlSGFzSW5GbGlnaHRPcGVyYXRpb249ITF9KGUpLFByKGUpLG51bGwpKSwodD0+e3Rocm93IGZ1bmN0aW9uKGUsdCl7ZS5fd3JpdGFibGVIYXNJbkZsaWdodE9wZXJhdGlvbj0hMSxUcihlLHQpfShlLHQpLHR9KSkpLGNsb3NlOigpPT4oZnVuY3Rpb24oZSl7ZS5fd3JpdGFibGVIYXNJbkZsaWdodE9wZXJhdGlvbj0hMH0oZSksbShvKCksKCgpPT4oZnVuY3Rpb24oZSl7ZS5fd3JpdGFibGVIYXNJbkZsaWdodE9wZXJhdGlvbj0hMTtcImVycm9yaW5nXCI9PT1lLl93cml0YWJsZVN0YXRlJiYoZS5fd3JpdGFibGVTdG9yZWRFcnJvcj12b2lkIDApO2UuX3dyaXRhYmxlU3RhdGU9XCJjbG9zZWRcIn0oZSksbnVsbCkpLCh0PT57dGhyb3cgZnVuY3Rpb24oZSx0KXtlLl93cml0YWJsZUhhc0luRmxpZ2h0T3BlcmF0aW9uPSExLGUuX3dyaXRhYmxlU3RhdGUsVHIoZSx0KX0oZSx0KSx0fSkpKSxhYm9ydDp0PT4oZS5fd3JpdGFibGVTdGF0ZT1cImVycm9yZWRcIixlLl93cml0YWJsZVN0b3JlZEVycm9yPXQsbih0KSl9LHtoaWdoV2F0ZXJNYXJrOmEsc2l6ZTppfSl9KGUsaSxsLHUscyxyLG8pLGUuX3JlYWRhYmxlU3RhdGU9XCJyZWFkYWJsZVwiLGUuX3JlYWRhYmxlU3RvcmVkRXJyb3I9dm9pZCAwLGUuX3JlYWRhYmxlQ2xvc2VSZXF1ZXN0ZWQ9ITEsZS5fcmVhZGFibGVQdWxsaW5nPSExLGUuX3JlYWRhYmxlPWZ1bmN0aW9uKGUsdCxyLG8sbixhKXtyZXR1cm4gbmV3IFJlYWRhYmxlU3RyZWFtKHtzdGFydDpyPT4oZS5fcmVhZGFibGVDb250cm9sbGVyPXIsdCgpLmNhdGNoKCh0PT57dnIoZSx0KX0pKSkscHVsbDooKT0+KGUuX3JlYWRhYmxlUHVsbGluZz0hMCxyKCkuY2F0Y2goKHQ9Pnt2cihlLHQpfSkpKSxjYW5jZWw6dD0+KGUuX3JlYWRhYmxlU3RhdGU9XCJjbG9zZWRcIixvKHQpKX0se2hpZ2hXYXRlck1hcms6bixzaXplOmF9KX0oZSxpLGMsZixuLGEpLGUuX2JhY2twcmVzc3VyZT12b2lkIDAsZS5fYmFja3ByZXNzdXJlQ2hhbmdlUHJvbWlzZT12b2lkIDAsZS5fYmFja3ByZXNzdXJlQ2hhbmdlUHJvbWlzZV9yZXNvbHZlPXZvaWQgMCxicihlLCEwKSxlLl90cmFuc2Zvcm1TdHJlYW1Db250cm9sbGVyPXZvaWQgMH0odGhpcyxjKChlPT57Yj1lfSkpLHMsdSxpLGwpLGZ1bmN0aW9uKGUsdCl7Y29uc3Qgcj1PYmplY3QuY3JlYXRlKFRyYW5zZm9ybVN0cmVhbURlZmF1bHRDb250cm9sbGVyLnByb3RvdHlwZSk7bGV0IG8sbjtvPXZvaWQgMCE9PXQudHJhbnNmb3JtP2U9PnQudHJhbnNmb3JtKGUscik6ZT0+e3RyeXtyZXR1cm4gcHIocixlKSxkKHZvaWQgMCl9Y2F0Y2goZSl7cmV0dXJuIGYoZSl9fTtuPXZvaWQgMCE9PXQuZmx1c2g/KCk9PnQuZmx1c2gocik6KCk9PmQodm9pZCAwKTshZnVuY3Rpb24oZSx0LHIsbyl7dC5fY29udHJvbGxlZFRyYW5zZm9ybVN0cmVhbT1lLGUuX3RyYW5zZm9ybVN0cmVhbUNvbnRyb2xsZXI9dCx0Ll90cmFuc2Zvcm1BbGdvcml0aG09cix0Ll9mbHVzaEFsZ29yaXRobT1vfShlLHIsbyxuKX0odGhpcyxhKSx2b2lkIDAhPT1hLnN0YXJ0P2IoYS5zdGFydCh0aGlzLl90cmFuc2Zvcm1TdHJlYW1Db250cm9sbGVyKSk6Yih2b2lkIDApfWdldCByZWFkYWJsZSgpe2lmKCFjcih0aGlzKSl0aHJvdyBncihcInJlYWRhYmxlXCIpO3JldHVybiB0aGlzLl9yZWFkYWJsZX1nZXQgd3JpdGFibGUoKXtpZighY3IodGhpcykpdGhyb3cgZ3IoXCJ3cml0YWJsZVwiKTtyZXR1cm4gdGhpcy5fd3JpdGFibGV9fWZ1bmN0aW9uIGNyKGUpe3JldHVybiEhbyhlKSYmKCEhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGUsXCJfdHJhbnNmb3JtU3RyZWFtQ29udHJvbGxlclwiKSYmZSBpbnN0YW5jZW9mIFRyYW5zZm9ybVN0cmVhbSl9ZnVuY3Rpb24gZHIoZSx0KXt2cihlLHQpLGZyKGUsdCl9ZnVuY3Rpb24gZnIoZSx0KXtfcihlLl90cmFuc2Zvcm1TdHJlYW1Db250cm9sbGVyKSxmdW5jdGlvbihlLHQpe2UuX3dyaXRhYmxlQ29udHJvbGxlci5lcnJvcih0KTtcIndyaXRhYmxlXCI9PT1lLl93cml0YWJsZVN0YXRlJiZxcihlLHQpfShlLHQpLGUuX2JhY2twcmVzc3VyZSYmYnIoZSwhMSl9ZnVuY3Rpb24gYnIoZSx0KXt2b2lkIDAhPT1lLl9iYWNrcHJlc3N1cmVDaGFuZ2VQcm9taXNlJiZlLl9iYWNrcHJlc3N1cmVDaGFuZ2VQcm9taXNlX3Jlc29sdmUoKSxlLl9iYWNrcHJlc3N1cmVDaGFuZ2VQcm9taXNlPWMoKHQ9PntlLl9iYWNrcHJlc3N1cmVDaGFuZ2VQcm9taXNlX3Jlc29sdmU9dH0pKSxlLl9iYWNrcHJlc3N1cmU9dH1PYmplY3QuZGVmaW5lUHJvcGVydGllcyhUcmFuc2Zvcm1TdHJlYW0ucHJvdG90eXBlLHtyZWFkYWJsZTp7ZW51bWVyYWJsZTohMH0sd3JpdGFibGU6e2VudW1lcmFibGU6ITB9fSksXCJzeW1ib2xcIj09dHlwZW9mIHQudG9TdHJpbmdUYWcmJk9iamVjdC5kZWZpbmVQcm9wZXJ0eShUcmFuc2Zvcm1TdHJlYW0ucHJvdG90eXBlLHQudG9TdHJpbmdUYWcse3ZhbHVlOlwiVHJhbnNmb3JtU3RyZWFtXCIsY29uZmlndXJhYmxlOiEwfSk7Y2xhc3MgVHJhbnNmb3JtU3RyZWFtRGVmYXVsdENvbnRyb2xsZXJ7Y29uc3RydWN0b3IoKXt0aHJvdyBuZXcgVHlwZUVycm9yKFwiSWxsZWdhbCBjb25zdHJ1Y3RvclwiKX1nZXQgZGVzaXJlZFNpemUoKXtpZighaHIodGhpcykpdGhyb3cgeXIoXCJkZXNpcmVkU2l6ZVwiKTtyZXR1cm4gUnIodGhpcy5fY29udHJvbGxlZFRyYW5zZm9ybVN0cmVhbSl9ZW5xdWV1ZShlKXtpZighaHIodGhpcykpdGhyb3cgeXIoXCJlbnF1ZXVlXCIpO3ByKHRoaXMsZSl9ZXJyb3IoZSl7aWYoIWhyKHRoaXMpKXRocm93IHlyKFwiZXJyb3JcIik7dmFyIHQ7dD1lLGRyKHRoaXMuX2NvbnRyb2xsZWRUcmFuc2Zvcm1TdHJlYW0sdCl9dGVybWluYXRlKCl7aWYoIWhyKHRoaXMpKXRocm93IHlyKFwidGVybWluYXRlXCIpOyFmdW5jdGlvbihlKXtjb25zdCB0PWUuX2NvbnRyb2xsZWRUcmFuc2Zvcm1TdHJlYW07U3IodCkmJndyKHQpO2NvbnN0IHI9bmV3IFR5cGVFcnJvcihcIlRyYW5zZm9ybVN0cmVhbSB0ZXJtaW5hdGVkXCIpO2ZyKHQscil9KHRoaXMpfX1mdW5jdGlvbiBocihlKXtyZXR1cm4hIW8oZSkmJighIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiX2NvbnRyb2xsZWRUcmFuc2Zvcm1TdHJlYW1cIikmJmUgaW5zdGFuY2VvZiBUcmFuc2Zvcm1TdHJlYW1EZWZhdWx0Q29udHJvbGxlcil9ZnVuY3Rpb24gX3IoZSl7ZS5fdHJhbnNmb3JtQWxnb3JpdGhtPXZvaWQgMCxlLl9mbHVzaEFsZ29yaXRobT12b2lkIDB9ZnVuY3Rpb24gcHIoZSx0KXtjb25zdCByPWUuX2NvbnRyb2xsZWRUcmFuc2Zvcm1TdHJlYW07aWYoIVNyKHIpKXRocm93IG5ldyBUeXBlRXJyb3IoXCJSZWFkYWJsZSBzaWRlIGlzIG5vdCBpbiBhIHN0YXRlIHRoYXQgcGVybWl0cyBlbnF1ZXVlXCIpO3RyeXshZnVuY3Rpb24oZSx0KXtlLl9yZWFkYWJsZVB1bGxpbmc9ITE7dHJ5e2UuX3JlYWRhYmxlQ29udHJvbGxlci5lbnF1ZXVlKHQpfWNhdGNoKHQpe3Rocm93IHZyKGUsdCksdH19KHIsdCl9Y2F0Y2goZSl7dGhyb3cgZnIocixlKSxyLl9yZWFkYWJsZVN0b3JlZEVycm9yfWNvbnN0IG89ZnVuY3Rpb24oZSl7cmV0dXJuIWZ1bmN0aW9uKGUpe2lmKCFTcihlKSlyZXR1cm4hMTtpZihlLl9yZWFkYWJsZVB1bGxpbmcpcmV0dXJuITA7aWYoUnIoZSk+MClyZXR1cm4hMDtyZXR1cm4hMX0oZSl9KHIpO28hPT1yLl9iYWNrcHJlc3N1cmUmJmJyKHIsITApfWZ1bmN0aW9uIG1yKGUsdCl7cmV0dXJuIG0oZS5fdHJhbnNmb3JtQWxnb3JpdGhtKHQpLHZvaWQgMCwodD0+e3Rocm93IGRyKGUuX2NvbnRyb2xsZWRUcmFuc2Zvcm1TdHJlYW0sdCksdH0pKX1mdW5jdGlvbiB5cihlKXtyZXR1cm4gbmV3IFR5cGVFcnJvcihgVHJhbnNmb3JtU3RyZWFtRGVmYXVsdENvbnRyb2xsZXIucHJvdG90eXBlLiR7ZX0gY2FuIG9ubHkgYmUgdXNlZCBvbiBhIFRyYW5zZm9ybVN0cmVhbURlZmF1bHRDb250cm9sbGVyYCl9ZnVuY3Rpb24gZ3IoZSl7cmV0dXJuIG5ldyBUeXBlRXJyb3IoYFRyYW5zZm9ybVN0cmVhbS5wcm90b3R5cGUuJHtlfSBjYW4gb25seSBiZSB1c2VkIG9uIGEgVHJhbnNmb3JtU3RyZWFtYCl9ZnVuY3Rpb24gU3IoZSl7cmV0dXJuIWUuX3JlYWRhYmxlQ2xvc2VSZXF1ZXN0ZWQmJlwicmVhZGFibGVcIj09PWUuX3JlYWRhYmxlU3RhdGV9ZnVuY3Rpb24gd3IoZSl7ZS5fcmVhZGFibGVTdGF0ZT1cImNsb3NlZFwiLGUuX3JlYWRhYmxlQ2xvc2VSZXF1ZXN0ZWQ9ITAsZS5fcmVhZGFibGVDb250cm9sbGVyLmNsb3NlKCl9ZnVuY3Rpb24gdnIoZSx0KXtcInJlYWRhYmxlXCI9PT1lLl9yZWFkYWJsZVN0YXRlJiYoZS5fcmVhZGFibGVTdGF0ZT1cImVycm9yZWRcIixlLl9yZWFkYWJsZVN0b3JlZEVycm9yPXQpLGUuX3JlYWRhYmxlQ29udHJvbGxlci5lcnJvcih0KX1mdW5jdGlvbiBScihlKXtyZXR1cm4gZS5fcmVhZGFibGVDb250cm9sbGVyLmRlc2lyZWRTaXplfWZ1bmN0aW9uIFRyKGUsdCl7XCJ3cml0YWJsZVwiIT09ZS5fd3JpdGFibGVTdGF0ZT9DcihlKTpxcihlLHQpfWZ1bmN0aW9uIHFyKGUsdCl7ZS5fd3JpdGFibGVTdGF0ZT1cImVycm9yaW5nXCIsZS5fd3JpdGFibGVTdG9yZWRFcnJvcj10LCFmdW5jdGlvbihlKXtyZXR1cm4gZS5fd3JpdGFibGVIYXNJbkZsaWdodE9wZXJhdGlvbn0oZSkmJmUuX3dyaXRhYmxlU3RhcnRlZCYmQ3IoZSl9ZnVuY3Rpb24gQ3IoZSl7ZS5fd3JpdGFibGVTdGF0ZT1cImVycm9yZWRcIn1mdW5jdGlvbiBQcihlKXtcImVycm9yaW5nXCI9PT1lLl93cml0YWJsZVN0YXRlJiZDcihlKX1PYmplY3QuZGVmaW5lUHJvcGVydGllcyhUcmFuc2Zvcm1TdHJlYW1EZWZhdWx0Q29udHJvbGxlci5wcm90b3R5cGUse2VucXVldWU6e2VudW1lcmFibGU6ITB9LGVycm9yOntlbnVtZXJhYmxlOiEwfSx0ZXJtaW5hdGU6e2VudW1lcmFibGU6ITB9LGRlc2lyZWRTaXplOntlbnVtZXJhYmxlOiEwfX0pLGEoVHJhbnNmb3JtU3RyZWFtRGVmYXVsdENvbnRyb2xsZXIucHJvdG90eXBlLmVucXVldWUsXCJlbnF1ZXVlXCIpLGEoVHJhbnNmb3JtU3RyZWFtRGVmYXVsdENvbnRyb2xsZXIucHJvdG90eXBlLmVycm9yLFwiZXJyb3JcIiksYShUcmFuc2Zvcm1TdHJlYW1EZWZhdWx0Q29udHJvbGxlci5wcm90b3R5cGUudGVybWluYXRlLFwidGVybWluYXRlXCIpLFwic3ltYm9sXCI9PXR5cGVvZiB0LnRvU3RyaW5nVGFnJiZPYmplY3QuZGVmaW5lUHJvcGVydHkoVHJhbnNmb3JtU3RyZWFtRGVmYXVsdENvbnRyb2xsZXIucHJvdG90eXBlLHQudG9TdHJpbmdUYWcse3ZhbHVlOlwiVHJhbnNmb3JtU3RyZWFtRGVmYXVsdENvbnRyb2xsZXJcIixjb25maWd1cmFibGU6ITB9KSxlLkJ5dGVMZW5ndGhRdWV1aW5nU3RyYXRlZ3k9Qnl0ZUxlbmd0aFF1ZXVpbmdTdHJhdGVneSxlLkNvdW50UXVldWluZ1N0cmF0ZWd5PUNvdW50UXVldWluZ1N0cmF0ZWd5LGUuUmVhZGFibGVCeXRlU3RyZWFtQ29udHJvbGxlcj1SZWFkYWJsZUJ5dGVTdHJlYW1Db250cm9sbGVyLGUuUmVhZGFibGVTdHJlYW09UmVhZGFibGVTdHJlYW0sZS5SZWFkYWJsZVN0cmVhbUJZT0JSZWFkZXI9UmVhZGFibGVTdHJlYW1CWU9CUmVhZGVyLGUuUmVhZGFibGVTdHJlYW1CWU9CUmVxdWVzdD1SZWFkYWJsZVN0cmVhbUJZT0JSZXF1ZXN0LGUuUmVhZGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlcj1SZWFkYWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyLGUuUmVhZGFibGVTdHJlYW1EZWZhdWx0UmVhZGVyPVJlYWRhYmxlU3RyZWFtRGVmYXVsdFJlYWRlcixlLlRyYW5zZm9ybVN0cmVhbT1UcmFuc2Zvcm1TdHJlYW0sZS5UcmFuc2Zvcm1TdHJlYW1EZWZhdWx0Q29udHJvbGxlcj1UcmFuc2Zvcm1TdHJlYW1EZWZhdWx0Q29udHJvbGxlcixlLldyaXRhYmxlU3RyZWFtPVdyaXRhYmxlU3RyZWFtLGUuV3JpdGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlcj1Xcml0YWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyLGUuV3JpdGFibGVTdHJlYW1EZWZhdWx0V3JpdGVyPVdyaXRhYmxlU3RyZWFtRGVmYXVsdFdyaXRlcixPYmplY3QuZGVmaW5lUHJvcGVydHkoZSxcIl9fZXNNb2R1bGVcIix7dmFsdWU6ITB9KX0pKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.js\n");

/***/ })

};
;