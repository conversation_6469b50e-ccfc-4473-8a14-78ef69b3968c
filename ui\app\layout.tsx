import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { CopilotKit } from "@copilotkit/react-core";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Zinniax Copilot",
  description: "Zinniax Copilot - Your AI Assistant",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="light">
      <head>
          <link rel="icon" type="image/x-icon" href="https://stage.ionm.zinniax.com/assets/img/logo/favicon.png" />
 
        <style>
          {`
            .poweredByContainer {
              padding: 0;
            }
            .poweredBy {
              background: var(--copilot-kit-background-color) !important;
              visibility: visible !important;
              display: block !important;
              position: static !important;
              text-align: center !important;
              font-size: 12px !important;
              padding: 3px 0 !important;
              margin: 0 !important;
            }
`}
        </style>
      </head>
      <body className={inter.className}>
        <CopilotKit runtimeUrl="/api2/copilotkit" agent="scheduling_agent">
          {children}
        </CopilotKit>
      </body>
    </html>
  );
}
