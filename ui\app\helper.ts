export function base64UrlDecode(input: string): string {
  // Convert from base64url to base64
  let base64 = input.replace(/-/g, '+').replace(/_/g, '/');

  // Pad with '=' characters
  while (base64.length % 4 !== 0) {
    base64 += '=';
  }

  // Decode base64 string
  const decoded = atob(base64);

  return decoded;
}

export function decodeJwt(token: string): { header: any; payload: any } | null {
  try {
    const [headerB64, payloadB64] = token.split('.');

    if (!headerB64 || !payloadB64) {
      throw new Error("Invalid token format");
    }

    const header = JSON.parse(base64UrlDecode(headerB64));
    const payload = JSON.parse(base64UrlDecode(payloadB64));

    if (header.alg !== 'HS512') {
      console.warn(`Unexpected algorithm: ${header.alg}`);
    }

    return { header, payload };
  } catch (err) {
    console.error('Failed to decode JWT:', err);
    return null;
  }
}