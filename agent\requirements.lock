# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.13
    # via langchain-community
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anthropic==0.55.0
    # via langchain-anthropic
anyio==4.9.0
    # via anthropic
    # via httpx
    # via openai
    # via starlette
async-timeout==4.0.3
    # via aiohttp
    # via langchain
attrs==25.3.0
    # via aiohttp
certifi==2025.6.15
    # via httpcore
    # via httpx
    # via requests
charset-normalizer==3.4.2
    # via requests
click==8.1.8
    # via langgraph-cli
    # via uvicorn
colorama==0.4.6
    # via click
    # via tqdm
copilotkit==0.1.39
    # via weather-agent
dataclasses-json==0.6.7
    # via langchain-community
distro==1.9.0
    # via anthropic
    # via openai
exceptiongroup==1.3.0
    # via anyio
fastapi==0.115.13
    # via copilotkit
frozenlist==1.7.0
    # via aiohttp
    # via aiosignal
greenlet==3.2.3
    # via sqlalchemy
h11==0.16.0
    # via httpcore
    # via uvicorn
httpcore==1.0.9
    # via httpx
httpx==0.27.2
    # via anthropic
    # via copilotkit
    # via langgraph-sdk
    # via langsmith
    # via openai
httpx-sse==0.4.1
    # via langchain-community
idna==3.10
    # via anyio
    # via httpx
    # via requests
    # via yarl
jiter==0.10.0
    # via anthropic
    # via openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
langchain==0.3.26
    # via copilotkit
    # via langchain-community
    # via weather-agent
langchain-anthropic==0.3.15
    # via copilotkit
    # via weather-agent
langchain-community==0.3.26
    # via weather-agent
langchain-core==0.3.66
    # via langchain
    # via langchain-anthropic
    # via langchain-community
    # via langchain-openai
    # via langchain-text-splitters
    # via langgraph
    # via langgraph-checkpoint
    # via langgraph-prebuilt
    # via weather-agent
langchain-openai==0.3.25
    # via copilotkit
    # via weather-agent
langchain-text-splitters==0.3.8
    # via langchain
langgraph==0.4.9
    # via copilotkit
langgraph-checkpoint==2.1.0
    # via langgraph
    # via langgraph-prebuilt
langgraph-cli==0.3.3
    # via weather-agent
langgraph-prebuilt==0.2.2
    # via langgraph
langgraph-sdk==0.1.70
    # via copilotkit
    # via langgraph
langsmith==0.4.1
    # via langchain
    # via langchain-community
    # via langchain-core
marshmallow==3.26.1
    # via dataclasses-json
multidict==6.5.1
    # via aiohttp
    # via yarl
mypy-extensions==1.1.0
    # via typing-inspect
numpy==2.0.2
    # via langchain-community
openai==1.91.0
    # via langchain-openai
    # via weather-agent
orjson==3.10.18
    # via langgraph-sdk
    # via langsmith
ormsgpack==1.10.0
    # via langgraph-checkpoint
packaging==24.2
    # via langchain-core
    # via langsmith
    # via marshmallow
partialjson==0.0.8
    # via copilotkit
propcache==0.3.2
    # via aiohttp
    # via yarl
pydantic==2.11.7
    # via anthropic
    # via fastapi
    # via langchain
    # via langchain-anthropic
    # via langchain-core
    # via langgraph
    # via langsmith
    # via openai
    # via pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via langchain-community
python-dotenv==1.1.1
    # via pydantic-settings
    # via weather-agent
pyyaml==6.0.2
    # via langchain
    # via langchain-community
    # via langchain-core
regex==2024.11.6
    # via tiktoken
requests==2.32.4
    # via langchain
    # via langchain-community
    # via langsmith
    # via requests-toolbelt
    # via tiktoken
requests-toolbelt==1.0.0
    # via langsmith
sniffio==1.3.1
    # via anthropic
    # via anyio
    # via httpx
    # via openai
sqlalchemy==2.0.41
    # via langchain
    # via langchain-community
starlette==0.46.2
    # via fastapi
tenacity==9.1.2
    # via langchain-community
    # via langchain-core
tiktoken==0.9.0
    # via langchain-openai
toml==0.10.2
    # via copilotkit
tqdm==4.67.1
    # via openai
typing-extensions==4.14.0
    # via anthropic
    # via anyio
    # via exceptiongroup
    # via fastapi
    # via langchain-core
    # via multidict
    # via openai
    # via pydantic
    # via pydantic-core
    # via sqlalchemy
    # via starlette
    # via typing-inspect
    # via typing-inspection
    # via uvicorn
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via pydantic
    # via pydantic-settings
urllib3==2.5.0
    # via requests
uvicorn==0.34.3
    # via weather-agent
xxhash==3.5.0
    # via langgraph
yarl==1.20.1
    # via aiohttp
zstandard==0.23.0
    # via langsmith
