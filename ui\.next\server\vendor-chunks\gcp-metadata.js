"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gcp-metadata";
exports.ids = ["vendor-chunks/gcp-metadata"];
exports.modules = {

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js":
/*!**************************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/gcp-residency.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.detectGCPResidency = exports.isGoogleComputeEngine = exports.isGoogleComputeEngineMACAddress = exports.isGoogleComputeEngineLinux = exports.isGoogleCloudServerless = exports.GCE_LINUX_BIOS_PATHS = void 0;\nconst fs_1 = __webpack_require__(/*! fs */ \"fs\");\nconst os_1 = __webpack_require__(/*! os */ \"os\");\n/**\n * Known paths unique to Google Compute Engine Linux instances\n */\nexports.GCE_LINUX_BIOS_PATHS = {\n    BIOS_DATE: '/sys/class/dmi/id/bios_date',\n    BIOS_VENDOR: '/sys/class/dmi/id/bios_vendor',\n};\nconst GCE_MAC_ADDRESS_REGEX = /^42:01/;\n/**\n * Determines if the process is running on a Google Cloud Serverless environment (Cloud Run or Cloud Functions instance).\n *\n * Uses the:\n * - {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n * - {@link https://cloud.google.com/functions/docs/env-var Cloud Functions environment variables}.\n *\n * @returns {boolean} `true` if the process is running on GCP serverless, `false` otherwise.\n */\nfunction isGoogleCloudServerless() {\n    /**\n     * `CLOUD_RUN_JOB` is used for Cloud Run Jobs\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     *\n     * `FUNCTION_NAME` is used in older Cloud Functions environments:\n     * - See {@link https://cloud.google.com/functions/docs/env-var Python 3.7 and Go 1.11}.\n     *\n     * `K_SERVICE` is used in Cloud Run and newer Cloud Functions environments:\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     * - See {@link https://cloud.google.com/functions/docs/env-var Cloud Functions newer runtimes}.\n     */\n    const isGFEnvironment = process.env.CLOUD_RUN_JOB ||\n        process.env.FUNCTION_NAME ||\n        process.env.K_SERVICE;\n    return !!isGFEnvironment;\n}\nexports.isGoogleCloudServerless = isGoogleCloudServerless;\n/**\n * Determines if the process is running on a Linux Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on Linux GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngineLinux() {\n    if ((0, os_1.platform)() !== 'linux')\n        return false;\n    try {\n        // ensure this file exist\n        (0, fs_1.statSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_DATE);\n        // ensure this file exist and matches\n        const biosVendor = (0, fs_1.readFileSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_VENDOR, 'utf8');\n        return /Google/.test(biosVendor);\n    }\n    catch (_a) {\n        return false;\n    }\n}\nexports.isGoogleComputeEngineLinux = isGoogleComputeEngineLinux;\n/**\n * Determines if the process is running on a Google Compute Engine instance with a known\n * MAC address.\n *\n * @returns {boolean} `true` if the process is running on GCE (as determined by MAC address), `false` otherwise.\n */\nfunction isGoogleComputeEngineMACAddress() {\n    const interfaces = (0, os_1.networkInterfaces)();\n    for (const item of Object.values(interfaces)) {\n        if (!item)\n            continue;\n        for (const { mac } of item) {\n            if (GCE_MAC_ADDRESS_REGEX.test(mac)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nexports.isGoogleComputeEngineMACAddress = isGoogleComputeEngineMACAddress;\n/**\n * Determines if the process is running on a Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngine() {\n    return isGoogleComputeEngineLinux() || isGoogleComputeEngineMACAddress();\n}\nexports.isGoogleComputeEngine = isGoogleComputeEngine;\n/**\n * Determines if the process is running on Google Cloud Platform.\n *\n * @returns {boolean} `true` if the process is running on GCP, `false` otherwise.\n */\nfunction detectGCPResidency() {\n    return isGoogleCloudServerless() || isGoogleComputeEngine();\n}\nexports.detectGCPResidency = detectGCPResidency;\n//# sourceMappingURL=gcp-residency.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/index.js":
/*!******************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.requestTimeout = exports.setGCPResidency = exports.getGCPResidency = exports.gcpResidencyCache = exports.resetIsAvailableCache = exports.isAvailable = exports.project = exports.instance = exports.METADATA_SERVER_DETECTION = exports.HEADERS = exports.HEADER_VALUE = exports.HEADER_NAME = exports.SECONDARY_HOST_ADDRESS = exports.HOST_ADDRESS = exports.BASE_PATH = void 0;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nconst jsonBigint = __webpack_require__(/*! json-bigint */ \"(rsc)/./node_modules/json-bigint/index.js\");\nconst gcp_residency_1 = __webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\");\nexports.BASE_PATH = '/computeMetadata/v1';\nexports.HOST_ADDRESS = 'http://***************';\nexports.SECONDARY_HOST_ADDRESS = 'http://metadata.google.internal.';\nexports.HEADER_NAME = 'Metadata-Flavor';\nexports.HEADER_VALUE = 'Google';\nexports.HEADERS = Object.freeze({ [exports.HEADER_NAME]: exports.HEADER_VALUE });\n/**\n * Metadata server detection override options.\n *\n * Available via `process.env.METADATA_SERVER_DETECTION`.\n */\nexports.METADATA_SERVER_DETECTION = Object.freeze({\n    'assume-present': \"don't try to ping the metadata server, but assume it's present\",\n    none: \"don't try to ping the metadata server, but don't try to use it either\",\n    'bios-only': \"treat the result of a BIOS probe as canonical (don't fall back to pinging)\",\n    'ping-only': 'skip the BIOS probe, and go straight to pinging',\n});\n/**\n * Returns the base URL while taking into account the GCE_METADATA_HOST\n * environment variable if it exists.\n *\n * @returns The base URL, e.g., http://***************/computeMetadata/v1.\n */\nfunction getBaseUrl(baseUrl) {\n    if (!baseUrl) {\n        baseUrl =\n            process.env.GCE_METADATA_IP ||\n                process.env.GCE_METADATA_HOST ||\n                exports.HOST_ADDRESS;\n    }\n    // If no scheme is provided default to HTTP:\n    if (!/^https?:\\/\\//.test(baseUrl)) {\n        baseUrl = `http://${baseUrl}`;\n    }\n    return new URL(exports.BASE_PATH, baseUrl).href;\n}\n// Accepts an options object passed from the user to the API. In previous\n// versions of the API, it referred to a `Request` or an `Axios` request\n// options object.  Now it refers to an object with very limited property\n// names. This is here to help ensure users don't pass invalid options when\n// they  upgrade from 0.4 to 0.5 to 0.8.\nfunction validate(options) {\n    Object.keys(options).forEach(key => {\n        switch (key) {\n            case 'params':\n            case 'property':\n            case 'headers':\n                break;\n            case 'qs':\n                throw new Error(\"'qs' is not a valid configuration option. Please use 'params' instead.\");\n            default:\n                throw new Error(`'${key}' is not a valid configuration option.`);\n        }\n    });\n}\nasync function metadataAccessor(type, options, noResponseRetries = 3, fastFail = false) {\n    options = options || {};\n    if (typeof options === 'string') {\n        options = { property: options };\n    }\n    let property = '';\n    if (typeof options === 'object' && options.property) {\n        property = '/' + options.property;\n    }\n    validate(options);\n    try {\n        const requestMethod = fastFail ? fastFailMetadataRequest : gaxios_1.request;\n        const res = await requestMethod({\n            url: `${getBaseUrl()}/${type}${property}`,\n            headers: Object.assign({}, exports.HEADERS, options.headers),\n            retryConfig: { noResponseRetries },\n            params: options.params,\n            responseType: 'text',\n            timeout: requestTimeout(),\n        });\n        // NOTE: node.js converts all incoming headers to lower case.\n        if (res.headers[exports.HEADER_NAME.toLowerCase()] !== exports.HEADER_VALUE) {\n            throw new Error(`Invalid response from metadata service: incorrect ${exports.HEADER_NAME} header.`);\n        }\n        else if (!res.data) {\n            throw new Error('Invalid response from the metadata service');\n        }\n        if (typeof res.data === 'string') {\n            try {\n                return jsonBigint.parse(res.data);\n            }\n            catch (_a) {\n                /* ignore */\n            }\n        }\n        return res.data;\n    }\n    catch (e) {\n        const err = e;\n        if (err.response && err.response.status !== 200) {\n            err.message = `Unsuccessful response status code. ${err.message}`;\n        }\n        throw e;\n    }\n}\nasync function fastFailMetadataRequest(options) {\n    const secondaryOptions = {\n        ...options,\n        url: options.url.replace(getBaseUrl(), getBaseUrl(exports.SECONDARY_HOST_ADDRESS)),\n    };\n    // We race a connection between DNS/IP to metadata server. There are a couple\n    // reasons for this:\n    //\n    // 1. the DNS is slow in some GCP environments; by checking both, we might\n    //    detect the runtime environment signficantly faster.\n    // 2. we can't just check the IP, which is tarpitted and slow to respond\n    //    on a user's local machine.\n    //\n    // Additional logic has been added to make sure that we don't create an\n    // unhandled rejection in scenarios where a failure happens sometime\n    // after a success.\n    //\n    // Note, however, if a failure happens prior to a success, a rejection should\n    // occur, this is for folks running locally.\n    //\n    let responded = false;\n    const r1 = (0, gaxios_1.request)(options)\n        .then(res => {\n        responded = true;\n        return res;\n    })\n        .catch(err => {\n        if (responded) {\n            return r2;\n        }\n        else {\n            responded = true;\n            throw err;\n        }\n    });\n    const r2 = (0, gaxios_1.request)(secondaryOptions)\n        .then(res => {\n        responded = true;\n        return res;\n    })\n        .catch(err => {\n        if (responded) {\n            return r1;\n        }\n        else {\n            responded = true;\n            throw err;\n        }\n    });\n    return Promise.race([r1, r2]);\n}\n/**\n * Obtain metadata for the current GCE instance\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction instance(options) {\n    return metadataAccessor('instance', options);\n}\nexports.instance = instance;\n/**\n * Obtain metadata for the current GCP Project.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction project(options) {\n    return metadataAccessor('project', options);\n}\nexports.project = project;\n/*\n * How many times should we retry detecting GCP environment.\n */\nfunction detectGCPAvailableRetries() {\n    return process.env.DETECT_GCP_RETRIES\n        ? Number(process.env.DETECT_GCP_RETRIES)\n        : 0;\n}\nlet cachedIsAvailableResponse;\n/**\n * Determine if the metadata server is currently available.\n */\nasync function isAvailable() {\n    if (process.env.METADATA_SERVER_DETECTION) {\n        const value = process.env.METADATA_SERVER_DETECTION.trim().toLocaleLowerCase();\n        if (!(value in exports.METADATA_SERVER_DETECTION)) {\n            throw new RangeError(`Unknown \\`METADATA_SERVER_DETECTION\\` env variable. Got \\`${value}\\`, but it should be \\`${Object.keys(exports.METADATA_SERVER_DETECTION).join('`, `')}\\`, or unset`);\n        }\n        switch (value) {\n            case 'assume-present':\n                return true;\n            case 'none':\n                return false;\n            case 'bios-only':\n                return getGCPResidency();\n            case 'ping-only':\n            // continue, we want to ping the server\n        }\n    }\n    try {\n        // If a user is instantiating several GCP libraries at the same time,\n        // this may result in multiple calls to isAvailable(), to detect the\n        // runtime environment. We use the same promise for each of these calls\n        // to reduce the network load.\n        if (cachedIsAvailableResponse === undefined) {\n            cachedIsAvailableResponse = metadataAccessor('instance', undefined, detectGCPAvailableRetries(), \n            // If the default HOST_ADDRESS has been overridden, we should not\n            // make an effort to try SECONDARY_HOST_ADDRESS (as we are likely in\n            // a non-GCP environment):\n            !(process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST));\n        }\n        await cachedIsAvailableResponse;\n        return true;\n    }\n    catch (e) {\n        const err = e;\n        if (process.env.DEBUG_AUTH) {\n            console.info(err);\n        }\n        if (err.type === 'request-timeout') {\n            // If running in a GCP environment, metadata endpoint should return\n            // within ms.\n            return false;\n        }\n        if (err.response && err.response.status === 404) {\n            return false;\n        }\n        else {\n            if (!(err.response && err.response.status === 404) &&\n                // A warning is emitted if we see an unexpected err.code, or err.code\n                // is not populated:\n                (!err.code ||\n                    ![\n                        'EHOSTDOWN',\n                        'EHOSTUNREACH',\n                        'ENETUNREACH',\n                        'ENOENT',\n                        'ENOTFOUND',\n                        'ECONNREFUSED',\n                    ].includes(err.code))) {\n                let code = 'UNKNOWN';\n                if (err.code)\n                    code = err.code;\n                process.emitWarning(`received unexpected error = ${err.message} code = ${code}`, 'MetadataLookupWarning');\n            }\n            // Failure to resolve the metadata service means that it is not available.\n            return false;\n        }\n    }\n}\nexports.isAvailable = isAvailable;\n/**\n * reset the memoized isAvailable() lookup.\n */\nfunction resetIsAvailableCache() {\n    cachedIsAvailableResponse = undefined;\n}\nexports.resetIsAvailableCache = resetIsAvailableCache;\n/**\n * A cache for the detected GCP Residency.\n */\nexports.gcpResidencyCache = null;\n/**\n * Detects GCP Residency.\n * Caches results to reduce costs for subsequent calls.\n *\n * @see setGCPResidency for setting\n */\nfunction getGCPResidency() {\n    if (exports.gcpResidencyCache === null) {\n        setGCPResidency();\n    }\n    return exports.gcpResidencyCache;\n}\nexports.getGCPResidency = getGCPResidency;\n/**\n * Sets the detected GCP Residency.\n * Useful for forcing metadata server detection behavior.\n *\n * Set `null` to autodetect the environment (default behavior).\n * @see getGCPResidency for getting\n */\nfunction setGCPResidency(value = null) {\n    exports.gcpResidencyCache = value !== null ? value : (0, gcp_residency_1.detectGCPResidency)();\n}\nexports.setGCPResidency = setGCPResidency;\n/**\n * Obtain the timeout for requests to the metadata server.\n *\n * In certain environments and conditions requests can take longer than\n * the default timeout to complete. This function will determine the\n * appropriate timeout based on the environment.\n *\n * @returns {number} a request timeout duration in milliseconds.\n */\nfunction requestTimeout() {\n    return getGCPResidency() ? 0 : 3000;\n}\nexports.requestTimeout = requestTimeout;\n__exportStar(__webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/index.js\n");

/***/ })

};
;