import { lexicographicSortSchema, printSchema } from "graphql";
import { outputFile, outputFileSync } from "../helpers/filesystem.js";
export const defaultPrintSchemaOptions = {
    sortedSchema: true,
};
const generatedSchemaWarning = `\
# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

`;
function getSchemaFileContent(schema, options) {
    const schemaToEmit = options.sortedSchema ? lexicographicSortSchema(schema) : schema;
    return generatedSchemaWarning + printSchema(schemaToEmit);
}
export function emitSchemaDefinitionFileSync(schemaFilePath, schema, options = defaultPrintSchemaOptions) {
    const schemaFileContent = getSchemaFileContent(schema, options);
    outputFileSync(schemaFilePath, schemaFileContent);
}
export async function emitSchemaDefinitionFile(schemaFilePath, schema, options = defaultPrintSchemaOptions) {
    const schemaFileContent = getSchemaFileContent(schema, options);
    await outputFile(schemaFilePath, schemaFileContent);
}
