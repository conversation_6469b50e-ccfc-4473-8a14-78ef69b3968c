/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBNEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHppbm5pYXgtY29waWxvdFxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcdWlcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBNEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHppbm5pYXgtY29waWxvdFxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcdWlcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5C%40copilotkit%5C%5Creact-core%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22CopilotKit%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5C%40copilotkit%5C%5Creact-core%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22CopilotKit%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@copilotkit/react-core/dist/index.mjs */ \"(rsc)/./node_modules/@copilotkit/react-core/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MGNvcGlsb3RraXQlNUMlNUNyZWFjdC1jb3JlJTVDJTVDZGlzdCU1QyU1Q2luZGV4Lm1qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNvcGlsb3RLaXQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3ppbm5pYXgtY29waWxvdCU1QyU1Q3ppbm5pYXgtY29waWxvdCU1QyU1Q3VpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTkFBcUsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNvcGlsb3RLaXRcIl0gKi8gXCJEOlxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcemlubmlheC1jb3BpbG90XFxcXHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxAY29waWxvdGtpdFxcXFxyZWFjdC1jb3JlXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5C%40copilotkit%5C%5Creact-core%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22CopilotKit%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5C%40copilotkit%5C%5Creact-core%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22CopilotKit%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5C%40copilotkit%5C%5Creact-core%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22CopilotKit%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@copilotkit/react-core/dist/index.mjs */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MGNvcGlsb3RraXQlNUMlNUNyZWFjdC1jb3JlJTVDJTVDZGlzdCU1QyU1Q2luZGV4Lm1qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNvcGlsb3RLaXQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3ppbm5pYXgtY29waWxvdCU1QyU1Q3ppbm5pYXgtY29waWxvdCU1QyU1Q3VpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTkFBcUsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNvcGlsb3RLaXRcIl0gKi8gXCJEOlxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcemlubmlheC1jb3BpbG90XFxcXHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxAY29waWxvdGtpdFxcXFxyZWFjdC1jb3JlXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5C%40copilotkit%5C%5Creact-core%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22CopilotKit%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3ppbm5pYXgtY29waWxvdCU1QyU1Q3ppbm5pYXgtY29waWxvdCU1QyU1Q3VpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDemlubmlheC1jb3BpbG90JTVDJTVDemlubmlheC1jb3BpbG90JTVDJTVDdWklNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN6aW5uaWF4LWNvcGlsb3QlNUMlNUN1aSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDbGliJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTJJO0FBQzNJO0FBQ0EsME9BQThJO0FBQzlJO0FBQ0EsME9BQThJO0FBQzlJO0FBQ0Esb1JBQW9LO0FBQ3BLO0FBQ0Esd09BQTZJO0FBQzdJO0FBQ0Esc1FBQTRKO0FBQzVKO0FBQ0Esc09BQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcemlubmlheC1jb3BpbG90XFxcXHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHppbm5pYXgtY29waWxvdFxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcdWlcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcemlubmlheC1jb3BpbG90XFxcXHppbm5pYXgtY29waWxvdFxcXFx1aVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcemlubmlheC1jb3BpbG90XFxcXHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHppbm5pYXgtY29waWxvdFxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcdWlcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFx6aW5uaWF4LWNvcGlsb3RcXFxcemlubmlheC1jb3BpbG90XFxcXHVpXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcemlubmlheC1jb3BpbG90XFxcXHppbm5pYXgtY29waWxvdFxcXFx1aVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGxpYlxcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Czinniax-copilot%5C%5Czinniax-copilot%5C%5Cui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/helper.ts":
/*!***********************!*\
  !*** ./app/helper.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64UrlDecode: () => (/* binding */ base64UrlDecode),\n/* harmony export */   decodeJwt: () => (/* binding */ decodeJwt)\n/* harmony export */ });\nfunction base64UrlDecode(input) {\n    // Convert from base64url to base64\n    let base64 = input.replace(/-/g, '+').replace(/_/g, '/');\n    // Pad with '=' characters\n    while(base64.length % 4 !== 0){\n        base64 += '=';\n    }\n    // Decode base64 string\n    const decoded = atob(base64);\n    return decoded;\n}\nfunction decodeJwt(token) {\n    try {\n        const [headerB64, payloadB64] = token.split('.');\n        if (!headerB64 || !payloadB64) {\n            throw new Error(\"Invalid token format\");\n        }\n        const header = JSON.parse(base64UrlDecode(headerB64));\n        const payload = JSON.parse(base64UrlDecode(payloadB64));\n        if (header.alg !== 'HS512') {\n            console.warn(`Unexpected algorithm: ${header.alg}`);\n        }\n        return {\n            header,\n            payload\n        };\n    } catch (err) {\n        console.error('Failed to decode JWT:', err);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/helper.ts\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _copilotkit_react_ui_styles_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/react-ui/styles.css */ \"(ssr)/./node_modules/@copilotkit/react-ui/dist/index.css\");\n/* harmony import */ var _copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @copilotkit/react-ui */ \"(ssr)/./node_modules/@copilotkit/react-ui/dist/chunk-RT4HE74K.mjs\");\n/* harmony import */ var _components_ChatArea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ChatArea */ \"(ssr)/./components/ChatArea.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helper */ \"(ssr)/./app/helper.ts\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-4DVPRMVH.mjs\");\n// page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst prompts = [\n    \"Cancel the case for Dr.Anderson Richard\",\n    \"Reschedule case 106258\",\n    \"Schedule a case for Dr.Davis Lucas\",\n    \"Reopen case 106188\",\n    \"Dr. Johnson has a spine surgery rescheduled\"\n];\nfunction PromptButtons({ onUserInteracted, animateOut, onAnimationEnd }) {\n    const { appendMessage } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCopilotChat)();\n    function handlePromptClick(prompt) {\n        appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.TextMessage({\n            content: prompt,\n            role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.Role.User\n        }));\n        onUserInteracted();\n    }\n    const buttonClass = \"rounded-full px-4 py-3 text-blue-700 shadow-sm border-[1.5px] border-blue-300 transition-all duration-200 max-w-xs\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col gap-3 items-center transition-opacity duration-500 ${animateOut ? 'opacity-0 pointer-events-none' : 'opacity-100'}`,\n        onTransitionEnd: onAnimationEnd,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap gap-3 justify-center\",\n            children: prompts.map((prompt, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: buttonClass,\n                    onClick: ()=>handlePromptClick(prompt),\n                    children: prompt\n                }, idx, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction Greeting({ animateOut, onAnimationEnd }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col items-center text-center transition-opacity duration-500 ${animateOut ? 'opacity-0 pointer-events-none' : 'opacity-100'}`,\n        onTransitionEnd: onAnimationEnd,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-4xl  font-bold text-blue-600 text-center\",\n                children: \"Hello there \\uD83D\\uDC4B\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl text-gray-700 mt-2 mb-3\",\n                children: \"How can I assist you today ?\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction Home() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userInteracted, setUserInteracted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hideSuggestions, setHideSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [suggestionAnimationDone, setSuggestionAnimationDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const copilotChatRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Listen for manual chat input to trigger suggestion removal\n    const { appendMessage } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCopilotChat)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Patch CopilotChat input to detect manual user message\n            const handler = {\n                \"Home.useEffect.handler\": (e)=>{\n                    if (!userInteracted && e?.detail?.role === 'user') {\n                        setUserInteracted(true);\n                    }\n                }\n            }[\"Home.useEffect.handler\"];\n            window.addEventListener('copilotkit-user-message', handler);\n            return ({\n                \"Home.useEffect\": ()=>window.removeEventListener('copilotkit-user-message', handler)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userInteracted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const observer = new MutationObserver({\n                \"Home.useEffect\": ()=>{\n                    const inputEl = document.querySelector('.copilotKitInputContainer textarea');\n                    if (inputEl) {\n                        const handleInput = {\n                            \"Home.useEffect.handleInput\": ()=>{\n                                if (!userInteracted) {\n                                    setUserInteracted(true);\n                                    const customEvent = new CustomEvent(\"copilotkit-user-message\", {\n                                        detail: {\n                                            role: 'user'\n                                        }\n                                    });\n                                    window.dispatchEvent(customEvent);\n                                }\n                            }\n                        }[\"Home.useEffect.handleInput\"];\n                        inputEl.addEventListener('keydown', handleInput);\n                        observer.disconnect(); // stop observing once found\n                    }\n                }\n            }[\"Home.useEffect\"]);\n            observer.observe(document.body, {\n                childList: true,\n                subtree: true\n            });\n            return ({\n                \"Home.useEffect\": ()=>observer.disconnect()\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userInteracted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const REMOTE_ACTION_URL = 'https://stage.ionm.copilot.zinniax.com';\n            // const REMOTE_ACTION_URL = 'http://localhost:8001'\n            console.log(\"NEXT_PUBLIC_REMOTE_ACTION_URL\", REMOTE_ACTION_URL);\n            // --- Session ID Management ---\n            let sessionId = localStorage.getItem(\"session_id\");\n            if (!sessionId) {\n                sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n                localStorage.setItem(\"session_id\", sessionId);\n                document.cookie = `session_id=${sessionId}; path=/;`;\n            }\n            const params = new URLSearchParams(window.location.search);\n            window.history.replaceState({}, document.title, \"/\");\n            const token = params.get(\"token\");\n            if (!token) {\n                window.location.href = \"https://stage.ionm.zinniax.com/\";\n                setLoading(false);\n                return;\n            }\n            if (token) {\n                const result = (0,_helper__WEBPACK_IMPORTED_MODULE_4__.decodeJwt)(token);\n                if (result) {\n                    const email = result.payload['sub'];\n                    if (email) {\n                        let username = email.split('@')[0];\n                        let words = username.replace(/\\./g, ' ').split(' ');\n                        username = words.map({\n                            \"Home.useEffect\": (word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()\n                        }[\"Home.useEffect\"]).join(' ');\n                        localStorage.setItem(\"copilotkit_username\", username);\n                        setUsername(username);\n                    }\n                }\n            }\n            if (token) {\n                localStorage.setItem(\"copilotkit_token\", token);\n                const link = REMOTE_ACTION_URL + '/api/store-token';\n                fetch(link, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        token,\n                        sessionId\n                    }),\n                    credentials: \"include\"\n                }).then({\n                    \"Home.useEffect\": ()=>setLoading(false)\n                }[\"Home.useEffect\"]);\n            }\n            const storedUsername = localStorage.getItem(\"copilotkit_username\");\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"Home.useEffect\"], []);\n    // When user interacts, start fade-out\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (userInteracted) setHideSuggestions(true);\n        }\n    }[\"Home.useEffect\"], [\n        userInteracted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    const footer = document.querySelector(\".copilotKitInputContainer .poweredBy\");\n                    const container = document.querySelector(\".copilotKitInputContainer\");\n                    if (footer && container) {\n                        // Prevent adding multiple overlays\n                        if (!container.querySelector(\".poweredByCover\")) {\n                            const overlay = document.createElement(\"div\");\n                            overlay.className = \"poweredByCover\";\n                            Object.assign(overlay.style, {\n                                position: \"absolute\",\n                                top: \"0\",\n                                left: \"0\",\n                                right: \"0\",\n                                bottom: \"0\",\n                                background: \"white\",\n                                zIndex: \"2\"\n                            });\n                            const wrapper = document.createElement(\"div\");\n                            wrapper.style.position = \"relative\";\n                            wrapper.appendChild(overlay);\n                            footer.parentElement?.insertBefore(wrapper, footer);\n                            wrapper.appendChild(footer);\n                        }\n                        clearInterval(interval); // once done, stop checking\n                    }\n                }\n            }[\"Home.useEffect.interval\"], 300); // Wait for CopilotChat to fully mount\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    // When fade-out animation ends, unmount suggestions\n    function handleSuggestionAnimationEnd() {\n        if (hideSuggestions) setSuggestionAnimationDone(true);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-10 w-10 border-t-4 border-blue-500 border-solid\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-600 text-sm\",\n                    children: \"Verifying session, please wait...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen w-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: \"/logo.png\",\n                alt: \"ZinniaX Logo\",\n                className: \"fixed top-4 left-4 h-10 w-auto \"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center pt-8 pb-2 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-blue-600 text-center\",\n                            children: \"ZinniaX Copilot\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-md text-gray-500 mb-2 text-center\",\n                        children: \"Your Everyday Copilot\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    !suggestionAnimationDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex flex-col items-center justify-center gap-4 px-4\",\n                        style: {\n                            paddingTop: '150px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Greeting, {\n                                animateOut: hideSuggestions,\n                                onAnimationEnd: handleSuggestionAnimationEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PromptButtons, {\n                                onUserInteracted: ()=>setUserInteracted(true),\n                                animateOut: hideSuggestions,\n                                onAnimationEnd: handleSuggestionAnimationEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex flex-col min-h-0 overflow-hidden items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-1 min-h-0 overflow-hidden w-full max-w-4xl\",\n                    style: {\n                        width: '60vw',\n                        minWidth: 320\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_8__.CopilotChat, {\n                            labels: {\n                                title: \"ZinniaX Copilot\",\n                                initial: \"\",\n                                placeholder: \"Ask Zinniax Copilot....\"\n                            },\n                            className: \"flex flex-col flex-1 min-h-0 overflow-hidden copilot-chat-with-suggestions\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/CaseListDisplay.tsx":
/*!****************************************!*\
  !*** ./components/CaseListDisplay.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst CaseListDisplay = ({ items, onSubmit, disabled, selectedIdxs })=>{\n    const [selectedIndices, setSelectedIndices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedIdxs.length > 0 ? selectedIdxs[0] : null);\n    const toggleSelection = (idx)=>{\n        if (disabled) return;\n        setSelectedIndex(idx);\n    };\n    const handleSubmit = ()=>{\n        if (disabled || !onSubmit || selectedIndex === null) return;\n        const selectedItem = items[selectedIndex];\n        onSubmit([\n            selectedItem\n        ]);\n    };\n    if (!items || items.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"No cases to display.\"\n        }, void 0, false, {\n            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\CaseListDisplay.tsx\",\n            lineNumber: 28,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: 24,\n            background: \"#fff\",\n            borderRadius: 16,\n            boxShadow: \"0 4px 24px rgba(0,0,0,0.09)\",\n            maxWidth: 340,\n            border: \"1px solid #e5e7eb\",\n            opacity: disabled ? 0.6 : 1,\n            pointerEvents: disabled ? \"none\" : \"auto\"\n        },\n        \"aria-disabled\": disabled,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                style: {\n                    fontSize: 20,\n                    fontWeight: 600,\n                    marginBottom: 12,\n                    color: \"#1e293b\",\n                    letterSpacing: 0.2\n                },\n                children: \"Select Case\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\CaseListDisplay.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                style: {\n                    paddingLeft: 0,\n                    margin: 0,\n                    maxHeight: 200,\n                    overflowY: \"auto\"\n                },\n                children: items.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        onClick: ()=>toggleSelection(idx),\n                        style: {\n                            margin: \"10px 0\",\n                            padding: \"8px 12px\",\n                            background: \"#f3f4f6\",\n                            borderRadius: 8,\n                            listStyle: \"none\",\n                            fontSize: 16,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 8,\n                            cursor: \"pointer\",\n                            fontWeight: selectedIndex === idx ? 600 : 400,\n                            boxShadow: selectedIndex === idx ? \"0 2px 8px rgba(56,189,248,0.15)\" : undefined\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                checked: selectedIndex === idx,\n                                onChange: ()=>toggleSelection(idx),\n                                onClick: (e)=>e.stopPropagation(),\n                                disabled: disabled\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\CaseListDisplay.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined),\n                            item\n                        ]\n                    }, idx, true, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\CaseListDisplay.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\CaseListDisplay.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSubmit,\n                style: {\n                    marginTop: 16,\n                    padding: \"8px 12px\",\n                    backgroundColor: \"#38bdf8\",\n                    border: \"none\",\n                    borderRadius: 8,\n                    color: \"#fff\",\n                    fontWeight: 600,\n                    cursor: disabled ? \"not-allowed\" : \"pointer\",\n                    width: \"100%\"\n                },\n                disabled: disabled,\n                children: \"Submit Selected\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\CaseListDisplay.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\CaseListDisplay.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CaseListDisplay);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/CaseListDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ChatArea.tsx":
/*!*********************************!*\
  !*** ./components/ChatArea.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatArea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-4DVPRMVH.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-B5UA5G3E.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CaseListDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CaseListDisplay */ \"(ssr)/./components/CaseListDisplay.tsx\");\n/* harmony import */ var _static_ConfirmationButtons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./static/ConfirmationButtons */ \"(ssr)/./components/static/ConfirmationButtons.tsx\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _HospitalListDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HospitalListDisplay */ \"(ssr)/./components/HospitalListDisplay.tsx\");\n/* harmony import */ var _DoctorListDisplay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./DoctorListDisplay */ \"(ssr)/./components/DoctorListDisplay.tsx\");\n\n\n\n\n\n\n\n\n\nfunction ChatArea() {\n    const [selectedCases, setSelectedCases] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedHospitals, setSelectedHospitals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDoctors, setSelectedDoctors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedItems, setProcessedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const { appendMessage } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__.useCopilotChat)();\n    async function handleCancelCases(selected, caseKey) {\n        try {\n            const caseIds = selected.map((caseStr)=>caseStr.split(\" - \").pop()?.trim()).filter(Boolean);\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: `${caseIds}`,\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n            setSelectedCases([]);\n            setProcessedItems((prev)=>new Set([\n                    ...prev,\n                    caseKey\n                ]));\n        } catch (err) {\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: `Error: ${err.message}`,\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n        }\n    }\n    async function handleSelectedHospitals(selected, hospitalKey) {\n        try {\n            const selectedHospital = selected[0];\n            const displayMessage = `hospitalId=${selectedHospital.id} \\ncity=${selectedHospital.city}`;\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: displayMessage,\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n            setSelectedHospitals(selected.map((item)=>item.id));\n            setProcessedItems((prev)=>new Set([\n                    ...prev,\n                    hospitalKey\n                ]));\n        } catch (err) {\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: `Error: ${err.message}`,\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n        }\n    }\n    async function handleSelectedDoctor(selected, DoctorKey) {\n        try {\n            const selectedDoctor = selected[0];\n            const displayMessage = `doctorId = ${selectedDoctor.id} \\nDoctor name = ${selectedDoctor.name}`;\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: displayMessage,\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n            setSelectedDoctors(selected.map((item)=>item.id));\n            setProcessedItems((prev)=>new Set([\n                    ...prev,\n                    DoctorKey\n                ]));\n        } catch (err) {\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: `Error: ${err.message}`,\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n        }\n    }\n    const rendered = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_8__.useCoAgentStateRender)({\n        name: \"scheduling_agent\",\n        render: {\n            \"ChatArea.useCoAgentStateRender[rendered]\": ({ state, status })=>{\n                if (status === \"inProgress\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 43\n                }, this);\n                // Create unique keys for current items\n                const hospitalKey = state.showHospitals ? `hospitals_${state.showHospitals.map({\n                    \"ChatArea.useCoAgentStateRender[rendered]\": (h)=>h.id\n                }[\"ChatArea.useCoAgentStateRender[rendered]\"]).join('_')}` : '';\n                const caseKey = state.showCase ? `cases_${state.showCase.map({\n                    \"ChatArea.useCoAgentStateRender[rendered]\": (c)=>c.caseNo\n                }[\"ChatArea.useCoAgentStateRender[rendered]\"]).join('_')}` : '';\n                const confirmationKey = state.confirmation_needed ? `confirmation_${state.confirmation_message}` : '';\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        state.frontend_tool == \"showHospitals\" && state.showHospitals?.length ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown, {\n                                    children: state.confirmation_message || \"Please select the hospital.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HospitalListDisplay__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    items: state.showHospitals,\n                                    selectedIdxs: selectedHospitals.map({\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (id)=>state.showHospitals.findIndex({\n                                                \"ChatArea.useCoAgentStateRender[rendered]\": (item)=>item.id === id\n                                            }[\"ChatArea.useCoAgentStateRender[rendered]\"])\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"]),\n                                    onSubmit: {\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (selected)=>handleSelectedHospitals(selected, hospitalKey)\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                                    disabled: processedItems.has(hospitalKey)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this) : null,\n                        state.frontend_tool == \"showDoctors\" && state.showDoctors?.length ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown, {\n                                    children: state.confirmation_message || \"Please select the hospital.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DoctorListDisplay__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    items: state.showDoctors,\n                                    selectedIdxs: selectedDoctors.map({\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (id)=>state.showDoctors.findIndex({\n                                                \"ChatArea.useCoAgentStateRender[rendered]\": (item)=>item.id === id\n                                            }[\"ChatArea.useCoAgentStateRender[rendered]\"])\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"]),\n                                    onSubmit: {\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (selected)=>handleSelectedDoctor(selected, hospitalKey)\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                                    disabled: processedItems.has(hospitalKey)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this) : null,\n                        state.frontend_tool == \"showCase\" && state.showCase ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CaseListDisplay__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            items: state.showCase.map({\n                                \"ChatArea.useCoAgentStateRender[rendered]\": (item)=>`${item.doctorName} - ${item.caseNo}`\n                            }[\"ChatArea.useCoAgentStateRender[rendered]\"]),\n                            selectedIdxs: selectedCases.map({\n                                \"ChatArea.useCoAgentStateRender[rendered]\": (caseStr)=>state.showCase.findIndex({\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (item)=>`${item.doctorName} - ${item.caseNo}` === caseStr\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"])\n                            }[\"ChatArea.useCoAgentStateRender[rendered]\"]),\n                            onSubmit: {\n                                \"ChatArea.useCoAgentStateRender[rendered]\": async (selected)=>{\n                                    setSelectedCases(selected);\n                                    await handleCancelCases(selected, caseKey);\n                                }\n                            }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                            disabled: processedItems.has(caseKey)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this) : null,\n                        state.frontend_tool == \"confirmation\" && state.confirmation_needed == true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: 24\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown, {\n                                    children: state.confirmation_message\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_static_ConfirmationButtons__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onYes: {\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": ()=>{\n                                            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                                                content: \"yes\",\n                                                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n                                            }));\n                                            setProcessedItems({\n                                                \"ChatArea.useCoAgentStateRender[rendered]\": (prev)=>new Set([\n                                                        ...prev,\n                                                        confirmationKey\n                                                    ])\n                                            }[\"ChatArea.useCoAgentStateRender[rendered]\"]);\n                                        }\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                                    onNo: {\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": ()=>{\n                                            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                                                content: \"no\",\n                                                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n                                            }));\n                                            setProcessedItems({\n                                                \"ChatArea.useCoAgentStateRender[rendered]\": (prev)=>new Set([\n                                                        ...prev,\n                                                        confirmationKey\n                                                    ])\n                                            }[\"ChatArea.useCoAgentStateRender[rendered]\"]);\n                                        }\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                                    disabled: processedItems.has(confirmationKey)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this);\n            }\n        }[\"ChatArea.useCoAgentStateRender[rendered]\"]\n    });\n    return rendered ?? null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ChatArea.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DoctorListDisplay.tsx":
/*!******************************************!*\
  !*** ./components/DoctorListDisplay.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst DoctorListDisplay = ({ items, onSubmit, disabled, selectedIdxs = [] })=>{\n    const [selectedIndices, setSelectedIndices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedIdxs);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedIdxs.length > 0 ? selectedIdxs[0] : null);\n    const toggleSelection = (idx)=>{\n        if (disabled) return;\n        setSelectedIndex(idx);\n    };\n    const handleSubmit = ()=>{\n        if (disabled || !onSubmit || selectedIndex === null) return;\n        const selectedItem = items[selectedIndex];\n        onSubmit([\n            selectedItem\n        ]);\n    };\n    const formatName = (name)=>{\n        // Input: \"Abbott, Ian\"\n        const [lastName, firstName] = name.split(\",\").map((part)=>part.trim());\n        if (!firstName || !lastName) return name; // fallback\n        return `Dr. ${firstName} ${lastName}`;\n    };\n    if (!items || items.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"No Doctors to display.\"\n        }, void 0, false, {\n            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\DoctorListDisplay.tsx\",\n            lineNumber: 46,\n            columnNumber: 16\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: 24,\n            background: \"#fff\",\n            borderRadius: 16,\n            boxShadow: \"0 4px 24px rgba(0,0,0,0.09)\",\n            maxWidth: 400,\n            border: \"1px solid #e5e7eb\",\n            opacity: disabled ? 0.6 : 1,\n            pointerEvents: disabled ? \"none\" : \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                style: {\n                    fontSize: 20,\n                    fontWeight: 600,\n                    marginBottom: 12\n                },\n                children: \"Select Doctor\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\DoctorListDisplay.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                style: {\n                    paddingLeft: 0,\n                    margin: 0,\n                    maxHeight: 240,\n                    overflowY: \"auto\"\n                },\n                children: items.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        onClick: ()=>toggleSelection(idx),\n                        style: {\n                            margin: \"10px 0\",\n                            padding: \"8px 12px\",\n                            background: \"#f3f4f6\",\n                            borderRadius: 8,\n                            listStyle: \"none\",\n                            fontSize: 14,\n                            cursor: \"pointer\",\n                            fontWeight: selectedIndex === idx ? 600 : 400,\n                            boxShadow: selectedIndex === idx ? \"0 2px 8px rgba(56,189,248,0.15)\" : undefined,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                checked: selectedIndex === idx,\n                                onChange: ()=>toggleSelection(idx),\n                                onClick: (e)=>e.stopPropagation(),\n                                disabled: disabled\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\DoctorListDisplay.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: 600,\n                                    marginLeft: 3\n                                },\n                                children: formatName(item.name)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\DoctorListDisplay.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, item.id, true, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\DoctorListDisplay.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 21\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\DoctorListDisplay.tsx\",\n                lineNumber: 66,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSubmit,\n                style: {\n                    marginTop: 16,\n                    padding: \"8px 12px\",\n                    backgroundColor: \"#38bdf8\",\n                    border: \"none\",\n                    borderRadius: 8,\n                    color: \"#fff\",\n                    fontWeight: 600,\n                    cursor: disabled ? \"not-allowed\" : \"pointer\",\n                    width: \"100%\"\n                },\n                disabled: disabled,\n                children: \"Submit Selected\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\DoctorListDisplay.tsx\",\n                lineNumber: 106,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\DoctorListDisplay.tsx\",\n        lineNumber: 50,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DoctorListDisplay);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DoctorListDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./components/HospitalListDisplay.tsx":
/*!********************************************!*\
  !*** ./components/HospitalListDisplay.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst HospitalListDisplay = ({ items, onSubmit, disabled, selectedIdxs = [] })=>{\n    const [selectedIndices, setSelectedIndices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedIdxs);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedIdxs.length > 0 ? selectedIdxs[0] : null);\n    const toggleSelection = (idx)=>{\n        if (disabled) return;\n        setSelectedIndex(idx);\n    };\n    const handleSubmit = ()=>{\n        if (disabled || !onSubmit || selectedIndex === null) return;\n        const selectedItem = items[selectedIndex];\n        onSubmit([\n            selectedItem\n        ]);\n    };\n    if (!items || items.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"No hospitals to display.\"\n        }, void 0, false, {\n            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n            lineNumber: 40,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: 24,\n            background: \"#fff\",\n            borderRadius: 16,\n            boxShadow: \"0 4px 24px rgba(0,0,0,0.09)\",\n            maxWidth: 400,\n            border: \"1px solid #e5e7eb\",\n            opacity: disabled ? 0.6 : 1,\n            pointerEvents: disabled ? \"none\" : \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                style: {\n                    fontSize: 20,\n                    fontWeight: 600,\n                    marginBottom: 12\n                },\n                children: \"Select Hospitals\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                style: {\n                    paddingLeft: 0,\n                    margin: 0,\n                    maxHeight: 240,\n                    overflowY: \"auto\"\n                },\n                children: items.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        onClick: ()=>toggleSelection(idx),\n                        style: {\n                            margin: \"10px 0\",\n                            padding: \"8px 12px\",\n                            background: \"#f3f4f6\",\n                            borderRadius: 8,\n                            listStyle: \"none\",\n                            fontSize: 14,\n                            cursor: \"pointer\",\n                            fontWeight: selectedIndex === idx ? 600 : 400,\n                            boxShadow: selectedIndex === idx ? \"0 2px 8px rgba(56,189,248,0.15)\" : undefined\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                checked: selectedIndex === idx,\n                                onChange: ()=>toggleSelection(idx),\n                                onClick: (e)=>e.stopPropagation(),\n                                disabled: disabled\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginLeft: 8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontWeight: 600\n                                        },\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: 12,\n                                            color: \"#555\"\n                                        },\n                                        children: [\n                                            item.city,\n                                            \" | \",\n                                            item.region\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: 12,\n                                            color: \"#888\"\n                                        },\n                                        children: item.address\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, item.id, true, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSubmit,\n                style: {\n                    marginTop: 16,\n                    padding: \"8px 12px\",\n                    backgroundColor: \"#38bdf8\",\n                    border: \"none\",\n                    borderRadius: 8,\n                    color: \"#fff\",\n                    fontWeight: 600,\n                    cursor: disabled ? \"not-allowed\" : \"pointer\",\n                    width: \"100%\"\n                },\n                disabled: disabled,\n                children: \"Submit Selected\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HospitalListDisplay);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/HospitalListDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./components/static/ConfirmationButtons.tsx":
/*!***************************************************!*\
  !*** ./components/static/ConfirmationButtons.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfirmationButtons: () => (/* binding */ ConfirmationButtons),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst baseButtonStyle = {\n    border: \"2px solid #1976d2\",\n    background: \"none\",\n    color: \"#1976d2\",\n    borderRadius: \"999px\",\n    padding: \"5px\",\n    fontSize: 18,\n    fontWeight: 600,\n    cursor: \"pointer\",\n    margin: \"5px\",\n    outline: \"none\",\n    transition: \"background 0.2s, color 0.2s\",\n    minWidth: 70,\n    minHeight: 10\n};\nconst hoverStyle = {\n    background: \"#e3f2fd\"\n};\nconst activeStyle = {\n    background: \"#1976d2\",\n    color: \"#fff\"\n};\nconst ConfirmationButtons = ({ onYes, onNo, loading = false, disabled })=>{\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [clickedButton, setClickedButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    disabled = loading || clickedButton !== null;\n    const handleClick = (type, callback)=>{\n        setClickedButton(type);\n        callback();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            justifyContent: \"left\",\n            marginTop: 24\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                style: {\n                    ...baseButtonStyle,\n                    ...hovered === \"yes\" ? hoverStyle : {},\n                    ...clickedButton === \"yes\" ? activeStyle : {},\n                    ...disabled ? {\n                        cursor: \"not-allowed\",\n                        opacity: 0.6\n                    } : {}\n                },\n                disabled: disabled,\n                onMouseEnter: ()=>setHovered(\"yes\"),\n                onMouseLeave: ()=>setHovered(null),\n                onClick: ()=>handleClick(\"yes\", onYes),\n                children: \"Yes\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\static\\\\ConfirmationButtons.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                style: {\n                    ...baseButtonStyle,\n                    ...hovered === \"no\" ? hoverStyle : {},\n                    ...clickedButton === \"no\" ? activeStyle : {},\n                    ...disabled ? {\n                        cursor: \"not-allowed\",\n                        opacity: 0.6\n                    } : {}\n                },\n                disabled: disabled,\n                onMouseEnter: ()=>setHovered(\"no\"),\n                onMouseLeave: ()=>setHovered(null),\n                onClick: ()=>handleClick(\"no\", onNo),\n                children: \"No\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\static\\\\ConfirmationButtons.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\static\\\\ConfirmationButtons.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConfirmationButtons);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/static/ConfirmationButtons.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1ec216861906\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZWMyMTY4NjE5MDZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-core */ \"(rsc)/./node_modules/@copilotkit/react-core/dist/index.mjs\");\n\n\n\n\nconst metadata = {\n    title: \"Zinniax Copilot\",\n    description: \"Zinniax Copilot - Your AI Assistant\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"light\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/x-icon\",\n                        href: \"https://stage.ionm.zinniax.com/assets/img/logo/favicon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        children: `\n            .poweredByContainer {\n              padding: 0;\n            }\n            .poweredBy {\n              background: var(--copilot-kit-background-color) !important;\n              visibility: visible !important;\n              display: block !important;\n              position: static !important;\n              text-align: center !important;\n              font-size: 12px !important;\n              padding: 3px 0 !important;\n              margin: 0 !important;\n            }\n`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__.CopilotKit, {\n                    runtimeUrl: \"/api2/copilotkit\",\n                    agent: \"scheduling_agent\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\layout.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"D:\\\\\\\\zinniax-copilot\\\\\\\\zinniax-copilot\\\\\\\\ui\\\\\\\\app\\\\\\\\page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/@copilotkit","vendor-chunks/graphql","vendor-chunks/debug","vendor-chunks/untruncate-json","vendor-chunks/extend","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/diff","vendor-chunks/character-entities","vendor-chunks/@urql","vendor-chunks/prop-types","vendor-chunks/wonka","vendor-chunks/mdast-util-definitions","vendor-chunks/@0no-co","vendor-chunks/uvu","vendor-chunks/react-is","vendor-chunks/uuid","vendor-chunks/trough","vendor-chunks/kleur","vendor-chunks/object-assign","vendor-chunks/dequal","vendor-chunks/@swc","vendor-chunks/comma-separated-tokens","vendor-chunks/trim-lines","vendor-chunks/unist-util-generated","vendor-chunks/decode-named-character-reference","vendor-chunks/space-separated-tokens","vendor-chunks/is-plain-obj","vendor-chunks/is-buffer","vendor-chunks/bail","vendor-chunks/refractor","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/hastscript","vendor-chunks/hast-util-to-parse5","vendor-chunks/property-information","vendor-chunks/parse5","vendor-chunks/@babel","vendor-chunks/micromark","vendor-chunks/react-syntax-highlighter","vendor-chunks/hast-util-from-parse5","vendor-chunks/entities","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/micromark-extension-math","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/style-to-js","vendor-chunks/zwitch","vendor-chunks/web-namespaces","vendor-chunks/vfile-message","vendor-chunks/vfile-location","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-math","vendor-chunks/remark-gfm","vendor-chunks/rehype-raw","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-math","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/html-void-elements","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/hast-util-raw","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/ccount","vendor-chunks/xtend","vendor-chunks/style-to-object","vendor-chunks/inline-style-parser","vendor-chunks/hast-util-parse-selector"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();