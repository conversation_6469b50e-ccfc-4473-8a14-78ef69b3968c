"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@repeaterjs";
exports.ids = ["vendor-chunks/@repeaterjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/@repeaterjs/repeater/cjs/repeater.js":
/*!***********************************************************!*\
  !*** ./node_modules/@repeaterjs/repeater/cjs/repeater.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nfunction __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\n\n/** An error subclass which is thrown when there are too many pending push or next operations on a single repeater. */\r\nvar RepeaterOverflowError = /** @class */ (function (_super) {\r\n    __extends(RepeaterOverflowError, _super);\r\n    function RepeaterOverflowError(message) {\r\n        var _this = _super.call(this, message) || this;\r\n        Object.defineProperty(_this, \"name\", {\r\n            value: \"RepeaterOverflowError\",\r\n            enumerable: false,\r\n        });\r\n        if (typeof Object.setPrototypeOf === \"function\") {\r\n            Object.setPrototypeOf(_this, _this.constructor.prototype);\r\n        }\r\n        else {\r\n            _this.__proto__ = _this.constructor.prototype;\r\n        }\r\n        if (typeof Error.captureStackTrace === \"function\") {\r\n            Error.captureStackTrace(_this, _this.constructor);\r\n        }\r\n        return _this;\r\n    }\r\n    return RepeaterOverflowError;\r\n}(Error));\r\n/** A buffer which allows you to push a set amount of values to the repeater without pushes waiting or throwing errors. */\r\nvar FixedBuffer = /** @class */ (function () {\r\n    function FixedBuffer(capacity) {\r\n        if (capacity < 0) {\r\n            throw new RangeError(\"Capacity may not be less than 0\");\r\n        }\r\n        this._c = capacity;\r\n        this._q = [];\r\n    }\r\n    Object.defineProperty(FixedBuffer.prototype, \"empty\", {\r\n        get: function () {\r\n            return this._q.length === 0;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(FixedBuffer.prototype, \"full\", {\r\n        get: function () {\r\n            return this._q.length >= this._c;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    FixedBuffer.prototype.add = function (value) {\r\n        if (this.full) {\r\n            throw new Error(\"Buffer full\");\r\n        }\r\n        else {\r\n            this._q.push(value);\r\n        }\r\n    };\r\n    FixedBuffer.prototype.remove = function () {\r\n        if (this.empty) {\r\n            throw new Error(\"Buffer empty\");\r\n        }\r\n        return this._q.shift();\r\n    };\r\n    return FixedBuffer;\r\n}());\r\n// TODO: Use a circular buffer here.\r\n/** Sliding buffers allow you to push a set amount of values to the repeater without pushes waiting or throwing errors. If the number of values exceeds the capacity set in the constructor, the buffer will discard the earliest values added. */\r\nvar SlidingBuffer = /** @class */ (function () {\r\n    function SlidingBuffer(capacity) {\r\n        if (capacity < 1) {\r\n            throw new RangeError(\"Capacity may not be less than 1\");\r\n        }\r\n        this._c = capacity;\r\n        this._q = [];\r\n    }\r\n    Object.defineProperty(SlidingBuffer.prototype, \"empty\", {\r\n        get: function () {\r\n            return this._q.length === 0;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(SlidingBuffer.prototype, \"full\", {\r\n        get: function () {\r\n            return false;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    SlidingBuffer.prototype.add = function (value) {\r\n        while (this._q.length >= this._c) {\r\n            this._q.shift();\r\n        }\r\n        this._q.push(value);\r\n    };\r\n    SlidingBuffer.prototype.remove = function () {\r\n        if (this.empty) {\r\n            throw new Error(\"Buffer empty\");\r\n        }\r\n        return this._q.shift();\r\n    };\r\n    return SlidingBuffer;\r\n}());\r\n/** Dropping buffers allow you to push a set amount of values to the repeater without the push function waiting or throwing errors. If the number of values exceeds the capacity set in the constructor, the buffer will discard the latest values added. */\r\nvar DroppingBuffer = /** @class */ (function () {\r\n    function DroppingBuffer(capacity) {\r\n        if (capacity < 1) {\r\n            throw new RangeError(\"Capacity may not be less than 1\");\r\n        }\r\n        this._c = capacity;\r\n        this._q = [];\r\n    }\r\n    Object.defineProperty(DroppingBuffer.prototype, \"empty\", {\r\n        get: function () {\r\n            return this._q.length === 0;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(DroppingBuffer.prototype, \"full\", {\r\n        get: function () {\r\n            return false;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    DroppingBuffer.prototype.add = function (value) {\r\n        if (this._q.length < this._c) {\r\n            this._q.push(value);\r\n        }\r\n    };\r\n    DroppingBuffer.prototype.remove = function () {\r\n        if (this.empty) {\r\n            throw new Error(\"Buffer empty\");\r\n        }\r\n        return this._q.shift();\r\n    };\r\n    return DroppingBuffer;\r\n}());\r\n/** Makes sure promise-likes don’t cause unhandled rejections. */\r\nfunction swallow(value) {\r\n    if (value != null && typeof value.then === \"function\") {\r\n        value.then(NOOP, NOOP);\r\n    }\r\n}\r\n/*** REPEATER STATES ***/\r\n/** The following is an enumeration of all possible repeater states. These states are ordered, and a repeater may only advance to higher states. */\r\n/** The initial state of the repeater. */\r\nvar Initial = 0;\r\n/** Repeaters advance to this state the first time the next method is called on the repeater. */\r\nvar Started = 1;\r\n/** Repeaters advance to this state when the stop function is called. */\r\nvar Stopped = 2;\r\n/** Repeaters advance to this state when there are no values left to be pulled from the repeater. */\r\nvar Done = 3;\r\n/** Repeaters advance to this state if an error is thrown into the repeater. */\r\nvar Rejected = 4;\r\n/** The maximum number of push or next operations which may exist on a single repeater. */\r\nvar MAX_QUEUE_LENGTH = 1024;\r\nvar NOOP = function () { };\r\n/** A helper function used to mimic the behavior of async generators where the final iteration is consumed. */\r\nfunction consumeExecution(r) {\r\n    var err = r.err;\r\n    var execution = Promise.resolve(r.execution).then(function (value) {\r\n        if (err != null) {\r\n            throw err;\r\n        }\r\n        return value;\r\n    });\r\n    r.err = undefined;\r\n    r.execution = execution.then(function () { return undefined; }, function () { return undefined; });\r\n    return r.pending === undefined ? execution : r.pending.then(function () { return execution; });\r\n}\r\n/** A helper function for building iterations from values. Promises are unwrapped, so that iterations never have their value property set to a promise. */\r\nfunction createIteration(r, value) {\r\n    var done = r.state >= Done;\r\n    return Promise.resolve(value).then(function (value) {\r\n        if (!done && r.state >= Rejected) {\r\n            return consumeExecution(r).then(function (value) { return ({\r\n                value: value,\r\n                done: true,\r\n            }); });\r\n        }\r\n        return { value: value, done: done };\r\n    });\r\n}\r\n/**\r\n * This function is bound and passed to the executor as the stop argument.\r\n *\r\n * Advances state to Stopped.\r\n */\r\nfunction stop(r, err) {\r\n    var e_1, _a;\r\n    if (r.state >= Stopped) {\r\n        return;\r\n    }\r\n    r.state = Stopped;\r\n    r.onnext();\r\n    r.onstop();\r\n    if (r.err == null) {\r\n        r.err = err;\r\n    }\r\n    if (r.pushes.length === 0 &&\r\n        (typeof r.buffer === \"undefined\" || r.buffer.empty)) {\r\n        finish(r);\r\n    }\r\n    else {\r\n        try {\r\n            for (var _b = __values(r.pushes), _d = _b.next(); !_d.done; _d = _b.next()) {\r\n                var push_1 = _d.value;\r\n                push_1.resolve();\r\n            }\r\n        }\r\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\r\n        finally {\r\n            try {\r\n                if (_d && !_d.done && (_a = _b.return)) _a.call(_b);\r\n            }\r\n            finally { if (e_1) throw e_1.error; }\r\n        }\r\n    }\r\n}\r\n/**\r\n * The difference between stopping a repeater vs finishing a repeater is that stopping a repeater allows next to continue to drain values from the push queue and buffer, while finishing a repeater will clear all pending values and end iteration immediately. Once, a repeater is finished, all iterations will have the done property set to true.\r\n *\r\n * Advances state to Done.\r\n */\r\nfunction finish(r) {\r\n    var e_2, _a;\r\n    if (r.state >= Done) {\r\n        return;\r\n    }\r\n    if (r.state < Stopped) {\r\n        stop(r);\r\n    }\r\n    r.state = Done;\r\n    r.buffer = undefined;\r\n    try {\r\n        for (var _b = __values(r.nexts), _d = _b.next(); !_d.done; _d = _b.next()) {\r\n            var next = _d.value;\r\n            var execution = r.pending === undefined\r\n                ? consumeExecution(r)\r\n                : r.pending.then(function () { return consumeExecution(r); });\r\n            next.resolve(createIteration(r, execution));\r\n        }\r\n    }\r\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\r\n    finally {\r\n        try {\r\n            if (_d && !_d.done && (_a = _b.return)) _a.call(_b);\r\n        }\r\n        finally { if (e_2) throw e_2.error; }\r\n    }\r\n    r.pushes = [];\r\n    r.nexts = [];\r\n}\r\n/**\r\n * Called when a promise passed to push rejects, or when a push call is unhandled.\r\n *\r\n * Advances state to Rejected.\r\n */\r\nfunction reject(r) {\r\n    if (r.state >= Rejected) {\r\n        return;\r\n    }\r\n    if (r.state < Done) {\r\n        finish(r);\r\n    }\r\n    r.state = Rejected;\r\n}\r\n/** This function is bound and passed to the executor as the push argument. */\r\nfunction push(r, value) {\r\n    swallow(value);\r\n    if (r.pushes.length >= MAX_QUEUE_LENGTH) {\r\n        throw new RepeaterOverflowError(\"No more than \" + MAX_QUEUE_LENGTH + \" pending calls to push are allowed on a single repeater.\");\r\n    }\r\n    else if (r.state >= Stopped) {\r\n        return Promise.resolve(undefined);\r\n    }\r\n    var valueP = r.pending === undefined\r\n        ? Promise.resolve(value)\r\n        : r.pending.then(function () { return value; });\r\n    valueP = valueP.catch(function (err) {\r\n        if (r.state < Stopped) {\r\n            r.err = err;\r\n        }\r\n        reject(r);\r\n        return undefined; // void :(\r\n    });\r\n    var nextP;\r\n    if (r.nexts.length) {\r\n        var next_1 = r.nexts.shift();\r\n        next_1.resolve(createIteration(r, valueP));\r\n        if (r.nexts.length) {\r\n            nextP = Promise.resolve(r.nexts[0].value);\r\n        }\r\n        else if (typeof r.buffer !== \"undefined\" && !r.buffer.full) {\r\n            nextP = Promise.resolve(undefined);\r\n        }\r\n        else {\r\n            nextP = new Promise(function (resolve) { return (r.onnext = resolve); });\r\n        }\r\n    }\r\n    else if (typeof r.buffer !== \"undefined\" && !r.buffer.full) {\r\n        r.buffer.add(valueP);\r\n        nextP = Promise.resolve(undefined);\r\n    }\r\n    else {\r\n        nextP = new Promise(function (resolve) { return r.pushes.push({ resolve: resolve, value: valueP }); });\r\n    }\r\n    // If an error is thrown into the repeater via the next or throw methods, we give the repeater a chance to handle this by rejecting the promise returned from push. If the push call is not immediately handled we throw the next iteration of the repeater.\r\n    // To check that the promise returned from push is floating, we modify the then and catch methods of the returned promise so that they flip the floating flag. The push function actually does not return a promise, because modern engines do not call the then and catch methods on native promises. By making next a plain old javascript object, we ensure that the then and catch methods will be called.\r\n    var floating = true;\r\n    var next = {};\r\n    var unhandled = nextP.catch(function (err) {\r\n        if (floating) {\r\n            throw err;\r\n        }\r\n        return undefined; // void :(\r\n    });\r\n    next.then = function (onfulfilled, onrejected) {\r\n        floating = false;\r\n        return Promise.prototype.then.call(nextP, onfulfilled, onrejected);\r\n    };\r\n    next.catch = function (onrejected) {\r\n        floating = false;\r\n        return Promise.prototype.catch.call(nextP, onrejected);\r\n    };\r\n    next.finally = nextP.finally.bind(nextP);\r\n    r.pending = valueP\r\n        .then(function () { return unhandled; })\r\n        .catch(function (err) {\r\n        r.err = err;\r\n        reject(r);\r\n    });\r\n    return next;\r\n}\r\n/**\r\n * Creates the stop callable promise which is passed to the executor\r\n */\r\nfunction createStop(r) {\r\n    var stop1 = stop.bind(null, r);\r\n    var stopP = new Promise(function (resolve) { return (r.onstop = resolve); });\r\n    stop1.then = stopP.then.bind(stopP);\r\n    stop1.catch = stopP.catch.bind(stopP);\r\n    stop1.finally = stopP.finally.bind(stopP);\r\n    return stop1;\r\n}\r\n/**\r\n * Calls the executor passed into the constructor. This function is called the first time the next method is called on the repeater.\r\n *\r\n * Advances state to Started.\r\n */\r\nfunction execute(r) {\r\n    if (r.state >= Started) {\r\n        return;\r\n    }\r\n    r.state = Started;\r\n    var push1 = push.bind(null, r);\r\n    var stop1 = createStop(r);\r\n    r.execution = new Promise(function (resolve) { return resolve(r.executor(push1, stop1)); });\r\n    // TODO: We should consider stopping all repeaters when the executor settles.\r\n    r.execution.catch(function () { return stop(r); });\r\n}\r\nvar records = new WeakMap();\r\n// NOTE: While repeaters implement and are assignable to the AsyncGenerator interface, and you can use the types interchangeably, we don’t use typescript’s implements syntax here because this would make supporting earlier versions of typescript trickier. This is because TypeScript version 3.6 changed the iterator types by adding the TReturn and TNext type parameters.\r\nvar Repeater = /** @class */ (function () {\r\n    function Repeater(executor, buffer) {\r\n        records.set(this, {\r\n            executor: executor,\r\n            buffer: buffer,\r\n            err: undefined,\r\n            state: Initial,\r\n            pushes: [],\r\n            nexts: [],\r\n            pending: undefined,\r\n            execution: undefined,\r\n            onnext: NOOP,\r\n            onstop: NOOP,\r\n        });\r\n    }\r\n    Repeater.prototype.next = function (value) {\r\n        swallow(value);\r\n        var r = records.get(this);\r\n        if (r === undefined) {\r\n            throw new Error(\"WeakMap error\");\r\n        }\r\n        if (r.nexts.length >= MAX_QUEUE_LENGTH) {\r\n            throw new RepeaterOverflowError(\"No more than \" + MAX_QUEUE_LENGTH + \" pending calls to next are allowed on a single repeater.\");\r\n        }\r\n        if (r.state <= Initial) {\r\n            execute(r);\r\n        }\r\n        r.onnext(value);\r\n        if (typeof r.buffer !== \"undefined\" && !r.buffer.empty) {\r\n            var result = createIteration(r, r.buffer.remove());\r\n            if (r.pushes.length) {\r\n                var push_2 = r.pushes.shift();\r\n                r.buffer.add(push_2.value);\r\n                r.onnext = push_2.resolve;\r\n            }\r\n            return result;\r\n        }\r\n        else if (r.pushes.length) {\r\n            var push_3 = r.pushes.shift();\r\n            r.onnext = push_3.resolve;\r\n            return createIteration(r, push_3.value);\r\n        }\r\n        else if (r.state >= Stopped) {\r\n            finish(r);\r\n            return createIteration(r, consumeExecution(r));\r\n        }\r\n        return new Promise(function (resolve) { return r.nexts.push({ resolve: resolve, value: value }); });\r\n    };\r\n    Repeater.prototype.return = function (value) {\r\n        swallow(value);\r\n        var r = records.get(this);\r\n        if (r === undefined) {\r\n            throw new Error(\"WeakMap error\");\r\n        }\r\n        finish(r);\r\n        // We override the execution because return should always return the value passed in.\r\n        r.execution = Promise.resolve(r.execution).then(function () { return value; });\r\n        return createIteration(r, consumeExecution(r));\r\n    };\r\n    Repeater.prototype.throw = function (err) {\r\n        var r = records.get(this);\r\n        if (r === undefined) {\r\n            throw new Error(\"WeakMap error\");\r\n        }\r\n        if (r.state <= Initial ||\r\n            r.state >= Stopped ||\r\n            (typeof r.buffer !== \"undefined\" && !r.buffer.empty)) {\r\n            finish(r);\r\n            // If r.err is already set, that mean the repeater has already produced an error, so we throw that error rather than the error passed in, because doing so might be more informative for the caller.\r\n            if (r.err == null) {\r\n                r.err = err;\r\n            }\r\n            return createIteration(r, consumeExecution(r));\r\n        }\r\n        return this.next(Promise.reject(err));\r\n    };\r\n    Repeater.prototype[Symbol.asyncIterator] = function () {\r\n        return this;\r\n    };\r\n    // TODO: Remove these static methods from the class.\r\n    Repeater.race = race;\r\n    Repeater.merge = merge;\r\n    Repeater.zip = zip;\r\n    Repeater.latest = latest;\r\n    return Repeater;\r\n}());\r\n/*** COMBINATOR FUNCTIONS ***/\r\n// TODO: move these combinators to their own file.\r\nfunction getIterators(values, options) {\r\n    var e_3, _a;\r\n    var iters = [];\r\n    var _loop_1 = function (value) {\r\n        if (value != null && typeof value[Symbol.asyncIterator] === \"function\") {\r\n            iters.push(value[Symbol.asyncIterator]());\r\n        }\r\n        else if (value != null && typeof value[Symbol.iterator] === \"function\") {\r\n            iters.push(value[Symbol.iterator]());\r\n        }\r\n        else {\r\n            iters.push((function valueToAsyncIterator() {\r\n                return __asyncGenerator(this, arguments, function valueToAsyncIterator_1() {\r\n                    return __generator(this, function (_a) {\r\n                        switch (_a.label) {\r\n                            case 0:\r\n                                if (!options.yieldValues) return [3 /*break*/, 3];\r\n                                return [4 /*yield*/, __await(value)];\r\n                            case 1: return [4 /*yield*/, _a.sent()];\r\n                            case 2:\r\n                                _a.sent();\r\n                                _a.label = 3;\r\n                            case 3:\r\n                                if (!options.returnValues) return [3 /*break*/, 5];\r\n                                return [4 /*yield*/, __await(value)];\r\n                            case 4: return [2 /*return*/, _a.sent()];\r\n                            case 5: return [2 /*return*/];\r\n                        }\r\n                    });\r\n                });\r\n            })());\r\n        }\r\n    };\r\n    try {\r\n        for (var values_1 = __values(values), values_1_1 = values_1.next(); !values_1_1.done; values_1_1 = values_1.next()) {\r\n            var value = values_1_1.value;\r\n            _loop_1(value);\r\n        }\r\n    }\r\n    catch (e_3_1) { e_3 = { error: e_3_1 }; }\r\n    finally {\r\n        try {\r\n            if (values_1_1 && !values_1_1.done && (_a = values_1.return)) _a.call(values_1);\r\n        }\r\n        finally { if (e_3) throw e_3.error; }\r\n    }\r\n    return iters;\r\n}\r\n// NOTE: whenever you see any variables called `advance` or `advances`, know that it is a hack to get around the fact that `Promise.race` leaks memory. These variables are intended to be set to the resolve function of a promise which is constructed and awaited as an alternative to Promise.race. For more information, see this comment in the Node.js issue tracker: https://github.com/nodejs/node/issues/17469#issuecomment-685216777.\r\nfunction race(contenders) {\r\n    var _this = this;\r\n    var iters = getIterators(contenders, { returnValues: true });\r\n    return new Repeater(function (push, stop) { return __awaiter(_this, void 0, void 0, function () {\r\n        var advance, stopped, finalIteration, iteration, i_1, _loop_2;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    if (!iters.length) {\r\n                        stop();\r\n                        return [2 /*return*/];\r\n                    }\r\n                    stopped = false;\r\n                    stop.then(function () {\r\n                        advance();\r\n                        stopped = true;\r\n                    });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, , 5, 7]);\r\n                    iteration = void 0;\r\n                    i_1 = 0;\r\n                    _loop_2 = function () {\r\n                        var j, iters_1, iters_1_1, iter;\r\n                        var e_4, _a;\r\n                        return __generator(this, function (_b) {\r\n                            switch (_b.label) {\r\n                                case 0:\r\n                                    j = i_1;\r\n                                    try {\r\n                                        for (iters_1 = (e_4 = void 0, __values(iters)), iters_1_1 = iters_1.next(); !iters_1_1.done; iters_1_1 = iters_1.next()) {\r\n                                            iter = iters_1_1.value;\r\n                                            Promise.resolve(iter.next()).then(function (iteration) {\r\n                                                if (iteration.done) {\r\n                                                    stop();\r\n                                                    if (finalIteration === undefined) {\r\n                                                        finalIteration = iteration;\r\n                                                    }\r\n                                                }\r\n                                                else if (i_1 === j) {\r\n                                                    // This iterator has won, advance i and resolve the promise.\r\n                                                    i_1++;\r\n                                                    advance(iteration);\r\n                                                }\r\n                                            }, function (err) { return stop(err); });\r\n                                        }\r\n                                    }\r\n                                    catch (e_4_1) { e_4 = { error: e_4_1 }; }\r\n                                    finally {\r\n                                        try {\r\n                                            if (iters_1_1 && !iters_1_1.done && (_a = iters_1.return)) _a.call(iters_1);\r\n                                        }\r\n                                        finally { if (e_4) throw e_4.error; }\r\n                                    }\r\n                                    return [4 /*yield*/, new Promise(function (resolve) { return (advance = resolve); })];\r\n                                case 1:\r\n                                    iteration = _b.sent();\r\n                                    if (!(iteration !== undefined)) return [3 /*break*/, 3];\r\n                                    return [4 /*yield*/, push(iteration.value)];\r\n                                case 2:\r\n                                    _b.sent();\r\n                                    _b.label = 3;\r\n                                case 3: return [2 /*return*/];\r\n                            }\r\n                        });\r\n                    };\r\n                    _a.label = 2;\r\n                case 2:\r\n                    if (!!stopped) return [3 /*break*/, 4];\r\n                    return [5 /*yield**/, _loop_2()];\r\n                case 3:\r\n                    _a.sent();\r\n                    return [3 /*break*/, 2];\r\n                case 4: return [2 /*return*/, finalIteration && finalIteration.value];\r\n                case 5:\r\n                    stop();\r\n                    return [4 /*yield*/, Promise.race(iters.map(function (iter) { return iter.return && iter.return(); }))];\r\n                case 6:\r\n                    _a.sent();\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); });\r\n}\r\nfunction merge(contenders) {\r\n    var _this = this;\r\n    var iters = getIterators(contenders, { yieldValues: true });\r\n    return new Repeater(function (push, stop) { return __awaiter(_this, void 0, void 0, function () {\r\n        var advances, stopped, finalIteration;\r\n        var _this = this;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    if (!iters.length) {\r\n                        stop();\r\n                        return [2 /*return*/];\r\n                    }\r\n                    advances = [];\r\n                    stopped = false;\r\n                    stop.then(function () {\r\n                        var e_5, _a;\r\n                        stopped = true;\r\n                        try {\r\n                            for (var advances_1 = __values(advances), advances_1_1 = advances_1.next(); !advances_1_1.done; advances_1_1 = advances_1.next()) {\r\n                                var advance = advances_1_1.value;\r\n                                advance();\r\n                            }\r\n                        }\r\n                        catch (e_5_1) { e_5 = { error: e_5_1 }; }\r\n                        finally {\r\n                            try {\r\n                                if (advances_1_1 && !advances_1_1.done && (_a = advances_1.return)) _a.call(advances_1);\r\n                            }\r\n                            finally { if (e_5) throw e_5.error; }\r\n                        }\r\n                    });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, , 3, 4]);\r\n                    return [4 /*yield*/, Promise.all(iters.map(function (iter, i) { return __awaiter(_this, void 0, void 0, function () {\r\n                            var iteration, _a;\r\n                            return __generator(this, function (_b) {\r\n                                switch (_b.label) {\r\n                                    case 0:\r\n                                        _b.trys.push([0, , 6, 9]);\r\n                                        _b.label = 1;\r\n                                    case 1:\r\n                                        if (!!stopped) return [3 /*break*/, 5];\r\n                                        Promise.resolve(iter.next()).then(function (iteration) { return advances[i](iteration); }, function (err) { return stop(err); });\r\n                                        return [4 /*yield*/, new Promise(function (resolve) {\r\n                                                advances[i] = resolve;\r\n                                            })];\r\n                                    case 2:\r\n                                        iteration = _b.sent();\r\n                                        if (!(iteration !== undefined)) return [3 /*break*/, 4];\r\n                                        if (iteration.done) {\r\n                                            finalIteration = iteration;\r\n                                            return [2 /*return*/];\r\n                                        }\r\n                                        return [4 /*yield*/, push(iteration.value)];\r\n                                    case 3:\r\n                                        _b.sent();\r\n                                        _b.label = 4;\r\n                                    case 4: return [3 /*break*/, 1];\r\n                                    case 5: return [3 /*break*/, 9];\r\n                                    case 6:\r\n                                        _a = iter.return;\r\n                                        if (!_a) return [3 /*break*/, 8];\r\n                                        return [4 /*yield*/, iter.return()];\r\n                                    case 7:\r\n                                        _a = (_b.sent());\r\n                                        _b.label = 8;\r\n                                    case 8:\r\n                                        return [7 /*endfinally*/];\r\n                                    case 9: return [2 /*return*/];\r\n                                }\r\n                            });\r\n                        }); }))];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, finalIteration && finalIteration.value];\r\n                case 3:\r\n                    stop();\r\n                    return [7 /*endfinally*/];\r\n                case 4: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); });\r\n}\r\nfunction zip(contenders) {\r\n    var _this = this;\r\n    var iters = getIterators(contenders, { returnValues: true });\r\n    return new Repeater(function (push, stop) { return __awaiter(_this, void 0, void 0, function () {\r\n        var advance, stopped, iterations, values;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    if (!iters.length) {\r\n                        stop();\r\n                        return [2 /*return*/, []];\r\n                    }\r\n                    stopped = false;\r\n                    stop.then(function () {\r\n                        advance();\r\n                        stopped = true;\r\n                    });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, , 6, 8]);\r\n                    _a.label = 2;\r\n                case 2:\r\n                    if (!!stopped) return [3 /*break*/, 5];\r\n                    Promise.all(iters.map(function (iter) { return iter.next(); })).then(function (iterations) { return advance(iterations); }, function (err) { return stop(err); });\r\n                    return [4 /*yield*/, new Promise(function (resolve) { return (advance = resolve); })];\r\n                case 3:\r\n                    iterations = _a.sent();\r\n                    if (iterations === undefined) {\r\n                        return [2 /*return*/];\r\n                    }\r\n                    values = iterations.map(function (iteration) { return iteration.value; });\r\n                    if (iterations.some(function (iteration) { return iteration.done; })) {\r\n                        return [2 /*return*/, values];\r\n                    }\r\n                    return [4 /*yield*/, push(values)];\r\n                case 4:\r\n                    _a.sent();\r\n                    return [3 /*break*/, 2];\r\n                case 5: return [3 /*break*/, 8];\r\n                case 6:\r\n                    stop();\r\n                    return [4 /*yield*/, Promise.all(iters.map(function (iter) { return iter.return && iter.return(); }))];\r\n                case 7:\r\n                    _a.sent();\r\n                    return [7 /*endfinally*/];\r\n                case 8: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); });\r\n}\r\nfunction latest(contenders) {\r\n    var _this = this;\r\n    var iters = getIterators(contenders, {\r\n        yieldValues: true,\r\n        returnValues: true,\r\n    });\r\n    return new Repeater(function (push, stop) { return __awaiter(_this, void 0, void 0, function () {\r\n        var advance, advances, stopped, iterations_1, values_2;\r\n        var _this = this;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    if (!iters.length) {\r\n                        stop();\r\n                        return [2 /*return*/, []];\r\n                    }\r\n                    advances = [];\r\n                    stopped = false;\r\n                    stop.then(function () {\r\n                        var e_6, _a;\r\n                        advance();\r\n                        try {\r\n                            for (var advances_2 = __values(advances), advances_2_1 = advances_2.next(); !advances_2_1.done; advances_2_1 = advances_2.next()) {\r\n                                var advance1 = advances_2_1.value;\r\n                                advance1();\r\n                            }\r\n                        }\r\n                        catch (e_6_1) { e_6 = { error: e_6_1 }; }\r\n                        finally {\r\n                            try {\r\n                                if (advances_2_1 && !advances_2_1.done && (_a = advances_2.return)) _a.call(advances_2);\r\n                            }\r\n                            finally { if (e_6) throw e_6.error; }\r\n                        }\r\n                        stopped = true;\r\n                    });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, , 5, 7]);\r\n                    Promise.all(iters.map(function (iter) { return iter.next(); })).then(function (iterations) { return advance(iterations); }, function (err) { return stop(err); });\r\n                    return [4 /*yield*/, new Promise(function (resolve) { return (advance = resolve); })];\r\n                case 2:\r\n                    iterations_1 = _a.sent();\r\n                    if (iterations_1 === undefined) {\r\n                        return [2 /*return*/];\r\n                    }\r\n                    values_2 = iterations_1.map(function (iteration) { return iteration.value; });\r\n                    if (iterations_1.every(function (iteration) { return iteration.done; })) {\r\n                        return [2 /*return*/, values_2];\r\n                    }\r\n                    // We continuously yield and mutate the same values array so we shallow copy it each time it is pushed.\r\n                    return [4 /*yield*/, push(values_2.slice())];\r\n                case 3:\r\n                    // We continuously yield and mutate the same values array so we shallow copy it each time it is pushed.\r\n                    _a.sent();\r\n                    return [4 /*yield*/, Promise.all(iters.map(function (iter, i) { return __awaiter(_this, void 0, void 0, function () {\r\n                            var iteration;\r\n                            return __generator(this, function (_a) {\r\n                                switch (_a.label) {\r\n                                    case 0:\r\n                                        if (iterations_1[i].done) {\r\n                                            return [2 /*return*/, iterations_1[i].value];\r\n                                        }\r\n                                        _a.label = 1;\r\n                                    case 1:\r\n                                        if (!!stopped) return [3 /*break*/, 4];\r\n                                        Promise.resolve(iter.next()).then(function (iteration) { return advances[i](iteration); }, function (err) { return stop(err); });\r\n                                        return [4 /*yield*/, new Promise(function (resolve) { return (advances[i] = resolve); })];\r\n                                    case 2:\r\n                                        iteration = _a.sent();\r\n                                        if (iteration === undefined) {\r\n                                            return [2 /*return*/, iterations_1[i].value];\r\n                                        }\r\n                                        else if (iteration.done) {\r\n                                            return [2 /*return*/, iteration.value];\r\n                                        }\r\n                                        values_2[i] = iteration.value;\r\n                                        return [4 /*yield*/, push(values_2.slice())];\r\n                                    case 3:\r\n                                        _a.sent();\r\n                                        return [3 /*break*/, 1];\r\n                                    case 4: return [2 /*return*/];\r\n                                }\r\n                            });\r\n                        }); }))];\r\n                case 4: return [2 /*return*/, _a.sent()];\r\n                case 5:\r\n                    stop();\r\n                    return [4 /*yield*/, Promise.all(iters.map(function (iter) { return iter.return && iter.return(); }))];\r\n                case 6:\r\n                    _a.sent();\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); });\r\n}\n\nexports.DroppingBuffer = DroppingBuffer;\nexports.FixedBuffer = FixedBuffer;\nexports.MAX_QUEUE_LENGTH = MAX_QUEUE_LENGTH;\nexports.Repeater = Repeater;\nexports.RepeaterOverflowError = RepeaterOverflowError;\nexports.SlidingBuffer = SlidingBuffer;\n//# sourceMappingURL=repeater.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@repeaterjs/repeater/cjs/repeater.js\n");

/***/ })

};
;