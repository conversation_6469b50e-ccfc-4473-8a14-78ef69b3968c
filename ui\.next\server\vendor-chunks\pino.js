/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino";
exports.ids = ["vendor-chunks/pino"];
exports.modules = {

/***/ "(rsc)/./node_modules/pino/lib/caller.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/caller.js ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
eval("\n\nfunction noOpPrepareStackTrace (_, stack) {\n  return stack\n}\n\nmodule.exports = function getCallers () {\n  const originalPrepare = Error.prepareStackTrace\n  Error.prepareStackTrace = noOpPrepareStackTrace\n  const stack = new Error().stack\n  Error.prepareStackTrace = originalPrepare\n\n  if (!Array.isArray(stack)) {\n    return undefined\n  }\n\n  const entries = stack.slice(2)\n\n  const fileNames = []\n\n  for (const entry of entries) {\n    if (!entry) {\n      continue\n    }\n\n    fileNames.push(entry.getFileName())\n  }\n\n  return fileNames\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvY2FsbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xccGlub1xcbGliXFxjYWxsZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmZ1bmN0aW9uIG5vT3BQcmVwYXJlU3RhY2tUcmFjZSAoXywgc3RhY2spIHtcbiAgcmV0dXJuIHN0YWNrXG59XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gZ2V0Q2FsbGVycyAoKSB7XG4gIGNvbnN0IG9yaWdpbmFsUHJlcGFyZSA9IEVycm9yLnByZXBhcmVTdGFja1RyYWNlXG4gIEVycm9yLnByZXBhcmVTdGFja1RyYWNlID0gbm9PcFByZXBhcmVTdGFja1RyYWNlXG4gIGNvbnN0IHN0YWNrID0gbmV3IEVycm9yKCkuc3RhY2tcbiAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBvcmlnaW5hbFByZXBhcmVcblxuICBpZiAoIUFycmF5LmlzQXJyYXkoc3RhY2spKSB7XG4gICAgcmV0dXJuIHVuZGVmaW5lZFxuICB9XG5cbiAgY29uc3QgZW50cmllcyA9IHN0YWNrLnNsaWNlKDIpXG5cbiAgY29uc3QgZmlsZU5hbWVzID0gW11cblxuICBmb3IgKGNvbnN0IGVudHJ5IG9mIGVudHJpZXMpIHtcbiAgICBpZiAoIWVudHJ5KSB7XG4gICAgICBjb250aW51ZVxuICAgIH1cblxuICAgIGZpbGVOYW1lcy5wdXNoKGVudHJ5LmdldEZpbGVOYW1lKCkpXG4gIH1cblxuICByZXR1cm4gZmlsZU5hbWVzXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/caller.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/constants.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/constants.js ***!
  \********************************************/
/***/ ((module) => {

eval("/**\n * Represents default log level values\n *\n * @enum {number}\n */\nconst DEFAULT_LEVELS = {\n  trace: 10,\n  debug: 20,\n  info: 30,\n  warn: 40,\n  error: 50,\n  fatal: 60\n}\n\n/**\n * Represents sort order direction: `ascending` or `descending`\n *\n * @enum {string}\n */\nconst SORTING_ORDER = {\n  ASC: 'ASC',\n  DESC: 'DESC'\n}\n\nmodule.exports = {\n  DEFAULT_LEVELS,\n  SORTING_ORDER\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xccGlub1xcbGliXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXByZXNlbnRzIGRlZmF1bHQgbG9nIGxldmVsIHZhbHVlc1xuICpcbiAqIEBlbnVtIHtudW1iZXJ9XG4gKi9cbmNvbnN0IERFRkFVTFRfTEVWRUxTID0ge1xuICB0cmFjZTogMTAsXG4gIGRlYnVnOiAyMCxcbiAgaW5mbzogMzAsXG4gIHdhcm46IDQwLFxuICBlcnJvcjogNTAsXG4gIGZhdGFsOiA2MFxufVxuXG4vKipcbiAqIFJlcHJlc2VudHMgc29ydCBvcmRlciBkaXJlY3Rpb246IGBhc2NlbmRpbmdgIG9yIGBkZXNjZW5kaW5nYFxuICpcbiAqIEBlbnVtIHtzdHJpbmd9XG4gKi9cbmNvbnN0IFNPUlRJTkdfT1JERVIgPSB7XG4gIEFTQzogJ0FTQycsXG4gIERFU0M6ICdERVNDJ1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgREVGQVVMVF9MRVZFTFMsXG4gIFNPUlRJTkdfT1JERVJcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/levels.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/levels.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n/* eslint no-prototype-builtins: 0 */\nconst {\n  lsCacheSym,\n  levelValSym,\n  useOnlyCustomLevelsSym,\n  streamSym,\n  formattersSym,\n  hooksSym,\n  levelCompSym\n} = __webpack_require__(/*! ./symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst { noop, genLog } = __webpack_require__(/*! ./tools */ \"(rsc)/./node_modules/pino/lib/tools.js\")\nconst { DEFAULT_LEVELS, SORTING_ORDER } = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pino/lib/constants.js\")\n\nconst levelMethods = {\n  fatal: (hook) => {\n    const logFatal = genLog(DEFAULT_LEVELS.fatal, hook)\n    return function (...args) {\n      const stream = this[streamSym]\n      logFatal.call(this, ...args)\n      if (typeof stream.flushSync === 'function') {\n        try {\n          stream.flushSync()\n        } catch (e) {\n          // https://github.com/pinojs/pino/pull/740#discussion_r346788313\n        }\n      }\n    }\n  },\n  error: (hook) => genLog(DEFAULT_LEVELS.error, hook),\n  warn: (hook) => genLog(DEFAULT_LEVELS.warn, hook),\n  info: (hook) => genLog(DEFAULT_LEVELS.info, hook),\n  debug: (hook) => genLog(DEFAULT_LEVELS.debug, hook),\n  trace: (hook) => genLog(DEFAULT_LEVELS.trace, hook)\n}\n\nconst nums = Object.keys(DEFAULT_LEVELS).reduce((o, k) => {\n  o[DEFAULT_LEVELS[k]] = k\n  return o\n}, {})\n\nconst initialLsCache = Object.keys(nums).reduce((o, k) => {\n  o[k] = '{\"level\":' + Number(k)\n  return o\n}, {})\n\nfunction genLsCache (instance) {\n  const formatter = instance[formattersSym].level\n  const { labels } = instance.levels\n  const cache = {}\n  for (const label in labels) {\n    const level = formatter(labels[label], Number(label))\n    cache[label] = JSON.stringify(level).slice(0, -1)\n  }\n  instance[lsCacheSym] = cache\n  return instance\n}\n\nfunction isStandardLevel (level, useOnlyCustomLevels) {\n  if (useOnlyCustomLevels) {\n    return false\n  }\n\n  switch (level) {\n    case 'fatal':\n    case 'error':\n    case 'warn':\n    case 'info':\n    case 'debug':\n    case 'trace':\n      return true\n    default:\n      return false\n  }\n}\n\nfunction setLevel (level) {\n  const { labels, values } = this.levels\n  if (typeof level === 'number') {\n    if (labels[level] === undefined) throw Error('unknown level value' + level)\n    level = labels[level]\n  }\n  if (values[level] === undefined) throw Error('unknown level ' + level)\n  const preLevelVal = this[levelValSym]\n  const levelVal = this[levelValSym] = values[level]\n  const useOnlyCustomLevelsVal = this[useOnlyCustomLevelsSym]\n  const levelComparison = this[levelCompSym]\n  const hook = this[hooksSym].logMethod\n\n  for (const key in values) {\n    if (levelComparison(values[key], levelVal) === false) {\n      this[key] = noop\n      continue\n    }\n    this[key] = isStandardLevel(key, useOnlyCustomLevelsVal) ? levelMethods[key](hook) : genLog(values[key], hook)\n  }\n\n  this.emit(\n    'level-change',\n    level,\n    levelVal,\n    labels[preLevelVal],\n    preLevelVal,\n    this\n  )\n}\n\nfunction getLevel (level) {\n  const { levels, levelVal } = this\n  // protection against potential loss of Pino scope from serializers (edge case with circular refs - https://github.com/pinojs/pino/issues/833)\n  return (levels && levels.labels) ? levels.labels[levelVal] : ''\n}\n\nfunction isLevelEnabled (logLevel) {\n  const { values } = this.levels\n  const logLevelVal = values[logLevel]\n  return logLevelVal !== undefined && this[levelCompSym](logLevelVal, this[levelValSym])\n}\n\n/**\n * Determine if the given `current` level is enabled by comparing it\n * against the current threshold (`expected`).\n *\n * @param {SORTING_ORDER} direction comparison direction \"ASC\" or \"DESC\"\n * @param {number} current current log level number representation\n * @param {number} expected threshold value to compare with\n * @returns {boolean}\n */\nfunction compareLevel (direction, current, expected) {\n  if (direction === SORTING_ORDER.DESC) {\n    return current <= expected\n  }\n\n  return current >= expected\n}\n\n/**\n * Create a level comparison function based on `levelComparison`\n * it could a default function which compares levels either in \"ascending\" or \"descending\" order or custom comparison function\n *\n * @param {SORTING_ORDER | Function} levelComparison sort levels order direction or custom comparison function\n * @returns Function\n */\nfunction genLevelComparison (levelComparison) {\n  if (typeof levelComparison === 'string') {\n    return compareLevel.bind(null, levelComparison)\n  }\n\n  return levelComparison\n}\n\nfunction mappings (customLevels = null, useOnlyCustomLevels = false) {\n  const customNums = customLevels\n    /* eslint-disable */\n    ? Object.keys(customLevels).reduce((o, k) => {\n        o[customLevels[k]] = k\n        return o\n      }, {})\n    : null\n    /* eslint-enable */\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { Infinity: { value: 'silent' } }),\n    useOnlyCustomLevels ? null : nums,\n    customNums\n  )\n  const values = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : DEFAULT_LEVELS,\n    customLevels\n  )\n  return { labels, values }\n}\n\nfunction assertDefaultLevelFound (defaultLevel, customLevels, useOnlyCustomLevels) {\n  if (typeof defaultLevel === 'number') {\n    const values = [].concat(\n      Object.keys(customLevels || {}).map(key => customLevels[key]),\n      useOnlyCustomLevels ? [] : Object.keys(nums).map(level => +level),\n      Infinity\n    )\n    if (!values.includes(defaultLevel)) {\n      throw Error(`default level:${defaultLevel} must be included in custom levels`)\n    }\n    return\n  }\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : DEFAULT_LEVELS,\n    customLevels\n  )\n  if (!(defaultLevel in labels)) {\n    throw Error(`default level:${defaultLevel} must be included in custom levels`)\n  }\n}\n\nfunction assertNoLevelCollisions (levels, customLevels) {\n  const { labels, values } = levels\n  for (const k in customLevels) {\n    if (k in values) {\n      throw Error('levels cannot be overridden')\n    }\n    if (customLevels[k] in labels) {\n      throw Error('pre-existing level values cannot be used for new levels')\n    }\n  }\n}\n\n/**\n * Validates whether `levelComparison` is correct\n *\n * @throws Error\n * @param {SORTING_ORDER | Function} levelComparison - value to validate\n * @returns\n */\nfunction assertLevelComparison (levelComparison) {\n  if (typeof levelComparison === 'function') {\n    return\n  }\n\n  if (typeof levelComparison === 'string' && Object.values(SORTING_ORDER).includes(levelComparison)) {\n    return\n  }\n\n  throw new Error('Levels comparison should be one of \"ASC\", \"DESC\" or \"function\" type')\n}\n\nmodule.exports = {\n  initialLsCache,\n  genLsCache,\n  levelMethods,\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  assertNoLevelCollisions,\n  assertDefaultLevelFound,\n  genLevelComparison,\n  assertLevelComparison\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/levels.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/meta.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/meta.js ***!
  \***************************************/
/***/ ((module) => {

"use strict";
eval("\n\nmodule.exports = { version: '9.7.0' }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvbWV0YS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixtQkFBbUIiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xccGlub1xcbGliXFxtZXRhLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHsgdmVyc2lvbjogJzkuNy4wJyB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/meta.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/multistream.js":
/*!**********************************************!*\
  !*** ./node_modules/pino/lib/multistream.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst metadata = Symbol.for('pino.metadata')\nconst { DEFAULT_LEVELS } = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pino/lib/constants.js\")\n\nconst DEFAULT_INFO_LEVEL = DEFAULT_LEVELS.info\n\nfunction multistream (streamsArray, opts) {\n  let counter = 0\n  streamsArray = streamsArray || []\n  opts = opts || { dedupe: false }\n\n  const streamLevels = Object.create(DEFAULT_LEVELS)\n  streamLevels.silent = Infinity\n  if (opts.levels && typeof opts.levels === 'object') {\n    Object.keys(opts.levels).forEach(i => {\n      streamLevels[i] = opts.levels[i]\n    })\n  }\n\n  const res = {\n    write,\n    add,\n    emit,\n    flushSync,\n    end,\n    minLevel: 0,\n    streams: [],\n    clone,\n    [metadata]: true,\n    streamLevels\n  }\n\n  if (Array.isArray(streamsArray)) {\n    streamsArray.forEach(add, res)\n  } else {\n    add.call(res, streamsArray)\n  }\n\n  // clean this object up\n  // or it will stay allocated forever\n  // as it is closed on the following closures\n  streamsArray = null\n\n  return res\n\n  // we can exit early because the streams are ordered by level\n  function write (data) {\n    let dest\n    const level = this.lastLevel\n    const { streams } = this\n    // for handling situation when several streams has the same level\n    let recordedLevel = 0\n    let stream\n\n    // if dedupe set to true we send logs to the stream with the highest level\n    // therefore, we have to change sorting order\n    for (let i = initLoopVar(streams.length, opts.dedupe); checkLoopVar(i, streams.length, opts.dedupe); i = adjustLoopVar(i, opts.dedupe)) {\n      dest = streams[i]\n      if (dest.level <= level) {\n        if (recordedLevel !== 0 && recordedLevel !== dest.level) {\n          break\n        }\n        stream = dest.stream\n        if (stream[metadata]) {\n          const { lastTime, lastMsg, lastObj, lastLogger } = this\n          stream.lastLevel = level\n          stream.lastTime = lastTime\n          stream.lastMsg = lastMsg\n          stream.lastObj = lastObj\n          stream.lastLogger = lastLogger\n        }\n        stream.write(data)\n        if (opts.dedupe) {\n          recordedLevel = dest.level\n        }\n      } else if (!opts.dedupe) {\n        break\n      }\n    }\n  }\n\n  function emit (...args) {\n    for (const { stream } of this.streams) {\n      if (typeof stream.emit === 'function') {\n        stream.emit(...args)\n      }\n    }\n  }\n\n  function flushSync () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n    }\n  }\n\n  function add (dest) {\n    if (!dest) {\n      return res\n    }\n\n    // Check that dest implements either StreamEntry or DestinationStream\n    const isStream = typeof dest.write === 'function' || dest.stream\n    const stream_ = dest.write ? dest : dest.stream\n    // This is necessary to provide a meaningful error message, otherwise it throws somewhere inside write()\n    if (!isStream) {\n      throw Error('stream object needs to implement either StreamEntry or DestinationStream interface')\n    }\n\n    const { streams, streamLevels } = this\n\n    let level\n    if (typeof dest.levelVal === 'number') {\n      level = dest.levelVal\n    } else if (typeof dest.level === 'string') {\n      level = streamLevels[dest.level]\n    } else if (typeof dest.level === 'number') {\n      level = dest.level\n    } else {\n      level = DEFAULT_INFO_LEVEL\n    }\n\n    const dest_ = {\n      stream: stream_,\n      level,\n      levelVal: undefined,\n      id: counter++\n    }\n\n    streams.unshift(dest_)\n    streams.sort(compareByLevel)\n\n    this.minLevel = streams[0].level\n\n    return res\n  }\n\n  function end () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n      stream.end()\n    }\n  }\n\n  function clone (level) {\n    const streams = new Array(this.streams.length)\n\n    for (let i = 0; i < streams.length; i++) {\n      streams[i] = {\n        level,\n        stream: this.streams[i].stream\n      }\n    }\n\n    return {\n      write,\n      add,\n      minLevel: level,\n      streams,\n      clone,\n      emit,\n      flushSync,\n      [metadata]: true\n    }\n  }\n}\n\nfunction compareByLevel (a, b) {\n  return a.level - b.level\n}\n\nfunction initLoopVar (length, dedupe) {\n  return dedupe ? length - 1 : 0\n}\n\nfunction adjustLoopVar (i, dedupe) {\n  return dedupe ? i - 1 : i + 1\n}\n\nfunction checkLoopVar (i, length, dedupe) {\n  return dedupe ? i >= 0 : i < length\n}\n\nmodule.exports = multistream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/multistream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/proto.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/proto.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst { EventEmitter } = __webpack_require__(/*! node:events */ \"node:events\")\nconst {\n  lsCacheSym,\n  levelValSym,\n  setLevelSym,\n  getLevelSym,\n  chindingsSym,\n  parsedChindingsSym,\n  mixinSym,\n  asJsonSym,\n  writeSym,\n  mixinMergeStrategySym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  serializersSym,\n  formattersSym,\n  errorKeySym,\n  messageKeySym,\n  useOnlyCustomLevelsSym,\n  needsMetadataGsym,\n  redactFmtSym,\n  stringifySym,\n  formatOptsSym,\n  stringifiersSym,\n  msgPrefixSym,\n  hooksSym\n} = __webpack_require__(/*! ./symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst {\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  initialLsCache,\n  genLsCache,\n  assertNoLevelCollisions\n} = __webpack_require__(/*! ./levels */ \"(rsc)/./node_modules/pino/lib/levels.js\")\nconst {\n  asChindings,\n  asJson,\n  buildFormatters,\n  stringify\n} = __webpack_require__(/*! ./tools */ \"(rsc)/./node_modules/pino/lib/tools.js\")\nconst {\n  version\n} = __webpack_require__(/*! ./meta */ \"(rsc)/./node_modules/pino/lib/meta.js\")\nconst redaction = __webpack_require__(/*! ./redaction */ \"(rsc)/./node_modules/pino/lib/redaction.js\")\n\n// note: use of class is satirical\n// https://github.com/pinojs/pino/pull/433#pullrequestreview-127703127\nconst constructor = class Pino {}\nconst prototype = {\n  constructor,\n  child,\n  bindings,\n  setBindings,\n  flush,\n  isLevelEnabled,\n  version,\n  get level () { return this[getLevelSym]() },\n  set level (lvl) { this[setLevelSym](lvl) },\n  get levelVal () { return this[levelValSym] },\n  set levelVal (n) { throw Error('levelVal is read-only') },\n  [lsCacheSym]: initialLsCache,\n  [writeSym]: write,\n  [asJsonSym]: asJson,\n  [getLevelSym]: getLevel,\n  [setLevelSym]: setLevel\n}\n\nObject.setPrototypeOf(prototype, EventEmitter.prototype)\n\n// exporting and consuming the prototype object using factory pattern fixes scoping issues with getters when serializing\nmodule.exports = function () {\n  return Object.create(prototype)\n}\n\nconst resetChildingsFormatter = bindings => bindings\nfunction child (bindings, options) {\n  if (!bindings) {\n    throw Error('missing bindings for child Pino')\n  }\n  options = options || {} // default options to empty object\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const instance = Object.create(this)\n\n  if (options.hasOwnProperty('serializers') === true) {\n    instance[serializersSym] = Object.create(null)\n\n    for (const k in serializers) {\n      instance[serializersSym][k] = serializers[k]\n    }\n    const parentSymbols = Object.getOwnPropertySymbols(serializers)\n    /* eslint no-var: off */\n    for (var i = 0; i < parentSymbols.length; i++) {\n      const ks = parentSymbols[i]\n      instance[serializersSym][ks] = serializers[ks]\n    }\n\n    for (const bk in options.serializers) {\n      instance[serializersSym][bk] = options.serializers[bk]\n    }\n    const bindingsSymbols = Object.getOwnPropertySymbols(options.serializers)\n    for (var bi = 0; bi < bindingsSymbols.length; bi++) {\n      const bks = bindingsSymbols[bi]\n      instance[serializersSym][bks] = options.serializers[bks]\n    }\n  } else instance[serializersSym] = serializers\n  if (options.hasOwnProperty('formatters')) {\n    const { level, bindings: chindings, log } = options.formatters\n    instance[formattersSym] = buildFormatters(\n      level || formatters.level,\n      chindings || resetChildingsFormatter,\n      log || formatters.log\n    )\n  } else {\n    instance[formattersSym] = buildFormatters(\n      formatters.level,\n      resetChildingsFormatter,\n      formatters.log\n    )\n  }\n  if (options.hasOwnProperty('customLevels') === true) {\n    assertNoLevelCollisions(this.levels, options.customLevels)\n    instance.levels = mappings(options.customLevels, instance[useOnlyCustomLevelsSym])\n    genLsCache(instance)\n  }\n\n  // redact must place before asChindings and only replace if exist\n  if ((typeof options.redact === 'object' && options.redact !== null) || Array.isArray(options.redact)) {\n    instance.redact = options.redact // replace redact directly\n    const stringifiers = redaction(instance.redact, stringify)\n    const formatOpts = { stringify: stringifiers[redactFmtSym] }\n    instance[stringifySym] = stringify\n    instance[stringifiersSym] = stringifiers\n    instance[formatOptsSym] = formatOpts\n  }\n\n  if (typeof options.msgPrefix === 'string') {\n    instance[msgPrefixSym] = (this[msgPrefixSym] || '') + options.msgPrefix\n  }\n\n  instance[chindingsSym] = asChindings(instance, bindings)\n  const childLevel = options.level || this.level\n  instance[setLevelSym](childLevel)\n  this.onChild(instance)\n  return instance\n}\n\nfunction bindings () {\n  const chindings = this[chindingsSym]\n  const chindingsJson = `{${chindings.substr(1)}}` // at least contains ,\"pid\":7068,\"hostname\":\"myMac\"\n  const bindingsFromJson = JSON.parse(chindingsJson)\n  delete bindingsFromJson.pid\n  delete bindingsFromJson.hostname\n  return bindingsFromJson\n}\n\nfunction setBindings (newBindings) {\n  const chindings = asChindings(this, newBindings)\n  this[chindingsSym] = chindings\n  delete this[parsedChindingsSym]\n}\n\n/**\n * Default strategy for creating `mergeObject` from arguments and the result from `mixin()`.\n * Fields from `mergeObject` have higher priority in this strategy.\n *\n * @param {Object} mergeObject The object a user has supplied to the logging function.\n * @param {Object} mixinObject The result of the `mixin` method.\n * @return {Object}\n */\nfunction defaultMixinMergeStrategy (mergeObject, mixinObject) {\n  return Object.assign(mixinObject, mergeObject)\n}\n\nfunction write (_obj, msg, num) {\n  const t = this[timeSym]()\n  const mixin = this[mixinSym]\n  const errorKey = this[errorKeySym]\n  const messageKey = this[messageKeySym]\n  const mixinMergeStrategy = this[mixinMergeStrategySym] || defaultMixinMergeStrategy\n  let obj\n  const streamWriteHook = this[hooksSym].streamWrite\n\n  if (_obj === undefined || _obj === null) {\n    obj = {}\n  } else if (_obj instanceof Error) {\n    obj = { [errorKey]: _obj }\n    if (msg === undefined) {\n      msg = _obj.message\n    }\n  } else {\n    obj = _obj\n    if (msg === undefined && _obj[messageKey] === undefined && _obj[errorKey]) {\n      msg = _obj[errorKey].message\n    }\n  }\n\n  if (mixin) {\n    obj = mixinMergeStrategy(obj, mixin(obj, num, this))\n  }\n\n  const s = this[asJsonSym](obj, msg, num, t)\n\n  const stream = this[streamSym]\n  if (stream[needsMetadataGsym] === true) {\n    stream.lastLevel = num\n    stream.lastObj = obj\n    stream.lastMsg = msg\n    stream.lastTime = t.slice(this[timeSliceIndexSym])\n    stream.lastLogger = this // for child loggers\n  }\n  stream.write(streamWriteHook ? streamWriteHook(s) : s)\n}\n\nfunction noop () {}\n\nfunction flush (cb) {\n  if (cb != null && typeof cb !== 'function') {\n    throw Error('callback must be a function')\n  }\n\n  const stream = this[streamSym]\n\n  if (typeof stream.flush === 'function') {\n    stream.flush(cb || noop)\n  } else if (cb) cb()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/proto.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/redaction.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/redaction.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst fastRedact = __webpack_require__(/*! fast-redact */ \"(rsc)/./node_modules/fast-redact/index.js\")\nconst { redactFmtSym, wildcardFirstSym } = __webpack_require__(/*! ./symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst { rx, validator } = fastRedact\n\nconst validate = validator({\n  ERR_PATHS_MUST_BE_STRINGS: () => 'pino – redacted paths must be strings',\n  ERR_INVALID_PATH: (s) => `pino – redact paths array contains an invalid path (${s})`\n})\n\nconst CENSOR = '[Redacted]'\nconst strict = false // TODO should this be configurable?\n\nfunction redaction (opts, serialize) {\n  const { paths, censor } = handle(opts)\n\n  const shape = paths.reduce((o, str) => {\n    rx.lastIndex = 0\n    const first = rx.exec(str)\n    const next = rx.exec(str)\n\n    // ns is the top-level path segment, brackets + quoting removed.\n    let ns = first[1] !== undefined\n      ? first[1].replace(/^(?:\"|'|`)(.*)(?:\"|'|`)$/, '$1')\n      : first[0]\n\n    if (ns === '*') {\n      ns = wildcardFirstSym\n    }\n\n    // top level key:\n    if (next === null) {\n      o[ns] = null\n      return o\n    }\n\n    // path with at least two segments:\n    // if ns is already redacted at the top level, ignore lower level redactions\n    if (o[ns] === null) {\n      return o\n    }\n\n    const { index } = next\n    const nextPath = `${str.substr(index, str.length - 1)}`\n\n    o[ns] = o[ns] || []\n\n    // shape is a mix of paths beginning with literal values and wildcard\n    // paths [ \"a.b.c\", \"*.b.z\" ] should reduce to a shape of\n    // { \"a\": [ \"b.c\", \"b.z\" ], *: [ \"b.z\" ] }\n    // note: \"b.z\" is in both \"a\" and * arrays because \"a\" matches the wildcard.\n    // (* entry has wildcardFirstSym as key)\n    if (ns !== wildcardFirstSym && o[ns].length === 0) {\n      // first time ns's get all '*' redactions so far\n      o[ns].push(...(o[wildcardFirstSym] || []))\n    }\n\n    if (ns === wildcardFirstSym) {\n      // new * path gets added to all previously registered literal ns's.\n      Object.keys(o).forEach(function (k) {\n        if (o[k]) {\n          o[k].push(nextPath)\n        }\n      })\n    }\n\n    o[ns].push(nextPath)\n    return o\n  }, {})\n\n  // the redactor assigned to the format symbol key\n  // provides top level redaction for instances where\n  // an object is interpolated into the msg string\n  const result = {\n    [redactFmtSym]: fastRedact({ paths, censor, serialize, strict })\n  }\n\n  const topCensor = (...args) => {\n    return typeof censor === 'function' ? serialize(censor(...args)) : serialize(censor)\n  }\n\n  return [...Object.keys(shape), ...Object.getOwnPropertySymbols(shape)].reduce((o, k) => {\n    // top level key:\n    if (shape[k] === null) {\n      o[k] = (value) => topCensor(value, [k])\n    } else {\n      const wrappedCensor = typeof censor === 'function'\n        ? (value, path) => {\n            return censor(value, [k, ...path])\n          }\n        : censor\n      o[k] = fastRedact({\n        paths: shape[k],\n        censor: wrappedCensor,\n        serialize,\n        strict\n      })\n    }\n    return o\n  }, result)\n}\n\nfunction handle (opts) {\n  if (Array.isArray(opts)) {\n    opts = { paths: opts, censor: CENSOR }\n    validate(opts)\n    return opts\n  }\n  let { paths, censor = CENSOR, remove } = opts\n  if (Array.isArray(paths) === false) { throw Error('pino – redact must contain an array of strings') }\n  if (remove === true) censor = undefined\n  validate({ paths, censor })\n\n  return { paths, censor }\n}\n\nmodule.exports = redaction\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/redaction.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/symbols.js":
/*!******************************************!*\
  !*** ./node_modules/pino/lib/symbols.js ***!
  \******************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst setLevelSym = Symbol('pino.setLevel')\nconst getLevelSym = Symbol('pino.getLevel')\nconst levelValSym = Symbol('pino.levelVal')\nconst levelCompSym = Symbol('pino.levelComp')\nconst useLevelLabelsSym = Symbol('pino.useLevelLabels')\nconst useOnlyCustomLevelsSym = Symbol('pino.useOnlyCustomLevels')\nconst mixinSym = Symbol('pino.mixin')\n\nconst lsCacheSym = Symbol('pino.lsCache')\nconst chindingsSym = Symbol('pino.chindings')\n\nconst asJsonSym = Symbol('pino.asJson')\nconst writeSym = Symbol('pino.write')\nconst redactFmtSym = Symbol('pino.redactFmt')\n\nconst timeSym = Symbol('pino.time')\nconst timeSliceIndexSym = Symbol('pino.timeSliceIndex')\nconst streamSym = Symbol('pino.stream')\nconst stringifySym = Symbol('pino.stringify')\nconst stringifySafeSym = Symbol('pino.stringifySafe')\nconst stringifiersSym = Symbol('pino.stringifiers')\nconst endSym = Symbol('pino.end')\nconst formatOptsSym = Symbol('pino.formatOpts')\nconst messageKeySym = Symbol('pino.messageKey')\nconst errorKeySym = Symbol('pino.errorKey')\nconst nestedKeySym = Symbol('pino.nestedKey')\nconst nestedKeyStrSym = Symbol('pino.nestedKeyStr')\nconst mixinMergeStrategySym = Symbol('pino.mixinMergeStrategy')\nconst msgPrefixSym = Symbol('pino.msgPrefix')\n\nconst wildcardFirstSym = Symbol('pino.wildcardFirst')\n\n// public symbols, no need to use the same pino\n// version for these\nconst serializersSym = Symbol.for('pino.serializers')\nconst formattersSym = Symbol.for('pino.formatters')\nconst hooksSym = Symbol.for('pino.hooks')\nconst needsMetadataGsym = Symbol.for('pino.metadata')\n\nmodule.exports = {\n  setLevelSym,\n  getLevelSym,\n  levelValSym,\n  levelCompSym,\n  useLevelLabelsSym,\n  mixinSym,\n  lsCacheSym,\n  chindingsSym,\n  asJsonSym,\n  writeSym,\n  serializersSym,\n  redactFmtSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  errorKeySym,\n  nestedKeySym,\n  wildcardFirstSym,\n  needsMetadataGsym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym,\n  msgPrefixSym\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/symbols.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/time.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/time.js ***!
  \***************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst nullTime = () => ''\n\nconst epochTime = () => `,\"time\":${Date.now()}`\n\nconst unixTime = () => `,\"time\":${Math.round(Date.now() / 1000.0)}`\n\nconst isoTime = () => `,\"time\":\"${new Date(Date.now()).toISOString()}\"` // using Date.now() for testability\n\nmodule.exports = { nullTime, epochTime, unixTime, isoTime }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvdGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxtQ0FBbUMsV0FBVzs7QUFFOUMsa0NBQWtDLGdDQUFnQzs7QUFFbEUsa0NBQWtDLG1DQUFtQzs7QUFFckUsbUJBQW1CIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXHBpbm9cXGxpYlxcdGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgbnVsbFRpbWUgPSAoKSA9PiAnJ1xuXG5jb25zdCBlcG9jaFRpbWUgPSAoKSA9PiBgLFwidGltZVwiOiR7RGF0ZS5ub3coKX1gXG5cbmNvbnN0IHVuaXhUaW1lID0gKCkgPT4gYCxcInRpbWVcIjoke01hdGgucm91bmQoRGF0ZS5ub3coKSAvIDEwMDAuMCl9YFxuXG5jb25zdCBpc29UaW1lID0gKCkgPT4gYCxcInRpbWVcIjpcIiR7bmV3IERhdGUoRGF0ZS5ub3coKSkudG9JU09TdHJpbmcoKX1cImAgLy8gdXNpbmcgRGF0ZS5ub3coKSBmb3IgdGVzdGFiaWxpdHlcblxubW9kdWxlLmV4cG9ydHMgPSB7IG51bGxUaW1lLCBlcG9jaFRpbWUsIHVuaXhUaW1lLCBpc29UaW1lIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/time.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/tools.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/tools.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst format = __webpack_require__(/*! quick-format-unescaped */ \"(rsc)/./node_modules/quick-format-unescaped/index.js\")\nconst { mapHttpRequest, mapHttpResponse } = __webpack_require__(/*! pino-std-serializers */ \"(rsc)/./node_modules/pino-std-serializers/index.js\")\nconst SonicBoom = __webpack_require__(/*! sonic-boom */ \"(rsc)/./node_modules/sonic-boom/index.js\")\nconst onExit = __webpack_require__(/*! on-exit-leak-free */ \"(rsc)/./node_modules/on-exit-leak-free/index.js\")\nconst {\n  lsCacheSym,\n  chindingsSym,\n  writeSym,\n  serializersSym,\n  formatOptsSym,\n  endSym,\n  stringifiersSym,\n  stringifySym,\n  stringifySafeSym,\n  wildcardFirstSym,\n  nestedKeySym,\n  formattersSym,\n  messageKeySym,\n  errorKeySym,\n  nestedKeyStrSym,\n  msgPrefixSym\n} = __webpack_require__(/*! ./symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst { isMainThread } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst transport = __webpack_require__(/*! ./transport */ \"(rsc)/./node_modules/pino/lib/transport.js\")\n\nfunction noop () {\n}\n\nfunction genLog (level, hook) {\n  if (!hook) return LOG\n\n  return function hookWrappedLog (...args) {\n    hook.call(this, args, LOG, level)\n  }\n\n  function LOG (o, ...n) {\n    if (typeof o === 'object') {\n      let msg = o\n      if (o !== null) {\n        if (o.method && o.headers && o.socket) {\n          o = mapHttpRequest(o)\n        } else if (typeof o.setHeader === 'function') {\n          o = mapHttpResponse(o)\n        }\n      }\n      let formatParams\n      if (msg === null && n.length === 0) {\n        formatParams = [null]\n      } else {\n        msg = n.shift()\n        formatParams = n\n      }\n      // We do not use a coercive check for `msg` as it is\n      // measurably slower than the explicit checks.\n      if (typeof this[msgPrefixSym] === 'string' && msg !== undefined && msg !== null) {\n        msg = this[msgPrefixSym] + msg\n      }\n      this[writeSym](o, format(msg, formatParams, this[formatOptsSym]), level)\n    } else {\n      let msg = o === undefined ? n.shift() : o\n\n      // We do not use a coercive check for `msg` as it is\n      // measurably slower than the explicit checks.\n      if (typeof this[msgPrefixSym] === 'string' && msg !== undefined && msg !== null) {\n        msg = this[msgPrefixSym] + msg\n      }\n      this[writeSym](null, format(msg, n, this[formatOptsSym]), level)\n    }\n  }\n}\n\n// magically escape strings for json\n// relying on their charCodeAt\n// everything below 32 needs JSON.stringify()\n// 34 and 92 happens all the time, so we\n// have a fast case for them\nfunction asString (str) {\n  let result = ''\n  let last = 0\n  let found = false\n  let point = 255\n  const l = str.length\n  if (l > 100) {\n    return JSON.stringify(str)\n  }\n  for (var i = 0; i < l && point >= 32; i++) {\n    point = str.charCodeAt(i)\n    if (point === 34 || point === 92) {\n      result += str.slice(last, i) + '\\\\'\n      last = i\n      found = true\n    }\n  }\n  if (!found) {\n    result = str\n  } else {\n    result += str.slice(last)\n  }\n  return point < 32 ? JSON.stringify(str) : '\"' + result + '\"'\n}\n\nfunction asJson (obj, msg, num, time) {\n  const stringify = this[stringifySym]\n  const stringifySafe = this[stringifySafeSym]\n  const stringifiers = this[stringifiersSym]\n  const end = this[endSym]\n  const chindings = this[chindingsSym]\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const messageKey = this[messageKeySym]\n  const errorKey = this[errorKeySym]\n  let data = this[lsCacheSym][num] + time\n\n  // we need the child bindings added to the output first so instance logged\n  // objects can take precedence when JSON.parse-ing the resulting log line\n  data = data + chindings\n\n  let value\n  if (formatters.log) {\n    obj = formatters.log(obj)\n  }\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  let propStr = ''\n  for (const key in obj) {\n    value = obj[key]\n    if (Object.prototype.hasOwnProperty.call(obj, key) && value !== undefined) {\n      if (serializers[key]) {\n        value = serializers[key](value)\n      } else if (key === errorKey && serializers.err) {\n        value = serializers.err(value)\n      }\n\n      const stringifier = stringifiers[key] || wildcardStringifier\n\n      switch (typeof value) {\n        case 'undefined':\n        case 'function':\n          continue\n        case 'number':\n          /* eslint no-fallthrough: \"off\" */\n          if (Number.isFinite(value) === false) {\n            value = null\n          }\n        // this case explicitly falls through to the next one\n        case 'boolean':\n          if (stringifier) value = stringifier(value)\n          break\n        case 'string':\n          value = (stringifier || asString)(value)\n          break\n        default:\n          value = (stringifier || stringify)(value, stringifySafe)\n      }\n      if (value === undefined) continue\n      const strKey = asString(key)\n      propStr += ',' + strKey + ':' + value\n    }\n  }\n\n  let msgStr = ''\n  if (msg !== undefined) {\n    value = serializers[messageKey] ? serializers[messageKey](msg) : msg\n    const stringifier = stringifiers[messageKey] || wildcardStringifier\n\n    switch (typeof value) {\n      case 'function':\n        break\n      case 'number':\n        /* eslint no-fallthrough: \"off\" */\n        if (Number.isFinite(value) === false) {\n          value = null\n        }\n      // this case explicitly falls through to the next one\n      case 'boolean':\n        if (stringifier) value = stringifier(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      case 'string':\n        value = (stringifier || asString)(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      default:\n        value = (stringifier || stringify)(value, stringifySafe)\n        msgStr = ',\"' + messageKey + '\":' + value\n    }\n  }\n\n  if (this[nestedKeySym] && propStr) {\n    // place all the obj properties under the specified key\n    // the nested key is already formatted from the constructor\n    return data + this[nestedKeyStrSym] + propStr.slice(1) + '}' + msgStr + end\n  } else {\n    return data + propStr + msgStr + end\n  }\n}\n\nfunction asChindings (instance, bindings) {\n  let value\n  let data = instance[chindingsSym]\n  const stringify = instance[stringifySym]\n  const stringifySafe = instance[stringifySafeSym]\n  const stringifiers = instance[stringifiersSym]\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  const serializers = instance[serializersSym]\n  const formatter = instance[formattersSym].bindings\n  bindings = formatter(bindings)\n\n  for (const key in bindings) {\n    value = bindings[key]\n    const valid = key !== 'level' &&\n      key !== 'serializers' &&\n      key !== 'formatters' &&\n      key !== 'customLevels' &&\n      bindings.hasOwnProperty(key) &&\n      value !== undefined\n    if (valid === true) {\n      value = serializers[key] ? serializers[key](value) : value\n      value = (stringifiers[key] || wildcardStringifier || stringify)(value, stringifySafe)\n      if (value === undefined) continue\n      data += ',\"' + key + '\":' + value\n    }\n  }\n  return data\n}\n\nfunction hasBeenTampered (stream) {\n  return stream.write !== stream.constructor.prototype.write\n}\n\nfunction buildSafeSonicBoom (opts) {\n  const stream = new SonicBoom(opts)\n  stream.on('error', filterBrokenPipe)\n  // If we are sync: false, we must flush on exit\n  if (!opts.sync && isMainThread) {\n    onExit.register(stream, autoEnd)\n\n    stream.on('close', function () {\n      onExit.unregister(stream)\n    })\n  }\n  return stream\n\n  function filterBrokenPipe (err) {\n    // Impossible to replicate across all operating systems\n    /* istanbul ignore next */\n    if (err.code === 'EPIPE') {\n      // If we get EPIPE, we should stop logging here\n      // however we have no control to the consumer of\n      // SonicBoom, so we just overwrite the write method\n      stream.write = noop\n      stream.end = noop\n      stream.flushSync = noop\n      stream.destroy = noop\n      return\n    }\n    stream.removeListener('error', filterBrokenPipe)\n    stream.emit('error', err)\n  }\n}\n\nfunction autoEnd (stream, eventName) {\n  // This check is needed only on some platforms\n  /* istanbul ignore next */\n  if (stream.destroyed) {\n    return\n  }\n\n  if (eventName === 'beforeExit') {\n    // We still have an event loop, let's use it\n    stream.flush()\n    stream.on('drain', function () {\n      stream.end()\n    })\n  } else {\n    // For some reason istanbul is not detecting this, but it's there\n    /* istanbul ignore next */\n    // We do not have an event loop, so flush synchronously\n    stream.flushSync()\n  }\n}\n\nfunction createArgsNormalizer (defaultOptions) {\n  return function normalizeArgs (instance, caller, opts = {}, stream) {\n    // support stream as a string\n    if (typeof opts === 'string') {\n      stream = buildSafeSonicBoom({ dest: opts })\n      opts = {}\n    } else if (typeof stream === 'string') {\n      if (opts && opts.transport) {\n        throw Error('only one of option.transport or stream can be specified')\n      }\n      stream = buildSafeSonicBoom({ dest: stream })\n    } else if (opts instanceof SonicBoom || opts.writable || opts._writableState) {\n      stream = opts\n      opts = {}\n    } else if (opts.transport) {\n      if (opts.transport instanceof SonicBoom || opts.transport.writable || opts.transport._writableState) {\n        throw Error('option.transport do not allow stream, please pass to option directly. e.g. pino(transport)')\n      }\n      if (opts.transport.targets && opts.transport.targets.length && opts.formatters && typeof opts.formatters.level === 'function') {\n        throw Error('option.transport.targets do not allow custom level formatters')\n      }\n\n      let customLevels\n      if (opts.customLevels) {\n        customLevels = opts.useOnlyCustomLevels ? opts.customLevels : Object.assign({}, opts.levels, opts.customLevels)\n      }\n      stream = transport({ caller, ...opts.transport, levels: customLevels })\n    }\n    opts = Object.assign({}, defaultOptions, opts)\n    opts.serializers = Object.assign({}, defaultOptions.serializers, opts.serializers)\n    opts.formatters = Object.assign({}, defaultOptions.formatters, opts.formatters)\n\n    if (opts.prettyPrint) {\n      throw new Error('prettyPrint option is no longer supported, see the pino-pretty package (https://github.com/pinojs/pino-pretty)')\n    }\n\n    const { enabled, onChild } = opts\n    if (enabled === false) opts.level = 'silent'\n    if (!onChild) opts.onChild = noop\n    if (!stream) {\n      if (!hasBeenTampered(process.stdout)) {\n        // If process.stdout.fd is undefined, it means that we are running\n        // in a worker thread. Let's assume we are logging to file descriptor 1.\n        stream = buildSafeSonicBoom({ fd: process.stdout.fd || 1 })\n      } else {\n        stream = process.stdout\n      }\n    }\n    return { opts, stream }\n  }\n}\n\nfunction stringify (obj, stringifySafeFn) {\n  try {\n    return JSON.stringify(obj)\n  } catch (_) {\n    try {\n      const stringify = stringifySafeFn || this[stringifySafeSym]\n      return stringify(obj)\n    } catch (_) {\n      return '\"[unable to serialize, circular reference is too complex to analyze]\"'\n    }\n  }\n}\n\nfunction buildFormatters (level, bindings, log) {\n  return {\n    level,\n    bindings,\n    log\n  }\n}\n\n/**\n * Convert a string integer file descriptor to a proper native integer\n * file descriptor.\n *\n * @param {string} destination The file descriptor string to attempt to convert.\n *\n * @returns {Number}\n */\nfunction normalizeDestFileDescriptor (destination) {\n  const fd = Number(destination)\n  if (typeof destination === 'string' && Number.isFinite(fd)) {\n    return fd\n  }\n  // destination could be undefined if we are in a worker\n  if (destination === undefined) {\n    // This is stdout in UNIX systems\n    return 1\n  }\n  return destination\n}\n\nmodule.exports = {\n  noop,\n  buildSafeSonicBoom,\n  asChindings,\n  asJson,\n  genLog,\n  createArgsNormalizer,\n  stringify,\n  buildFormatters,\n  normalizeDestFileDescriptor\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/tools.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/transport.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/transport.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst { createRequire } = __webpack_require__(/*! module */ \"module\")\nconst getCallers = __webpack_require__(/*! ./caller */ \"(rsc)/./node_modules/pino/lib/caller.js\")\nconst { join, isAbsolute, sep } = __webpack_require__(/*! node:path */ \"node:path\")\nconst sleep = __webpack_require__(/*! atomic-sleep */ \"(rsc)/./node_modules/atomic-sleep/index.js\")\nconst onExit = __webpack_require__(/*! on-exit-leak-free */ \"(rsc)/./node_modules/on-exit-leak-free/index.js\")\nconst ThreadStream = __webpack_require__(/*! thread-stream */ \"(rsc)/./node_modules/thread-stream/index.js\")\n\nfunction setupOnExit (stream) {\n  // This is leak free, it does not leave event handlers\n  onExit.register(stream, autoEnd)\n  onExit.registerBeforeExit(stream, flush)\n\n  stream.on('close', function () {\n    onExit.unregister(stream)\n  })\n}\n\nfunction buildStream (filename, workerData, workerOpts, sync) {\n  const stream = new ThreadStream({\n    filename,\n    workerData,\n    workerOpts,\n    sync\n  })\n\n  stream.on('ready', onReady)\n  stream.on('close', function () {\n    process.removeListener('exit', onExit)\n  })\n\n  process.on('exit', onExit)\n\n  function onReady () {\n    process.removeListener('exit', onExit)\n    stream.unref()\n\n    if (workerOpts.autoEnd !== false) {\n      setupOnExit(stream)\n    }\n  }\n\n  function onExit () {\n    /* istanbul ignore next */\n    if (stream.closed) {\n      return\n    }\n    stream.flushSync()\n    // Apparently there is a very sporadic race condition\n    // that in certain OS would prevent the messages to be flushed\n    // because the thread might not have been created still.\n    // Unfortunately we need to sleep(100) in this case.\n    sleep(100)\n    stream.end()\n  }\n\n  return stream\n}\n\nfunction autoEnd (stream) {\n  stream.ref()\n  stream.flushSync()\n  stream.end()\n  stream.once('close', function () {\n    stream.unref()\n  })\n}\n\nfunction flush (stream) {\n  stream.flushSync()\n}\n\nfunction transport (fullOptions) {\n  const { pipeline, targets, levels, dedupe, worker = {}, caller = getCallers(), sync = false } = fullOptions\n\n  const options = {\n    ...fullOptions.options\n  }\n\n  // Backwards compatibility\n  const callers = typeof caller === 'string' ? [caller] : caller\n\n  // This will be eventually modified by bundlers\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n\n  let target = fullOptions.target\n\n  if (target && targets) {\n    throw new Error('only one of target or targets can be specified')\n  }\n\n  if (targets) {\n    target = bundlerOverrides['pino-worker'] || join(__dirname, 'worker.js')\n    options.targets = targets.filter(dest => dest.target).map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })\n    options.pipelines = targets.filter(dest => dest.pipeline).map((dest) => {\n      return dest.pipeline.map((t) => {\n        return {\n          ...t,\n          level: dest.level, // duplicate the pipeline `level` property defined in the upper level\n          target: fixTarget(t.target)\n        }\n      })\n    })\n  } else if (pipeline) {\n    target = bundlerOverrides['pino-worker'] || join(__dirname, 'worker.js')\n    options.pipelines = [pipeline.map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })]\n  }\n\n  if (levels) {\n    options.levels = levels\n  }\n\n  if (dedupe) {\n    options.dedupe = dedupe\n  }\n\n  options.pinoWillSendConfig = true\n\n  return buildStream(fixTarget(target), options, worker, sync)\n\n  function fixTarget (origin) {\n    origin = bundlerOverrides[origin] || origin\n\n    if (isAbsolute(origin) || origin.indexOf('file://') === 0) {\n      return origin\n    }\n\n    if (origin === 'pino/file') {\n      return join(__dirname, '..', 'file.js')\n    }\n\n    let fixTarget\n\n    for (const filePath of callers) {\n      try {\n        const context = filePath === 'node:repl'\n          ? process.cwd() + sep\n          : filePath\n\n        fixTarget = createRequire(context).resolve(origin)\n        break\n      } catch (err) {\n        // Silent catch\n        continue\n      }\n    }\n\n    if (!fixTarget) {\n      throw new Error(`unable to determine transport target for \"${origin}\"`)\n    }\n\n    return fixTarget\n  }\n}\n\nmodule.exports = transport\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvdHJhbnNwb3J0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLFFBQVEsZ0JBQWdCLEVBQUUsbUJBQU8sQ0FBQyxzQkFBUTtBQUMxQyxtQkFBbUIsbUJBQU8sQ0FBQyx5REFBVTtBQUNyQyxRQUFRLHdCQUF3QixFQUFFLG1CQUFPLENBQUMsNEJBQVc7QUFDckQsY0FBYyxtQkFBTyxDQUFDLGdFQUFjO0FBQ3BDLGVBQWUsbUJBQU8sQ0FBQywwRUFBbUI7QUFDMUMscUJBQXFCLG1CQUFPLENBQUMsa0VBQWU7O0FBRTVDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVLDhDQUE4Qyx3Q0FBd0M7O0FBRWhHO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLG1FQUFtRSxPQUFPO0FBQzFFOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxwaW5vXFxsaWJcXHRyYW5zcG9ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgeyBjcmVhdGVSZXF1aXJlIH0gPSByZXF1aXJlKCdtb2R1bGUnKVxuY29uc3QgZ2V0Q2FsbGVycyA9IHJlcXVpcmUoJy4vY2FsbGVyJylcbmNvbnN0IHsgam9pbiwgaXNBYnNvbHV0ZSwgc2VwIH0gPSByZXF1aXJlKCdub2RlOnBhdGgnKVxuY29uc3Qgc2xlZXAgPSByZXF1aXJlKCdhdG9taWMtc2xlZXAnKVxuY29uc3Qgb25FeGl0ID0gcmVxdWlyZSgnb24tZXhpdC1sZWFrLWZyZWUnKVxuY29uc3QgVGhyZWFkU3RyZWFtID0gcmVxdWlyZSgndGhyZWFkLXN0cmVhbScpXG5cbmZ1bmN0aW9uIHNldHVwT25FeGl0IChzdHJlYW0pIHtcbiAgLy8gVGhpcyBpcyBsZWFrIGZyZWUsIGl0IGRvZXMgbm90IGxlYXZlIGV2ZW50IGhhbmRsZXJzXG4gIG9uRXhpdC5yZWdpc3RlcihzdHJlYW0sIGF1dG9FbmQpXG4gIG9uRXhpdC5yZWdpc3RlckJlZm9yZUV4aXQoc3RyZWFtLCBmbHVzaClcblxuICBzdHJlYW0ub24oJ2Nsb3NlJywgZnVuY3Rpb24gKCkge1xuICAgIG9uRXhpdC51bnJlZ2lzdGVyKHN0cmVhbSlcbiAgfSlcbn1cblxuZnVuY3Rpb24gYnVpbGRTdHJlYW0gKGZpbGVuYW1lLCB3b3JrZXJEYXRhLCB3b3JrZXJPcHRzLCBzeW5jKSB7XG4gIGNvbnN0IHN0cmVhbSA9IG5ldyBUaHJlYWRTdHJlYW0oe1xuICAgIGZpbGVuYW1lLFxuICAgIHdvcmtlckRhdGEsXG4gICAgd29ya2VyT3B0cyxcbiAgICBzeW5jXG4gIH0pXG5cbiAgc3RyZWFtLm9uKCdyZWFkeScsIG9uUmVhZHkpXG4gIHN0cmVhbS5vbignY2xvc2UnLCBmdW5jdGlvbiAoKSB7XG4gICAgcHJvY2Vzcy5yZW1vdmVMaXN0ZW5lcignZXhpdCcsIG9uRXhpdClcbiAgfSlcblxuICBwcm9jZXNzLm9uKCdleGl0Jywgb25FeGl0KVxuXG4gIGZ1bmN0aW9uIG9uUmVhZHkgKCkge1xuICAgIHByb2Nlc3MucmVtb3ZlTGlzdGVuZXIoJ2V4aXQnLCBvbkV4aXQpXG4gICAgc3RyZWFtLnVucmVmKClcblxuICAgIGlmICh3b3JrZXJPcHRzLmF1dG9FbmQgIT09IGZhbHNlKSB7XG4gICAgICBzZXR1cE9uRXhpdChzdHJlYW0pXG4gICAgfVxuICB9XG5cbiAgZnVuY3Rpb24gb25FeGl0ICgpIHtcbiAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICAgIGlmIChzdHJlYW0uY2xvc2VkKSB7XG4gICAgICByZXR1cm5cbiAgICB9XG4gICAgc3RyZWFtLmZsdXNoU3luYygpXG4gICAgLy8gQXBwYXJlbnRseSB0aGVyZSBpcyBhIHZlcnkgc3BvcmFkaWMgcmFjZSBjb25kaXRpb25cbiAgICAvLyB0aGF0IGluIGNlcnRhaW4gT1Mgd291bGQgcHJldmVudCB0aGUgbWVzc2FnZXMgdG8gYmUgZmx1c2hlZFxuICAgIC8vIGJlY2F1c2UgdGhlIHRocmVhZCBtaWdodCBub3QgaGF2ZSBiZWVuIGNyZWF0ZWQgc3RpbGwuXG4gICAgLy8gVW5mb3J0dW5hdGVseSB3ZSBuZWVkIHRvIHNsZWVwKDEwMCkgaW4gdGhpcyBjYXNlLlxuICAgIHNsZWVwKDEwMClcbiAgICBzdHJlYW0uZW5kKClcbiAgfVxuXG4gIHJldHVybiBzdHJlYW1cbn1cblxuZnVuY3Rpb24gYXV0b0VuZCAoc3RyZWFtKSB7XG4gIHN0cmVhbS5yZWYoKVxuICBzdHJlYW0uZmx1c2hTeW5jKClcbiAgc3RyZWFtLmVuZCgpXG4gIHN0cmVhbS5vbmNlKCdjbG9zZScsIGZ1bmN0aW9uICgpIHtcbiAgICBzdHJlYW0udW5yZWYoKVxuICB9KVxufVxuXG5mdW5jdGlvbiBmbHVzaCAoc3RyZWFtKSB7XG4gIHN0cmVhbS5mbHVzaFN5bmMoKVxufVxuXG5mdW5jdGlvbiB0cmFuc3BvcnQgKGZ1bGxPcHRpb25zKSB7XG4gIGNvbnN0IHsgcGlwZWxpbmUsIHRhcmdldHMsIGxldmVscywgZGVkdXBlLCB3b3JrZXIgPSB7fSwgY2FsbGVyID0gZ2V0Q2FsbGVycygpLCBzeW5jID0gZmFsc2UgfSA9IGZ1bGxPcHRpb25zXG5cbiAgY29uc3Qgb3B0aW9ucyA9IHtcbiAgICAuLi5mdWxsT3B0aW9ucy5vcHRpb25zXG4gIH1cblxuICAvLyBCYWNrd2FyZHMgY29tcGF0aWJpbGl0eVxuICBjb25zdCBjYWxsZXJzID0gdHlwZW9mIGNhbGxlciA9PT0gJ3N0cmluZycgPyBbY2FsbGVyXSA6IGNhbGxlclxuXG4gIC8vIFRoaXMgd2lsbCBiZSBldmVudHVhbGx5IG1vZGlmaWVkIGJ5IGJ1bmRsZXJzXG4gIGNvbnN0IGJ1bmRsZXJPdmVycmlkZXMgPSAnX19idW5kbGVyUGF0aHNPdmVycmlkZXMnIGluIGdsb2JhbFRoaXMgPyBnbG9iYWxUaGlzLl9fYnVuZGxlclBhdGhzT3ZlcnJpZGVzIDoge31cblxuICBsZXQgdGFyZ2V0ID0gZnVsbE9wdGlvbnMudGFyZ2V0XG5cbiAgaWYgKHRhcmdldCAmJiB0YXJnZXRzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdvbmx5IG9uZSBvZiB0YXJnZXQgb3IgdGFyZ2V0cyBjYW4gYmUgc3BlY2lmaWVkJylcbiAgfVxuXG4gIGlmICh0YXJnZXRzKSB7XG4gICAgdGFyZ2V0ID0gYnVuZGxlck92ZXJyaWRlc1sncGluby13b3JrZXInXSB8fCBqb2luKF9fZGlybmFtZSwgJ3dvcmtlci5qcycpXG4gICAgb3B0aW9ucy50YXJnZXRzID0gdGFyZ2V0cy5maWx0ZXIoZGVzdCA9PiBkZXN0LnRhcmdldCkubWFwKChkZXN0KSA9PiB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5kZXN0LFxuICAgICAgICB0YXJnZXQ6IGZpeFRhcmdldChkZXN0LnRhcmdldClcbiAgICAgIH1cbiAgICB9KVxuICAgIG9wdGlvbnMucGlwZWxpbmVzID0gdGFyZ2V0cy5maWx0ZXIoZGVzdCA9PiBkZXN0LnBpcGVsaW5lKS5tYXAoKGRlc3QpID0+IHtcbiAgICAgIHJldHVybiBkZXN0LnBpcGVsaW5lLm1hcCgodCkgPT4ge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIC4uLnQsXG4gICAgICAgICAgbGV2ZWw6IGRlc3QubGV2ZWwsIC8vIGR1cGxpY2F0ZSB0aGUgcGlwZWxpbmUgYGxldmVsYCBwcm9wZXJ0eSBkZWZpbmVkIGluIHRoZSB1cHBlciBsZXZlbFxuICAgICAgICAgIHRhcmdldDogZml4VGFyZ2V0KHQudGFyZ2V0KVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH0pXG4gIH0gZWxzZSBpZiAocGlwZWxpbmUpIHtcbiAgICB0YXJnZXQgPSBidW5kbGVyT3ZlcnJpZGVzWydwaW5vLXdvcmtlciddIHx8IGpvaW4oX19kaXJuYW1lLCAnd29ya2VyLmpzJylcbiAgICBvcHRpb25zLnBpcGVsaW5lcyA9IFtwaXBlbGluZS5tYXAoKGRlc3QpID0+IHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLmRlc3QsXG4gICAgICAgIHRhcmdldDogZml4VGFyZ2V0KGRlc3QudGFyZ2V0KVxuICAgICAgfVxuICAgIH0pXVxuICB9XG5cbiAgaWYgKGxldmVscykge1xuICAgIG9wdGlvbnMubGV2ZWxzID0gbGV2ZWxzXG4gIH1cblxuICBpZiAoZGVkdXBlKSB7XG4gICAgb3B0aW9ucy5kZWR1cGUgPSBkZWR1cGVcbiAgfVxuXG4gIG9wdGlvbnMucGlub1dpbGxTZW5kQ29uZmlnID0gdHJ1ZVxuXG4gIHJldHVybiBidWlsZFN0cmVhbShmaXhUYXJnZXQodGFyZ2V0KSwgb3B0aW9ucywgd29ya2VyLCBzeW5jKVxuXG4gIGZ1bmN0aW9uIGZpeFRhcmdldCAob3JpZ2luKSB7XG4gICAgb3JpZ2luID0gYnVuZGxlck92ZXJyaWRlc1tvcmlnaW5dIHx8IG9yaWdpblxuXG4gICAgaWYgKGlzQWJzb2x1dGUob3JpZ2luKSB8fCBvcmlnaW4uaW5kZXhPZignZmlsZTovLycpID09PSAwKSB7XG4gICAgICByZXR1cm4gb3JpZ2luXG4gICAgfVxuXG4gICAgaWYgKG9yaWdpbiA9PT0gJ3Bpbm8vZmlsZScpIHtcbiAgICAgIHJldHVybiBqb2luKF9fZGlybmFtZSwgJy4uJywgJ2ZpbGUuanMnKVxuICAgIH1cblxuICAgIGxldCBmaXhUYXJnZXRcblxuICAgIGZvciAoY29uc3QgZmlsZVBhdGggb2YgY2FsbGVycykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgY29udGV4dCA9IGZpbGVQYXRoID09PSAnbm9kZTpyZXBsJ1xuICAgICAgICAgID8gcHJvY2Vzcy5jd2QoKSArIHNlcFxuICAgICAgICAgIDogZmlsZVBhdGhcblxuICAgICAgICBmaXhUYXJnZXQgPSBjcmVhdGVSZXF1aXJlKGNvbnRleHQpLnJlc29sdmUob3JpZ2luKVxuICAgICAgICBicmVha1xuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIC8vIFNpbGVudCBjYXRjaFxuICAgICAgICBjb250aW51ZVxuICAgICAgfVxuICAgIH1cblxuICAgIGlmICghZml4VGFyZ2V0KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYHVuYWJsZSB0byBkZXRlcm1pbmUgdHJhbnNwb3J0IHRhcmdldCBmb3IgXCIke29yaWdpbn1cImApXG4gICAgfVxuXG4gICAgcmV0dXJuIGZpeFRhcmdldFxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gdHJhbnNwb3J0XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/transport.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/pino.js":
/*!***********************************!*\
  !*** ./node_modules/pino/pino.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst os = __webpack_require__(/*! node:os */ \"node:os\")\nconst stdSerializers = __webpack_require__(/*! pino-std-serializers */ \"(rsc)/./node_modules/pino-std-serializers/index.js\")\nconst caller = __webpack_require__(/*! ./lib/caller */ \"(rsc)/./node_modules/pino/lib/caller.js\")\nconst redaction = __webpack_require__(/*! ./lib/redaction */ \"(rsc)/./node_modules/pino/lib/redaction.js\")\nconst time = __webpack_require__(/*! ./lib/time */ \"(rsc)/./node_modules/pino/lib/time.js\")\nconst proto = __webpack_require__(/*! ./lib/proto */ \"(rsc)/./node_modules/pino/lib/proto.js\")\nconst symbols = __webpack_require__(/*! ./lib/symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst { configure } = __webpack_require__(/*! safe-stable-stringify */ \"(rsc)/./node_modules/safe-stable-stringify/index.js\")\nconst { assertDefaultLevelFound, mappings, genLsCache, genLevelComparison, assertLevelComparison } = __webpack_require__(/*! ./lib/levels */ \"(rsc)/./node_modules/pino/lib/levels.js\")\nconst { DEFAULT_LEVELS, SORTING_ORDER } = __webpack_require__(/*! ./lib/constants */ \"(rsc)/./node_modules/pino/lib/constants.js\")\nconst {\n  createArgsNormalizer,\n  asChindings,\n  buildSafeSonicBoom,\n  buildFormatters,\n  stringify,\n  normalizeDestFileDescriptor,\n  noop\n} = __webpack_require__(/*! ./lib/tools */ \"(rsc)/./node_modules/pino/lib/tools.js\")\nconst { version } = __webpack_require__(/*! ./lib/meta */ \"(rsc)/./node_modules/pino/lib/meta.js\")\nconst {\n  chindingsSym,\n  redactFmtSym,\n  serializersSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  setLevelSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  errorKeySym,\n  nestedKeySym,\n  mixinSym,\n  levelCompSym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym,\n  msgPrefixSym\n} = symbols\nconst { epochTime, nullTime } = time\nconst { pid } = process\nconst hostname = os.hostname()\nconst defaultErrorSerializer = stdSerializers.err\nconst defaultOptions = {\n  level: 'info',\n  levelComparison: SORTING_ORDER.ASC,\n  levels: DEFAULT_LEVELS,\n  messageKey: 'msg',\n  errorKey: 'err',\n  nestedKey: null,\n  enabled: true,\n  base: { pid, hostname },\n  serializers: Object.assign(Object.create(null), {\n    err: defaultErrorSerializer\n  }),\n  formatters: Object.assign(Object.create(null), {\n    bindings (bindings) {\n      return bindings\n    },\n    level (label, number) {\n      return { level: number }\n    }\n  }),\n  hooks: {\n    logMethod: undefined,\n    streamWrite: undefined\n  },\n  timestamp: epochTime,\n  name: undefined,\n  redact: null,\n  customLevels: null,\n  useOnlyCustomLevels: false,\n  depthLimit: 5,\n  edgeLimit: 100\n}\n\nconst normalize = createArgsNormalizer(defaultOptions)\n\nconst serializers = Object.assign(Object.create(null), stdSerializers)\n\nfunction pino (...args) {\n  const instance = {}\n  const { opts, stream } = normalize(instance, caller(), ...args)\n\n  if (opts.level && typeof opts.level === 'string' && DEFAULT_LEVELS[opts.level.toLowerCase()] !== undefined) opts.level = opts.level.toLowerCase()\n\n  const {\n    redact,\n    crlf,\n    serializers,\n    timestamp,\n    messageKey,\n    errorKey,\n    nestedKey,\n    base,\n    name,\n    level,\n    customLevels,\n    levelComparison,\n    mixin,\n    mixinMergeStrategy,\n    useOnlyCustomLevels,\n    formatters,\n    hooks,\n    depthLimit,\n    edgeLimit,\n    onChild,\n    msgPrefix\n  } = opts\n\n  const stringifySafe = configure({\n    maximumDepth: depthLimit,\n    maximumBreadth: edgeLimit\n  })\n\n  const allFormatters = buildFormatters(\n    formatters.level,\n    formatters.bindings,\n    formatters.log\n  )\n\n  const stringifyFn = stringify.bind({\n    [stringifySafeSym]: stringifySafe\n  })\n  const stringifiers = redact ? redaction(redact, stringifyFn) : {}\n  const formatOpts = redact\n    ? { stringify: stringifiers[redactFmtSym] }\n    : { stringify: stringifyFn }\n  const end = '}' + (crlf ? '\\r\\n' : '\\n')\n  const coreChindings = asChindings.bind(null, {\n    [chindingsSym]: '',\n    [serializersSym]: serializers,\n    [stringifiersSym]: stringifiers,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [formattersSym]: allFormatters\n  })\n\n  let chindings = ''\n  if (base !== null) {\n    if (name === undefined) {\n      chindings = coreChindings(base)\n    } else {\n      chindings = coreChindings(Object.assign({}, base, { name }))\n    }\n  }\n\n  const time = (timestamp instanceof Function)\n    ? timestamp\n    : (timestamp ? epochTime : nullTime)\n  const timeSliceIndex = time().indexOf(':') + 1\n\n  if (useOnlyCustomLevels && !customLevels) throw Error('customLevels is required if useOnlyCustomLevels is set true')\n  if (mixin && typeof mixin !== 'function') throw Error(`Unknown mixin type \"${typeof mixin}\" - expected \"function\"`)\n  if (msgPrefix && typeof msgPrefix !== 'string') throw Error(`Unknown msgPrefix type \"${typeof msgPrefix}\" - expected \"string\"`)\n\n  assertDefaultLevelFound(level, customLevels, useOnlyCustomLevels)\n  const levels = mappings(customLevels, useOnlyCustomLevels)\n\n  if (typeof stream.emit === 'function') {\n    stream.emit('message', { code: 'PINO_CONFIG', config: { levels, messageKey, errorKey } })\n  }\n\n  assertLevelComparison(levelComparison)\n  const levelCompFunc = genLevelComparison(levelComparison)\n\n  Object.assign(instance, {\n    levels,\n    [levelCompSym]: levelCompFunc,\n    [useOnlyCustomLevelsSym]: useOnlyCustomLevels,\n    [streamSym]: stream,\n    [timeSym]: time,\n    [timeSliceIndexSym]: timeSliceIndex,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [stringifiersSym]: stringifiers,\n    [endSym]: end,\n    [formatOptsSym]: formatOpts,\n    [messageKeySym]: messageKey,\n    [errorKeySym]: errorKey,\n    [nestedKeySym]: nestedKey,\n    // protect against injection\n    [nestedKeyStrSym]: nestedKey ? `,${JSON.stringify(nestedKey)}:{` : '',\n    [serializersSym]: serializers,\n    [mixinSym]: mixin,\n    [mixinMergeStrategySym]: mixinMergeStrategy,\n    [chindingsSym]: chindings,\n    [formattersSym]: allFormatters,\n    [hooksSym]: hooks,\n    silent: noop,\n    onChild,\n    [msgPrefixSym]: msgPrefix\n  })\n\n  Object.setPrototypeOf(instance, proto())\n\n  genLsCache(instance)\n\n  instance[setLevelSym](level)\n\n  return instance\n}\n\nmodule.exports = pino\n\nmodule.exports.destination = (dest = process.stdout.fd) => {\n  if (typeof dest === 'object') {\n    dest.dest = normalizeDestFileDescriptor(dest.dest || process.stdout.fd)\n    return buildSafeSonicBoom(dest)\n  } else {\n    return buildSafeSonicBoom({ dest: normalizeDestFileDescriptor(dest), minLength: 0 })\n  }\n}\n\nmodule.exports.transport = __webpack_require__(/*! ./lib/transport */ \"(rsc)/./node_modules/pino/lib/transport.js\")\nmodule.exports.multistream = __webpack_require__(/*! ./lib/multistream */ \"(rsc)/./node_modules/pino/lib/multistream.js\")\n\nmodule.exports.levels = mappings()\nmodule.exports.stdSerializers = serializers\nmodule.exports.stdTimeFunctions = Object.assign({}, time)\nmodule.exports.symbols = symbols\nmodule.exports.version = version\n\n// Enables default and name export with TypeScript and Babel\nmodule.exports[\"default\"] = pino\nmodule.exports.pino = pino\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/pino.js\n");

/***/ })

};
;