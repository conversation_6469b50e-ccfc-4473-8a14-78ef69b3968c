"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino-abstract-transport";
exports.ids = ["vendor-chunks/pino-abstract-transport"];
exports.modules = {

/***/ "(rsc)/./node_modules/pino-abstract-transport/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/pino-abstract-transport/index.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst metadata = Symbol.for('pino.metadata')\nconst split = __webpack_require__(/*! split2 */ \"(rsc)/./node_modules/split2/index.js\")\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\")\nconst { parentPort, workerData } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\n\nfunction createDeferred () {\n  let resolve\n  let reject\n  const promise = new Promise((_resolve, _reject) => {\n    resolve = _resolve\n    reject = _reject\n  })\n  promise.resolve = resolve\n  promise.reject = reject\n  return promise\n}\n\nmodule.exports = function build (fn, opts = {}) {\n  const waitForConfig = opts.expectPinoConfig === true && workerData?.workerData?.pinoWillSendConfig === true\n  const parseLines = opts.parse === 'lines'\n  const parseLine = typeof opts.parseLine === 'function' ? opts.parseLine : JSON.parse\n  const close = opts.close || defaultClose\n  const stream = split(function (line) {\n    let value\n\n    try {\n      value = parseLine(line)\n    } catch (error) {\n      this.emit('unknown', line, error)\n      return\n    }\n\n    if (value === null) {\n      this.emit('unknown', line, 'Null value ignored')\n      return\n    }\n\n    if (typeof value !== 'object') {\n      value = {\n        data: value,\n        time: Date.now()\n      }\n    }\n\n    if (stream[metadata]) {\n      stream.lastTime = value.time\n      stream.lastLevel = value.level\n      stream.lastObj = value\n    }\n\n    if (parseLines) {\n      return line\n    }\n\n    return value\n  }, { autoDestroy: true })\n\n  stream._destroy = function (err, cb) {\n    const promise = close(err, cb)\n    if (promise && typeof promise.then === 'function') {\n      promise.then(cb, cb)\n    }\n  }\n\n  if (opts.expectPinoConfig === true && workerData?.workerData?.pinoWillSendConfig !== true) {\n    setImmediate(() => {\n      stream.emit('error', new Error('This transport is not compatible with the current version of pino. Please upgrade pino to the latest version.'))\n    })\n  }\n\n  if (opts.metadata !== false) {\n    stream[metadata] = true\n    stream.lastTime = 0\n    stream.lastLevel = 0\n    stream.lastObj = null\n  }\n\n  if (waitForConfig) {\n    let pinoConfig = {}\n    const configReceived = createDeferred()\n    parentPort.on('message', function handleMessage (message) {\n      if (message.code === 'PINO_CONFIG') {\n        pinoConfig = message.config\n        configReceived.resolve()\n        parentPort.off('message', handleMessage)\n      }\n    })\n\n    Object.defineProperties(stream, {\n      levels: {\n        get () { return pinoConfig.levels }\n      },\n      messageKey: {\n        get () { return pinoConfig.messageKey }\n      },\n      errorKey: {\n        get () { return pinoConfig.errorKey }\n      }\n    })\n\n    return configReceived.then(finish)\n  }\n\n  return finish()\n\n  function finish () {\n    let res = fn(stream)\n\n    if (res && typeof res.catch === 'function') {\n      res.catch((err) => {\n        stream.destroy(err)\n      })\n\n      // set it to null to not retain a reference to the promise\n      res = null\n    } else if (opts.enablePipelining && res) {\n      return Duplex.from({ writable: stream, readable: res })\n    }\n\n    return stream\n  }\n}\n\nfunction defaultClose (err, cb) {\n  process.nextTick(cb, err)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino-abstract-transport/index.js\n");

/***/ })

};
;