"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino-std-serializers";
exports.ids = ["vendor-chunks/pino-std-serializers"];
exports.modules = {

/***/ "(rsc)/./node_modules/pino-std-serializers/index.js":
/*!****************************************************!*\
  !*** ./node_modules/pino-std-serializers/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst errSerializer = __webpack_require__(/*! ./lib/err */ \"(rsc)/./node_modules/pino-std-serializers/lib/err.js\")\nconst errWithCauseSerializer = __webpack_require__(/*! ./lib/err-with-cause */ \"(rsc)/./node_modules/pino-std-serializers/lib/err-with-cause.js\")\nconst reqSerializers = __webpack_require__(/*! ./lib/req */ \"(rsc)/./node_modules/pino-std-serializers/lib/req.js\")\nconst resSerializers = __webpack_require__(/*! ./lib/res */ \"(rsc)/./node_modules/pino-std-serializers/lib/res.js\")\n\nmodule.exports = {\n  err: errSerializer,\n  errWithCause: errWithCauseSerializer,\n  mapHttpRequest: reqSerializers.mapHttpRequest,\n  mapHttpResponse: resSerializers.mapHttpResponse,\n  req: reqSerializers.reqSerializer,\n  res: resSerializers.resSerializer,\n\n  wrapErrorSerializer: function wrapErrorSerializer (customSerializer) {\n    if (customSerializer === errSerializer) return customSerializer\n    return function wrapErrSerializer (err) {\n      return customSerializer(errSerializer(err))\n    }\n  },\n\n  wrapRequestSerializer: function wrapRequestSerializer (customSerializer) {\n    if (customSerializer === reqSerializers.reqSerializer) return customSerializer\n    return function wrappedReqSerializer (req) {\n      return customSerializer(reqSerializers.reqSerializer(req))\n    }\n  },\n\n  wrapResponseSerializer: function wrapResponseSerializer (customSerializer) {\n    if (customSerializer === resSerializers.resSerializer) return customSerializer\n    return function wrappedResSerializer (res) {\n      return customSerializer(resSerializers.resSerializer(res))\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino-std-serializers/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino-std-serializers/lib/err-helpers.js":
/*!**************************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/err-helpers.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("\n\n// **************************************************************\n// * Code initially copied/adapted from \"pony-cause\" npm module *\n// * Please upstream improvements there                         *\n// **************************************************************\n\nconst isErrorLike = (err) => {\n  return err && typeof err.message === 'string'\n}\n\n/**\n * @param {Error|{ cause?: unknown|(()=>err)}} err\n * @returns {Error|Object|undefined}\n */\nconst getErrorCause = (err) => {\n  if (!err) return\n\n  /** @type {unknown} */\n  // @ts-ignore\n  const cause = err.cause\n\n  // VError / NError style causes\n  if (typeof cause === 'function') {\n    // @ts-ignore\n    const causeResult = err.cause()\n\n    return isErrorLike(causeResult)\n      ? causeResult\n      : undefined\n  } else {\n    return isErrorLike(cause)\n      ? cause\n      : undefined\n  }\n}\n\n/**\n * Internal method that keeps a track of which error we have already added, to avoid circular recursion\n *\n * @private\n * @param {Error} err\n * @param {Set<Error>} seen\n * @returns {string}\n */\nconst _stackWithCauses = (err, seen) => {\n  if (!isErrorLike(err)) return ''\n\n  const stack = err.stack || ''\n\n  // Ensure we don't go circular or crazily deep\n  if (seen.has(err)) {\n    return stack + '\\ncauses have become circular...'\n  }\n\n  const cause = getErrorCause(err)\n\n  if (cause) {\n    seen.add(err)\n    return (stack + '\\ncaused by: ' + _stackWithCauses(cause, seen))\n  } else {\n    return stack\n  }\n}\n\n/**\n * @param {Error} err\n * @returns {string}\n */\nconst stackWithCauses = (err) => _stackWithCauses(err, new Set())\n\n/**\n * Internal method that keeps a track of which error we have already added, to avoid circular recursion\n *\n * @private\n * @param {Error} err\n * @param {Set<Error>} seen\n * @param {boolean} [skip]\n * @returns {string}\n */\nconst _messageWithCauses = (err, seen, skip) => {\n  if (!isErrorLike(err)) return ''\n\n  const message = skip ? '' : (err.message || '')\n\n  // Ensure we don't go circular or crazily deep\n  if (seen.has(err)) {\n    return message + ': ...'\n  }\n\n  const cause = getErrorCause(err)\n\n  if (cause) {\n    seen.add(err)\n\n    // @ts-ignore\n    const skipIfVErrorStyleCause = typeof err.cause === 'function'\n\n    return (message +\n      (skipIfVErrorStyleCause ? '' : ': ') +\n      _messageWithCauses(cause, seen, skipIfVErrorStyleCause))\n  } else {\n    return message\n  }\n}\n\n/**\n * @param {Error} err\n * @returns {string}\n */\nconst messageWithCauses = (err) => _messageWithCauses(err, new Set())\n\nmodule.exports = {\n  isErrorLike,\n  getErrorCause,\n  stackWithCauses,\n  messageWithCauses\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino-std-serializers/lib/err-helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino-std-serializers/lib/err-proto.js":
/*!************************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/err-proto.js ***!
  \************************************************************/
/***/ ((module) => {

eval("\n\nconst seen = Symbol('circular-ref-tag')\nconst rawSymbol = Symbol('pino-raw-err-ref')\n\nconst pinoErrProto = Object.create({}, {\n  type: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  message: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  stack: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  aggregateErrors: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoErrProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nmodule.exports = {\n  pinoErrProto,\n  pinoErrorSymbols: {\n    seen,\n    rawSymbol\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby1zdGQtc2VyaWFsaXplcnMvbGliL2Vyci1wcm90by5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBOztBQUVBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXHBpbm8tc3RkLXNlcmlhbGl6ZXJzXFxsaWJcXGVyci1wcm90by5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3Qgc2VlbiA9IFN5bWJvbCgnY2lyY3VsYXItcmVmLXRhZycpXG5jb25zdCByYXdTeW1ib2wgPSBTeW1ib2woJ3Bpbm8tcmF3LWVyci1yZWYnKVxuXG5jb25zdCBwaW5vRXJyUHJvdG8gPSBPYmplY3QuY3JlYXRlKHt9LCB7XG4gIHR5cGU6IHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgIHZhbHVlOiB1bmRlZmluZWRcbiAgfSxcbiAgbWVzc2FnZToge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgd3JpdGFibGU6IHRydWUsXG4gICAgdmFsdWU6IHVuZGVmaW5lZFxuICB9LFxuICBzdGFjazoge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgd3JpdGFibGU6IHRydWUsXG4gICAgdmFsdWU6IHVuZGVmaW5lZFxuICB9LFxuICBhZ2dyZWdhdGVFcnJvcnM6IHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgIHZhbHVlOiB1bmRlZmluZWRcbiAgfSxcbiAgcmF3OiB7XG4gICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gdGhpc1tyYXdTeW1ib2xdXG4gICAgfSxcbiAgICBzZXQ6IGZ1bmN0aW9uICh2YWwpIHtcbiAgICAgIHRoaXNbcmF3U3ltYm9sXSA9IHZhbFxuICAgIH1cbiAgfVxufSlcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShwaW5vRXJyUHJvdG8sIHJhd1N5bWJvbCwge1xuICB3cml0YWJsZTogdHJ1ZSxcbiAgdmFsdWU6IHt9XG59KVxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgcGlub0VyclByb3RvLFxuICBwaW5vRXJyb3JTeW1ib2xzOiB7XG4gICAgc2VlbixcbiAgICByYXdTeW1ib2xcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino-std-serializers/lib/err-proto.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino-std-serializers/lib/err-with-cause.js":
/*!*****************************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/err-with-cause.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = errWithCauseSerializer\n\nconst { isErrorLike } = __webpack_require__(/*! ./err-helpers */ \"(rsc)/./node_modules/pino-std-serializers/lib/err-helpers.js\")\nconst { pinoErrProto, pinoErrorSymbols } = __webpack_require__(/*! ./err-proto */ \"(rsc)/./node_modules/pino-std-serializers/lib/err-proto.js\")\nconst { seen } = pinoErrorSymbols\n\nconst { toString } = Object.prototype\n\nfunction errWithCauseSerializer (err) {\n  if (!isErrorLike(err)) {\n    return err\n  }\n\n  err[seen] = undefined // tag to prevent re-looking at this\n  const _err = Object.create(pinoErrProto)\n  _err.type = toString.call(err.constructor) === '[object Function]'\n    ? err.constructor.name\n    : err.name\n  _err.message = err.message\n  _err.stack = err.stack\n\n  if (Array.isArray(err.errors)) {\n    _err.aggregateErrors = err.errors.map(err => errWithCauseSerializer(err))\n  }\n\n  if (isErrorLike(err.cause) && !Object.prototype.hasOwnProperty.call(err.cause, seen)) {\n    _err.cause = errWithCauseSerializer(err.cause)\n  }\n\n  for (const key in err) {\n    if (_err[key] === undefined) {\n      const val = err[key]\n      if (isErrorLike(val)) {\n        if (!Object.prototype.hasOwnProperty.call(val, seen)) {\n          _err[key] = errWithCauseSerializer(val)\n        }\n      } else {\n        _err[key] = val\n      }\n    }\n  }\n\n  delete err[seen] // clean up tag in case err is serialized again later\n  _err.raw = err\n  return _err\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino-std-serializers/lib/err-with-cause.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino-std-serializers/lib/err.js":
/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/err.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = errSerializer\n\nconst { messageWithCauses, stackWithCauses, isErrorLike } = __webpack_require__(/*! ./err-helpers */ \"(rsc)/./node_modules/pino-std-serializers/lib/err-helpers.js\")\nconst { pinoErrProto, pinoErrorSymbols } = __webpack_require__(/*! ./err-proto */ \"(rsc)/./node_modules/pino-std-serializers/lib/err-proto.js\")\nconst { seen } = pinoErrorSymbols\n\nconst { toString } = Object.prototype\n\nfunction errSerializer (err) {\n  if (!isErrorLike(err)) {\n    return err\n  }\n\n  err[seen] = undefined // tag to prevent re-looking at this\n  const _err = Object.create(pinoErrProto)\n  _err.type = toString.call(err.constructor) === '[object Function]'\n    ? err.constructor.name\n    : err.name\n  _err.message = messageWithCauses(err)\n  _err.stack = stackWithCauses(err)\n\n  if (Array.isArray(err.errors)) {\n    _err.aggregateErrors = err.errors.map(err => errSerializer(err))\n  }\n\n  for (const key in err) {\n    if (_err[key] === undefined) {\n      const val = err[key]\n      if (isErrorLike(val)) {\n        // We append cause messages and stacks to _err, therefore skipping causes here\n        if (key !== 'cause' && !Object.prototype.hasOwnProperty.call(val, seen)) {\n          _err[key] = errSerializer(val)\n        }\n      } else {\n        _err[key] = val\n      }\n    }\n  }\n\n  delete err[seen] // clean up tag in case err is serialized again later\n  _err.raw = err\n  return _err\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino-std-serializers/lib/err.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino-std-serializers/lib/req.js":
/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/req.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  mapHttpRequest,\n  reqSerializer\n}\n\nconst rawSymbol = Symbol('pino-raw-req-ref')\nconst pinoReqProto = Object.create({}, {\n  id: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  method: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  url: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  query: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  params: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  headers: {\n    enumerable: true,\n    writable: true,\n    value: {}\n  },\n  remoteAddress: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  remotePort: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoReqProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nfunction reqSerializer (req) {\n  // req.info is for hapi compat.\n  const connection = req.info || req.socket\n  const _req = Object.create(pinoReqProto)\n  _req.id = (typeof req.id === 'function' ? req.id() : (req.id || (req.info ? req.info.id : undefined)))\n  _req.method = req.method\n  // req.originalUrl is for expressjs compat.\n  if (req.originalUrl) {\n    _req.url = req.originalUrl\n  } else {\n    const path = req.path\n    // path for safe hapi compat.\n    _req.url = typeof path === 'string' ? path : (req.url ? req.url.path || req.url : undefined)\n  }\n\n  if (req.query) {\n    _req.query = req.query\n  }\n\n  if (req.params) {\n    _req.params = req.params\n  }\n\n  _req.headers = req.headers\n  _req.remoteAddress = connection && connection.remoteAddress\n  _req.remotePort = connection && connection.remotePort\n  // req.raw is  for hapi compat/equivalence\n  _req.raw = req.raw || req\n  return _req\n}\n\nfunction mapHttpRequest (req) {\n  return {\n    req: reqSerializer(req)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino-std-serializers/lib/req.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino-std-serializers/lib/res.js":
/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/res.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  mapHttpResponse,\n  resSerializer\n}\n\nconst rawSymbol = Symbol('pino-raw-res-ref')\nconst pinoResProto = Object.create({}, {\n  statusCode: {\n    enumerable: true,\n    writable: true,\n    value: 0\n  },\n  headers: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoResProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nfunction resSerializer (res) {\n  const _res = Object.create(pinoResProto)\n  _res.statusCode = res.headersSent ? res.statusCode : null\n  _res.headers = res.getHeaders ? res.getHeaders() : res._headers\n  _res.raw = res\n  return _res\n}\n\nfunction mapHttpResponse (res) {\n  return {\n    res: resSerializer(res)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby1zdGQtc2VyaWFsaXplcnMvbGliL3Jlcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xccGluby1zdGQtc2VyaWFsaXplcnNcXGxpYlxccmVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgbWFwSHR0cFJlc3BvbnNlLFxuICByZXNTZXJpYWxpemVyXG59XG5cbmNvbnN0IHJhd1N5bWJvbCA9IFN5bWJvbCgncGluby1yYXctcmVzLXJlZicpXG5jb25zdCBwaW5vUmVzUHJvdG8gPSBPYmplY3QuY3JlYXRlKHt9LCB7XG4gIHN0YXR1c0NvZGU6IHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgIHZhbHVlOiAwXG4gIH0sXG4gIGhlYWRlcnM6IHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgIHZhbHVlOiAnJ1xuICB9LFxuICByYXc6IHtcbiAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiB0aGlzW3Jhd1N5bWJvbF1cbiAgICB9LFxuICAgIHNldDogZnVuY3Rpb24gKHZhbCkge1xuICAgICAgdGhpc1tyYXdTeW1ib2xdID0gdmFsXG4gICAgfVxuICB9XG59KVxuT2JqZWN0LmRlZmluZVByb3BlcnR5KHBpbm9SZXNQcm90bywgcmF3U3ltYm9sLCB7XG4gIHdyaXRhYmxlOiB0cnVlLFxuICB2YWx1ZToge31cbn0pXG5cbmZ1bmN0aW9uIHJlc1NlcmlhbGl6ZXIgKHJlcykge1xuICBjb25zdCBfcmVzID0gT2JqZWN0LmNyZWF0ZShwaW5vUmVzUHJvdG8pXG4gIF9yZXMuc3RhdHVzQ29kZSA9IHJlcy5oZWFkZXJzU2VudCA/IHJlcy5zdGF0dXNDb2RlIDogbnVsbFxuICBfcmVzLmhlYWRlcnMgPSByZXMuZ2V0SGVhZGVycyA/IHJlcy5nZXRIZWFkZXJzKCkgOiByZXMuX2hlYWRlcnNcbiAgX3Jlcy5yYXcgPSByZXNcbiAgcmV0dXJuIF9yZXNcbn1cblxuZnVuY3Rpb24gbWFwSHR0cFJlc3BvbnNlIChyZXMpIHtcbiAgcmV0dXJuIHtcbiAgICByZXM6IHJlc1NlcmlhbGl6ZXIocmVzKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino-std-serializers/lib/res.js\n");

/***/ })

};
;