"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@bufbuild";
exports.ids = ["vendor-chunks/@bufbuild"];
exports.modules = {

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   protoInt64: () => (/* binding */ protoInt64)\n/* harmony export */ });\n/* harmony import */ var _wire_varint_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./wire/varint.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js\");\n// Copyright 2021-2025 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/**\n * Int64Support for the current environment.\n */\nconst protoInt64 = /*@__PURE__*/ makeInt64Support();\nfunction makeInt64Support() {\n    const dv = new DataView(new ArrayBuffer(8));\n    // note that Safari 14 implements BigInt, but not the DataView methods\n    const ok = typeof BigInt === \"function\" &&\n        typeof dv.getBigInt64 === \"function\" &&\n        typeof dv.getBigUint64 === \"function\" &&\n        typeof dv.setBigInt64 === \"function\" &&\n        typeof dv.setBigUint64 === \"function\" &&\n        (typeof process != \"object\" ||\n            typeof process.env != \"object\" ||\n            process.env.BUF_BIGINT_DISABLE !== \"1\");\n    if (ok) {\n        const MIN = BigInt(\"-9223372036854775808\");\n        const MAX = BigInt(\"9223372036854775807\");\n        const UMIN = BigInt(\"0\");\n        const UMAX = BigInt(\"18446744073709551615\");\n        return {\n            zero: BigInt(0),\n            supported: true,\n            parse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > MAX || bi < MIN) {\n                    throw new Error(`invalid int64: ${value}`);\n                }\n                return bi;\n            },\n            uParse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > UMAX || bi < UMIN) {\n                    throw new Error(`invalid uint64: ${value}`);\n                }\n                return bi;\n            },\n            enc(value) {\n                dv.setBigInt64(0, this.parse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            uEnc(value) {\n                dv.setBigInt64(0, this.uParse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            dec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigInt64(0, true);\n            },\n            uDec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigUint64(0, true);\n            },\n        };\n    }\n    return {\n        zero: \"0\",\n        supported: false,\n        parse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return value;\n        },\n        uParse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return value;\n        },\n        enc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return (0,_wire_varint_js__WEBPACK_IMPORTED_MODULE_0__.int64FromString)(value);\n        },\n        uEnc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return (0,_wire_varint_js__WEBPACK_IMPORTED_MODULE_0__.int64FromString)(value);\n        },\n        dec(lo, hi) {\n            return (0,_wire_varint_js__WEBPACK_IMPORTED_MODULE_0__.int64ToString)(lo, hi);\n        },\n        uDec(lo, hi) {\n            return (0,_wire_varint_js__WEBPACK_IMPORTED_MODULE_0__.uInt64ToString)(lo, hi);\n        },\n    };\n}\nfunction assertInt64String(value) {\n    if (!/^-?[0-9]+$/.test(value)) {\n        throw new Error(\"invalid int64: \" + value);\n    }\n}\nfunction assertUInt64String(value) {\n    if (!/^[0-9]+$/.test(value)) {\n        throw new Error(\"invalid uint64: \" + value);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/binary-encoding.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/wire/binary-encoding.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BinaryReader: () => (/* binding */ BinaryReader),\n/* harmony export */   BinaryWriter: () => (/* binding */ BinaryWriter),\n/* harmony export */   FLOAT32_MAX: () => (/* binding */ FLOAT32_MAX),\n/* harmony export */   FLOAT32_MIN: () => (/* binding */ FLOAT32_MIN),\n/* harmony export */   INT32_MAX: () => (/* binding */ INT32_MAX),\n/* harmony export */   INT32_MIN: () => (/* binding */ INT32_MIN),\n/* harmony export */   UINT32_MAX: () => (/* binding */ UINT32_MAX),\n/* harmony export */   WireType: () => (/* binding */ WireType)\n/* harmony export */ });\n/* harmony import */ var _varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./varint.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js\");\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../proto-int64.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _text_encoding_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text-encoding.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/text-encoding.js\");\n// Copyright 2021-2025 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/**\n * Protobuf binary format wire types.\n *\n * A wire type provides just enough information to find the length of the\n * following value.\n *\n * See https://developers.google.com/protocol-buffers/docs/encoding#structure\n */\nvar WireType;\n(function (WireType) {\n    /**\n     * Used for int32, int64, uint32, uint64, sint32, sint64, bool, enum\n     */\n    WireType[WireType[\"Varint\"] = 0] = \"Varint\";\n    /**\n     * Used for fixed64, sfixed64, double.\n     * Always 8 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit64\"] = 1] = \"Bit64\";\n    /**\n     * Used for string, bytes, embedded messages, packed repeated fields\n     *\n     * Only repeated numeric types (types which use the varint, 32-bit,\n     * or 64-bit wire types) can be packed. In proto3, such fields are\n     * packed by default.\n     */\n    WireType[WireType[\"LengthDelimited\"] = 2] = \"LengthDelimited\";\n    /**\n     * Start of a tag-delimited aggregate, such as a proto2 group, or a message\n     * in editions with message_encoding = DELIMITED.\n     */\n    WireType[WireType[\"StartGroup\"] = 3] = \"StartGroup\";\n    /**\n     * End of a tag-delimited aggregate.\n     */\n    WireType[WireType[\"EndGroup\"] = 4] = \"EndGroup\";\n    /**\n     * Used for fixed32, sfixed32, float.\n     * Always 4 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit32\"] = 5] = \"Bit32\";\n})(WireType || (WireType = {}));\n/**\n * Maximum value for a 32-bit floating point value (Protobuf FLOAT).\n */\nconst FLOAT32_MAX = 3.4028234663852886e38;\n/**\n * Minimum value for a 32-bit floating point value (Protobuf FLOAT).\n */\nconst FLOAT32_MIN = -3.4028234663852886e38;\n/**\n * Maximum value for an unsigned 32-bit integer (Protobuf UINT32, FIXED32).\n */\nconst UINT32_MAX = 0xffffffff;\n/**\n * Maximum value for a signed 32-bit integer (Protobuf INT32, SFIXED32, SINT32).\n */\nconst INT32_MAX = 0x7fffffff;\n/**\n * Minimum value for a signed 32-bit integer (Protobuf INT32, SFIXED32, SINT32).\n */\nconst INT32_MIN = -0x80000000;\nclass BinaryWriter {\n    constructor(encodeUtf8 = (0,_text_encoding_js__WEBPACK_IMPORTED_MODULE_0__.getTextEncoding)().encodeUtf8) {\n        this.encodeUtf8 = encodeUtf8;\n        /**\n         * Previous fork states.\n         */\n        this.stack = [];\n        this.chunks = [];\n        this.buf = [];\n    }\n    /**\n     * Return all bytes written and reset this writer.\n     */\n    finish() {\n        if (this.buf.length) {\n            this.chunks.push(new Uint8Array(this.buf)); // flush the buffer\n            this.buf = [];\n        }\n        let len = 0;\n        for (let i = 0; i < this.chunks.length; i++)\n            len += this.chunks[i].length;\n        let bytes = new Uint8Array(len);\n        let offset = 0;\n        for (let i = 0; i < this.chunks.length; i++) {\n            bytes.set(this.chunks[i], offset);\n            offset += this.chunks[i].length;\n        }\n        this.chunks = [];\n        return bytes;\n    }\n    /**\n     * Start a new fork for length-delimited data like a message\n     * or a packed repeated field.\n     *\n     * Must be joined later with `join()`.\n     */\n    fork() {\n        this.stack.push({ chunks: this.chunks, buf: this.buf });\n        this.chunks = [];\n        this.buf = [];\n        return this;\n    }\n    /**\n     * Join the last fork. Write its length and bytes, then\n     * return to the previous state.\n     */\n    join() {\n        // get chunk of fork\n        let chunk = this.finish();\n        // restore previous state\n        let prev = this.stack.pop();\n        if (!prev)\n            throw new Error(\"invalid state, fork stack empty\");\n        this.chunks = prev.chunks;\n        this.buf = prev.buf;\n        // write length of chunk as varint\n        this.uint32(chunk.byteLength);\n        return this.raw(chunk);\n    }\n    /**\n     * Writes a tag (field number and wire type).\n     *\n     * Equivalent to `uint32( (fieldNo << 3 | type) >>> 0 )`.\n     *\n     * Generated code should compute the tag ahead of time and call `uint32()`.\n     */\n    tag(fieldNo, type) {\n        return this.uint32(((fieldNo << 3) | type) >>> 0);\n    }\n    /**\n     * Write a chunk of raw bytes.\n     */\n    raw(chunk) {\n        if (this.buf.length) {\n            this.chunks.push(new Uint8Array(this.buf));\n            this.buf = [];\n        }\n        this.chunks.push(chunk);\n        return this;\n    }\n    /**\n     * Write a `uint32` value, an unsigned 32 bit varint.\n     */\n    uint32(value) {\n        assertUInt32(value);\n        // write value as varint 32, inlined for speed\n        while (value > 0x7f) {\n            this.buf.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        this.buf.push(value);\n        return this;\n    }\n    /**\n     * Write a `int32` value, a signed 32 bit varint.\n     */\n    int32(value) {\n        assertInt32(value);\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32write)(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `bool` value, a variant.\n     */\n    bool(value) {\n        this.buf.push(value ? 1 : 0);\n        return this;\n    }\n    /**\n     * Write a `bytes` value, length-delimited arbitrary data.\n     */\n    bytes(value) {\n        this.uint32(value.byteLength); // write length of chunk as varint\n        return this.raw(value);\n    }\n    /**\n     * Write a `string` value, length-delimited data converted to UTF-8 text.\n     */\n    string(value) {\n        let chunk = this.encodeUtf8(value);\n        this.uint32(chunk.byteLength); // write length of chunk as varint\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `float` value, 32-bit floating point number.\n     */\n    float(value) {\n        assertFloat32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setFloat32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `double` value, a 64-bit floating point number.\n     */\n    double(value) {\n        let chunk = new Uint8Array(8);\n        new DataView(chunk.buffer).setFloat64(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed32` value, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32(value) {\n        assertUInt32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setUint32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sfixed32` value, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32(value) {\n        assertInt32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setInt32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sint32` value, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32(value) {\n        assertInt32(value);\n        // zigzag encode\n        value = ((value << 1) ^ (value >> 31)) >>> 0;\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32write)(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `fixed64` value, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed64` value, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uEnc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `int64` value, a signed 64-bit varint.\n     */\n    int64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value);\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `sint64` value, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64(value) {\n        const tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value), \n        // zigzag encode\n        sign = tc.hi >> 31, lo = (tc.lo << 1) ^ sign, hi = ((tc.hi << 1) | (tc.lo >>> 31)) ^ sign;\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(lo, hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `uint64` value, an unsigned 64-bit varint.\n     */\n    uint64(value) {\n        const tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uEnc(value);\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n}\nclass BinaryReader {\n    constructor(buf, decodeUtf8 = (0,_text_encoding_js__WEBPACK_IMPORTED_MODULE_0__.getTextEncoding)().decodeUtf8) {\n        this.decodeUtf8 = decodeUtf8;\n        this.varint64 = _varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64read; // dirty cast for `this`\n        /**\n         * Read a `uint32` field, an unsigned 32 bit varint.\n         */\n        this.uint32 = _varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32read;\n        this.buf = buf;\n        this.len = buf.length;\n        this.pos = 0;\n        this.view = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);\n    }\n    /**\n     * Reads a tag - field number and wire type.\n     */\n    tag() {\n        let tag = this.uint32(), fieldNo = tag >>> 3, wireType = tag & 7;\n        if (fieldNo <= 0 || wireType < 0 || wireType > 5)\n            throw new Error(\"illegal tag: field no \" + fieldNo + \" wire type \" + wireType);\n        return [fieldNo, wireType];\n    }\n    /**\n     * Skip one element and return the skipped data.\n     *\n     * When skipping StartGroup, provide the tags field number to check for\n     * matching field number in the EndGroup tag.\n     */\n    skip(wireType, fieldNo) {\n        let start = this.pos;\n        switch (wireType) {\n            case WireType.Varint:\n                while (this.buf[this.pos++] & 0x80) {\n                    // ignore\n                }\n                break;\n            // @ts-expect-error TS7029: Fallthrough case in switch\n            case WireType.Bit64:\n                this.pos += 4;\n            case WireType.Bit32:\n                this.pos += 4;\n                break;\n            case WireType.LengthDelimited:\n                let len = this.uint32();\n                this.pos += len;\n                break;\n            case WireType.StartGroup:\n                for (;;) {\n                    const [fn, wt] = this.tag();\n                    if (wt === WireType.EndGroup) {\n                        if (fieldNo !== undefined && fn !== fieldNo) {\n                            throw new Error(\"invalid end group tag\");\n                        }\n                        break;\n                    }\n                    this.skip(wt, fn);\n                }\n                break;\n            default:\n                throw new Error(\"cant skip wire type \" + wireType);\n        }\n        this.assertBounds();\n        return this.buf.subarray(start, this.pos);\n    }\n    /**\n     * Throws error if position in byte array is out of range.\n     */\n    assertBounds() {\n        if (this.pos > this.len)\n            throw new RangeError(\"premature EOF\");\n    }\n    /**\n     * Read a `int32` field, a signed 32 bit varint.\n     */\n    int32() {\n        return this.uint32() | 0;\n    }\n    /**\n     * Read a `sint32` field, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32() {\n        let zze = this.uint32();\n        // decode zigzag\n        return (zze >>> 1) ^ -(zze & 1);\n    }\n    /**\n     * Read a `int64` field, a signed 64-bit varint.\n     */\n    int64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(...this.varint64());\n    }\n    /**\n     * Read a `uint64` field, an unsigned 64-bit varint.\n     */\n    uint64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uDec(...this.varint64());\n    }\n    /**\n     * Read a `sint64` field, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64() {\n        let [lo, hi] = this.varint64();\n        // decode zig zag\n        let s = -(lo & 1);\n        lo = ((lo >>> 1) | ((hi & 1) << 31)) ^ s;\n        hi = (hi >>> 1) ^ s;\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(lo, hi);\n    }\n    /**\n     * Read a `bool` field, a variant.\n     */\n    bool() {\n        let [lo, hi] = this.varint64();\n        return lo !== 0 || hi !== 0;\n    }\n    /**\n     * Read a `fixed32` field, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32() {\n        // biome-ignore lint/suspicious/noAssignInExpressions: no\n        return this.view.getUint32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `sfixed32` field, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32() {\n        // biome-ignore lint/suspicious/noAssignInExpressions: no\n        return this.view.getInt32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `fixed64` field, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uDec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `fixed64` field, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `float` field, 32-bit floating point number.\n     */\n    float() {\n        // biome-ignore lint/suspicious/noAssignInExpressions: no\n        return this.view.getFloat32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `double` field, a 64-bit floating point number.\n     */\n    double() {\n        // biome-ignore lint/suspicious/noAssignInExpressions: no\n        return this.view.getFloat64((this.pos += 8) - 8, true);\n    }\n    /**\n     * Read a `bytes` field, length-delimited arbitrary data.\n     */\n    bytes() {\n        let len = this.uint32(), start = this.pos;\n        this.pos += len;\n        this.assertBounds();\n        return this.buf.subarray(start, start + len);\n    }\n    /**\n     * Read a `string` field, length-delimited data converted to UTF-8 text.\n     */\n    string() {\n        return this.decodeUtf8(this.bytes());\n    }\n}\n/**\n * Assert a valid signed protobuf 32-bit integer as a number or string.\n */\nfunction assertInt32(arg) {\n    if (typeof arg == \"string\") {\n        arg = Number(arg);\n    }\n    else if (typeof arg != \"number\") {\n        throw new Error(\"invalid int32: \" + typeof arg);\n    }\n    if (!Number.isInteger(arg) ||\n        arg > INT32_MAX ||\n        arg < INT32_MIN)\n        throw new Error(\"invalid int32: \" + arg);\n}\n/**\n * Assert a valid unsigned protobuf 32-bit integer as a number or string.\n */\nfunction assertUInt32(arg) {\n    if (typeof arg == \"string\") {\n        arg = Number(arg);\n    }\n    else if (typeof arg != \"number\") {\n        throw new Error(\"invalid uint32: \" + typeof arg);\n    }\n    if (!Number.isInteger(arg) ||\n        arg > UINT32_MAX ||\n        arg < 0)\n        throw new Error(\"invalid uint32: \" + arg);\n}\n/**\n * Assert a valid protobuf float value as a number or string.\n */\nfunction assertFloat32(arg) {\n    if (typeof arg == \"string\") {\n        const o = arg;\n        arg = Number(arg);\n        if (Number.isNaN(arg) && o !== \"NaN\") {\n            throw new Error(\"invalid float32: \" + o);\n        }\n    }\n    else if (typeof arg != \"number\") {\n        throw new Error(\"invalid float32: \" + typeof arg);\n    }\n    if (Number.isFinite(arg) &&\n        (arg > FLOAT32_MAX || arg < FLOAT32_MIN))\n        throw new Error(\"invalid float32: \" + arg);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/binary-encoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/text-encoding.js":
/*!************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/wire/text-encoding.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   configureTextEncoding: () => (/* binding */ configureTextEncoding),\n/* harmony export */   getTextEncoding: () => (/* binding */ getTextEncoding)\n/* harmony export */ });\n// Copyright 2021-2025 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nconst symbol = Symbol.for(\"@bufbuild/protobuf/text-encoding\");\n/**\n * Protobuf-ES requires the Text Encoding API to convert UTF-8 from and to\n * binary. This WHATWG API is widely available, but it is not part of the\n * ECMAScript standard. On runtimes where it is not available, use this\n * function to provide your own implementation.\n *\n * Note that the Text Encoding API does not provide a way to validate UTF-8.\n * Our implementation falls back to use encodeURIComponent().\n */\nfunction configureTextEncoding(textEncoding) {\n    globalThis[symbol] = textEncoding;\n}\nfunction getTextEncoding() {\n    if (globalThis[symbol] == undefined) {\n        const te = new globalThis.TextEncoder();\n        const td = new globalThis.TextDecoder();\n        globalThis[symbol] = {\n            encodeUtf8(text) {\n                return te.encode(text);\n            },\n            decodeUtf8(bytes) {\n                return td.decode(bytes);\n            },\n            checkUtf8(text) {\n                try {\n                    encodeURIComponent(text);\n                    return true;\n                }\n                catch (_) {\n                    return false;\n                }\n            },\n        };\n    }\n    return globalThis[symbol];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/text-encoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   int64FromString: () => (/* binding */ int64FromString),\n/* harmony export */   int64ToString: () => (/* binding */ int64ToString),\n/* harmony export */   uInt64ToString: () => (/* binding */ uInt64ToString),\n/* harmony export */   varint32read: () => (/* binding */ varint32read),\n/* harmony export */   varint32write: () => (/* binding */ varint32write),\n/* harmony export */   varint64read: () => (/* binding */ varint64read),\n/* harmony export */   varint64write: () => (/* binding */ varint64write)\n/* harmony export */ });\n// Copyright 2008 Google Inc.  All rights reserved.\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are\n// met:\n//\n// * Redistributions of source code must retain the above copyright\n// notice, this list of conditions and the following disclaimer.\n// * Redistributions in binary form must reproduce the above\n// copyright notice, this list of conditions and the following disclaimer\n// in the documentation and/or other materials provided with the\n// distribution.\n// * Neither the name of Google Inc. nor the names of its\n// contributors may be used to endorse or promote products derived from\n// this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n//\n// Code generated by the Protocol Buffer compiler is owned by the owner\n// of the input file used when generating it.  This code is not\n// standalone and requires a support library to be linked with it.  This\n// support library is itself covered by the above license.\n/**\n * Read a 64 bit varint as two JS numbers.\n *\n * Returns tuple:\n * [0]: low bits\n * [1]: high bits\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L175\n */\nfunction varint64read() {\n    let lowBits = 0;\n    let highBits = 0;\n    for (let shift = 0; shift < 28; shift += 7) {\n        let b = this.buf[this.pos++];\n        lowBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    let middleByte = this.buf[this.pos++];\n    // last four bits of the first 32 bit number\n    lowBits |= (middleByte & 0x0f) << 28;\n    // 3 upper bits are part of the next 32 bit number\n    highBits = (middleByte & 0x70) >> 4;\n    if ((middleByte & 0x80) == 0) {\n        this.assertBounds();\n        return [lowBits, highBits];\n    }\n    for (let shift = 3; shift <= 31; shift += 7) {\n        let b = this.buf[this.pos++];\n        highBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    throw new Error(\"invalid varint\");\n}\n/**\n * Write a 64 bit varint, given as two JS numbers, to the given bytes array.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/writer.js#L344\n */\nfunction varint64write(lo, hi, bytes) {\n    for (let i = 0; i < 28; i = i + 7) {\n        const shift = lo >>> i;\n        const hasNext = !(shift >>> 7 == 0 && hi == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    const splitBits = ((lo >>> 28) & 0x0f) | ((hi & 0x07) << 4);\n    const hasMoreBits = !(hi >> 3 == 0);\n    bytes.push((hasMoreBits ? splitBits | 0x80 : splitBits) & 0xff);\n    if (!hasMoreBits) {\n        return;\n    }\n    for (let i = 3; i < 31; i = i + 7) {\n        const shift = hi >>> i;\n        const hasNext = !(shift >>> 7 == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    bytes.push((hi >>> 31) & 0x01);\n}\n// constants for binary math\nconst TWO_PWR_32_DBL = 0x100000000;\n/**\n * Parse decimal string of 64 bit integer value as two JS numbers.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction int64FromString(dec) {\n    // Check for minus sign.\n    const minus = dec[0] === \"-\";\n    if (minus) {\n        dec = dec.slice(1);\n    }\n    // Work 6 decimal digits at a time, acting like we're converting base 1e6\n    // digits to binary. This is safe to do with floating point math because\n    // Number.isSafeInteger(ALL_32_BITS * 1e6) == true.\n    const base = 1e6;\n    let lowBits = 0;\n    let highBits = 0;\n    function add1e6digit(begin, end) {\n        // Note: Number('') is 0.\n        const digit1e6 = Number(dec.slice(begin, end));\n        highBits *= base;\n        lowBits = lowBits * base + digit1e6;\n        // Carry bits from lowBits to\n        if (lowBits >= TWO_PWR_32_DBL) {\n            highBits = highBits + ((lowBits / TWO_PWR_32_DBL) | 0);\n            lowBits = lowBits % TWO_PWR_32_DBL;\n        }\n    }\n    add1e6digit(-24, -18);\n    add1e6digit(-18, -12);\n    add1e6digit(-12, -6);\n    add1e6digit(-6);\n    return minus ? negate(lowBits, highBits) : newBits(lowBits, highBits);\n}\n/**\n * Losslessly converts a 64-bit signed integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction int64ToString(lo, hi) {\n    let bits = newBits(lo, hi);\n    // If we're treating the input as a signed value and the high bit is set, do\n    // a manual two's complement conversion before the decimal conversion.\n    const negative = bits.hi & 0x80000000;\n    if (negative) {\n        bits = negate(bits.lo, bits.hi);\n    }\n    const result = uInt64ToString(bits.lo, bits.hi);\n    return negative ? \"-\" + result : result;\n}\n/**\n * Losslessly converts a 64-bit unsigned integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction uInt64ToString(lo, hi) {\n    ({ lo, hi } = toUnsigned(lo, hi));\n    // Skip the expensive conversion if the number is small enough to use the\n    // built-in conversions.\n    // Number.MAX_SAFE_INTEGER = 0x001FFFFF FFFFFFFF, thus any number with\n    // highBits <= 0x1FFFFF can be safely expressed with a double and retain\n    // integer precision.\n    // Proven by: Number.isSafeInteger(0x1FFFFF * 2**32 + 0xFFFFFFFF) == true.\n    if (hi <= 0x1fffff) {\n        return String(TWO_PWR_32_DBL * hi + lo);\n    }\n    // What this code is doing is essentially converting the input number from\n    // base-2 to base-1e7, which allows us to represent the 64-bit range with\n    // only 3 (very large) digits. Those digits are then trivial to convert to\n    // a base-10 string.\n    // The magic numbers used here are -\n    // 2^24 = 16777216 = (1,6777216) in base-1e7.\n    // 2^48 = 281474976710656 = (2,8147497,6710656) in base-1e7.\n    // Split 32:32 representation into 16:24:24 representation so our\n    // intermediate digits don't overflow.\n    const low = lo & 0xffffff;\n    const mid = ((lo >>> 24) | (hi << 8)) & 0xffffff;\n    const high = (hi >> 16) & 0xffff;\n    // Assemble our three base-1e7 digits, ignoring carries. The maximum\n    // value in a digit at this step is representable as a 48-bit integer, which\n    // can be stored in a 64-bit floating point number.\n    let digitA = low + mid * 6777216 + high * 6710656;\n    let digitB = mid + high * 8147497;\n    let digitC = high * 2;\n    // Apply carries from A to B and from B to C.\n    const base = 10000000;\n    if (digitA >= base) {\n        digitB += Math.floor(digitA / base);\n        digitA %= base;\n    }\n    if (digitB >= base) {\n        digitC += Math.floor(digitB / base);\n        digitB %= base;\n    }\n    // If digitC is 0, then we should have returned in the trivial code path\n    // at the top for non-safe integers. Given this, we can assume both digitB\n    // and digitA need leading zeros.\n    return (digitC.toString() +\n        decimalFrom1e7WithLeadingZeros(digitB) +\n        decimalFrom1e7WithLeadingZeros(digitA));\n}\nfunction toUnsigned(lo, hi) {\n    return { lo: lo >>> 0, hi: hi >>> 0 };\n}\nfunction newBits(lo, hi) {\n    return { lo: lo | 0, hi: hi | 0 };\n}\n/**\n * Returns two's compliment negation of input.\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_Operators#Signed_32-bit_integers\n */\nfunction negate(lowBits, highBits) {\n    highBits = ~highBits;\n    if (lowBits) {\n        lowBits = ~lowBits + 1;\n    }\n    else {\n        // If lowBits is 0, then bitwise-not is 0xFFFFFFFF,\n        // adding 1 to that, results in 0x100000000, which leaves\n        // the low bits 0x0 and simply adds one to the high bits.\n        highBits += 1;\n    }\n    return newBits(lowBits, highBits);\n}\n/**\n * Returns decimal representation of digit1e7 with leading zeros.\n */\nconst decimalFrom1e7WithLeadingZeros = (digit1e7) => {\n    const partial = String(digit1e7);\n    return \"0000000\".slice(partial.length) + partial;\n};\n/**\n * Write a 32 bit varint, signed or unsigned. Same as `varint64write(0, value, bytes)`\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/1b18833f4f2a2f681f4e4a25cdf3b0a43115ec26/js/binary/encoder.js#L144\n */\nfunction varint32write(value, bytes) {\n    if (value >= 0) {\n        // write value as varint 32\n        while (value > 0x7f) {\n            bytes.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        bytes.push(value);\n    }\n    else {\n        for (let i = 0; i < 9; i++) {\n            bytes.push((value & 127) | 128);\n            value = value >> 7;\n        }\n        bytes.push(1);\n    }\n}\n/**\n * Read an unsigned 32 bit varint.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L220\n */\nfunction varint32read() {\n    let b = this.buf[this.pos++];\n    let result = b & 0x7f;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 7;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 14;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 21;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    // Extract only last 4 bits\n    b = this.buf[this.pos++];\n    result |= (b & 0x0f) << 28;\n    for (let readBytes = 5; (b & 0x80) !== 0 && readBytes < 10; readBytes++)\n        b = this.buf[this.pos++];\n    if ((b & 0x80) != 0)\n        throw new Error(\"invalid varint\");\n    this.assertBounds();\n    // Result can have 32 bits, convert it to unsigned\n    return result >>> 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGJ1ZmJ1aWxkL3Byb3RvYnVmL2Rpc3QvZXNtL3dpcmUvdmFyaW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RDtBQUM1RCxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSx3QkFBd0IsWUFBWTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixhQUFhO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxvQkFBb0IsUUFBUTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFFBQVE7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxPQUFPLFNBQVM7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU87QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLG9DQUFvQztBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYnVmYnVpbGRcXHByb3RvYnVmXFxkaXN0XFxlc21cXHdpcmVcXHZhcmludC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAwOCBHb29nbGUgSW5jLiAgQWxsIHJpZ2h0cyByZXNlcnZlZC5cbi8vXG4vLyBSZWRpc3RyaWJ1dGlvbiBhbmQgdXNlIGluIHNvdXJjZSBhbmQgYmluYXJ5IGZvcm1zLCB3aXRoIG9yIHdpdGhvdXRcbi8vIG1vZGlmaWNhdGlvbiwgYXJlIHBlcm1pdHRlZCBwcm92aWRlZCB0aGF0IHRoZSBmb2xsb3dpbmcgY29uZGl0aW9ucyBhcmVcbi8vIG1ldDpcbi8vXG4vLyAqIFJlZGlzdHJpYnV0aW9ucyBvZiBzb3VyY2UgY29kZSBtdXN0IHJldGFpbiB0aGUgYWJvdmUgY29weXJpZ2h0XG4vLyBub3RpY2UsIHRoaXMgbGlzdCBvZiBjb25kaXRpb25zIGFuZCB0aGUgZm9sbG93aW5nIGRpc2NsYWltZXIuXG4vLyAqIFJlZGlzdHJpYnV0aW9ucyBpbiBiaW5hcnkgZm9ybSBtdXN0IHJlcHJvZHVjZSB0aGUgYWJvdmVcbi8vIGNvcHlyaWdodCBub3RpY2UsIHRoaXMgbGlzdCBvZiBjb25kaXRpb25zIGFuZCB0aGUgZm9sbG93aW5nIGRpc2NsYWltZXJcbi8vIGluIHRoZSBkb2N1bWVudGF0aW9uIGFuZC9vciBvdGhlciBtYXRlcmlhbHMgcHJvdmlkZWQgd2l0aCB0aGVcbi8vIGRpc3RyaWJ1dGlvbi5cbi8vICogTmVpdGhlciB0aGUgbmFtZSBvZiBHb29nbGUgSW5jLiBub3IgdGhlIG5hbWVzIG9mIGl0c1xuLy8gY29udHJpYnV0b3JzIG1heSBiZSB1c2VkIHRvIGVuZG9yc2Ugb3IgcHJvbW90ZSBwcm9kdWN0cyBkZXJpdmVkIGZyb21cbi8vIHRoaXMgc29mdHdhcmUgd2l0aG91dCBzcGVjaWZpYyBwcmlvciB3cml0dGVuIHBlcm1pc3Npb24uXG4vL1xuLy8gVEhJUyBTT0ZUV0FSRSBJUyBQUk9WSURFRCBCWSBUSEUgQ09QWVJJR0hUIEhPTERFUlMgQU5EIENPTlRSSUJVVE9SU1xuLy8gXCJBUyBJU1wiIEFORCBBTlkgRVhQUkVTUyBPUiBJTVBMSUVEIFdBUlJBTlRJRVMsIElOQ0xVRElORywgQlVUIE5PVFxuLy8gTElNSVRFRCBUTywgVEhFIElNUExJRUQgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFkgQU5EIEZJVE5FU1MgRk9SXG4vLyBBIFBBUlRJQ1VMQVIgUFVSUE9TRSBBUkUgRElTQ0xBSU1FRC4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIENPUFlSSUdIVFxuLy8gT1dORVIgT1IgQ09OVFJJQlVUT1JTIEJFIExJQUJMRSBGT1IgQU5ZIERJUkVDVCwgSU5ESVJFQ1QsIElOQ0lERU5UQUwsXG4vLyBTUEVDSUFMLCBFWEVNUExBUlksIE9SIENPTlNFUVVFTlRJQUwgREFNQUdFUyAoSU5DTFVESU5HLCBCVVQgTk9UXG4vLyBMSU1JVEVEIFRPLCBQUk9DVVJFTUVOVCBPRiBTVUJTVElUVVRFIEdPT0RTIE9SIFNFUlZJQ0VTOyBMT1NTIE9GIFVTRSxcbi8vIERBVEEsIE9SIFBST0ZJVFM7IE9SIEJVU0lORVNTIElOVEVSUlVQVElPTikgSE9XRVZFUiBDQVVTRUQgQU5EIE9OIEFOWVxuLy8gVEhFT1JZIE9GIExJQUJJTElUWSwgV0hFVEhFUiBJTiBDT05UUkFDVCwgU1RSSUNUIExJQUJJTElUWSwgT1IgVE9SVFxuLy8gKElOQ0xVRElORyBORUdMSUdFTkNFIE9SIE9USEVSV0lTRSkgQVJJU0lORyBJTiBBTlkgV0FZIE9VVCBPRiBUSEUgVVNFXG4vLyBPRiBUSElTIFNPRlRXQVJFLCBFVkVOIElGIEFEVklTRUQgT0YgVEhFIFBPU1NJQklMSVRZIE9GIFNVQ0ggREFNQUdFLlxuLy9cbi8vIENvZGUgZ2VuZXJhdGVkIGJ5IHRoZSBQcm90b2NvbCBCdWZmZXIgY29tcGlsZXIgaXMgb3duZWQgYnkgdGhlIG93bmVyXG4vLyBvZiB0aGUgaW5wdXQgZmlsZSB1c2VkIHdoZW4gZ2VuZXJhdGluZyBpdC4gIFRoaXMgY29kZSBpcyBub3Rcbi8vIHN0YW5kYWxvbmUgYW5kIHJlcXVpcmVzIGEgc3VwcG9ydCBsaWJyYXJ5IHRvIGJlIGxpbmtlZCB3aXRoIGl0LiAgVGhpc1xuLy8gc3VwcG9ydCBsaWJyYXJ5IGlzIGl0c2VsZiBjb3ZlcmVkIGJ5IHRoZSBhYm92ZSBsaWNlbnNlLlxuLyoqXG4gKiBSZWFkIGEgNjQgYml0IHZhcmludCBhcyB0d28gSlMgbnVtYmVycy5cbiAqXG4gKiBSZXR1cm5zIHR1cGxlOlxuICogWzBdOiBsb3cgYml0c1xuICogWzFdOiBoaWdoIGJpdHNcbiAqXG4gKiBDb3B5cmlnaHQgMjAwOCBHb29nbGUgSW5jLiAgQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3Byb3RvY29sYnVmZmVycy9wcm90b2J1Zi9ibG9iLzhhNzE5MjdkNzRhNGNlMzRlZmUyZDg3NjlmZGExOThmNTJkMjBkMTIvanMvZXhwZXJpbWVudGFsL3J1bnRpbWUva2VybmVsL2J1ZmZlcl9kZWNvZGVyLmpzI0wxNzVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZhcmludDY0cmVhZCgpIHtcbiAgICBsZXQgbG93Qml0cyA9IDA7XG4gICAgbGV0IGhpZ2hCaXRzID0gMDtcbiAgICBmb3IgKGxldCBzaGlmdCA9IDA7IHNoaWZ0IDwgMjg7IHNoaWZ0ICs9IDcpIHtcbiAgICAgICAgbGV0IGIgPSB0aGlzLmJ1Zlt0aGlzLnBvcysrXTtcbiAgICAgICAgbG93Qml0cyB8PSAoYiAmIDB4N2YpIDw8IHNoaWZ0O1xuICAgICAgICBpZiAoKGIgJiAweDgwKSA9PSAwKSB7XG4gICAgICAgICAgICB0aGlzLmFzc2VydEJvdW5kcygpO1xuICAgICAgICAgICAgcmV0dXJuIFtsb3dCaXRzLCBoaWdoQml0c107XG4gICAgICAgIH1cbiAgICB9XG4gICAgbGV0IG1pZGRsZUJ5dGUgPSB0aGlzLmJ1Zlt0aGlzLnBvcysrXTtcbiAgICAvLyBsYXN0IGZvdXIgYml0cyBvZiB0aGUgZmlyc3QgMzIgYml0IG51bWJlclxuICAgIGxvd0JpdHMgfD0gKG1pZGRsZUJ5dGUgJiAweDBmKSA8PCAyODtcbiAgICAvLyAzIHVwcGVyIGJpdHMgYXJlIHBhcnQgb2YgdGhlIG5leHQgMzIgYml0IG51bWJlclxuICAgIGhpZ2hCaXRzID0gKG1pZGRsZUJ5dGUgJiAweDcwKSA+PiA0O1xuICAgIGlmICgobWlkZGxlQnl0ZSAmIDB4ODApID09IDApIHtcbiAgICAgICAgdGhpcy5hc3NlcnRCb3VuZHMoKTtcbiAgICAgICAgcmV0dXJuIFtsb3dCaXRzLCBoaWdoQml0c107XG4gICAgfVxuICAgIGZvciAobGV0IHNoaWZ0ID0gMzsgc2hpZnQgPD0gMzE7IHNoaWZ0ICs9IDcpIHtcbiAgICAgICAgbGV0IGIgPSB0aGlzLmJ1Zlt0aGlzLnBvcysrXTtcbiAgICAgICAgaGlnaEJpdHMgfD0gKGIgJiAweDdmKSA8PCBzaGlmdDtcbiAgICAgICAgaWYgKChiICYgMHg4MCkgPT0gMCkge1xuICAgICAgICAgICAgdGhpcy5hc3NlcnRCb3VuZHMoKTtcbiAgICAgICAgICAgIHJldHVybiBbbG93Qml0cywgaGlnaEJpdHNdO1xuICAgICAgICB9XG4gICAgfVxuICAgIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgdmFyaW50XCIpO1xufVxuLyoqXG4gKiBXcml0ZSBhIDY0IGJpdCB2YXJpbnQsIGdpdmVuIGFzIHR3byBKUyBudW1iZXJzLCB0byB0aGUgZ2l2ZW4gYnl0ZXMgYXJyYXkuXG4gKlxuICogQ29weXJpZ2h0IDIwMDggR29vZ2xlIEluYy4gIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKlxuICogU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9wcm90b2NvbGJ1ZmZlcnMvcHJvdG9idWYvYmxvYi84YTcxOTI3ZDc0YTRjZTM0ZWZlMmQ4NzY5ZmRhMTk4ZjUyZDIwZDEyL2pzL2V4cGVyaW1lbnRhbC9ydW50aW1lL2tlcm5lbC93cml0ZXIuanMjTDM0NFxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFyaW50NjR3cml0ZShsbywgaGksIGJ5dGVzKSB7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCAyODsgaSA9IGkgKyA3KSB7XG4gICAgICAgIGNvbnN0IHNoaWZ0ID0gbG8gPj4+IGk7XG4gICAgICAgIGNvbnN0IGhhc05leHQgPSAhKHNoaWZ0ID4+PiA3ID09IDAgJiYgaGkgPT0gMCk7XG4gICAgICAgIGNvbnN0IGJ5dGUgPSAoaGFzTmV4dCA/IHNoaWZ0IHwgMHg4MCA6IHNoaWZ0KSAmIDB4ZmY7XG4gICAgICAgIGJ5dGVzLnB1c2goYnl0ZSk7XG4gICAgICAgIGlmICghaGFzTmV4dCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0IHNwbGl0Qml0cyA9ICgobG8gPj4+IDI4KSAmIDB4MGYpIHwgKChoaSAmIDB4MDcpIDw8IDQpO1xuICAgIGNvbnN0IGhhc01vcmVCaXRzID0gIShoaSA+PiAzID09IDApO1xuICAgIGJ5dGVzLnB1c2goKGhhc01vcmVCaXRzID8gc3BsaXRCaXRzIHwgMHg4MCA6IHNwbGl0Qml0cykgJiAweGZmKTtcbiAgICBpZiAoIWhhc01vcmVCaXRzKSB7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgZm9yIChsZXQgaSA9IDM7IGkgPCAzMTsgaSA9IGkgKyA3KSB7XG4gICAgICAgIGNvbnN0IHNoaWZ0ID0gaGkgPj4+IGk7XG4gICAgICAgIGNvbnN0IGhhc05leHQgPSAhKHNoaWZ0ID4+PiA3ID09IDApO1xuICAgICAgICBjb25zdCBieXRlID0gKGhhc05leHQgPyBzaGlmdCB8IDB4ODAgOiBzaGlmdCkgJiAweGZmO1xuICAgICAgICBieXRlcy5wdXNoKGJ5dGUpO1xuICAgICAgICBpZiAoIWhhc05leHQpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgIH1cbiAgICBieXRlcy5wdXNoKChoaSA+Pj4gMzEpICYgMHgwMSk7XG59XG4vLyBjb25zdGFudHMgZm9yIGJpbmFyeSBtYXRoXG5jb25zdCBUV09fUFdSXzMyX0RCTCA9IDB4MTAwMDAwMDAwO1xuLyoqXG4gKiBQYXJzZSBkZWNpbWFsIHN0cmluZyBvZiA2NCBiaXQgaW50ZWdlciB2YWx1ZSBhcyB0d28gSlMgbnVtYmVycy5cbiAqXG4gKiBDb3B5cmlnaHQgMjAwOCBHb29nbGUgSW5jLiAgQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3Byb3RvY29sYnVmZmVycy9wcm90b2J1Zi1qYXZhc2NyaXB0L2Jsb2IvYTQyOGM1ODI3M2FiYWQwN2M2NjA3MWQ5NzUzYmM0ZDEyODlkZTQyNi9leHBlcmltZW50YWwvcnVudGltZS9pbnQ2NC5qcyNMMTBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGludDY0RnJvbVN0cmluZyhkZWMpIHtcbiAgICAvLyBDaGVjayBmb3IgbWludXMgc2lnbi5cbiAgICBjb25zdCBtaW51cyA9IGRlY1swXSA9PT0gXCItXCI7XG4gICAgaWYgKG1pbnVzKSB7XG4gICAgICAgIGRlYyA9IGRlYy5zbGljZSgxKTtcbiAgICB9XG4gICAgLy8gV29yayA2IGRlY2ltYWwgZGlnaXRzIGF0IGEgdGltZSwgYWN0aW5nIGxpa2Ugd2UncmUgY29udmVydGluZyBiYXNlIDFlNlxuICAgIC8vIGRpZ2l0cyB0byBiaW5hcnkuIFRoaXMgaXMgc2FmZSB0byBkbyB3aXRoIGZsb2F0aW5nIHBvaW50IG1hdGggYmVjYXVzZVxuICAgIC8vIE51bWJlci5pc1NhZmVJbnRlZ2VyKEFMTF8zMl9CSVRTICogMWU2KSA9PSB0cnVlLlxuICAgIGNvbnN0IGJhc2UgPSAxZTY7XG4gICAgbGV0IGxvd0JpdHMgPSAwO1xuICAgIGxldCBoaWdoQml0cyA9IDA7XG4gICAgZnVuY3Rpb24gYWRkMWU2ZGlnaXQoYmVnaW4sIGVuZCkge1xuICAgICAgICAvLyBOb3RlOiBOdW1iZXIoJycpIGlzIDAuXG4gICAgICAgIGNvbnN0IGRpZ2l0MWU2ID0gTnVtYmVyKGRlYy5zbGljZShiZWdpbiwgZW5kKSk7XG4gICAgICAgIGhpZ2hCaXRzICo9IGJhc2U7XG4gICAgICAgIGxvd0JpdHMgPSBsb3dCaXRzICogYmFzZSArIGRpZ2l0MWU2O1xuICAgICAgICAvLyBDYXJyeSBiaXRzIGZyb20gbG93Qml0cyB0b1xuICAgICAgICBpZiAobG93Qml0cyA+PSBUV09fUFdSXzMyX0RCTCkge1xuICAgICAgICAgICAgaGlnaEJpdHMgPSBoaWdoQml0cyArICgobG93Qml0cyAvIFRXT19QV1JfMzJfREJMKSB8IDApO1xuICAgICAgICAgICAgbG93Qml0cyA9IGxvd0JpdHMgJSBUV09fUFdSXzMyX0RCTDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBhZGQxZTZkaWdpdCgtMjQsIC0xOCk7XG4gICAgYWRkMWU2ZGlnaXQoLTE4LCAtMTIpO1xuICAgIGFkZDFlNmRpZ2l0KC0xMiwgLTYpO1xuICAgIGFkZDFlNmRpZ2l0KC02KTtcbiAgICByZXR1cm4gbWludXMgPyBuZWdhdGUobG93Qml0cywgaGlnaEJpdHMpIDogbmV3Qml0cyhsb3dCaXRzLCBoaWdoQml0cyk7XG59XG4vKipcbiAqIExvc3NsZXNzbHkgY29udmVydHMgYSA2NC1iaXQgc2lnbmVkIGludGVnZXIgaW4gMzI6MzIgc3BsaXQgcmVwcmVzZW50YXRpb25cbiAqIGludG8gYSBkZWNpbWFsIHN0cmluZy5cbiAqXG4gKiBDb3B5cmlnaHQgMjAwOCBHb29nbGUgSW5jLiAgQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3Byb3RvY29sYnVmZmVycy9wcm90b2J1Zi1qYXZhc2NyaXB0L2Jsb2IvYTQyOGM1ODI3M2FiYWQwN2M2NjA3MWQ5NzUzYmM0ZDEyODlkZTQyNi9leHBlcmltZW50YWwvcnVudGltZS9pbnQ2NC5qcyNMMTBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGludDY0VG9TdHJpbmcobG8sIGhpKSB7XG4gICAgbGV0IGJpdHMgPSBuZXdCaXRzKGxvLCBoaSk7XG4gICAgLy8gSWYgd2UncmUgdHJlYXRpbmcgdGhlIGlucHV0IGFzIGEgc2lnbmVkIHZhbHVlIGFuZCB0aGUgaGlnaCBiaXQgaXMgc2V0LCBkb1xuICAgIC8vIGEgbWFudWFsIHR3bydzIGNvbXBsZW1lbnQgY29udmVyc2lvbiBiZWZvcmUgdGhlIGRlY2ltYWwgY29udmVyc2lvbi5cbiAgICBjb25zdCBuZWdhdGl2ZSA9IGJpdHMuaGkgJiAweDgwMDAwMDAwO1xuICAgIGlmIChuZWdhdGl2ZSkge1xuICAgICAgICBiaXRzID0gbmVnYXRlKGJpdHMubG8sIGJpdHMuaGkpO1xuICAgIH1cbiAgICBjb25zdCByZXN1bHQgPSB1SW50NjRUb1N0cmluZyhiaXRzLmxvLCBiaXRzLmhpKTtcbiAgICByZXR1cm4gbmVnYXRpdmUgPyBcIi1cIiArIHJlc3VsdCA6IHJlc3VsdDtcbn1cbi8qKlxuICogTG9zc2xlc3NseSBjb252ZXJ0cyBhIDY0LWJpdCB1bnNpZ25lZCBpbnRlZ2VyIGluIDMyOjMyIHNwbGl0IHJlcHJlc2VudGF0aW9uXG4gKiBpbnRvIGEgZGVjaW1hbCBzdHJpbmcuXG4gKlxuICogQ29weXJpZ2h0IDIwMDggR29vZ2xlIEluYy4gIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKlxuICogU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9wcm90b2NvbGJ1ZmZlcnMvcHJvdG9idWYtamF2YXNjcmlwdC9ibG9iL2E0MjhjNTgyNzNhYmFkMDdjNjYwNzFkOTc1M2JjNGQxMjg5ZGU0MjYvZXhwZXJpbWVudGFsL3J1bnRpbWUvaW50NjQuanMjTDEwXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1SW50NjRUb1N0cmluZyhsbywgaGkpIHtcbiAgICAoeyBsbywgaGkgfSA9IHRvVW5zaWduZWQobG8sIGhpKSk7XG4gICAgLy8gU2tpcCB0aGUgZXhwZW5zaXZlIGNvbnZlcnNpb24gaWYgdGhlIG51bWJlciBpcyBzbWFsbCBlbm91Z2ggdG8gdXNlIHRoZVxuICAgIC8vIGJ1aWx0LWluIGNvbnZlcnNpb25zLlxuICAgIC8vIE51bWJlci5NQVhfU0FGRV9JTlRFR0VSID0gMHgwMDFGRkZGRiBGRkZGRkZGRiwgdGh1cyBhbnkgbnVtYmVyIHdpdGhcbiAgICAvLyBoaWdoQml0cyA8PSAweDFGRkZGRiBjYW4gYmUgc2FmZWx5IGV4cHJlc3NlZCB3aXRoIGEgZG91YmxlIGFuZCByZXRhaW5cbiAgICAvLyBpbnRlZ2VyIHByZWNpc2lvbi5cbiAgICAvLyBQcm92ZW4gYnk6IE51bWJlci5pc1NhZmVJbnRlZ2VyKDB4MUZGRkZGICogMioqMzIgKyAweEZGRkZGRkZGKSA9PSB0cnVlLlxuICAgIGlmIChoaSA8PSAweDFmZmZmZikge1xuICAgICAgICByZXR1cm4gU3RyaW5nKFRXT19QV1JfMzJfREJMICogaGkgKyBsbyk7XG4gICAgfVxuICAgIC8vIFdoYXQgdGhpcyBjb2RlIGlzIGRvaW5nIGlzIGVzc2VudGlhbGx5IGNvbnZlcnRpbmcgdGhlIGlucHV0IG51bWJlciBmcm9tXG4gICAgLy8gYmFzZS0yIHRvIGJhc2UtMWU3LCB3aGljaCBhbGxvd3MgdXMgdG8gcmVwcmVzZW50IHRoZSA2NC1iaXQgcmFuZ2Ugd2l0aFxuICAgIC8vIG9ubHkgMyAodmVyeSBsYXJnZSkgZGlnaXRzLiBUaG9zZSBkaWdpdHMgYXJlIHRoZW4gdHJpdmlhbCB0byBjb252ZXJ0IHRvXG4gICAgLy8gYSBiYXNlLTEwIHN0cmluZy5cbiAgICAvLyBUaGUgbWFnaWMgbnVtYmVycyB1c2VkIGhlcmUgYXJlIC1cbiAgICAvLyAyXjI0ID0gMTY3NzcyMTYgPSAoMSw2Nzc3MjE2KSBpbiBiYXNlLTFlNy5cbiAgICAvLyAyXjQ4ID0gMjgxNDc0OTc2NzEwNjU2ID0gKDIsODE0NzQ5Nyw2NzEwNjU2KSBpbiBiYXNlLTFlNy5cbiAgICAvLyBTcGxpdCAzMjozMiByZXByZXNlbnRhdGlvbiBpbnRvIDE2OjI0OjI0IHJlcHJlc2VudGF0aW9uIHNvIG91clxuICAgIC8vIGludGVybWVkaWF0ZSBkaWdpdHMgZG9uJ3Qgb3ZlcmZsb3cuXG4gICAgY29uc3QgbG93ID0gbG8gJiAweGZmZmZmZjtcbiAgICBjb25zdCBtaWQgPSAoKGxvID4+PiAyNCkgfCAoaGkgPDwgOCkpICYgMHhmZmZmZmY7XG4gICAgY29uc3QgaGlnaCA9IChoaSA+PiAxNikgJiAweGZmZmY7XG4gICAgLy8gQXNzZW1ibGUgb3VyIHRocmVlIGJhc2UtMWU3IGRpZ2l0cywgaWdub3JpbmcgY2Fycmllcy4gVGhlIG1heGltdW1cbiAgICAvLyB2YWx1ZSBpbiBhIGRpZ2l0IGF0IHRoaXMgc3RlcCBpcyByZXByZXNlbnRhYmxlIGFzIGEgNDgtYml0IGludGVnZXIsIHdoaWNoXG4gICAgLy8gY2FuIGJlIHN0b3JlZCBpbiBhIDY0LWJpdCBmbG9hdGluZyBwb2ludCBudW1iZXIuXG4gICAgbGV0IGRpZ2l0QSA9IGxvdyArIG1pZCAqIDY3NzcyMTYgKyBoaWdoICogNjcxMDY1NjtcbiAgICBsZXQgZGlnaXRCID0gbWlkICsgaGlnaCAqIDgxNDc0OTc7XG4gICAgbGV0IGRpZ2l0QyA9IGhpZ2ggKiAyO1xuICAgIC8vIEFwcGx5IGNhcnJpZXMgZnJvbSBBIHRvIEIgYW5kIGZyb20gQiB0byBDLlxuICAgIGNvbnN0IGJhc2UgPSAxMDAwMDAwMDtcbiAgICBpZiAoZGlnaXRBID49IGJhc2UpIHtcbiAgICAgICAgZGlnaXRCICs9IE1hdGguZmxvb3IoZGlnaXRBIC8gYmFzZSk7XG4gICAgICAgIGRpZ2l0QSAlPSBiYXNlO1xuICAgIH1cbiAgICBpZiAoZGlnaXRCID49IGJhc2UpIHtcbiAgICAgICAgZGlnaXRDICs9IE1hdGguZmxvb3IoZGlnaXRCIC8gYmFzZSk7XG4gICAgICAgIGRpZ2l0QiAlPSBiYXNlO1xuICAgIH1cbiAgICAvLyBJZiBkaWdpdEMgaXMgMCwgdGhlbiB3ZSBzaG91bGQgaGF2ZSByZXR1cm5lZCBpbiB0aGUgdHJpdmlhbCBjb2RlIHBhdGhcbiAgICAvLyBhdCB0aGUgdG9wIGZvciBub24tc2FmZSBpbnRlZ2Vycy4gR2l2ZW4gdGhpcywgd2UgY2FuIGFzc3VtZSBib3RoIGRpZ2l0QlxuICAgIC8vIGFuZCBkaWdpdEEgbmVlZCBsZWFkaW5nIHplcm9zLlxuICAgIHJldHVybiAoZGlnaXRDLnRvU3RyaW5nKCkgK1xuICAgICAgICBkZWNpbWFsRnJvbTFlN1dpdGhMZWFkaW5nWmVyb3MoZGlnaXRCKSArXG4gICAgICAgIGRlY2ltYWxGcm9tMWU3V2l0aExlYWRpbmdaZXJvcyhkaWdpdEEpKTtcbn1cbmZ1bmN0aW9uIHRvVW5zaWduZWQobG8sIGhpKSB7XG4gICAgcmV0dXJuIHsgbG86IGxvID4+PiAwLCBoaTogaGkgPj4+IDAgfTtcbn1cbmZ1bmN0aW9uIG5ld0JpdHMobG8sIGhpKSB7XG4gICAgcmV0dXJuIHsgbG86IGxvIHwgMCwgaGk6IGhpIHwgMCB9O1xufVxuLyoqXG4gKiBSZXR1cm5zIHR3bydzIGNvbXBsaW1lbnQgbmVnYXRpb24gb2YgaW5wdXQuXG4gKiBAc2VlIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0phdmFTY3JpcHQvUmVmZXJlbmNlL09wZXJhdG9ycy9CaXR3aXNlX09wZXJhdG9ycyNTaWduZWRfMzItYml0X2ludGVnZXJzXG4gKi9cbmZ1bmN0aW9uIG5lZ2F0ZShsb3dCaXRzLCBoaWdoQml0cykge1xuICAgIGhpZ2hCaXRzID0gfmhpZ2hCaXRzO1xuICAgIGlmIChsb3dCaXRzKSB7XG4gICAgICAgIGxvd0JpdHMgPSB+bG93Qml0cyArIDE7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICAvLyBJZiBsb3dCaXRzIGlzIDAsIHRoZW4gYml0d2lzZS1ub3QgaXMgMHhGRkZGRkZGRixcbiAgICAgICAgLy8gYWRkaW5nIDEgdG8gdGhhdCwgcmVzdWx0cyBpbiAweDEwMDAwMDAwMCwgd2hpY2ggbGVhdmVzXG4gICAgICAgIC8vIHRoZSBsb3cgYml0cyAweDAgYW5kIHNpbXBseSBhZGRzIG9uZSB0byB0aGUgaGlnaCBiaXRzLlxuICAgICAgICBoaWdoQml0cyArPSAxO1xuICAgIH1cbiAgICByZXR1cm4gbmV3Qml0cyhsb3dCaXRzLCBoaWdoQml0cyk7XG59XG4vKipcbiAqIFJldHVybnMgZGVjaW1hbCByZXByZXNlbnRhdGlvbiBvZiBkaWdpdDFlNyB3aXRoIGxlYWRpbmcgemVyb3MuXG4gKi9cbmNvbnN0IGRlY2ltYWxGcm9tMWU3V2l0aExlYWRpbmdaZXJvcyA9IChkaWdpdDFlNykgPT4ge1xuICAgIGNvbnN0IHBhcnRpYWwgPSBTdHJpbmcoZGlnaXQxZTcpO1xuICAgIHJldHVybiBcIjAwMDAwMDBcIi5zbGljZShwYXJ0aWFsLmxlbmd0aCkgKyBwYXJ0aWFsO1xufTtcbi8qKlxuICogV3JpdGUgYSAzMiBiaXQgdmFyaW50LCBzaWduZWQgb3IgdW5zaWduZWQuIFNhbWUgYXMgYHZhcmludDY0d3JpdGUoMCwgdmFsdWUsIGJ5dGVzKWBcbiAqXG4gKiBDb3B5cmlnaHQgMjAwOCBHb29nbGUgSW5jLiAgQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3Byb3RvY29sYnVmZmVycy9wcm90b2J1Zi9ibG9iLzFiMTg4MzNmNGYyYTJmNjgxZjRlNGEyNWNkZjNiMGE0MzExNWVjMjYvanMvYmluYXJ5L2VuY29kZXIuanMjTDE0NFxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFyaW50MzJ3cml0ZSh2YWx1ZSwgYnl0ZXMpIHtcbiAgICBpZiAodmFsdWUgPj0gMCkge1xuICAgICAgICAvLyB3cml0ZSB2YWx1ZSBhcyB2YXJpbnQgMzJcbiAgICAgICAgd2hpbGUgKHZhbHVlID4gMHg3Zikge1xuICAgICAgICAgICAgYnl0ZXMucHVzaCgodmFsdWUgJiAweDdmKSB8IDB4ODApO1xuICAgICAgICAgICAgdmFsdWUgPSB2YWx1ZSA+Pj4gNztcbiAgICAgICAgfVxuICAgICAgICBieXRlcy5wdXNoKHZhbHVlKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgOTsgaSsrKSB7XG4gICAgICAgICAgICBieXRlcy5wdXNoKCh2YWx1ZSAmIDEyNykgfCAxMjgpO1xuICAgICAgICAgICAgdmFsdWUgPSB2YWx1ZSA+PiA3O1xuICAgICAgICB9XG4gICAgICAgIGJ5dGVzLnB1c2goMSk7XG4gICAgfVxufVxuLyoqXG4gKiBSZWFkIGFuIHVuc2lnbmVkIDMyIGJpdCB2YXJpbnQuXG4gKlxuICogU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9wcm90b2NvbGJ1ZmZlcnMvcHJvdG9idWYvYmxvYi84YTcxOTI3ZDc0YTRjZTM0ZWZlMmQ4NzY5ZmRhMTk4ZjUyZDIwZDEyL2pzL2V4cGVyaW1lbnRhbC9ydW50aW1lL2tlcm5lbC9idWZmZXJfZGVjb2Rlci5qcyNMMjIwXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2YXJpbnQzMnJlYWQoKSB7XG4gICAgbGV0IGIgPSB0aGlzLmJ1Zlt0aGlzLnBvcysrXTtcbiAgICBsZXQgcmVzdWx0ID0gYiAmIDB4N2Y7XG4gICAgaWYgKChiICYgMHg4MCkgPT0gMCkge1xuICAgICAgICB0aGlzLmFzc2VydEJvdW5kcygpO1xuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH1cbiAgICBiID0gdGhpcy5idWZbdGhpcy5wb3MrK107XG4gICAgcmVzdWx0IHw9IChiICYgMHg3ZikgPDwgNztcbiAgICBpZiAoKGIgJiAweDgwKSA9PSAwKSB7XG4gICAgICAgIHRoaXMuYXNzZXJ0Qm91bmRzKCk7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfVxuICAgIGIgPSB0aGlzLmJ1Zlt0aGlzLnBvcysrXTtcbiAgICByZXN1bHQgfD0gKGIgJiAweDdmKSA8PCAxNDtcbiAgICBpZiAoKGIgJiAweDgwKSA9PSAwKSB7XG4gICAgICAgIHRoaXMuYXNzZXJ0Qm91bmRzKCk7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfVxuICAgIGIgPSB0aGlzLmJ1Zlt0aGlzLnBvcysrXTtcbiAgICByZXN1bHQgfD0gKGIgJiAweDdmKSA8PCAyMTtcbiAgICBpZiAoKGIgJiAweDgwKSA9PSAwKSB7XG4gICAgICAgIHRoaXMuYXNzZXJ0Qm91bmRzKCk7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfVxuICAgIC8vIEV4dHJhY3Qgb25seSBsYXN0IDQgYml0c1xuICAgIGIgPSB0aGlzLmJ1Zlt0aGlzLnBvcysrXTtcbiAgICByZXN1bHQgfD0gKGIgJiAweDBmKSA8PCAyODtcbiAgICBmb3IgKGxldCByZWFkQnl0ZXMgPSA1OyAoYiAmIDB4ODApICE9PSAwICYmIHJlYWRCeXRlcyA8IDEwOyByZWFkQnl0ZXMrKylcbiAgICAgICAgYiA9IHRoaXMuYnVmW3RoaXMucG9zKytdO1xuICAgIGlmICgoYiAmIDB4ODApICE9IDApXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgdmFyaW50XCIpO1xuICAgIHRoaXMuYXNzZXJ0Qm91bmRzKCk7XG4gICAgLy8gUmVzdWx0IGNhbiBoYXZlIDMyIGJpdHMsIGNvbnZlcnQgaXQgdG8gdW5zaWduZWRcbiAgICByZXR1cm4gcmVzdWx0ID4+PiAwO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js\n");

/***/ })

};
;