"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/js-tiktoken";
exports.ids = ["vendor-chunks/js-tiktoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/js-tiktoken/dist/lite.cjs":
/*!************************************************!*\
  !*** ./node_modules/js-tiktoken/dist/lite.cjs ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar base64 = __webpack_require__(/*! base64-js */ \"(rsc)/./node_modules/base64-js/index.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar base64__default = /*#__PURE__*/_interopDefault(base64);\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\n\n// src/core.ts\nfunction bytePairMerge(piece, ranks) {\n  let parts = Array.from(\n    { length: piece.length },\n    (_, i) => ({ start: i, end: i + 1 })\n  );\n  while (parts.length > 1) {\n    let minRank = null;\n    for (let i = 0; i < parts.length - 1; i++) {\n      const slice = piece.slice(parts[i].start, parts[i + 1].end);\n      const rank = ranks.get(slice.join(\",\"));\n      if (rank == null)\n        continue;\n      if (minRank == null || rank < minRank[0]) {\n        minRank = [rank, i];\n      }\n    }\n    if (minRank != null) {\n      const i = minRank[1];\n      parts[i] = { start: parts[i].start, end: parts[i + 1].end };\n      parts.splice(i + 1, 1);\n    } else {\n      break;\n    }\n  }\n  return parts;\n}\nfunction bytePairEncode(piece, ranks) {\n  if (piece.length === 1)\n    return [ranks.get(piece.join(\",\"))];\n  return bytePairMerge(piece, ranks).map((p) => ranks.get(piece.slice(p.start, p.end).join(\",\"))).filter((x) => x != null);\n}\nfunction escapeRegex(str) {\n  return str.replace(/[\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\n}\nvar _Tiktoken = class {\n  /** @internal */\n  specialTokens;\n  /** @internal */\n  inverseSpecialTokens;\n  /** @internal */\n  patStr;\n  /** @internal */\n  textEncoder = new TextEncoder();\n  /** @internal */\n  textDecoder = new TextDecoder(\"utf-8\");\n  /** @internal */\n  rankMap = /* @__PURE__ */ new Map();\n  /** @internal */\n  textMap = /* @__PURE__ */ new Map();\n  constructor(ranks, extendedSpecialTokens) {\n    this.patStr = ranks.pat_str;\n    const uncompressed = ranks.bpe_ranks.split(\"\\n\").filter(Boolean).reduce((memo, x) => {\n      const [_, offsetStr, ...tokens] = x.split(\" \");\n      const offset = Number.parseInt(offsetStr, 10);\n      tokens.forEach((token, i) => memo[token] = offset + i);\n      return memo;\n    }, {});\n    for (const [token, rank] of Object.entries(uncompressed)) {\n      const bytes = base64__default.default.toByteArray(token);\n      this.rankMap.set(bytes.join(\",\"), rank);\n      this.textMap.set(rank, bytes);\n    }\n    this.specialTokens = { ...ranks.special_tokens, ...extendedSpecialTokens };\n    this.inverseSpecialTokens = Object.entries(this.specialTokens).reduce((memo, [text, rank]) => {\n      memo[rank] = this.textEncoder.encode(text);\n      return memo;\n    }, {});\n  }\n  encode(text, allowedSpecial = [], disallowedSpecial = \"all\") {\n    const regexes = new RegExp(this.patStr, \"ug\");\n    const specialRegex = _Tiktoken.specialTokenRegex(\n      Object.keys(this.specialTokens)\n    );\n    const ret = [];\n    const allowedSpecialSet = new Set(\n      allowedSpecial === \"all\" ? Object.keys(this.specialTokens) : allowedSpecial\n    );\n    const disallowedSpecialSet = new Set(\n      disallowedSpecial === \"all\" ? Object.keys(this.specialTokens).filter(\n        (x) => !allowedSpecialSet.has(x)\n      ) : disallowedSpecial\n    );\n    if (disallowedSpecialSet.size > 0) {\n      const disallowedSpecialRegex = _Tiktoken.specialTokenRegex([\n        ...disallowedSpecialSet\n      ]);\n      const specialMatch = text.match(disallowedSpecialRegex);\n      if (specialMatch != null) {\n        throw new Error(\n          `The text contains a special token that is not allowed: ${specialMatch[0]}`\n        );\n      }\n    }\n    let start = 0;\n    while (true) {\n      let nextSpecial = null;\n      let startFind = start;\n      while (true) {\n        specialRegex.lastIndex = startFind;\n        nextSpecial = specialRegex.exec(text);\n        if (nextSpecial == null || allowedSpecialSet.has(nextSpecial[0]))\n          break;\n        startFind = nextSpecial.index + 1;\n      }\n      const end = nextSpecial?.index ?? text.length;\n      for (const match of text.substring(start, end).matchAll(regexes)) {\n        const piece = this.textEncoder.encode(match[0]);\n        const token2 = this.rankMap.get(piece.join(\",\"));\n        if (token2 != null) {\n          ret.push(token2);\n          continue;\n        }\n        ret.push(...bytePairEncode(piece, this.rankMap));\n      }\n      if (nextSpecial == null)\n        break;\n      let token = this.specialTokens[nextSpecial[0]];\n      ret.push(token);\n      start = nextSpecial.index + nextSpecial[0].length;\n    }\n    return ret;\n  }\n  decode(tokens) {\n    const res = [];\n    let length = 0;\n    for (let i2 = 0; i2 < tokens.length; ++i2) {\n      const token = tokens[i2];\n      const bytes = this.textMap.get(token) ?? this.inverseSpecialTokens[token];\n      if (bytes != null) {\n        res.push(bytes);\n        length += bytes.length;\n      }\n    }\n    const mergedArray = new Uint8Array(length);\n    let i = 0;\n    for (const bytes of res) {\n      mergedArray.set(bytes, i);\n      i += bytes.length;\n    }\n    return this.textDecoder.decode(mergedArray);\n  }\n};\nvar Tiktoken = _Tiktoken;\n__publicField(Tiktoken, \"specialTokenRegex\", (tokens) => {\n  return new RegExp(tokens.map((i) => escapeRegex(i)).join(\"|\"), \"g\");\n});\nfunction getEncodingNameForModel(model) {\n  switch (model) {\n    case \"gpt2\": {\n      return \"gpt2\";\n    }\n    case \"code-cushman-001\":\n    case \"code-cushman-002\":\n    case \"code-davinci-001\":\n    case \"code-davinci-002\":\n    case \"cushman-codex\":\n    case \"davinci-codex\":\n    case \"davinci-002\":\n    case \"text-davinci-002\":\n    case \"text-davinci-003\": {\n      return \"p50k_base\";\n    }\n    case \"code-davinci-edit-001\":\n    case \"text-davinci-edit-001\": {\n      return \"p50k_edit\";\n    }\n    case \"ada\":\n    case \"babbage\":\n    case \"babbage-002\":\n    case \"code-search-ada-code-001\":\n    case \"code-search-babbage-code-001\":\n    case \"curie\":\n    case \"davinci\":\n    case \"text-ada-001\":\n    case \"text-babbage-001\":\n    case \"text-curie-001\":\n    case \"text-davinci-001\":\n    case \"text-search-ada-doc-001\":\n    case \"text-search-babbage-doc-001\":\n    case \"text-search-curie-doc-001\":\n    case \"text-search-davinci-doc-001\":\n    case \"text-similarity-ada-001\":\n    case \"text-similarity-babbage-001\":\n    case \"text-similarity-curie-001\":\n    case \"text-similarity-davinci-001\": {\n      return \"r50k_base\";\n    }\n    case \"gpt-3.5-turbo-instruct-0914\":\n    case \"gpt-3.5-turbo-instruct\":\n    case \"gpt-3.5-turbo-16k-0613\":\n    case \"gpt-3.5-turbo-16k\":\n    case \"gpt-3.5-turbo-0613\":\n    case \"gpt-3.5-turbo-0301\":\n    case \"gpt-3.5-turbo\":\n    case \"gpt-4-32k-0613\":\n    case \"gpt-4-32k-0314\":\n    case \"gpt-4-32k\":\n    case \"gpt-4-0613\":\n    case \"gpt-4-0314\":\n    case \"gpt-4\":\n    case \"gpt-3.5-turbo-1106\":\n    case \"gpt-35-turbo\":\n    case \"gpt-4-1106-preview\":\n    case \"gpt-4-vision-preview\":\n    case \"gpt-3.5-turbo-0125\":\n    case \"gpt-4-turbo\":\n    case \"gpt-4-turbo-2024-04-09\":\n    case \"gpt-4-turbo-preview\":\n    case \"gpt-4-0125-preview\":\n    case \"text-embedding-ada-002\":\n    case \"text-embedding-3-small\":\n    case \"text-embedding-3-large\": {\n      return \"cl100k_base\";\n    }\n    case \"gpt-4o\":\n    case \"gpt-4o-2024-05-13\":\n    case \"gpt-4o-2024-08-06\":\n    case \"gpt-4o-2024-11-20\":\n    case \"gpt-4o-mini-2024-07-18\":\n    case \"gpt-4o-mini\":\n    case \"gpt-4o-search-preview\":\n    case \"gpt-4o-search-preview-2025-03-11\":\n    case \"gpt-4o-mini-search-preview\":\n    case \"gpt-4o-mini-search-preview-2025-03-11\":\n    case \"gpt-4o-audio-preview\":\n    case \"gpt-4o-audio-preview-2024-12-17\":\n    case \"gpt-4o-audio-preview-2024-10-01\":\n    case \"gpt-4o-mini-audio-preview\":\n    case \"gpt-4o-mini-audio-preview-2024-12-17\":\n    case \"o1\":\n    case \"o1-2024-12-17\":\n    case \"o1-mini\":\n    case \"o1-mini-2024-09-12\":\n    case \"o1-preview\":\n    case \"o1-preview-2024-09-12\":\n    case \"o1-pro\":\n    case \"o1-pro-2025-03-19\":\n    case \"o3\":\n    case \"o3-2025-04-16\":\n    case \"o3-mini\":\n    case \"o3-mini-2025-01-31\":\n    case \"o4-mini\":\n    case \"o4-mini-2025-04-16\":\n    case \"chatgpt-4o-latest\":\n    case \"gpt-4o-realtime\":\n    case \"gpt-4o-realtime-preview-2024-10-01\":\n    case \"gpt-4o-realtime-preview-2024-12-17\":\n    case \"gpt-4o-mini-realtime-preview\":\n    case \"gpt-4o-mini-realtime-preview-2024-12-17\":\n    case \"gpt-4.1\":\n    case \"gpt-4.1-2025-04-14\":\n    case \"gpt-4.1-mini\":\n    case \"gpt-4.1-mini-2025-04-14\":\n    case \"gpt-4.1-nano\":\n    case \"gpt-4.1-nano-2025-04-14\":\n    case \"gpt-4.5-preview\":\n    case \"gpt-4.5-preview-2025-02-27\": {\n      return \"o200k_base\";\n    }\n    default:\n      throw new Error(\"Unknown model\");\n  }\n}\n\nexports.Tiktoken = Tiktoken;\nexports.getEncodingNameForModel = getEncodingNameForModel;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/js-tiktoken/dist/lite.cjs\n");

/***/ })

};
;