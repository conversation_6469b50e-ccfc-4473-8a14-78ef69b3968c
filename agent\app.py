from fastapi import <PERSON><PERSON><PERSON>, Request, Response, <PERSON><PERSON>
from typing import Annotated, Dict, Any, Literal, Optional
import uuid
from fastapi.middleware.cors import CORSMiddleware
from copilotkit.integrations.fastapi import add_fastapi_endpoint
from copilotkit import CopilotKitRemoteEndpoint, LangGraphAgent
import uvicorn
from datetime import datetime
import os
import asyncio
import re
import json
import logging
from typing import Literal, Optional, Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.prebuilt import ToolNode
from copilotkit import CopilotKitState
from apis import ZinniaXAPI
from langchain_ollama import ChatOllama
from langgraph.cache.memory import InMemoryCache
from langgraph.checkpoint.memory import MemorySaver
from langgraph.types import CachePolicy
from tools import (
    get_schedule_details, get_cancel_details_id, get_uncancel_details,
    validate_schedule_details, validate_cancel_details, get_cancel_details_doctorname_date,
    validate_cancel_reschedule_doctor_details, validate_uncancel_details,
    get_reschedule_case_details, validate_reschedule_details, get_reschedule_details_doctorname_date
)
from helper import decode_jwt_no_secret, bind_thread_to_user, get_username_for_thread, add_token_usage_for_user, parse_time_string


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Configuration ---
STAGE_URL = os.environ.get("STAGE_URL")


async def get_user_token_from_config(config: RunnableConfig) -> Optional[str]:
    """Safely get or store user token using thread_id as the key in a JSON file."""
    try:
        print("\n==============================\n🤖 Starting FUNCTION: get_user_token_from_config\n==============================\n")           
        
        print('⚙️'*100)
        print('CONFIG', config)
        print('⚙️'*100)


        data = config["metadata"]
        thread_id = data.get("thread_id")
        if not thread_id:
            print("Warning: thread_id not found in metadata.")
            return None, None
            

        # Load existing thread_id to token mapping
        tokens_path = "tokens.json"
        tokens = {}
        if os.path.exists(tokens_path):
            try:
                with open(tokens_path, "r") as f:
                    tokens = json.load(f)
            except Exception as e:
                print(f"Error reading tokens.json: {e}")

        # If token already exists for this thread_id, return it
        if thread_id in tokens.keys():
            return None, tokens[thread_id]
            

        # Extract session_id and user_token from metadata
        # Assumes the first item (after 'thread_id') is the session_id: token pair

        print('⚙️'*100)
        # print('CONFIG DATA', data)
        print('⚙️'*100)
        
        session_id, user_token = next(
            ((k, v) for k, v in data.items() if k != "thread_id"), (None, None)
        )

        if not user_token:
            print("Warning: user_token not found in metadata.")
            return None, None
            

        username = decode_jwt_no_secret(user_token)
        if not username:
            print("No UserName found with this token.")
            return None, None
            
        print('THREAD ID',thread_id) 
        # Store the thread_id: user_token mapping
        tokens[thread_id] = user_token
        with open(tokens_path, "w") as f:
            json.dump(tokens, f, indent=2)

        # Bind thread_id to username
        bind_thread_to_user(thread_id, username)

        return username, user_token

        

    except KeyError:
        print("Warning: Could not find metadata in config.")
        return None, None

class AgentState(CopilotKitState):
    """
    State management for the scheduling agent
    """

    confirmation_needed: bool = False
    deferred_tool_call: Optional[dict] = None
    awaiting_user_response: bool = False
    showCase: Optional[dict] = None
    showHospitals: Optional[dict] = None
    showDoctors: Optional[dict] = None
    confirmation_message: Optional[str] = None
    frontend_tool: Optional[str] = None


# List of all tools
critical_tools = [
    'get_cancel_details_id',
    'get_uncancel_details',
    'get_schedule_details',
    'get_cancel_details_doctorname_date',
    'get_reschedule_case_details',
    'get_reschedule_details_doctorname_date'
]

tools = [
    get_schedule_details,
    get_cancel_details_id,
    get_reschedule_case_details,
    get_uncancel_details,
    get_cancel_details_doctorname_date,
    get_reschedule_details_doctorname_date
]


def is_critical_tool_call(tool_call):
    """Checks if a tool call is for a critical action that requires confirmation."""
    return tool_call.get("name") in critical_tools


async def chat_node(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    print("\n==============================\n🟦 Entering NODE: chat_node\n==============================\n")
    print("Entering chat_node")
    username, user_token = await get_user_token_from_config(config)
    print(f"Username:, {username}  --------    User Token: {user_token}")
    if not user_token:
        logger.error("No user token found in config. Cannot proceed.")
        return {
            "messages": [AIMessage(content="please try again.")],
            "confirmation_needed": False,
            "deferred_tool_call": None,
            "awaiting_user_response": False
        }

    # print('user_token', user_token)

    # Initialize the model
    # Disable SSL verification for ngrok URLs (development only)
    import ssl
    import os

    # Set environment variables to disable SSL verification
    os.environ['PYTHONHTTPSVERIFY'] = '0'
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''

    # Also disable SSL warnings
    try:
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    except ImportError:
        pass

    try:
        # Try HTTPS first
        model = ChatOllama(
            model="qwen3:8b",
            base_url='https://partially-model-boa.ngrok-free.app',
            temperature=0
        )
        print("✅ Successfully connected to ChatOllama with HTTPS")
    except Exception as e:
        logger.error(f"Failed to initialize ChatOllama with HTTPS: {e}")
        try:
            # Try HTTP as fallback
            model = ChatOllama(
                model="qwen3:8b",
                base_url='http://partially-model-boa.ngrok-free.app',
                temperature=0
            )
            print("✅ Successfully connected to ChatOllama with HTTP")
        except Exception as e2:
            logger.error(f"Failed to initialize ChatOllama with HTTP: {e2}")
            # Final fallback to OpenAI if available
            openai_key = os.environ.get('OPENAI_API_KEY', '')
            if openai_key:
                model = ChatOpenAI(model="gpt-4o-mini", temperature=0, api_key=openai_key)
                print("✅ Fallback to OpenAI model")
            else:
                raise Exception("No working model available. Please check your ngrok URL or provide OpenAI API key.")
    # model = ChatOpenAI(model="gpt-4o-mini", temperature=0,api_key='')

    # Bind tools to the model
    available_tools = [*tools]
    model_with_tools = model.bind_tools(available_tools)

    print(f"Chat node - Tools available: {len(available_tools)}")

    system_message = SystemMessage(
        content=(
            f"""
            [INST] <<SYS>>
            You are an AI assistant for managing surgical cases in the ZinniaX EHR system.

            Your ONLY way to perform scheduling, cancellation, rescheduling, or uncancellation is by calling the correct tool.
            Never answer these requests directly. Always use a tool.
            Today is {datetime.now().strftime("%Y-%m-%d")}. if not clear ask to user.(NEVER ASSUME ANY DETAILS) \n\
            \n\
            ---\n\
            **Available Tools and When to Use Them:**\n\
            \n\
            1. **get_schedule_details**\n\
                - Use to schedule a new surgical case.\n\
                - Requires: surgeon, date, time, hospital.\n\
                - Optional: procedure, patientName, city.\n\

            2. **CANCELLATION TOOLS - Choose based on what information the user provides:**\n\
                \n\
                **get_cancel_details_id**\n\
                - Use ONLY when the user provides a specific case ID number.\n\
                - Requires: caseId.\n\
                - Examples: "Cancel case 12345", "106418 - cancelled", "Cancel case #78910"\n\
                \n\
                **get_cancel_details_doctorname_date**\n\
                - Use when the user wants to cancel but does NOT provide a case ID.\n\
                - Use when user provides surgeon name, hospital, and date instead of case ID.\n\
                - Requires: surgeon, hospital, date.\n\
                - Examples: "Cancel Dr. Smith's cases at General Hospital today", "Cancel all cases for Dr. Lee at Mercy on June 12"\n\

            3. **RESCHEDULING TOOLS - Choose based on what information the user provides:**\n\
                \n\
                **get_reschedule_case_details**\n\
                - Use ONLY when the user provides a specific case ID number.\n\
                - Requires: caseId, newDate.\n\
                - Optional: newTime.\n\
                - Examples: "Reschedule case 12345 to tomorrow", "Move case #78910 to 4am same date"\n\
                \n\
                **get_reschedule_details_doctorname_date**\n\
                - Use when the user wants to reschedule but does NOT provide a case ID.\n\
                - Use when user provides surgeon name, hospital, and date instead of case ID.\n\
                - Requires: surgeon, hospital, date.\n\
                - Examples: "Reschedule Dr. Patel's cases at Mercy Hospital on July 1", "Move all Dr. Johnson's cases at City Hospital tomorrow"\n\

            4. **get_uncancel_details**\n\
                - Use to uncancel (reactivate/reopen) a previously cancelled case by its case ID.\n\
                - Requires: caseId.\n\

            \n\
            ---\n\
            **CRITICAL DECISION RULES:**\n\
            - **If user mentions a case ID/number**: Use the "_id" or "_case_details" version of the tool\n\
            - **If user does NOT mention a case ID**: Use the "_doctorname_date" version of the tool\n\
            - For any scheduling, cancellation, rescheduling, or uncancellation request, ALWAYS call the appropriate tool.\n\
            - Never answer these requests directly.\n\
            - If information is missing, ask the user for the required details.\n\
            - Never combine actions. Handle one request at a time.\n\
            - If a user asks for something outside these actions, you may answer directly.\n\
            - **IMPORTANT**: When a user wants to edit or modify details (like changing time, date, doctor, hospital, etc.), call the appropriate tool with the UPDATED arguments that include the user's requested changes.\
            - **EDITING DURING CONFIRMATION**: If a user is in a confirmation state and wants to edit details, always call the same tool type with the updated parameters. The system will re-validate and show a new confirmation.\n\
            \n\
            **Examples - Pay attention to whether case ID is provided:**\n\
            \n\
            **SCHEDULING:**\n\
            - User: "Schedule a case for Dr. Lee at General Hospital on June 12 at 8am."\n\
              -> Call get_schedule_details with surgeon="Dr. Lee", hospital="General Hospital", date="2024-06-12", time="08:00"\n\
            \n\
            **CANCELLATION - With Case ID:**\n\
            - User: "Cancel case 78910" or "106418 - AHMC - cancelled" or "Cancel case #12345"\n\
              -> Call get_cancel_details_id with caseId="78910" (because case ID is provided)\n\
            \n\
            **CANCELLATION - Without Case ID:**\n\
            - User: "Cancel Dr. Smith's cases at General Hospital today" or "Cancel all Dr. Lee's cases at Mercy tomorrow"\n\
              -> Call get_cancel_details_doctorname_date with surgeon="Dr. Smith", hospital="General Hospital", date="2024-XX-XX" (because no case ID provided)\n\
            \n\
            **RESCHEDULING - With Case ID:**\n\
            - User: "Reschedule case 109981 to 4am, same date" or "Move case #12345 to tomorrow"\n\
              -> Call get_reschedule_case_details with caseId="109981", newTime="04:00", newDate="same date" (because case ID is provided)\n\
            \n\
            **RESCHEDULING - Without Case ID:**\n\
            - User: "Reschedule Dr. Patel's cases at Mercy Hospital on July 1" or "Move all Dr. Johnson's cases tomorrow"\n\
              -> Call get_reschedule_details_doctorname_date with surgeon="Dr. Patel", hospital="Mercy Hospital", date="2024-07-01" (because no case ID provided)\n\
            \n\
            **EDITING DETAILS:**\n\
            - User: "Change the time to 10am" (after a previous scheduling attempt)\n\
              -> Call get_schedule_details with the previous details but update time="10:00"\n\
            - User: "Use Dr. Smith instead" (after a previous scheduling attempt)\n\
              -> Call get_schedule_details with the previous details but update surgeon="Dr. Smith"\n\
            - User: "Change surgeon to Dr. Anderson and hospital to MSH" (during confirmation)\n\
              -> Call get_schedule_details with updated surgeon="Dr. Anderson" and hospital="MSH"\n\
            - User: "Update time to 4am" (during reschedule confirmation)\n\
              -> Call get_reschedule_case_details with updated newTime="04:00"\n\
            \n\
            ---\n\
            **Remember - CRITICAL TOOL SELECTION LOGIC:**\n\
            1. **ALWAYS check if the user provides a case ID/number first**\n\
            2. **For CANCELLATION:**\n\
               - Case ID provided? -> Use get_cancel_details_id\n\
               - No case ID? -> Use get_cancel_details_doctorname_date\n\
            3. **For RESCHEDULING:**\n\
               - Case ID provided? -> Use get_reschedule_case_details\n\
               - No case ID? -> Use get_reschedule_details_doctorname_date\n\
            4. **You must always use a tool for any scheduling, cancellation, rescheduling, or uncancellation.**\n\
            5. **If you are unsure which tool to use, ask the user for clarification.**\n\
            6. **When users want to edit details, update the tool arguments accordingly and call the tool again.**\n\
            """
        )
    )

    # Get the response from the model
    response = await model_with_tools.ainvoke([system_message, *state["messages"]], config)

    # Check if user is trying to edit a deferred tool call
    deferred_tool_call = state.get("deferred_tool_call")
    if deferred_tool_call and isinstance(response, AIMessage) and response.tool_calls:
        # User provided edit details, update the deferred tool call with new details
        new_tool_call = response.tool_calls[0]

        # Handle both same tool name and compatible tool names (e.g., editing scheduling details)
        compatible_tools = {
            "get_schedule_details": ["get_schedule_details"],
            "get_cancel_details_id": ["get_cancel_details_id"],
            "get_reschedule_case_details": ["get_reschedule_case_details", "get_schedule_details"],
            "get_uncancel_details": ["get_uncancel_details"]
        }

        deferred_tool_name = deferred_tool_call["name"]
        new_tool_name = new_tool_call["name"]

        if new_tool_name in compatible_tools.get(deferred_tool_name, []):
            print(f"Updating deferred tool call with new details: {new_tool_call['args']}")

            # For compatible but different tool names, use the original tool name
            if new_tool_name != deferred_tool_name:
                print(f"Converting {new_tool_name} to {deferred_tool_name} for consistency")
                new_tool_call["name"] = deferred_tool_name

            # Merge the new args with existing ones (new args override existing)
            updated_args = {**deferred_tool_call.get("args", {}), **new_tool_call.get("args", {})}
            updated_tool_call = {
                "name": deferred_tool_name,
                "args": updated_args
            }

            # Re-validate the updated tool call before confirmation
            tool_name = updated_tool_call["name"]
            args = updated_tool_call.get("args", {})

            # Validation logic for updated details
            if tool_name == "get_schedule_details":
                # Check for mandatory parameters before validation
                mandatory_params = ['surgeon', 'hospital', 'date', 'time']
                missing_params = []

                for param in mandatory_params:
                    if not args.get(param) or str(args.get(param)).strip() == "":
                        missing_params.append(param)

                if missing_params:
                    missing_list = ", ".join(missing_params)
                    return {
                        "messages": [*state["messages"], AIMessage(content=f"I need the following mandatory information to schedule the case: {missing_list}. Please provide these details.")]
                    }

                flag, details = await validate_schedule_details(**args, token=user_token)
                if flag == False:
                    if isinstance(details, list):
                        return {
                            "messages": [],
                            "showHospitals": details,
                            "frontend_tool": "showHospitals",
                            "confirmation_message": "Multiple Hospitals Found with Same Name."
                        }
                    else:
                        return {
                            "messages": [*state["messages"], AIMessage(content=details)]
                        }
                elif flag == "MutliDotorName":
                    if isinstance(details, list):
                        return {
                            "messages": [],
                            "showDoctors": details,
                            "frontend_tool": "showDoctors",
                            "confirmation_message": "Multiple Doctors Found with Same Name."
                        }
                    else:
                        return {
                            "messages": [*state["messages"], AIMessage(content=details)]
                        }
                # Update with validated details
                updated_tool_call["args"] = details

            elif tool_name == "get_reschedule_case_details":
                # Check for mandatory parameters before validation
                mandatory_params = ['caseId', 'newDate']
                missing_params = []

                for param in mandatory_params:
                    if not args.get(param) or str(args.get(param)).strip() == "":
                        missing_params.append(param)

                if missing_params:
                    missing_list = ", ".join(missing_params)
                    return {
                        "messages": [*state["messages"], AIMessage(content=f"I need the following mandatory information to reschedule the case: {missing_list}. Please provide these details.")]
                    }

                flag, details = await validate_reschedule_details(
                    args.get('caseId'), args.get('newDate'), args.get('newTime'), token=user_token
                )
                if not flag:
                    return {
                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                # Update with validated details
                updated_tool_call["args"] = details

            elif tool_name == "get_cancel_details_id":
                # Check for mandatory parameters before validation
                if not args.get('caseId') or str(args.get('caseId')).strip() == "":
                    return {
                        "messages": [*state["messages"], AIMessage(content="I need the case ID to cancel the case. Please provide the case ID.")]
                    }

                flag, details = await validate_cancel_details(args.get('caseId'), token=user_token)
                if not flag:
                    return {
                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                # Update with validated details
                updated_tool_call["args"] = details

            elif tool_name == "get_uncancel_details":
                # Check for mandatory parameters before validation
                if not args.get('caseId') or str(args.get('caseId')).strip() == "":
                    return {
                        "messages": [*state["messages"], AIMessage(content="I need the case ID to uncancel the case. Please provide the case ID.")]
                    }

                flag, details = await validate_uncancel_details(args.get('caseId'), token=user_token)
                if not flag:
                    return {
                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                # Update with validated details
                updated_tool_call["args"] = details

            # Generate new confirmation message with updated and validated details
            confirmation_message = await generate_confirmation_message(updated_tool_call, user_token)
            return {
                "messages": [],
                "confirmation_needed": True,
                "deferred_tool_call": updated_tool_call,
                "awaiting_user_response": True,
                "frontend_tool": "confirmation",
                "confirmation_message": confirmation_message
            }

    # Handle case where user is editing but LLM didn't generate a tool call
    # This can happen when user provides partial updates like "change time to 4am"
    elif deferred_tool_call and not (isinstance(response, AIMessage) and response.tool_calls):
        print("User might be editing details but no tool call generated. Asking for clarification.")
        return {
            "messages": [*state["messages"], AIMessage(content="I understand you want to make changes. Could you please provide the complete updated details? For example: 'Change the surgeon to Dr. Smith and time to 10:00 AM'")]
        }

    # Handle tool calls
    if isinstance(response, AIMessage) and response.tool_calls:
        print('RESPONSE MAIN --> ', response.usage_metadata['total_tokens'])
        tokens_used = response.usage_metadata.get('total_tokens', 0)
        thread_id = config["metadata"].get("thread_id", "unknown")
        username = get_username_for_thread(thread_id)

        print('👾'*100)        
        print('USERNAME', username)
        print('THREAD ID', thread_id)
        print('👾'*100)

        if username != None:
            add_token_usage_for_user(username, tokens_used)
        tool_call = response.tool_calls[0]
        print(f"Model selected tool: {tool_call['name']}")

        # For critical tools, initiate confirmation workflow
        if is_critical_tool_call(tool_call):
            args = tool_call.get("args", {})
            tool_name = tool_call["name"]

            # Validation and confirmation logic
            if tool_name == "get_schedule_details":
                # Check for mandatory parameters before validation
                mandatory_params = ['surgeon', 'hospital', 'date', 'time']
                missing_params = []

                for param in mandatory_params:
                    if not args.get(param) or str(args.get(param)).strip() == "":
                        missing_params.append(param)

                if missing_params:
                    missing_list = ", ".join(missing_params)
                    return {
                        "messages": [*state["messages"], AIMessage(content=f"I need the following mandatory information to schedule the case: {missing_list}. Please provide these details.")]
                    }

                flag, details = await validate_schedule_details(**args, token=user_token)
                if flag == False:
                    if isinstance(details, list):
                        return {

                            "messages": [],
                            "showHospitals": details,
                            "frontend_tool": "showHospitals",
                            "confirmation_message": "Multiple Hospitals Found with Same Name."

                        }

                    else:
                        return {
                            "messages": [*state["messages"], AIMessage(content=details)]
                        }
                
                elif flag == "MutliDotorName":
                    if isinstance(details, list):
                        return {

                            "messages": [],
                            "showDoctors": details,
                            "frontend_tool": "showDoctors",
                            "confirmation_message": "Multiple Doctors Found with Same Name."

                        }

                    else:
                        return {
                            "messages": [*state["messages"], AIMessage(content=details)]
                        }
                    

                tool_call["args"] = details
                confirmation_message = await generate_confirmation_message(tool_call, user_token)
                return {

                    "messages": [],
                    "confirmation_needed": True,
                    "showHospitals": [],
                    "showDoctors": [],
                    "deferred_tool_call": tool_call,
                    "frontend_tool": "confirmation",
                    "awaiting_user_response": True,
                    "confirmation_message": confirmation_message
                }

            elif tool_name == "get_cancel_details_id":
                # Check for mandatory parameters before validation
                if not args.get('caseId') or str(args.get('caseId')).strip() == "":
                    return {
                        "messages": [*state["messages"], AIMessage(content="I need the case ID to cancel the case. Please provide the case ID.")]
                    }

                flag, details = await validate_cancel_details(args.get('caseId'), token=user_token)
                if not flag:
                    return {

                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                tool_call["args"] = details

                confirmation_message = await generate_confirmation_message(tool_call, user_token)

                return {

                    "messages": [],
                    "confirmation_needed": True,
                    "deferred_tool_call": tool_call,
                    "frontend_tool": "confirmation",
                    "awaiting_user_response": True,
                    "confirmation_message": confirmation_message
                }

            elif tool_name in ["get_cancel_details_doctorname_date", 'get_reschedule_details_doctorname_date']:
                print('ARGS 2', args)

                # Check for mandatory parameters before validation
                mandatory_params = ['surgeon', 'hospital', 'date']
                missing_params = []

                for param in mandatory_params:
                    if not args.get(param) or str(args.get(param)).strip() == "":
                        missing_params.append(param)

                if missing_params:
                    missing_list = ", ".join(missing_params)
                    action = "cancel" if tool_name == "get_cancel_details_doctorname_date" else "reschedule"
                    return {
                        "messages": [*state["messages"], AIMessage(content=f"I need the following mandatory information to {action} the case: {missing_list}. Please provide these details.")]
                    }

                flag, details = await validate_cancel_reschedule_doctor_details(
                    args.get('surgeon'), args.get('hospital'), args.get('date'),args.get('city') , args.get('hospitalId'),  args.get('doctorId'), token=user_token
                )

                if flag == False:
                    # Multiple cases found - show them for selection

                    return {
                        "messages": [AIMessage(content=details)],
                        "confirmation_needed": False,
                        "deferred_tool_call": None,
                        "awaiting_user_response": False,
                        "showCase": []

                    }
                elif flag == "MutliDotorName":
                    if isinstance(details, list):
                        return {

                            "messages": [],
                            "showDoctors": details,
                            "frontend_tool": "showDoctors",
                            "confirmation_message": "Multiple Doctors Found with Same Name."

                        }

                    else:
                        return {
                            "messages": [*state["messages"], AIMessage(content=details)]
                        }
                elif flag == "MutliHospitalName":
                    if isinstance(details, list):
                        return {

                            "messages": [],
                            "showHospitals": details,
                            "frontend_tool": "showHospitals",
                            "confirmation_message": "Multiple Hospitals Found with Same Name."

                        }

                    else:
                        return {
                            "messages": [*state["messages"], AIMessage(content=details)]
                        }
                else :
                    # Single case found - proceed to confirmation
                    tool_call["args"] = details
                    if tool_name == "get_cancel_details_doctorname_date":
                        confirmation_message = await generate_confirmation_message(tool_call, user_token)
                        return {
                            "messages": [*state["messages"], AIMessage(content=confirmation_message)],
                            "confirmation_needed": True,
                            "deferred_tool_call": {"name": "get_cancel_details_id", "args": details},
                            "awaiting_user_response": True,
                            "showCase": details,
                            "frontend_tool": "showCase",

                            "confirmation_message": confirmation_message

                        }
                    else:
                        confirmation_message = await generate_confirmation_message(tool_call, user_token)
                        print('get_reschedule_case_details',details)
                        return {
                            "messages": [*state["messages"], AIMessage(content=confirmation_message)],
                            "confirmation_needed": True,
                            "deferred_tool_call": {"name": "get_reschedule_case_details", "args": details},
                            "awaiting_user_response": True,
                            "showCase": details,
                            "frontend_tool": "showCase",
                            "confirmation_message": confirmation_message

                        }
            elif tool_name == 'get_reschedule_case_details':
                # Check for mandatory parameters before validation
                mandatory_params = ['caseId', 'newDate']
                missing_params = []

                for param in mandatory_params:
                    if not args.get(param) or str(args.get(param)).strip() == "":
                        missing_params.append(param)

                if missing_params:
                    missing_list = ", ".join(missing_params)
                    return {
                        "messages": [*state["messages"], AIMessage(content=f"I need the following mandatory information to reschedule the case: {missing_list}. Please provide these details.")]
                    }

                flag, details = await validate_reschedule_details(
                    args.get('caseId'), args.get('newDate'), args.get('newTime'), token=user_token
                )
                # tool_call["args"]['newTime'] = details['newTime']
                tool_call.update({"args": details})
                # print('tool call',tool_call)
                print('VALIDATE RESCHEDULE DETAILS', flag, details)
                if not flag:
                    return {

                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                
                
                confirmation_message = await generate_confirmation_message(tool_call, user_token)
                return {

                    "messages": [],
                    "confirmation_needed": True,
                    "deferred_tool_call": tool_call,
                    "awaiting_user_response": True,
                    "frontend_tool": "confirmation",
                    "confirmation_message": confirmation_message
                }

            elif tool_name == "get_uncancel_details":
                # Check for mandatory parameters before validation
                if not args.get('caseId') or str(args.get('caseId')).strip() == "":
                    return {
                        "messages": [*state["messages"], AIMessage(content="I need the case ID to uncancel the case. Please provide the case ID.")]
                    }

                flag, details = await validate_uncancel_details(args.get('caseId'), token=user_token)
                if not flag:
                    return {

                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                tool_call["args"] = details
                confirmation_message = await generate_confirmation_message(tool_call, user_token)
                return {

                    "messages": [],
                    "confirmation_needed": True,
                    "deferred_tool_call": tool_call,
                    "awaiting_user_response": True,
                    "frontend_tool": "confirmation",
                    "confirmation_message": confirmation_message
                }
        else:
            # For non-critical tools, proceed normally
            return {

                "messages": [*state["messages"], response]
            }

    # If no tool call, just return the model's response
    # print('RESPONSE --> ', response.usage_metadata['total_tokens'])

    tokens_used = response.usage_metadata.get('total_tokens', 0)
    thread_id = config["metadata"].get("thread_id", "unknown")
    username = get_username_for_thread(thread_id)
    if username:
        add_token_usage_for_user(username, tokens_used)
    cleaned = re.sub(r'<think>.*?</think>\s*', '',
                     response.content, flags=re.DOTALL)
    print('CLEANED RESPONSE --> ', cleaned)

    result = {
        "messages": [*state["messages"], AIMessage(content=cleaned)],
    }
    print(f"🔍 CHAT_NODE RETURNING: {result}")
    return result


async def generate_confirmation_message(tool_call: dict, user_token) -> str:

    print("\n==============================\n🟦 Entering FUNCTION: generate_confirmation_message\n==============================\n")
    """Generate a user-friendly confirmation message for a given tool call."""
    args = tool_call.get("args", {})
    print('ARGS', args)
    tool_name = tool_call["name"]

    # Generate confirmation messages based on tool type
    prompts = {
        "get_cancel_details_doctorname_date": "Multiple cases found. Please select which case to cancel.",
        "get_reschedule_details_doctorname_date": "Multiple cases found. Please select which case to reschedule."
    }
    if tool_name == 'get_schedule_details':
        date = parse_time_string(args.get('date', 'N/A'),user_token)
        args.update({'date': date})

        details = {
                        'Hospital Name': args.get('hospital', 'N/A'),
                        'Surgeon Name': args.get('surgeon', 'N/A'),
                        'Procedure': args.get('procedure', 'N/A'),
                        'Scheduled on': date + " at " + args.get('time', 'N/A'),                       
                        'Patient Name': args.get('patientName', 'N/A'),
                        
                    }
        details_text = "\n".join([
            f"- **{key}**: {val}"
            for key, val in details.items()
            if val and str(val).strip().upper() != 'N/A'
        ])
        return (
            "Are you sure you want to Schedule a case with following details:\n\n\n"
            "📋 **Case Details**:\n"
            f"{details_text}"
        )

    if tool_name in prompts:
        return prompts[tool_name]

    if tool_name in ["get_cancel_details_id", "get_reschedule_case_details", "get_uncancel_details"]:
        case_id = args.get("caseId")
        try:
            async with ZinniaXAPI() as api:
                case_details = await api.fetch_case_details(case_id, token=user_token)
            if not case_details:
                return f"I couldn't find details for case #{case_id}. Are you sure you want to proceed?"

            if tool_name == "get_cancel_details_id":
                print('CASE DETAILS', case_details)
                details = {
                'caseID': case_id,
                'Hospital Name': case_details.get('hospital', 'N/A'),
                'Surgeon Name': case_details.get('surgeonName', 'N/A'),
                'Procedure': case_details.get('procedure', 'N/A'),
                'Date-Time': case_details.get('dateOfSurgeryHospitalTime', 'N/A'),
                'Patient Name': case_details.get('patientName', 'N/A'),
            }
                details_text = "\n".join([
                    f"- **{key}**: {val}"
                    for key, val in details.items()
                    if val and str(val).strip().upper() != 'N/A'
                ])
                # print('DETAILS TEXT', details_text)
                return (
                    "Are you sure you want to cancel a case with following details:\n\n\n"
                    "📋 **Case Details**:\n"
                    f"{details_text}"
                )

            elif tool_name == "get_reschedule_case_details":
                print('RESCHEDULE ARGS: ', args)
                if 'newTime' not in args.keys():
                    args['newTime']  = case_details.get('dateOfSurgeryHospitalTime', 'N/A').split(',')[1].strip()

                details = {
                'caseID': case_id,
                'Hospital Name': case_details.get('hospital', 'N/A'),
                'Surgeon Name': case_details.get('surgeonName', 'N/A'),
                'Procedure': case_details.get('procedure', 'N/A'),
                'Scheduled On': args.get('newDate' , 'N/A') + " at " + args.get('newTime', 'N/A'),               
                'Patient Name': case_details.get('patientName', 'N/A')
                
            }
                details_text = "\n".join([
                    f"- **{key}**: {val}"
                    for key, val in details.items()
                    if val and str(val).strip().upper() != 'N/A'
                ])

                print('DETAILS TEXT', details)
                return (
                    "Are you sure you want to Reschedule a case with following details:\n\n\n"
                    "📋 **Case Details**:\n"
                    f"{details_text}"
                )

            elif tool_name == "get_uncancel_details":
                details = {
                'caseID': case_id,
                'Hospital Name': case_details.get('hospital', 'N/A'),
                'Surgeon Name': case_details.get('surgeonName', 'N/A'),
                'Procedure': case_details.get('procedure', 'N/A'),
                'Date-Time': case_details.get('dateOfSurgeryHospitalTime', 'N/A'),
                'Patient Name': case_details.get('patientName', 'N/A')
                
            }
                details_text = "\n".join([
                    f"- **{key}**: {val}"
                    for key, val in details.items()
                    if val and str(val).strip().upper() != 'N/A'
                ])

                # print('DETAILS TEXT', details_text)
                return (
                    "Are you sure you want to Reopen a case with following details:\n\n\n"
                    "📋 **Case Details**:\n"
                    f"{details_text}"
                )
        except Exception as e:
            logger.error(f"Error fetching case details for confirmation: {e}")
            return "I had trouble retrieving case details. Are you sure you want to proceed?"

    return "Sorry,can you please confirm the action you want to take? I need more details to proceed."


async def confirmation_node(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    print("\n==============================\n🟨 Entering NODE: confirmation_node\n==============================\n")
    print("Entering confirmation_node")
    # print('state', state)
    username, user_token = await get_user_token_from_config(config)
    last_message = state["messages"][-1]
    if not isinstance(last_message, HumanMessage):
        return {
            **dict(state),
            "messages": [*state["messages"], AIMessage(content="I'm waiting for your confirmation. Please respond with 'yes' or 'no'.")],
            "confirmation_needed": True,
            "awaiting_user_response": True,
        }

    user_response = last_message.content.lower().strip()
    print(f"User confirmation response: '{user_response}'")

    # Handle case selection
    if state.get("showCase") and isinstance(state["showCase"], list):
        print("Handling case selection from multiple options")

        try:
            selected_case_id = last_message.content.strip()
            deferred_tool_call = state.get("deferred_tool_call", {})
            tool_name = deferred_tool_call.get("name", "")

            if tool_name == 'get_cancel_details_id':
                tool_call = {
                    "name": "get_cancel_details_id",
                    "args": {"caseId": selected_case_id}
                }
                flag, details = await validate_cancel_details(selected_case_id, token=user_token)
                if flag:
                    tool_call["args"] = details
                    confirmation_message = await generate_confirmation_message(tool_call, user_token=user_token)
                    return {
                        **dict(state),
                        "messages": [],
                        "confirmation_needed": True,
                        "deferred_tool_call": tool_call,
                        "awaiting_user_response": True,
                        "showCase": None,
                        "frontend_tool": "confirmation",
                        "confirmation_message": confirmation_message
                    }
                else:
                    return {

                        "messages": [*state["messages"], AIMessage(content=details)]
                    }

            elif tool_name == 'get_reschedule_case_details':
                return {
                    **dict(state),
                    "messages": [*state["messages"], AIMessage(content="Please provide the new date and time for rescheduling (format: YYYY-MM-DD HH:MM):")],
                    "confirmation_needed": False,
                    "deferred_tool_call": {"name": "get_reschedule_case_details", "args": {"caseId": selected_case_id}},
                    "awaiting_user_response": True,
                    "showCase": None,

                }
            else:
                return {

                    "messages": [*state["messages"], AIMessage(content="Invalid action. Please try again.")]
                }

        except Exception as e:
            logger.error(f"Error handling case selection: {e}")
            return {
                **dict(state),
                "messages": [*state["messages"], AIMessage(content="Invalid selection. Please enter a valid case ID from the list.")],
                "showCase": state["showCase"]
            }

    # Handle positive confirmation
    if any(word in user_response for word in ["yes", "confirm"]):
        print("User confirmed. Executing deferred tool call.")
        deferred_tool_call = state.get("deferred_tool_call")
        if not deferred_tool_call:
            return {

                "messages": [*state["messages"], AIMessage(content="My apologies, there was no action to confirm. Let's try again.")]
            }

        try:
            result_message = await execute_tool_call(deferred_tool_call, user_token)
            return {
                "messages": [*state["messages"], AIMessage(content=result_message)],
                "deferred_tool_call": None,
                "awaiting_user_response": False,
                "confirmation_needed": False,
                "frontend_tool": None,  # Clear frontend_tool to ensure proper routing to END
            }
        except Exception as e:
            logger.error(f"Error executing confirmed tool call: {e}")
            return {

                "messages": [*state["messages"], AIMessage(content=f"I encountered an error while performing the action: {e}")]
            }

    # Handle negative confirmation
    elif any(word in user_response for word in ["no", "stop", "cancel"]):
        print("User declined. Cancelling operation.")
        return {

            "messages": [*state["messages"], AIMessage(content="Okay, Do you want to edit the details or start over?")],
            "deferred_tool_call": None,
            "awaiting_user_response": False,
            "confirmation_needed": False
        }

    # Unclear response - user might be trying to edit details
    else:
        print("User response unclear. Routing to chat_node for potential editing.")
        return {
        **dict(state),
        "messages": [*state["messages"]],  # Keep the user's message for chat_node to process
        "confirmation_needed": False,
        # Keep deferred_tool_call so chat_node can update it
        "awaiting_user_response": False,  # This will route to chat_node
        "showCase": None,
        "frontend_tool": "confirmation",
        }


async def execute_tool_call(tool_call, user_token):
    """Execute a tool call and return the result."""
    async with ZinniaXAPI() as api:
        tool_name = tool_call["name"]
        args = tool_call.get("args", {})

        if tool_name == "get_schedule_details":
            details_data = {
                'hospital': args['hospital'],
                'surgeon': args['surgeon'],
                'procedure': args.get('procedure', ''),
                'date': args['date'],
                'time': args['time'],
                'patientName': args.get('patientName', ''),
                'city': args.get('city', ''),
                'hospitalId': args.get('hospitalId', ''),
                'doctorId' : args.get('doctorId', '')
            }
            print("Detailsaa", details_data)
            success, result, caseId = await api.schedule_new_case(details_data, user_token)
            if success:
                case_link = f"https://stage.ionm.zinniax.com/case/{caseId}/summary"
                details_data.pop('hospitalId')
                details_data.pop('doctorId')

                # Create clean formatted details for display (like confirmation message format)
                display_details = {
                    'caseID': result,
                    'Hospital Name': args['hospital'],
                    'Surgeon Name': args['surgeon'],
                    'Scheduled On': f"{args['date']} at {args['time']}"
                }

                # Add optional fields only if they have values
                if args.get('procedure') and args.get('procedure').strip():
                    display_details['Procedure'] = args['procedure']
                if args.get('patientName') and args.get('patientName').strip():
                    display_details['Patient Name'] = args['patientName']
                if args.get('city') and args.get('city').strip():
                    display_details['City'] = args['city']

                details_text = "\n".join([
                    f"- **{key}**: {val}"
                    for key, val in display_details.items()
                    if val and str(val).strip()
                ])
                
                return (f"Successfully scheduled new case with ID: "
                        f"[{result}]({case_link}).\n\n"
                        f"📋 **Case Details**:\n{details_text}"
                        )
            else:
                return f"Failed to schedule case: {result}"


        elif tool_name == "get_cancel_details_id":
            print("FINALLY CALLING CANCELLATION FUNCTION")
            print(args)
            details = {
                'hospital': args['hospital'],
                'surgeon': args['surgeonName'],
                'procedure': args.get('procedure', ''),
                'date and time': args['dateOfSurgeryHospitalTime'],
                'patientName': args.get('patientName', ''),
                'hospitalId': args.get('hospitalId', ''),
                'doctorId' : args.get('doctorId', '')
            }
            caseNo = args['caseId']
            success, result = await api.cancel_case(caseNo, user_token)
            if success:
                details_text = "\n".join(
                    [f"- {key}: {val}" for key, val in details.items()])

                return (f"Successfully Cancelled the case with ID: "
                        f"{caseNo}.\n\n"
                        )
            else:
                return f"{result}"

        elif tool_name == "get_reschedule_case_details":
            print("FINALLY CALLING RESCHEDULE FUNCTION")
            print(args)
            case_id = args['caseId']
            new_date = args['newDate']
            new_time = args['newTime']
            details = {
                'caseId': case_id,
                'newDate': new_date,
                'newTime': new_time
            }
            print(case_id, new_date, new_time)
            success, result = await api.reschedule_case(case_id, new_date, new_time, user_token)
            if success:
                return f"Successfully Rescheduled case with ID: {case_id} to {new_date} at {new_time}."
            else:
                return f"{result}"

        elif tool_name == "get_uncancel_details":
            caseNo = args['caseId']
            details = {
                'caseId': caseNo,
                'hospital': args['hospital'],
                'surgeon': args['surgeonName'],
                'procedure': args.get('procedure', ''),
                'date and time': args['dateOfSurgeryHospitalTime'],
                'patientName': args.get('patientName', ''),
            }
            success, result = await api.uncancel_case(caseNo, user_token)

            if success:
                details = "\n".join(
                    [f"- {key}: {val}" for key, val in details.items()])
                return (f"Successfully Un-cancelled the case with ID: "
                        f"{caseNo}.\n\n")
            else:
                return f"Failed to Reopen case: {result}"

        else:
            return f"Unknown tool: {tool_name}"

    return "I'm sorry, I don't know how to handle that action. Please contact support."


def should_continue(state: AgentState) -> Literal["confirmation_node", "tool_node", "__end__"]:
    print("\n==============================\n🟧 Entering ROUTER: should_continue\n==============================\n")
    print("Executing should_continue router")
    # print("router should continue, ", state)
    # If awaiting user response, end the turn
    if state.get("awaiting_user_response", False):
        print("Awaiting user response - ending turn")
        return "__end__"

    # Check if the last message has tool calls
    last_message = state["messages"][-1] if state["messages"] else None
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        # If it's a critical tool call, we should have already handled it in chat_node
        # So if we get here with tool calls, they should be non-critical
        print("Non-critical tool call detected - routing to tool_node")
        return "tool_node"

    # Otherwise, end the turn
    print("No tool calls or awaiting response - ending turn")
    return "__end__"


def route_entry(state: AgentState) -> Literal["chat_node", "confirmation_node"]:
    print("\n==============================\n🟪 Entering ROUTER: route_entry\n==============================\n")
    print('royter_state', state)
    # print("router states, ", state)
    print("Executing route_entry")

    if state.get("awaiting_user_response", False):
        print("Awaiting user response - routing to confirmation_node")
        return "confirmation_node"

    print("New request - routing to chat_node")
    return "chat_node"


# --- Build the workflow graph ---
workflow = StateGraph(AgentState)

# Add nodes
workflow.add_node("chat_node", chat_node,cache_policy=CachePolicy(ttl=60))
workflow.add_node("tool_node", ToolNode(tools=tools))
workflow.add_node("confirmation_node", confirmation_node)

# Set up routing
workflow.add_conditional_edges(
    START,
    route_entry,
    {
        "chat_node": "chat_node",
        "confirmation_node": "confirmation_node",
    }
)

workflow.add_conditional_edges(
    "chat_node",
    should_continue,
    {
        "confirmation_node": "confirmation_node",
        "tool_node": "tool_node",
        "__end__": END,
    }
)

def route_confirmation_node(state):
    """Route confirmation_node based on state"""
    print(f"🔀 ROUTING CONFIRMATION_NODE:")
    print(f"   frontend_tool: {state.get('frontend_tool')}")
    print(f"   confirmation_needed: {state.get('confirmation_needed')}")
    print(f"   awaiting_user_response: {state.get('awaiting_user_response')}")
    print(f"   deferred_tool_call: {state.get('deferred_tool_call')}")

    # If user wants to edit details (unclear response), route to chat_node
    if state.get("frontend_tool") == "confirmation" and not state.get("confirmation_needed", False):
        print("   → Routing to chat_node for editing details")
        return "chat_node"
    # Otherwise, end the conversation (including after successful tool execution)
    else:
        print("   → Ending conversation from confirmation_node")
        return "__end__"

workflow.add_conditional_edges(
    "confirmation_node",
    route_confirmation_node,
    {
        "chat_node": "chat_node",
        "__end__": END,
    }
)
workflow.add_edge("tool_node", "chat_node")

# Compile the graph
is_langgraph_api = (
    os.environ.get("LANGGRAPH_API", "false").lower() == "true" or
    os.environ.get("LANGGRAPH_API_DIR") is not None
)

if is_langgraph_api:
    graph = workflow.compile()
else:
    memory = MemorySaver()
    graph = workflow.compile(checkpointer=memory,interrupt_after=['tool_node'],cache=InMemoryCache())

  
async def run_chatbot():
    print("\n==============================\n🤖 Starting FUNCTION: run_chatbot\n==============================\n")
    """Run an interactive chatbot in the terminal."""
    print("🏥 Welcome to the Scheduling Assistant!")
    print("I can help you schedule, cancel, or reschedule medical cases.")
    print("Type 'exit' to quit.\n")

    config = {"configurable": {"thread_id": "main_conversation"}}

    # Initialize state properly
    current_state = {
        "messages": [],
        "copilotkit": {"actions": []},
        "confirmation_needed": False,
        "deferred_tool_call": None,
        "awaiting_user_response": False,
        "showCase": None,
    }

    while True:
        try:
            user_input = input("You: ").strip()
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print("👋 Goodbye! Have a great day!")
                break
            if not user_input:
                continue

            # Add user message to state
            current_state["messages"].append(HumanMessage(content=user_input))

            # Run the workflow
            result = await graph.ainvoke(current_state, config)

            if result:
                # Preserve copilotkit context
                if not result.get("copilotkit"):
                    result["copilotkit"] = {"actions": []}

                current_state = result

                print(
                    f"State after workflow: confirmation_needed={result.get('confirmation_needed')}, awaiting_user_response={result.get('awaiting_user_response')}")

            # Display the bot's response
            if result and result.get("messages"):
                last_ai_message = next(
                    (msg for msg in reversed(result["messages"])
                     if isinstance(msg, AIMessage) and msg.content),
                    None
                )
                if last_ai_message:
                    print(f"🤖 Bot: {last_ai_message.content}")

        except KeyboardInterrupt:
            print("\n👋 Goodbye! Have a great day!")
            break
        except Exception as e:
            logger.error(f"Error in chatbot loop: {e}", exc_info=True)
            print(
                f"🚨 Sorry, I encountered an error: {str(e)}. Let's start over.")
            current_state = {
                "messages": [],
                "copilotkit": {"actions": []},
                "confirmation_needed": False,
                "deferred_tool_call": None,
                "awaiting_user_response": False,
                "showCase": None,
            }
            continue


# FastAPI setup
import uvicorn
from fastapi import FastAPI,Request, Response, Cookie
from copilotkit import CopilotKitRemoteEndpoint, LangGraphAgent
from copilotkit.integrations.fastapi import add_fastapi_endpoint
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from typing import Annotated, Dict, Any, Literal, Optional

app = FastAPI()


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For production, restrict this!
    # allow_origins=["*"],  # For production, restrict this!
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory session-token store (use Redis/DB for production)
session_tokens = {}




import os
import json
sdk = CopilotKitRemoteEndpoint(
    agents=[
        LangGraphAgent(
            name="scheduling_agent",
            description="A professional assistant for scheduling, canceling, and managing medical cases.",
            graph=graph,
            config=session_tokens,


        )
    ],
)

add_fastapi_endpoint(app, sdk, "/api/copilotkit/", use_thread_pool=False)


# Add middleware to log requests
@app.middleware("http")
async def normalize_url(request: Request, call_next):
    # Normalize CopilotKit URLs
    if request.url.path == "/api/copilotkit":
        request.scope["path"] = "/api/copilotkit/"
    elif request.url.path == "/api/copilotkit/":
        request.scope["path"] = "/api/copilotkit"
    
    response = await call_next(request)
    return response

@app.middleware("http")
async def debug_routes(request: Request, call_next):
    print(f"🛣️ Route called: {request.method} {request.url.path}")
    response = await call_next(request)
    print(f"🛣️ Route response: {response.status_code}")
    return response
@app.get("/api/health")
def health():
    print("\n==============================\n🟩 Entering ENDPOINT: health\n==============================\n")
    """Health check endpoint."""
    return {"status": "okkkkkkkk"}

@app.get("/api/test")
def health():
    print("\n==============================\n🟩 Entering ENDPOINT: health\n==============================\n")
    """Health check endpoint."""
    return {"status": "okkkkkkk"}

@app.post("/api/store-token")
async def store_token(request: Request, response: Response):
    print("\n==============================\n🟩 Entering ENDPOINT: store_token\n==============================\n")
    data = await request.json()
    session_id = data.get("sessionId")
    token = data.get("token")
    
    if not token or not session_id:
        return {"status": "error", "message": "No token or session_id provided"}

    # Store token against the session_id
    print(f"Storing token for session_id: {session_id}")
    print(f"Storing token : {token}")
    session_tokens.clear()
    session_tokens[session_id] = token
    # bind_thread_to_user(session_id, token)
    return {"status": "success", "session_id": session_id}


USER_USAGE_FILE = "user_token_usage.json"
@app.get("/api/user-token-usage")
def get_user_token_usage():
    print("\n==============================\n🟩 Entering ENDPOINT: get_user_token_usage\n==============================\n")
    file_path = "user_token_usage.json"
    if not os.path.exists(file_path):
        return {"status": "error", "message": "user_token_usage.json not found"}
    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        return data
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/api/tokens")
def get_tokens():
    print("\n==============================\n🟩 Entering ENDPOINT: get_tokens\n==============================\n")
    file_path = "tokens.json"
    if not os.path.exists(file_path):
        return {"status": "error", "message": "tokens.json not found"}
    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        return data
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/api/thread-to-user")
def get_thread_to_user():
    print("\n==============================\n🟩 Entering ENDPOINT: get_thread_to_user\n==============================\n")
    file_path = "thread_to_user.json"
    if not os.path.exists(file_path):
        return {"status": "error", "message": "thread_to_user.json not found"}
    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        return data
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.post("/api/clean-files")
def clean_files():
    print("\n==============================\n🟩 Entering ENDPOINT: clean_files\n==============================\n")
    files = [
        ("thread_to_user.json", "thread_to_user"),
        ("user_token_usage.json", "user_token_usage"),
        ("tokens.json", "tokens")
    ]
    results = {}
    for file_path, key in files:
        try:
            if os.path.exists(file_path):
                with open(file_path, "w") as f:
                    json.dump({}, f, indent=2)
                results[key] = "cleared"
            else:
                results[key] = "file not found"
        except Exception as e:
            results[key] = f"error: {str(e)}"
    return {"status": "success", "results": results}


def main():
    """Run the uvicorn server."""
    port = int(os.getenv("PORT", "8001"))

    # Check if SSL certificates are available
    # ssl_keyfile = '../privkey.key'
    # ssl_certfile = '../cert.crt'

    uvicorn_config = {
        "app": "app:app",
        "host": "0.0.0.0",
        "port": port,
        "reload": True,
        "forwarded_allow_ips": '*',
        "proxy_headers": True,
        
    }

    # Add SSL configuration if certificates are provided
    # if ssl_keyfile and ssl_certfile:
    #     uvicorn_config.update({
    #         "ssl_keyfile": ssl_keyfile,
    #         "ssl_certfile": ssl_certfile
    #     })

    uvicorn.run(**uvicorn_config)


if __name__ == "__main__":

    main()
    # asyncio.run(run_chatbot())
