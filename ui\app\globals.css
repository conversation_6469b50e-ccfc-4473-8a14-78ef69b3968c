@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add this to your global CSS or component styles */

body {
  font-family: 'Inter', sans-serif;
}

.copilot-chat {
  height: 100vh !important;
  max-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
}

.copilot-chat-messages {
  flex: 1 !important;
  overflow-y: auto !important;
  min-height: 0 !important;
}

.copilot-chat-input {
  flex-shrink: 0 !important;
  position: sticky !important;
  bottom: 0 !important;
  background: white !important;
  z-index: 10 !important;
}

.copilotKitMessage.copilotKitAssistantMessage .copilotKitMessageControls {
  /* position: absolute !important;
    left: 0 !important;
    gap: 1rem !important;
    opacity: 0 !important;
    transition: opacity 0.2s ease !important;
    padding: 5px 0 0 0 !important; */
  display: none !important;
}

/* .copilotKitMessage.copilotKitAssistantMessage:hover .copilotKitMessageControls {
    display: flex !important;
    opacity: 1 !important;
  } */

.poweredBy {
  color: white !important;
}

.poweredByCover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 2;
}

.copilotKitMarkdown {
  background: #ffffff;
  width: auto;
  height: fit-content;
  border-radius: 20px;
  padding: 10px;
  color: rgb(27, 27, 27);
}