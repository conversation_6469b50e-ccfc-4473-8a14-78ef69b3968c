"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/https-proxy-agent";
exports.ids = ["vendor-chunks/https-proxy-agent"];
exports.modules = {

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/agent.js":
/*!******************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/agent.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst net_1 = __importDefault(__webpack_require__(/*! net */ \"net\"));\nconst tls_1 = __importDefault(__webpack_require__(/*! tls */ \"tls\"));\nconst url_1 = __importDefault(__webpack_require__(/*! url */ \"url\"));\nconst assert_1 = __importDefault(__webpack_require__(/*! assert */ \"assert\"));\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst agent_base_1 = __webpack_require__(/*! agent-base */ \"(rsc)/./node_modules/agent-base/dist/src/index.js\");\nconst parse_proxy_response_1 = __importDefault(__webpack_require__(/*! ./parse-proxy-response */ \"(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\"));\nconst debug = debug_1.default('https-proxy-agent:agent');\n/**\n * The `HttpsProxyAgent` implements an HTTP Agent subclass that connects to\n * the specified \"HTTP(s) proxy server\" in order to proxy HTTPS requests.\n *\n * Outgoing HTTP requests are first tunneled through the proxy server using the\n * `CONNECT` HTTP request method to establish a connection to the proxy server,\n * and then the proxy server connects to the destination target and issues the\n * HTTP request from the proxy server.\n *\n * `https:` requests have their socket connection upgraded to TLS once\n * the connection to the proxy server has been established.\n *\n * @api public\n */\nclass HttpsProxyAgent extends agent_base_1.Agent {\n    constructor(_opts) {\n        let opts;\n        if (typeof _opts === 'string') {\n            opts = url_1.default.parse(_opts);\n        }\n        else {\n            opts = _opts;\n        }\n        if (!opts) {\n            throw new Error('an HTTP(S) proxy server `host` and `port` must be specified!');\n        }\n        debug('creating new HttpsProxyAgent instance: %o', opts);\n        super(opts);\n        const proxy = Object.assign({}, opts);\n        // If `true`, then connect to the proxy server over TLS.\n        // Defaults to `false`.\n        this.secureProxy = opts.secureProxy || isHTTPS(proxy.protocol);\n        // Prefer `hostname` over `host`, and set the `port` if needed.\n        proxy.host = proxy.hostname || proxy.host;\n        if (typeof proxy.port === 'string') {\n            proxy.port = parseInt(proxy.port, 10);\n        }\n        if (!proxy.port && proxy.host) {\n            proxy.port = this.secureProxy ? 443 : 80;\n        }\n        // ALPN is supported by Node.js >= v5.\n        // attempt to negotiate http/1.1 for proxy servers that support http/2\n        if (this.secureProxy && !('ALPNProtocols' in proxy)) {\n            proxy.ALPNProtocols = ['http 1.1'];\n        }\n        if (proxy.host && proxy.path) {\n            // If both a `host` and `path` are specified then it's most likely\n            // the result of a `url.parse()` call... we need to remove the\n            // `path` portion so that `net.connect()` doesn't attempt to open\n            // that as a Unix socket file.\n            delete proxy.path;\n            delete proxy.pathname;\n        }\n        this.proxy = proxy;\n    }\n    /**\n     * Called when the node-core HTTP client library is creating a\n     * new HTTP request.\n     *\n     * @api protected\n     */\n    callback(req, opts) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const { proxy, secureProxy } = this;\n            // Create a socket connection to the proxy server.\n            let socket;\n            if (secureProxy) {\n                debug('Creating `tls.Socket`: %o', proxy);\n                socket = tls_1.default.connect(proxy);\n            }\n            else {\n                debug('Creating `net.Socket`: %o', proxy);\n                socket = net_1.default.connect(proxy);\n            }\n            const headers = Object.assign({}, proxy.headers);\n            const hostname = `${opts.host}:${opts.port}`;\n            let payload = `CONNECT ${hostname} HTTP/1.1\\r\\n`;\n            // Inject the `Proxy-Authorization` header if necessary.\n            if (proxy.auth) {\n                headers['Proxy-Authorization'] = `Basic ${Buffer.from(proxy.auth).toString('base64')}`;\n            }\n            // The `Host` header should only include the port\n            // number when it is not the default port.\n            let { host, port, secureEndpoint } = opts;\n            if (!isDefaultPort(port, secureEndpoint)) {\n                host += `:${port}`;\n            }\n            headers.Host = host;\n            headers.Connection = 'close';\n            for (const name of Object.keys(headers)) {\n                payload += `${name}: ${headers[name]}\\r\\n`;\n            }\n            const proxyResponsePromise = parse_proxy_response_1.default(socket);\n            socket.write(`${payload}\\r\\n`);\n            const { statusCode, buffered } = yield proxyResponsePromise;\n            if (statusCode === 200) {\n                req.once('socket', resume);\n                if (opts.secureEndpoint) {\n                    // The proxy is connecting to a TLS server, so upgrade\n                    // this socket connection to a TLS connection.\n                    debug('Upgrading socket connection to TLS');\n                    const servername = opts.servername || opts.host;\n                    return tls_1.default.connect(Object.assign(Object.assign({}, omit(opts, 'host', 'hostname', 'path', 'port')), { socket,\n                        servername }));\n                }\n                return socket;\n            }\n            // Some other status code that's not 200... need to re-play the HTTP\n            // header \"data\" events onto the socket once the HTTP machinery is\n            // attached so that the node core `http` can parse and handle the\n            // error status code.\n            // Close the original socket, and a new \"fake\" socket is returned\n            // instead, so that the proxy doesn't get the HTTP request\n            // written to it (which may contain `Authorization` headers or other\n            // sensitive data).\n            //\n            // See: https://hackerone.com/reports/541502\n            socket.destroy();\n            const fakeSocket = new net_1.default.Socket({ writable: false });\n            fakeSocket.readable = true;\n            // Need to wait for the \"socket\" event to re-play the \"data\" events.\n            req.once('socket', (s) => {\n                debug('replaying proxy buffer for failed request');\n                assert_1.default(s.listenerCount('data') > 0);\n                // Replay the \"buffered\" Buffer onto the fake `socket`, since at\n                // this point the HTTP module machinery has been hooked up for\n                // the user.\n                s.push(buffered);\n                s.push(null);\n            });\n            return fakeSocket;\n        });\n    }\n}\nexports[\"default\"] = HttpsProxyAgent;\nfunction resume(socket) {\n    socket.resume();\n}\nfunction isDefaultPort(port, secure) {\n    return Boolean((!secure && port === 80) || (secure && port === 443));\n}\nfunction isHTTPS(protocol) {\n    return typeof protocol === 'string' ? /^https:?$/i.test(protocol) : false;\n}\nfunction omit(obj, ...keys) {\n    const ret = {};\n    let key;\n    for (key in obj) {\n        if (!keys.includes(key)) {\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n//# sourceMappingURL=agent.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/agent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/index.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nconst agent_1 = __importDefault(__webpack_require__(/*! ./agent */ \"(rsc)/./node_modules/https-proxy-agent/dist/agent.js\"));\nfunction createHttpsProxyAgent(opts) {\n    return new agent_1.default(opts);\n}\n(function (createHttpsProxyAgent) {\n    createHttpsProxyAgent.HttpsProxyAgent = agent_1.default;\n    createHttpsProxyAgent.prototype = agent_1.default.prototype;\n})(createHttpsProxyAgent || (createHttpsProxyAgent = {}));\nmodule.exports = createHttpsProxyAgent;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cHMtcHJveHktYWdlbnQvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsZ0NBQWdDLG1CQUFPLENBQUMscUVBQVM7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxzREFBc0Q7QUFDdkQ7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxodHRwcy1wcm94eS1hZ2VudFxcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5jb25zdCBhZ2VudF8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCIuL2FnZW50XCIpKTtcbmZ1bmN0aW9uIGNyZWF0ZUh0dHBzUHJveHlBZ2VudChvcHRzKSB7XG4gICAgcmV0dXJuIG5ldyBhZ2VudF8xLmRlZmF1bHQob3B0cyk7XG59XG4oZnVuY3Rpb24gKGNyZWF0ZUh0dHBzUHJveHlBZ2VudCkge1xuICAgIGNyZWF0ZUh0dHBzUHJveHlBZ2VudC5IdHRwc1Byb3h5QWdlbnQgPSBhZ2VudF8xLmRlZmF1bHQ7XG4gICAgY3JlYXRlSHR0cHNQcm94eUFnZW50LnByb3RvdHlwZSA9IGFnZW50XzEuZGVmYXVsdC5wcm90b3R5cGU7XG59KShjcmVhdGVIdHRwc1Byb3h5QWdlbnQgfHwgKGNyZWF0ZUh0dHBzUHJveHlBZ2VudCA9IHt9KSk7XG5tb2R1bGUuZXhwb3J0cyA9IGNyZWF0ZUh0dHBzUHJveHlBZ2VudDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js":
/*!*********************************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/parse-proxy-response.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst debug = debug_1.default('https-proxy-agent:parse-proxy-response');\nfunction parseProxyResponse(socket) {\n    return new Promise((resolve, reject) => {\n        // we need to buffer any HTTP traffic that happens with the proxy before we get\n        // the CONNECT response, so that if the response is anything other than an \"200\"\n        // response code, then we can re-play the \"data\" events on the socket once the\n        // HTTP parser is hooked up...\n        let buffersLength = 0;\n        const buffers = [];\n        function read() {\n            const b = socket.read();\n            if (b)\n                ondata(b);\n            else\n                socket.once('readable', read);\n        }\n        function cleanup() {\n            socket.removeListener('end', onend);\n            socket.removeListener('error', onerror);\n            socket.removeListener('close', onclose);\n            socket.removeListener('readable', read);\n        }\n        function onclose(err) {\n            debug('onclose had error %o', err);\n        }\n        function onend() {\n            debug('onend');\n        }\n        function onerror(err) {\n            cleanup();\n            debug('onerror %o', err);\n            reject(err);\n        }\n        function ondata(b) {\n            buffers.push(b);\n            buffersLength += b.length;\n            const buffered = Buffer.concat(buffers, buffersLength);\n            const endOfHeaders = buffered.indexOf('\\r\\n\\r\\n');\n            if (endOfHeaders === -1) {\n                // keep buffering\n                debug('have not received end of HTTP headers yet...');\n                read();\n                return;\n            }\n            const firstLine = buffered.toString('ascii', 0, buffered.indexOf('\\r\\n'));\n            const statusCode = +firstLine.split(' ')[1];\n            debug('got proxy server response: %o', firstLine);\n            resolve({\n                statusCode,\n                buffered\n            });\n        }\n        socket.on('error', onerror);\n        socket.on('close', onclose);\n        socket.on('end', onend);\n        read();\n    });\n}\nexports[\"default\"] = parseProxyResponse;\n//# sourceMappingURL=parse-proxy-response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\n");

/***/ })

};
;