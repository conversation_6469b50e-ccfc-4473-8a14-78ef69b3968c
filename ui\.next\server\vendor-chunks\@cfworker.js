"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@cfworker";
exports.ids = ["vendor-chunks/@cfworker"];
exports.modules = {

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.deepCompareStrict = deepCompareStrict;\nfunction deepCompareStrict(a, b) {\n    const typeofa = typeof a;\n    if (typeofa !== typeof b) {\n        return false;\n    }\n    if (Array.isArray(a)) {\n        if (!Array.isArray(b)) {\n            return false;\n        }\n        const length = a.length;\n        if (length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < length; i++) {\n            if (!deepCompareStrict(a[i], b[i])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (typeofa === 'object') {\n        if (!a || !b) {\n            return a === b;\n        }\n        const aKeys = Object.keys(a);\n        const bKeys = Object.keys(b);\n        const length = aKeys.length;\n        if (length !== bKeys.length) {\n            return false;\n        }\n        for (const k of aKeys) {\n            if (!deepCompareStrict(a[k], b[k])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    return a === b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/dereference.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/dereference.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.initialBaseURI = exports.ignoredKeyword = exports.schemaMapKeyword = exports.schemaArrayKeyword = exports.schemaKeyword = void 0;\nexports.dereference = dereference;\nconst pointer_js_1 = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/pointer.js\");\nexports.schemaKeyword = {\n    additionalItems: true,\n    unevaluatedItems: true,\n    items: true,\n    contains: true,\n    additionalProperties: true,\n    unevaluatedProperties: true,\n    propertyNames: true,\n    not: true,\n    if: true,\n    then: true,\n    else: true\n};\nexports.schemaArrayKeyword = {\n    prefixItems: true,\n    items: true,\n    allOf: true,\n    anyOf: true,\n    oneOf: true\n};\nexports.schemaMapKeyword = {\n    $defs: true,\n    definitions: true,\n    properties: true,\n    patternProperties: true,\n    dependentSchemas: true\n};\nexports.ignoredKeyword = {\n    id: true,\n    $id: true,\n    $ref: true,\n    $schema: true,\n    $anchor: true,\n    $vocabulary: true,\n    $comment: true,\n    default: true,\n    enum: true,\n    const: true,\n    required: true,\n    type: true,\n    maximum: true,\n    minimum: true,\n    exclusiveMaximum: true,\n    exclusiveMinimum: true,\n    multipleOf: true,\n    maxLength: true,\n    minLength: true,\n    pattern: true,\n    format: true,\n    maxItems: true,\n    minItems: true,\n    uniqueItems: true,\n    maxProperties: true,\n    minProperties: true\n};\nexports.initialBaseURI = typeof self !== 'undefined' &&\n    self.location &&\n    self.location.origin !== 'null'\n    ?\n        new URL(self.location.origin + self.location.pathname + location.search)\n    : new URL('https://github.com/cfworker');\nfunction dereference(schema, lookup = Object.create(null), baseURI = exports.initialBaseURI, basePointer = '') {\n    if (schema && typeof schema === 'object' && !Array.isArray(schema)) {\n        const id = schema.$id || schema.id;\n        if (id) {\n            const url = new URL(id, baseURI.href);\n            if (url.hash.length > 1) {\n                lookup[url.href] = schema;\n            }\n            else {\n                url.hash = '';\n                if (basePointer === '') {\n                    baseURI = url;\n                }\n                else {\n                    dereference(schema, lookup, baseURI);\n                }\n            }\n        }\n    }\n    else if (schema !== true && schema !== false) {\n        return lookup;\n    }\n    const schemaURI = baseURI.href + (basePointer ? '#' + basePointer : '');\n    if (lookup[schemaURI] !== undefined) {\n        throw new Error(`Duplicate schema URI \"${schemaURI}\".`);\n    }\n    lookup[schemaURI] = schema;\n    if (schema === true || schema === false) {\n        return lookup;\n    }\n    if (schema.__absolute_uri__ === undefined) {\n        Object.defineProperty(schema, '__absolute_uri__', {\n            enumerable: false,\n            value: schemaURI\n        });\n    }\n    if (schema.$ref && schema.__absolute_ref__ === undefined) {\n        const url = new URL(schema.$ref, baseURI.href);\n        url.hash = url.hash;\n        Object.defineProperty(schema, '__absolute_ref__', {\n            enumerable: false,\n            value: url.href\n        });\n    }\n    if (schema.$recursiveRef && schema.__absolute_recursive_ref__ === undefined) {\n        const url = new URL(schema.$recursiveRef, baseURI.href);\n        url.hash = url.hash;\n        Object.defineProperty(schema, '__absolute_recursive_ref__', {\n            enumerable: false,\n            value: url.href\n        });\n    }\n    if (schema.$anchor) {\n        const url = new URL('#' + schema.$anchor, baseURI.href);\n        lookup[url.href] = schema;\n    }\n    for (let key in schema) {\n        if (exports.ignoredKeyword[key]) {\n            continue;\n        }\n        const keyBase = `${basePointer}/${(0, pointer_js_1.encodePointer)(key)}`;\n        const subSchema = schema[key];\n        if (Array.isArray(subSchema)) {\n            if (exports.schemaArrayKeyword[key]) {\n                const length = subSchema.length;\n                for (let i = 0; i < length; i++) {\n                    dereference(subSchema[i], lookup, baseURI, `${keyBase}/${i}`);\n                }\n            }\n        }\n        else if (exports.schemaMapKeyword[key]) {\n            for (let subKey in subSchema) {\n                dereference(subSchema[subKey], lookup, baseURI, `${keyBase}/${(0, pointer_js_1.encodePointer)(subKey)}`);\n            }\n        }\n        else {\n            dereference(subSchema, lookup, baseURI, keyBase);\n        }\n    }\n    return lookup;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/dereference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/format.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/format.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.format = void 0;\nconst DATE = /^(\\d\\d\\d\\d)-(\\d\\d)-(\\d\\d)$/;\nconst DAYS = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst TIME = /^(\\d\\d):(\\d\\d):(\\d\\d)(\\.\\d+)?(z|[+-]\\d\\d(?::?\\d\\d)?)?$/i;\nconst HOSTNAME = /^(?=.{1,253}\\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\\.?$/i;\nconst URIREF = /^(?:[a-z][a-z0-9+\\-.]*:)?(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'\"()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\\?(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nconst URITEMPLATE = /^(?:(?:[^\\x00-\\x20\"'<>%\\\\^`{|}]|%[0-9a-f]{2})|\\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?)*\\})*$/i;\nconst URL_ = /^(?:(?:https?|ftp):\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!10(?:\\.\\d{1,3}){3})(?!127(?:\\.\\d{1,3}){3})(?!169\\.254(?:\\.\\d{1,3}){2})(?!192\\.168(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-?)*[a-z\\u{00a1}-\\u{ffff}0-9]+)(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-?)*[a-z\\u{00a1}-\\u{ffff}0-9]+)*(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}]{2,})))(?::\\d{2,5})?(?:\\/[^\\s]*)?$/iu;\nconst UUID = /^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i;\nconst JSON_POINTER = /^(?:\\/(?:[^~/]|~0|~1)*)*$/;\nconst JSON_POINTER_URI_FRAGMENT = /^#(?:\\/(?:[a-z0-9_\\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i;\nconst RELATIVE_JSON_POINTER = /^(?:0|[1-9][0-9]*)(?:#|(?:\\/(?:[^~/]|~0|~1)*)*)$/;\nconst EMAIL = (input) => {\n    if (input[0] === '\"')\n        return false;\n    const [name, host, ...rest] = input.split('@');\n    if (!name ||\n        !host ||\n        rest.length !== 0 ||\n        name.length > 64 ||\n        host.length > 253)\n        return false;\n    if (name[0] === '.' || name.endsWith('.') || name.includes('..'))\n        return false;\n    if (!/^[a-z0-9.-]+$/i.test(host) ||\n        !/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+$/i.test(name))\n        return false;\n    return host\n        .split('.')\n        .every(part => /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/i.test(part));\n};\nconst IPV4 = /^(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/;\nconst IPV6 = /^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))$/i;\nconst DURATION = (input) => input.length > 1 &&\n    input.length < 80 &&\n    (/^P\\d+([.,]\\d+)?W$/.test(input) ||\n        (/^P[\\dYMDTHS]*(\\d[.,]\\d+)?[YMDHS]$/.test(input) &&\n            /^P([.,\\d]+Y)?([.,\\d]+M)?([.,\\d]+D)?(T([.,\\d]+H)?([.,\\d]+M)?([.,\\d]+S)?)?$/.test(input)));\nfunction bind(r) {\n    return r.test.bind(r);\n}\nexports.format = {\n    date,\n    time: time.bind(undefined, false),\n    'date-time': date_time,\n    duration: DURATION,\n    uri,\n    'uri-reference': bind(URIREF),\n    'uri-template': bind(URITEMPLATE),\n    url: bind(URL_),\n    email: EMAIL,\n    hostname: bind(HOSTNAME),\n    ipv4: bind(IPV4),\n    ipv6: bind(IPV6),\n    regex: regex,\n    uuid: bind(UUID),\n    'json-pointer': bind(JSON_POINTER),\n    'json-pointer-uri-fragment': bind(JSON_POINTER_URI_FRAGMENT),\n    'relative-json-pointer': bind(RELATIVE_JSON_POINTER)\n};\nfunction isLeapYear(year) {\n    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\nfunction date(str) {\n    const matches = str.match(DATE);\n    if (!matches)\n        return false;\n    const year = +matches[1];\n    const month = +matches[2];\n    const day = +matches[3];\n    return (month >= 1 &&\n        month <= 12 &&\n        day >= 1 &&\n        day <= (month == 2 && isLeapYear(year) ? 29 : DAYS[month]));\n}\nfunction time(full, str) {\n    const matches = str.match(TIME);\n    if (!matches)\n        return false;\n    const hour = +matches[1];\n    const minute = +matches[2];\n    const second = +matches[3];\n    const timeZone = !!matches[5];\n    return (((hour <= 23 && minute <= 59 && second <= 59) ||\n        (hour == 23 && minute == 59 && second == 60)) &&\n        (!full || timeZone));\n}\nconst DATE_TIME_SEPARATOR = /t|\\s/i;\nfunction date_time(str) {\n    const dateTime = str.split(DATE_TIME_SEPARATOR);\n    return dateTime.length == 2 && date(dateTime[0]) && time(true, dateTime[1]);\n}\nconst NOT_URI_FRAGMENT = /\\/|:/;\nconst URI_PATTERN = /^(?:[a-z][a-z0-9+\\-.]*:)(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\\?(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nfunction uri(str) {\n    return NOT_URI_FRAGMENT.test(str) && URI_PATTERN.test(str);\n}\nconst Z_ANCHOR = /[^\\\\]\\\\Z/;\nfunction regex(str) {\n    if (Z_ANCHOR.test(str))\n        return false;\n    try {\n        new RegExp(str, 'u');\n        return true;\n    }\n    catch (e) {\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/format.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/index.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./deep-compare-strict.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js\"), exports);\n__exportStar(__webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/dereference.js\"), exports);\n__exportStar(__webpack_require__(/*! ./format.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/format.js\"), exports);\n__exportStar(__webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/pointer.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./ucs2-length.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js\"), exports);\n__exportStar(__webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/validate.js\"), exports);\n__exportStar(__webpack_require__(/*! ./validator.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/validator.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/pointer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/pointer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.encodePointer = encodePointer;\nexports.escapePointer = escapePointer;\nfunction encodePointer(p) {\n    return encodeURI(escapePointer(p));\n}\nfunction escapePointer(p) {\n    return p.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvY29tbW9uanMvcG9pbnRlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUI7QUFDckIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAY2Z3b3JrZXJcXGpzb24tc2NoZW1hXFxkaXN0XFxjb21tb25qc1xccG9pbnRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZW5jb2RlUG9pbnRlciA9IGVuY29kZVBvaW50ZXI7XG5leHBvcnRzLmVzY2FwZVBvaW50ZXIgPSBlc2NhcGVQb2ludGVyO1xuZnVuY3Rpb24gZW5jb2RlUG9pbnRlcihwKSB7XG4gICAgcmV0dXJuIGVuY29kZVVSSShlc2NhcGVQb2ludGVyKHApKTtcbn1cbmZ1bmN0aW9uIGVzY2FwZVBvaW50ZXIocCkge1xuICAgIHJldHVybiBwLnJlcGxhY2UoL34vZywgJ34wJykucmVwbGFjZSgvXFwvL2csICd+MScpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/pointer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/types.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/types.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OutputFormat = void 0;\nvar OutputFormat;\n(function (OutputFormat) {\n    OutputFormat[OutputFormat[\"Flag\"] = 1] = \"Flag\";\n    OutputFormat[OutputFormat[\"Basic\"] = 2] = \"Basic\";\n    OutputFormat[OutputFormat[\"Detailed\"] = 4] = \"Detailed\";\n})(OutputFormat || (exports.OutputFormat = OutputFormat = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvY29tbW9uanMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG1CQUFtQixvQkFBb0Isb0JBQW9CIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBjZndvcmtlclxcanNvbi1zY2hlbWFcXGRpc3RcXGNvbW1vbmpzXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuT3V0cHV0Rm9ybWF0ID0gdm9pZCAwO1xudmFyIE91dHB1dEZvcm1hdDtcbihmdW5jdGlvbiAoT3V0cHV0Rm9ybWF0KSB7XG4gICAgT3V0cHV0Rm9ybWF0W091dHB1dEZvcm1hdFtcIkZsYWdcIl0gPSAxXSA9IFwiRmxhZ1wiO1xuICAgIE91dHB1dEZvcm1hdFtPdXRwdXRGb3JtYXRbXCJCYXNpY1wiXSA9IDJdID0gXCJCYXNpY1wiO1xuICAgIE91dHB1dEZvcm1hdFtPdXRwdXRGb3JtYXRbXCJEZXRhaWxlZFwiXSA9IDRdID0gXCJEZXRhaWxlZFwiO1xufSkoT3V0cHV0Rm9ybWF0IHx8IChleHBvcnRzLk91dHB1dEZvcm1hdCA9IE91dHB1dEZvcm1hdCA9IHt9KSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ucs2length = ucs2length;\nfunction ucs2length(s) {\n    let result = 0;\n    let length = s.length;\n    let index = 0;\n    let charCode;\n    while (index < length) {\n        result++;\n        charCode = s.charCodeAt(index++);\n        if (charCode >= 0xd800 && charCode <= 0xdbff && index < length) {\n            charCode = s.charCodeAt(index);\n            if ((charCode & 0xfc00) == 0xdc00) {\n                index++;\n            }\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvY29tbW9uanMvdWNzMi1sZW5ndGguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGNmd29ya2VyXFxqc29uLXNjaGVtYVxcZGlzdFxcY29tbW9uanNcXHVjczItbGVuZ3RoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy51Y3MybGVuZ3RoID0gdWNzMmxlbmd0aDtcbmZ1bmN0aW9uIHVjczJsZW5ndGgocykge1xuICAgIGxldCByZXN1bHQgPSAwO1xuICAgIGxldCBsZW5ndGggPSBzLmxlbmd0aDtcbiAgICBsZXQgaW5kZXggPSAwO1xuICAgIGxldCBjaGFyQ29kZTtcbiAgICB3aGlsZSAoaW5kZXggPCBsZW5ndGgpIHtcbiAgICAgICAgcmVzdWx0Kys7XG4gICAgICAgIGNoYXJDb2RlID0gcy5jaGFyQ29kZUF0KGluZGV4KyspO1xuICAgICAgICBpZiAoY2hhckNvZGUgPj0gMHhkODAwICYmIGNoYXJDb2RlIDw9IDB4ZGJmZiAmJiBpbmRleCA8IGxlbmd0aCkge1xuICAgICAgICAgICAgY2hhckNvZGUgPSBzLmNoYXJDb2RlQXQoaW5kZXgpO1xuICAgICAgICAgICAgaWYgKChjaGFyQ29kZSAmIDB4ZmMwMCkgPT0gMHhkYzAwKSB7XG4gICAgICAgICAgICAgICAgaW5kZXgrKztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/validate.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/validate.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.validate = validate;\nconst deep_compare_strict_js_1 = __webpack_require__(/*! ./deep-compare-strict.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js\");\nconst dereference_js_1 = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/dereference.js\");\nconst format_js_1 = __webpack_require__(/*! ./format.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/format.js\");\nconst pointer_js_1 = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/pointer.js\");\nconst ucs2_length_js_1 = __webpack_require__(/*! ./ucs2-length.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js\");\nfunction validate(instance, schema, draft = '2019-09', lookup = (0, dereference_js_1.dereference)(schema), shortCircuit = true, recursiveAnchor = null, instanceLocation = '#', schemaLocation = '#', evaluated = Object.create(null)) {\n    if (schema === true) {\n        return { valid: true, errors: [] };\n    }\n    if (schema === false) {\n        return {\n            valid: false,\n            errors: [\n                {\n                    instanceLocation,\n                    keyword: 'false',\n                    keywordLocation: instanceLocation,\n                    error: 'False boolean schema.'\n                }\n            ]\n        };\n    }\n    const rawInstanceType = typeof instance;\n    let instanceType;\n    switch (rawInstanceType) {\n        case 'boolean':\n        case 'number':\n        case 'string':\n            instanceType = rawInstanceType;\n            break;\n        case 'object':\n            if (instance === null) {\n                instanceType = 'null';\n            }\n            else if (Array.isArray(instance)) {\n                instanceType = 'array';\n            }\n            else {\n                instanceType = 'object';\n            }\n            break;\n        default:\n            throw new Error(`Instances of \"${rawInstanceType}\" type are not supported.`);\n    }\n    const { $ref, $recursiveRef, $recursiveAnchor, type: $type, const: $const, enum: $enum, required: $required, not: $not, anyOf: $anyOf, allOf: $allOf, oneOf: $oneOf, if: $if, then: $then, else: $else, format: $format, properties: $properties, patternProperties: $patternProperties, additionalProperties: $additionalProperties, unevaluatedProperties: $unevaluatedProperties, minProperties: $minProperties, maxProperties: $maxProperties, propertyNames: $propertyNames, dependentRequired: $dependentRequired, dependentSchemas: $dependentSchemas, dependencies: $dependencies, prefixItems: $prefixItems, items: $items, additionalItems: $additionalItems, unevaluatedItems: $unevaluatedItems, contains: $contains, minContains: $minContains, maxContains: $maxContains, minItems: $minItems, maxItems: $maxItems, uniqueItems: $uniqueItems, minimum: $minimum, maximum: $maximum, exclusiveMinimum: $exclusiveMinimum, exclusiveMaximum: $exclusiveMaximum, multipleOf: $multipleOf, minLength: $minLength, maxLength: $maxLength, pattern: $pattern, __absolute_ref__, __absolute_recursive_ref__ } = schema;\n    const errors = [];\n    if ($recursiveAnchor === true && recursiveAnchor === null) {\n        recursiveAnchor = schema;\n    }\n    if ($recursiveRef === '#') {\n        const refSchema = recursiveAnchor === null\n            ? lookup[__absolute_recursive_ref__]\n            : recursiveAnchor;\n        const keywordLocation = `${schemaLocation}/$recursiveRef`;\n        const result = validate(instance, recursiveAnchor === null ? schema : recursiveAnchor, draft, lookup, shortCircuit, refSchema, instanceLocation, keywordLocation, evaluated);\n        if (!result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: '$recursiveRef',\n                keywordLocation,\n                error: 'A subschema had errors.'\n            }, ...result.errors);\n        }\n    }\n    if ($ref !== undefined) {\n        const uri = __absolute_ref__ || $ref;\n        const refSchema = lookup[uri];\n        if (refSchema === undefined) {\n            let message = `Unresolved $ref \"${$ref}\".`;\n            if (__absolute_ref__ && __absolute_ref__ !== $ref) {\n                message += `  Absolute URI \"${__absolute_ref__}\".`;\n            }\n            message += `\\nKnown schemas:\\n- ${Object.keys(lookup).join('\\n- ')}`;\n            throw new Error(message);\n        }\n        const keywordLocation = `${schemaLocation}/$ref`;\n        const result = validate(instance, refSchema, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation, evaluated);\n        if (!result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: '$ref',\n                keywordLocation,\n                error: 'A subschema had errors.'\n            }, ...result.errors);\n        }\n        if (draft === '4' || draft === '7') {\n            return { valid: errors.length === 0, errors };\n        }\n    }\n    if (Array.isArray($type)) {\n        let length = $type.length;\n        let valid = false;\n        for (let i = 0; i < length; i++) {\n            if (instanceType === $type[i] ||\n                ($type[i] === 'integer' &&\n                    instanceType === 'number' &&\n                    instance % 1 === 0 &&\n                    instance === instance)) {\n                valid = true;\n                break;\n            }\n        }\n        if (!valid) {\n            errors.push({\n                instanceLocation,\n                keyword: 'type',\n                keywordLocation: `${schemaLocation}/type`,\n                error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type.join('\", \"')}\".`\n            });\n        }\n    }\n    else if ($type === 'integer') {\n        if (instanceType !== 'number' || instance % 1 || instance !== instance) {\n            errors.push({\n                instanceLocation,\n                keyword: 'type',\n                keywordLocation: `${schemaLocation}/type`,\n                error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type}\".`\n            });\n        }\n    }\n    else if ($type !== undefined && instanceType !== $type) {\n        errors.push({\n            instanceLocation,\n            keyword: 'type',\n            keywordLocation: `${schemaLocation}/type`,\n            error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type}\".`\n        });\n    }\n    if ($const !== undefined) {\n        if (instanceType === 'object' || instanceType === 'array') {\n            if (!(0, deep_compare_strict_js_1.deepCompareStrict)(instance, $const)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'const',\n                    keywordLocation: `${schemaLocation}/const`,\n                    error: `Instance does not match ${JSON.stringify($const)}.`\n                });\n            }\n        }\n        else if (instance !== $const) {\n            errors.push({\n                instanceLocation,\n                keyword: 'const',\n                keywordLocation: `${schemaLocation}/const`,\n                error: `Instance does not match ${JSON.stringify($const)}.`\n            });\n        }\n    }\n    if ($enum !== undefined) {\n        if (instanceType === 'object' || instanceType === 'array') {\n            if (!$enum.some(value => (0, deep_compare_strict_js_1.deepCompareStrict)(instance, value))) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'enum',\n                    keywordLocation: `${schemaLocation}/enum`,\n                    error: `Instance does not match any of ${JSON.stringify($enum)}.`\n                });\n            }\n        }\n        else if (!$enum.some(value => instance === value)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'enum',\n                keywordLocation: `${schemaLocation}/enum`,\n                error: `Instance does not match any of ${JSON.stringify($enum)}.`\n            });\n        }\n    }\n    if ($not !== undefined) {\n        const keywordLocation = `${schemaLocation}/not`;\n        const result = validate(instance, $not, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation);\n        if (result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: 'not',\n                keywordLocation,\n                error: 'Instance matched \"not\" schema.'\n            });\n        }\n    }\n    let subEvaluateds = [];\n    if ($anyOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/anyOf`;\n        const errorsLength = errors.length;\n        let anyValid = false;\n        for (let i = 0; i < $anyOf.length; i++) {\n            const subSchema = $anyOf[i];\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            anyValid = anyValid || result.valid;\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n        }\n        if (anyValid) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'anyOf',\n                keywordLocation,\n                error: 'Instance does not match any subschemas.'\n            });\n        }\n    }\n    if ($allOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/allOf`;\n        const errorsLength = errors.length;\n        let allValid = true;\n        for (let i = 0; i < $allOf.length; i++) {\n            const subSchema = $allOf[i];\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            allValid = allValid && result.valid;\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n        }\n        if (allValid) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'allOf',\n                keywordLocation,\n                error: `Instance does not match every subschema.`\n            });\n        }\n    }\n    if ($oneOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/oneOf`;\n        const errorsLength = errors.length;\n        const matches = $oneOf.filter((subSchema, i) => {\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n            return result.valid;\n        }).length;\n        if (matches === 1) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'oneOf',\n                keywordLocation,\n                error: `Instance does not match exactly one subschema (${matches} matches).`\n            });\n        }\n    }\n    if (instanceType === 'object' || instanceType === 'array') {\n        Object.assign(evaluated, ...subEvaluateds);\n    }\n    if ($if !== undefined) {\n        const keywordLocation = `${schemaLocation}/if`;\n        const conditionResult = validate(instance, $if, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation, evaluated).valid;\n        if (conditionResult) {\n            if ($then !== undefined) {\n                const thenResult = validate(instance, $then, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${schemaLocation}/then`, evaluated);\n                if (!thenResult.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'if',\n                        keywordLocation,\n                        error: `Instance does not match \"then\" schema.`\n                    }, ...thenResult.errors);\n                }\n            }\n        }\n        else if ($else !== undefined) {\n            const elseResult = validate(instance, $else, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${schemaLocation}/else`, evaluated);\n            if (!elseResult.valid) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'if',\n                    keywordLocation,\n                    error: `Instance does not match \"else\" schema.`\n                }, ...elseResult.errors);\n            }\n        }\n    }\n    if (instanceType === 'object') {\n        if ($required !== undefined) {\n            for (const key of $required) {\n                if (!(key in instance)) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'required',\n                        keywordLocation: `${schemaLocation}/required`,\n                        error: `Instance does not have required property \"${key}\".`\n                    });\n                }\n            }\n        }\n        const keys = Object.keys(instance);\n        if ($minProperties !== undefined && keys.length < $minProperties) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minProperties',\n                keywordLocation: `${schemaLocation}/minProperties`,\n                error: `Instance does not have at least ${$minProperties} properties.`\n            });\n        }\n        if ($maxProperties !== undefined && keys.length > $maxProperties) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxProperties',\n                keywordLocation: `${schemaLocation}/maxProperties`,\n                error: `Instance does not have at least ${$maxProperties} properties.`\n            });\n        }\n        if ($propertyNames !== undefined) {\n            const keywordLocation = `${schemaLocation}/propertyNames`;\n            for (const key in instance) {\n                const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                const result = validate(key, $propertyNames, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                if (!result.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'propertyNames',\n                        keywordLocation,\n                        error: `Property name \"${key}\" does not match schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        if ($dependentRequired !== undefined) {\n            const keywordLocation = `${schemaLocation}/dependantRequired`;\n            for (const key in $dependentRequired) {\n                if (key in instance) {\n                    const required = $dependentRequired[key];\n                    for (const dependantKey of required) {\n                        if (!(dependantKey in instance)) {\n                            errors.push({\n                                instanceLocation,\n                                keyword: 'dependentRequired',\n                                keywordLocation,\n                                error: `Instance has \"${key}\" but does not have \"${dependantKey}\".`\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if ($dependentSchemas !== undefined) {\n            for (const key in $dependentSchemas) {\n                const keywordLocation = `${schemaLocation}/dependentSchemas`;\n                if (key in instance) {\n                    const result = validate(instance, $dependentSchemas[key], draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${keywordLocation}/${(0, pointer_js_1.encodePointer)(key)}`, evaluated);\n                    if (!result.valid) {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'dependentSchemas',\n                            keywordLocation,\n                            error: `Instance has \"${key}\" but does not match dependant schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if ($dependencies !== undefined) {\n            const keywordLocation = `${schemaLocation}/dependencies`;\n            for (const key in $dependencies) {\n                if (key in instance) {\n                    const propsOrSchema = $dependencies[key];\n                    if (Array.isArray(propsOrSchema)) {\n                        for (const dependantKey of propsOrSchema) {\n                            if (!(dependantKey in instance)) {\n                                errors.push({\n                                    instanceLocation,\n                                    keyword: 'dependencies',\n                                    keywordLocation,\n                                    error: `Instance has \"${key}\" but does not have \"${dependantKey}\".`\n                                });\n                            }\n                        }\n                    }\n                    else {\n                        const result = validate(instance, propsOrSchema, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${keywordLocation}/${(0, pointer_js_1.encodePointer)(key)}`);\n                        if (!result.valid) {\n                            errors.push({\n                                instanceLocation,\n                                keyword: 'dependencies',\n                                keywordLocation,\n                                error: `Instance has \"${key}\" but does not match dependant schema.`\n                            }, ...result.errors);\n                        }\n                    }\n                }\n            }\n        }\n        const thisEvaluated = Object.create(null);\n        let stop = false;\n        if ($properties !== undefined) {\n            const keywordLocation = `${schemaLocation}/properties`;\n            for (const key in $properties) {\n                if (!(key in instance)) {\n                    continue;\n                }\n                const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                const result = validate(instance[key], $properties[key], draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, `${keywordLocation}/${(0, pointer_js_1.encodePointer)(key)}`);\n                if (result.valid) {\n                    evaluated[key] = thisEvaluated[key] = true;\n                }\n                else {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'properties',\n                        keywordLocation,\n                        error: `Property \"${key}\" does not match schema.`\n                    }, ...result.errors);\n                    if (stop)\n                        break;\n                }\n            }\n        }\n        if (!stop && $patternProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/patternProperties`;\n            for (const pattern in $patternProperties) {\n                const regex = new RegExp(pattern, 'u');\n                const subSchema = $patternProperties[pattern];\n                for (const key in instance) {\n                    if (!regex.test(key)) {\n                        continue;\n                    }\n                    const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                    const result = validate(instance[key], subSchema, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, `${keywordLocation}/${(0, pointer_js_1.encodePointer)(pattern)}`);\n                    if (result.valid) {\n                        evaluated[key] = thisEvaluated[key] = true;\n                    }\n                    else {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'patternProperties',\n                            keywordLocation,\n                            error: `Property \"${key}\" matches pattern \"${pattern}\" but does not match associated schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if (!stop && $additionalProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/additionalProperties`;\n            for (const key in instance) {\n                if (thisEvaluated[key]) {\n                    continue;\n                }\n                const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                const result = validate(instance[key], $additionalProperties, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                if (result.valid) {\n                    evaluated[key] = true;\n                }\n                else {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'additionalProperties',\n                        keywordLocation,\n                        error: `Property \"${key}\" does not match additional properties schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        else if (!stop && $unevaluatedProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/unevaluatedProperties`;\n            for (const key in instance) {\n                if (!evaluated[key]) {\n                    const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                    const result = validate(instance[key], $unevaluatedProperties, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                    if (result.valid) {\n                        evaluated[key] = true;\n                    }\n                    else {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'unevaluatedProperties',\n                            keywordLocation,\n                            error: `Property \"${key}\" does not match unevaluated properties schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n    }\n    else if (instanceType === 'array') {\n        if ($maxItems !== undefined && instance.length > $maxItems) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxItems',\n                keywordLocation: `${schemaLocation}/maxItems`,\n                error: `Array has too many items (${instance.length} > ${$maxItems}).`\n            });\n        }\n        if ($minItems !== undefined && instance.length < $minItems) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minItems',\n                keywordLocation: `${schemaLocation}/minItems`,\n                error: `Array has too few items (${instance.length} < ${$minItems}).`\n            });\n        }\n        const length = instance.length;\n        let i = 0;\n        let stop = false;\n        if ($prefixItems !== undefined) {\n            const keywordLocation = `${schemaLocation}/prefixItems`;\n            const length2 = Math.min($prefixItems.length, length);\n            for (; i < length2; i++) {\n                const result = validate(instance[i], $prefixItems[i], draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, `${keywordLocation}/${i}`);\n                evaluated[i] = true;\n                if (!result.valid) {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'prefixItems',\n                        keywordLocation,\n                        error: `Items did not match schema.`\n                    }, ...result.errors);\n                    if (stop)\n                        break;\n                }\n            }\n        }\n        if ($items !== undefined) {\n            const keywordLocation = `${schemaLocation}/items`;\n            if (Array.isArray($items)) {\n                const length2 = Math.min($items.length, length);\n                for (; i < length2; i++) {\n                    const result = validate(instance[i], $items[i], draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, `${keywordLocation}/${i}`);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'items',\n                            keywordLocation,\n                            error: `Items did not match schema.`\n                        }, ...result.errors);\n                        if (stop)\n                            break;\n                    }\n                }\n            }\n            else {\n                for (; i < length; i++) {\n                    const result = validate(instance[i], $items, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'items',\n                            keywordLocation,\n                            error: `Items did not match schema.`\n                        }, ...result.errors);\n                        if (stop)\n                            break;\n                    }\n                }\n            }\n            if (!stop && $additionalItems !== undefined) {\n                const keywordLocation = `${schemaLocation}/additionalItems`;\n                for (; i < length; i++) {\n                    const result = validate(instance[i], $additionalItems, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'additionalItems',\n                            keywordLocation,\n                            error: `Items did not match additional items schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if ($contains !== undefined) {\n            if (length === 0 && $minContains === undefined) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'contains',\n                    keywordLocation: `${schemaLocation}/contains`,\n                    error: `Array is empty. It must contain at least one item matching the schema.`\n                });\n            }\n            else if ($minContains !== undefined && length < $minContains) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minContains',\n                    keywordLocation: `${schemaLocation}/minContains`,\n                    error: `Array has less items (${length}) than minContains (${$minContains}).`\n                });\n            }\n            else {\n                const keywordLocation = `${schemaLocation}/contains`;\n                const errorsLength = errors.length;\n                let contained = 0;\n                for (let j = 0; j < length; j++) {\n                    const result = validate(instance[j], $contains, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${j}`, keywordLocation);\n                    if (result.valid) {\n                        evaluated[j] = true;\n                        contained++;\n                    }\n                    else {\n                        errors.push(...result.errors);\n                    }\n                }\n                if (contained >= ($minContains || 0)) {\n                    errors.length = errorsLength;\n                }\n                if ($minContains === undefined &&\n                    $maxContains === undefined &&\n                    contained === 0) {\n                    errors.splice(errorsLength, 0, {\n                        instanceLocation,\n                        keyword: 'contains',\n                        keywordLocation,\n                        error: `Array does not contain item matching schema.`\n                    });\n                }\n                else if ($minContains !== undefined && contained < $minContains) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'minContains',\n                        keywordLocation: `${schemaLocation}/minContains`,\n                        error: `Array must contain at least ${$minContains} items matching schema. Only ${contained} items were found.`\n                    });\n                }\n                else if ($maxContains !== undefined && contained > $maxContains) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'maxContains',\n                        keywordLocation: `${schemaLocation}/maxContains`,\n                        error: `Array may contain at most ${$maxContains} items matching schema. ${contained} items were found.`\n                    });\n                }\n            }\n        }\n        if (!stop && $unevaluatedItems !== undefined) {\n            const keywordLocation = `${schemaLocation}/unevaluatedItems`;\n            for (i; i < length; i++) {\n                if (evaluated[i]) {\n                    continue;\n                }\n                const result = validate(instance[i], $unevaluatedItems, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                evaluated[i] = true;\n                if (!result.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'unevaluatedItems',\n                        keywordLocation,\n                        error: `Items did not match unevaluated items schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        if ($uniqueItems) {\n            for (let j = 0; j < length; j++) {\n                const a = instance[j];\n                const ao = typeof a === 'object' && a !== null;\n                for (let k = 0; k < length; k++) {\n                    if (j === k) {\n                        continue;\n                    }\n                    const b = instance[k];\n                    const bo = typeof b === 'object' && b !== null;\n                    if (a === b || (ao && bo && (0, deep_compare_strict_js_1.deepCompareStrict)(a, b))) {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'uniqueItems',\n                            keywordLocation: `${schemaLocation}/uniqueItems`,\n                            error: `Duplicate items at indexes ${j} and ${k}.`\n                        });\n                        j = Number.MAX_SAFE_INTEGER;\n                        k = Number.MAX_SAFE_INTEGER;\n                    }\n                }\n            }\n        }\n    }\n    else if (instanceType === 'number') {\n        if (draft === '4') {\n            if ($minimum !== undefined &&\n                (($exclusiveMinimum === true && instance <= $minimum) ||\n                    instance < $minimum)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minimum',\n                    keywordLocation: `${schemaLocation}/minimum`,\n                    error: `${instance} is less than ${$exclusiveMinimum ? 'or equal to ' : ''} ${$minimum}.`\n                });\n            }\n            if ($maximum !== undefined &&\n                (($exclusiveMaximum === true && instance >= $maximum) ||\n                    instance > $maximum)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'maximum',\n                    keywordLocation: `${schemaLocation}/maximum`,\n                    error: `${instance} is greater than ${$exclusiveMaximum ? 'or equal to ' : ''} ${$maximum}.`\n                });\n            }\n        }\n        else {\n            if ($minimum !== undefined && instance < $minimum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minimum',\n                    keywordLocation: `${schemaLocation}/minimum`,\n                    error: `${instance} is less than ${$minimum}.`\n                });\n            }\n            if ($maximum !== undefined && instance > $maximum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'maximum',\n                    keywordLocation: `${schemaLocation}/maximum`,\n                    error: `${instance} is greater than ${$maximum}.`\n                });\n            }\n            if ($exclusiveMinimum !== undefined && instance <= $exclusiveMinimum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'exclusiveMinimum',\n                    keywordLocation: `${schemaLocation}/exclusiveMinimum`,\n                    error: `${instance} is less than ${$exclusiveMinimum}.`\n                });\n            }\n            if ($exclusiveMaximum !== undefined && instance >= $exclusiveMaximum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'exclusiveMaximum',\n                    keywordLocation: `${schemaLocation}/exclusiveMaximum`,\n                    error: `${instance} is greater than or equal to ${$exclusiveMaximum}.`\n                });\n            }\n        }\n        if ($multipleOf !== undefined) {\n            const remainder = instance % $multipleOf;\n            if (Math.abs(0 - remainder) >= 1.1920929e-7 &&\n                Math.abs($multipleOf - remainder) >= 1.1920929e-7) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'multipleOf',\n                    keywordLocation: `${schemaLocation}/multipleOf`,\n                    error: `${instance} is not a multiple of ${$multipleOf}.`\n                });\n            }\n        }\n    }\n    else if (instanceType === 'string') {\n        const length = $minLength === undefined && $maxLength === undefined\n            ? 0\n            : (0, ucs2_length_js_1.ucs2length)(instance);\n        if ($minLength !== undefined && length < $minLength) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minLength',\n                keywordLocation: `${schemaLocation}/minLength`,\n                error: `String is too short (${length} < ${$minLength}).`\n            });\n        }\n        if ($maxLength !== undefined && length > $maxLength) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxLength',\n                keywordLocation: `${schemaLocation}/maxLength`,\n                error: `String is too long (${length} > ${$maxLength}).`\n            });\n        }\n        if ($pattern !== undefined && !new RegExp($pattern, 'u').test(instance)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'pattern',\n                keywordLocation: `${schemaLocation}/pattern`,\n                error: `String does not match pattern.`\n            });\n        }\n        if ($format !== undefined &&\n            format_js_1.format[$format] &&\n            !format_js_1.format[$format](instance)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'format',\n                keywordLocation: `${schemaLocation}/format`,\n                error: `String does not match format \"${$format}\".`\n            });\n        }\n    }\n    return { valid: errors.length === 0, errors };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/validate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/validator.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/commonjs/validator.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Validator = void 0;\nconst dereference_js_1 = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/dereference.js\");\nconst validate_js_1 = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/validate.js\");\nclass Validator {\n    schema;\n    draft;\n    shortCircuit;\n    lookup;\n    constructor(schema, draft = '2019-09', shortCircuit = true) {\n        this.schema = schema;\n        this.draft = draft;\n        this.shortCircuit = shortCircuit;\n        this.lookup = (0, dereference_js_1.dereference)(schema);\n    }\n    validate(instance) {\n        return (0, validate_js_1.validate)(instance, this.schema, this.draft, this.lookup, this.shortCircuit);\n    }\n    addSchema(schema, id) {\n        if (id) {\n            schema = { ...schema, $id: id };\n        }\n        (0, dereference_js_1.dereference)(schema, this.lookup);\n    }\n}\nexports.Validator = Validator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvY29tbW9uanMvdmFsaWRhdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQjtBQUNqQix5QkFBeUIsbUJBQU8sQ0FBQyxpR0FBa0I7QUFDbkQsc0JBQXNCLG1CQUFPLENBQUMsMkZBQWU7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGNmd29ya2VyXFxqc29uLXNjaGVtYVxcZGlzdFxcY29tbW9uanNcXHZhbGlkYXRvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuVmFsaWRhdG9yID0gdm9pZCAwO1xuY29uc3QgZGVyZWZlcmVuY2VfanNfMSA9IHJlcXVpcmUoXCIuL2RlcmVmZXJlbmNlLmpzXCIpO1xuY29uc3QgdmFsaWRhdGVfanNfMSA9IHJlcXVpcmUoXCIuL3ZhbGlkYXRlLmpzXCIpO1xuY2xhc3MgVmFsaWRhdG9yIHtcbiAgICBzY2hlbWE7XG4gICAgZHJhZnQ7XG4gICAgc2hvcnRDaXJjdWl0O1xuICAgIGxvb2t1cDtcbiAgICBjb25zdHJ1Y3RvcihzY2hlbWEsIGRyYWZ0ID0gJzIwMTktMDknLCBzaG9ydENpcmN1aXQgPSB0cnVlKSB7XG4gICAgICAgIHRoaXMuc2NoZW1hID0gc2NoZW1hO1xuICAgICAgICB0aGlzLmRyYWZ0ID0gZHJhZnQ7XG4gICAgICAgIHRoaXMuc2hvcnRDaXJjdWl0ID0gc2hvcnRDaXJjdWl0O1xuICAgICAgICB0aGlzLmxvb2t1cCA9ICgwLCBkZXJlZmVyZW5jZV9qc18xLmRlcmVmZXJlbmNlKShzY2hlbWEpO1xuICAgIH1cbiAgICB2YWxpZGF0ZShpbnN0YW5jZSkge1xuICAgICAgICByZXR1cm4gKDAsIHZhbGlkYXRlX2pzXzEudmFsaWRhdGUpKGluc3RhbmNlLCB0aGlzLnNjaGVtYSwgdGhpcy5kcmFmdCwgdGhpcy5sb29rdXAsIHRoaXMuc2hvcnRDaXJjdWl0KTtcbiAgICB9XG4gICAgYWRkU2NoZW1hKHNjaGVtYSwgaWQpIHtcbiAgICAgICAgaWYgKGlkKSB7XG4gICAgICAgICAgICBzY2hlbWEgPSB7IC4uLnNjaGVtYSwgJGlkOiBpZCB9O1xuICAgICAgICB9XG4gICAgICAgICgwLCBkZXJlZmVyZW5jZV9qc18xLmRlcmVmZXJlbmNlKShzY2hlbWEsIHRoaXMubG9va3VwKTtcbiAgICB9XG59XG5leHBvcnRzLlZhbGlkYXRvciA9IFZhbGlkYXRvcjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/commonjs/validator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepCompareStrict: () => (/* binding */ deepCompareStrict)\n/* harmony export */ });\nfunction deepCompareStrict(a, b) {\n    const typeofa = typeof a;\n    if (typeofa !== typeof b) {\n        return false;\n    }\n    if (Array.isArray(a)) {\n        if (!Array.isArray(b)) {\n            return false;\n        }\n        const length = a.length;\n        if (length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < length; i++) {\n            if (!deepCompareStrict(a[i], b[i])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (typeofa === 'object') {\n        if (!a || !b) {\n            return a === b;\n        }\n        const aKeys = Object.keys(a);\n        const bKeys = Object.keys(b);\n        const length = aKeys.length;\n        if (length !== bKeys.length) {\n            return false;\n        }\n        for (const k of aKeys) {\n            if (!deepCompareStrict(a[k], b[k])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    return a === b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/dereference.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dereference: () => (/* binding */ dereference),\n/* harmony export */   ignoredKeyword: () => (/* binding */ ignoredKeyword),\n/* harmony export */   initialBaseURI: () => (/* binding */ initialBaseURI),\n/* harmony export */   schemaArrayKeyword: () => (/* binding */ schemaArrayKeyword),\n/* harmony export */   schemaKeyword: () => (/* binding */ schemaKeyword),\n/* harmony export */   schemaMapKeyword: () => (/* binding */ schemaMapKeyword)\n/* harmony export */ });\n/* harmony import */ var _pointer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js\");\n\nconst schemaKeyword = {\n    additionalItems: true,\n    unevaluatedItems: true,\n    items: true,\n    contains: true,\n    additionalProperties: true,\n    unevaluatedProperties: true,\n    propertyNames: true,\n    not: true,\n    if: true,\n    then: true,\n    else: true\n};\nconst schemaArrayKeyword = {\n    prefixItems: true,\n    items: true,\n    allOf: true,\n    anyOf: true,\n    oneOf: true\n};\nconst schemaMapKeyword = {\n    $defs: true,\n    definitions: true,\n    properties: true,\n    patternProperties: true,\n    dependentSchemas: true\n};\nconst ignoredKeyword = {\n    id: true,\n    $id: true,\n    $ref: true,\n    $schema: true,\n    $anchor: true,\n    $vocabulary: true,\n    $comment: true,\n    default: true,\n    enum: true,\n    const: true,\n    required: true,\n    type: true,\n    maximum: true,\n    minimum: true,\n    exclusiveMaximum: true,\n    exclusiveMinimum: true,\n    multipleOf: true,\n    maxLength: true,\n    minLength: true,\n    pattern: true,\n    format: true,\n    maxItems: true,\n    minItems: true,\n    uniqueItems: true,\n    maxProperties: true,\n    minProperties: true\n};\nlet initialBaseURI = typeof self !== 'undefined' &&\n    self.location &&\n    self.location.origin !== 'null'\n    ?\n        new URL(self.location.origin + self.location.pathname + location.search)\n    : new URL('https://github.com/cfworker');\nfunction dereference(schema, lookup = Object.create(null), baseURI = initialBaseURI, basePointer = '') {\n    if (schema && typeof schema === 'object' && !Array.isArray(schema)) {\n        const id = schema.$id || schema.id;\n        if (id) {\n            const url = new URL(id, baseURI.href);\n            if (url.hash.length > 1) {\n                lookup[url.href] = schema;\n            }\n            else {\n                url.hash = '';\n                if (basePointer === '') {\n                    baseURI = url;\n                }\n                else {\n                    dereference(schema, lookup, baseURI);\n                }\n            }\n        }\n    }\n    else if (schema !== true && schema !== false) {\n        return lookup;\n    }\n    const schemaURI = baseURI.href + (basePointer ? '#' + basePointer : '');\n    if (lookup[schemaURI] !== undefined) {\n        throw new Error(`Duplicate schema URI \"${schemaURI}\".`);\n    }\n    lookup[schemaURI] = schema;\n    if (schema === true || schema === false) {\n        return lookup;\n    }\n    if (schema.__absolute_uri__ === undefined) {\n        Object.defineProperty(schema, '__absolute_uri__', {\n            enumerable: false,\n            value: schemaURI\n        });\n    }\n    if (schema.$ref && schema.__absolute_ref__ === undefined) {\n        const url = new URL(schema.$ref, baseURI.href);\n        url.hash = url.hash;\n        Object.defineProperty(schema, '__absolute_ref__', {\n            enumerable: false,\n            value: url.href\n        });\n    }\n    if (schema.$recursiveRef && schema.__absolute_recursive_ref__ === undefined) {\n        const url = new URL(schema.$recursiveRef, baseURI.href);\n        url.hash = url.hash;\n        Object.defineProperty(schema, '__absolute_recursive_ref__', {\n            enumerable: false,\n            value: url.href\n        });\n    }\n    if (schema.$anchor) {\n        const url = new URL('#' + schema.$anchor, baseURI.href);\n        lookup[url.href] = schema;\n    }\n    for (let key in schema) {\n        if (ignoredKeyword[key]) {\n            continue;\n        }\n        const keyBase = `${basePointer}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_0__.encodePointer)(key)}`;\n        const subSchema = schema[key];\n        if (Array.isArray(subSchema)) {\n            if (schemaArrayKeyword[key]) {\n                const length = subSchema.length;\n                for (let i = 0; i < length; i++) {\n                    dereference(subSchema[i], lookup, baseURI, `${keyBase}/${i}`);\n                }\n            }\n        }\n        else if (schemaMapKeyword[key]) {\n            for (let subKey in subSchema) {\n                dereference(subSchema[subKey], lookup, baseURI, `${keyBase}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_0__.encodePointer)(subKey)}`);\n            }\n        }\n        else {\n            dereference(subSchema, lookup, baseURI, keyBase);\n        }\n    }\n    return lookup;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/format.js":
/*!***************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/format.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   format: () => (/* binding */ format)\n/* harmony export */ });\nconst DATE = /^(\\d\\d\\d\\d)-(\\d\\d)-(\\d\\d)$/;\nconst DAYS = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst TIME = /^(\\d\\d):(\\d\\d):(\\d\\d)(\\.\\d+)?(z|[+-]\\d\\d(?::?\\d\\d)?)?$/i;\nconst HOSTNAME = /^(?=.{1,253}\\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\\.?$/i;\nconst URIREF = /^(?:[a-z][a-z0-9+\\-.]*:)?(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'\"()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\\?(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nconst URITEMPLATE = /^(?:(?:[^\\x00-\\x20\"'<>%\\\\^`{|}]|%[0-9a-f]{2})|\\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?)*\\})*$/i;\nconst URL_ = /^(?:(?:https?|ftp):\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!10(?:\\.\\d{1,3}){3})(?!127(?:\\.\\d{1,3}){3})(?!169\\.254(?:\\.\\d{1,3}){2})(?!192\\.168(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-?)*[a-z\\u{00a1}-\\u{ffff}0-9]+)(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-?)*[a-z\\u{00a1}-\\u{ffff}0-9]+)*(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}]{2,})))(?::\\d{2,5})?(?:\\/[^\\s]*)?$/iu;\nconst UUID = /^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i;\nconst JSON_POINTER = /^(?:\\/(?:[^~/]|~0|~1)*)*$/;\nconst JSON_POINTER_URI_FRAGMENT = /^#(?:\\/(?:[a-z0-9_\\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i;\nconst RELATIVE_JSON_POINTER = /^(?:0|[1-9][0-9]*)(?:#|(?:\\/(?:[^~/]|~0|~1)*)*)$/;\nconst EMAIL = (input) => {\n    if (input[0] === '\"')\n        return false;\n    const [name, host, ...rest] = input.split('@');\n    if (!name ||\n        !host ||\n        rest.length !== 0 ||\n        name.length > 64 ||\n        host.length > 253)\n        return false;\n    if (name[0] === '.' || name.endsWith('.') || name.includes('..'))\n        return false;\n    if (!/^[a-z0-9.-]+$/i.test(host) ||\n        !/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+$/i.test(name))\n        return false;\n    return host\n        .split('.')\n        .every(part => /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/i.test(part));\n};\nconst IPV4 = /^(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/;\nconst IPV6 = /^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))$/i;\nconst DURATION = (input) => input.length > 1 &&\n    input.length < 80 &&\n    (/^P\\d+([.,]\\d+)?W$/.test(input) ||\n        (/^P[\\dYMDTHS]*(\\d[.,]\\d+)?[YMDHS]$/.test(input) &&\n            /^P([.,\\d]+Y)?([.,\\d]+M)?([.,\\d]+D)?(T([.,\\d]+H)?([.,\\d]+M)?([.,\\d]+S)?)?$/.test(input)));\nfunction bind(r) {\n    return r.test.bind(r);\n}\nconst format = {\n    date,\n    time: time.bind(undefined, false),\n    'date-time': date_time,\n    duration: DURATION,\n    uri,\n    'uri-reference': bind(URIREF),\n    'uri-template': bind(URITEMPLATE),\n    url: bind(URL_),\n    email: EMAIL,\n    hostname: bind(HOSTNAME),\n    ipv4: bind(IPV4),\n    ipv6: bind(IPV6),\n    regex: regex,\n    uuid: bind(UUID),\n    'json-pointer': bind(JSON_POINTER),\n    'json-pointer-uri-fragment': bind(JSON_POINTER_URI_FRAGMENT),\n    'relative-json-pointer': bind(RELATIVE_JSON_POINTER)\n};\nfunction isLeapYear(year) {\n    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\nfunction date(str) {\n    const matches = str.match(DATE);\n    if (!matches)\n        return false;\n    const year = +matches[1];\n    const month = +matches[2];\n    const day = +matches[3];\n    return (month >= 1 &&\n        month <= 12 &&\n        day >= 1 &&\n        day <= (month == 2 && isLeapYear(year) ? 29 : DAYS[month]));\n}\nfunction time(full, str) {\n    const matches = str.match(TIME);\n    if (!matches)\n        return false;\n    const hour = +matches[1];\n    const minute = +matches[2];\n    const second = +matches[3];\n    const timeZone = !!matches[5];\n    return (((hour <= 23 && minute <= 59 && second <= 59) ||\n        (hour == 23 && minute == 59 && second == 60)) &&\n        (!full || timeZone));\n}\nconst DATE_TIME_SEPARATOR = /t|\\s/i;\nfunction date_time(str) {\n    const dateTime = str.split(DATE_TIME_SEPARATOR);\n    return dateTime.length == 2 && date(dateTime[0]) && time(true, dateTime[1]);\n}\nconst NOT_URI_FRAGMENT = /\\/|:/;\nconst URI_PATTERN = /^(?:[a-z][a-z0-9+\\-.]*:)(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\\?(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nfunction uri(str) {\n    return NOT_URI_FRAGMENT.test(str) && URI_PATTERN.test(str);\n}\nconst Z_ANCHOR = /[^\\\\]\\\\Z/;\nfunction regex(str) {\n    if (Z_ANCHOR.test(str))\n        return false;\n    try {\n        new RegExp(str, 'u');\n        return true;\n    }\n    catch (e) {\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL2Zvcm1hdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE1BQU0sMEJBQTBCLEtBQUssb0NBQW9DLEtBQUs7QUFDdEcsNkVBQTZFLGNBQWMsRUFBRSwrQkFBK0IsSUFBSSxHQUFHLEVBQUUsZUFBZSxJQUFJLEdBQUcsRUFBRSxhQUFhLElBQUksZ0JBQWdCLElBQUksR0FBRyxFQUFFLGdCQUFnQixJQUFJLEdBQUcsSUFBSSxTQUFTLElBQUksZ0JBQWdCLElBQUksR0FBRyxFQUFFLGdCQUFnQixJQUFJLEdBQUcsSUFBSSxTQUFTLElBQUksZ0JBQWdCLElBQUksR0FBRyxFQUFFLGdCQUFnQixJQUFJLEdBQUcsSUFBSSxTQUFTLElBQUksYUFBYSxJQUFJLGlCQUFpQixJQUFJLEdBQUcsSUFBSSxTQUFTLElBQUksaUJBQWlCLElBQUksVUFBVSxJQUFJLHVDQUF1QyxFQUFFLGdEQUFnRCxJQUFJLEdBQUcsSUFBSSxTQUFTLElBQUksYUFBYSxJQUFJLGdCQUFnQixJQUFJLEdBQUcsSUFBSSxTQUFTLElBQUksMkNBQTJDLDhDQUE4QyxFQUFFLDBEQUEwRCxhQUFhLEVBQUUsMkNBQTJDLGVBQWUsRUFBRSxvQ0FBb0MsZUFBZSxFQUFFLGlDQUFpQyxlQUFlLEVBQUUsaUNBQWlDLGVBQWUsRUFBRSxpQ0FBaUMsZUFBZSxFQUFFLHFDQUFxQyxpQkFBaUIsRUFBRSxrQ0FBa0MsaUJBQWlCLEVBQUU7QUFDaHBDLGlEQUFpRCxFQUFFLFlBQVksRUFBRSxJQUFJLE1BQU0sZ0NBQWdDLEVBQUUsaUJBQWlCLElBQUksZ0NBQWdDLEVBQUUsaUJBQWlCLElBQUksU0FBUztBQUNsTSx3RUFBd0UsSUFBSSxFQUFFLEVBQUUsZUFBZSxJQUFJLEVBQUUsRUFBRSxvQkFBb0IsSUFBSSxFQUFFLEVBQUUsb0JBQW9CLElBQUksRUFBRSxFQUFFLHNDQUFzQyxJQUFJLEVBQUUsRUFBRSxnREFBZ0QsSUFBSSxvQkFBb0IsRUFBRSx1REFBdUQsS0FBSyxJQUFJLEtBQUssZ0JBQWdCLEtBQUssSUFBSSxLQUFLLHFCQUFxQixLQUFLLElBQUksS0FBSyxnQkFBZ0IsS0FBSyxJQUFJLEtBQUssc0JBQXNCLEtBQUssSUFBSSxLQUFLLEVBQUUsR0FBRyxVQUFVLElBQUk7QUFDbGYsc0NBQXNDLEVBQUUsYUFBYSxFQUFFLEdBQUcsRUFBRSxTQUFTLEdBQUc7QUFDeEU7QUFDQSxrRUFBa0UsZUFBZSxFQUFFO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsRUFBRTtBQUNwQztBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsS0FBSztBQUNqRDtBQUNBLHFEQUFxRCxFQUFFO0FBQ3ZELDJCQUEyQixJQUFJLEdBQUcsRUFBRSxVQUFVLElBQUksZ0JBQWdCLElBQUksR0FBRyxFQUFFLFdBQVcsSUFBSSx5RUFBeUUsRUFBRSxpQkFBaUIsSUFBSSxHQUFHLEVBQUUsYUFBYSxJQUFJLEVBQUUsSUFBSSwyRUFBMkUsRUFBRSxpQkFBaUIsSUFBSSxHQUFHLEVBQUUsYUFBYSxJQUFJLEVBQUUsSUFBSSxjQUFjLElBQUksMkVBQTJFLEVBQUUsa0JBQWtCLElBQUksR0FBRyxFQUFFLGFBQWEsSUFBSSxFQUFFLElBQUksY0FBYyxJQUFJLEVBQUUsSUFBSSx5RUFBeUUsRUFBRSxrQkFBa0IsSUFBSSxHQUFHLEVBQUUsYUFBYSxJQUFJLEVBQUUsSUFBSSxjQUFjLElBQUksRUFBRSxJQUFJLHlFQUF5RSxFQUFFLGtCQUFrQixJQUFJLEdBQUcsRUFBRSxhQUFhLElBQUksRUFBRSxJQUFJLGNBQWMsSUFBSSxFQUFFLElBQUkseUVBQXlFLEVBQUUsc0JBQXNCLElBQUksRUFBRSxJQUFJLGNBQWMsSUFBSSxFQUFFLElBQUkseUVBQXlFLEVBQUU7QUFDci9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlGQUFpRixjQUFjLEVBQUUsK0JBQStCLElBQUksR0FBRyxFQUFFLGVBQWUsSUFBSSxHQUFHLEVBQUUsYUFBYSxJQUFJLGdCQUFnQixJQUFJLEdBQUcsRUFBRSxnQkFBZ0IsSUFBSSxHQUFHLElBQUksU0FBUyxJQUFJLGdCQUFnQixJQUFJLEdBQUcsRUFBRSxnQkFBZ0IsSUFBSSxHQUFHLElBQUksU0FBUyxJQUFJLGdCQUFnQixJQUFJLEdBQUcsRUFBRSxnQkFBZ0IsSUFBSSxHQUFHLElBQUksU0FBUyxJQUFJLGFBQWEsSUFBSSxpQkFBaUIsSUFBSSxHQUFHLElBQUksU0FBUyxJQUFJLGlCQUFpQixJQUFJLFVBQVUsSUFBSSx1Q0FBdUMsRUFBRSxnREFBZ0QsSUFBSSxHQUFHLElBQUksU0FBUyxJQUFJLGFBQWEsSUFBSSxnQkFBZ0IsSUFBSSxHQUFHLElBQUksU0FBUyxJQUFJLDJDQUEyQyw4Q0FBOEMsRUFBRSx5REFBeUQsYUFBYSxFQUFFLDBDQUEwQyxlQUFlLEVBQUUsbUNBQW1DLGVBQWUsRUFBRSxnQ0FBZ0MsZUFBZSxFQUFFLGdDQUFnQyxlQUFlLEVBQUUsZ0NBQWdDLGVBQWUsRUFBRSxtQ0FBbUMsaUJBQWlCLEVBQUUsaUNBQWlDLGlCQUFpQixFQUFFO0FBQzNvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGNmd29ya2VyXFxqc29uLXNjaGVtYVxcZGlzdFxcZXNtXFxmb3JtYXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgREFURSA9IC9eKFxcZFxcZFxcZFxcZCktKFxcZFxcZCktKFxcZFxcZCkkLztcbmNvbnN0IERBWVMgPSBbMCwgMzEsIDI4LCAzMSwgMzAsIDMxLCAzMCwgMzEsIDMxLCAzMCwgMzEsIDMwLCAzMV07XG5jb25zdCBUSU1FID0gL14oXFxkXFxkKTooXFxkXFxkKTooXFxkXFxkKShcXC5cXGQrKT8oenxbKy1dXFxkXFxkKD86Oj9cXGRcXGQpPyk/JC9pO1xuY29uc3QgSE9TVE5BTUUgPSAvXig/PS57MSwyNTN9XFwuPyQpW2EtejAtOV0oPzpbYS16MC05LV17MCw2MX1bYS16MC05XSk/KD86XFwuW2EtejAtOV0oPzpbLTAtOWEtel17MCw2MX1bMC05YS16XSk/KSpcXC4/JC9pO1xuY29uc3QgVVJJUkVGID0gL14oPzpbYS16XVthLXowLTkrXFwtLl0qOik/KD86XFwvP1xcLyg/Oig/OlthLXowLTlcXC0uX34hJCYnKCkqKyw7PTpdfCVbMC05YS1mXXsyfSkqQCk/KD86XFxbKD86KD86KD86KD86WzAtOWEtZl17MSw0fTopezZ9fDo6KD86WzAtOWEtZl17MSw0fTopezV9fCg/OlswLTlhLWZdezEsNH0pPzo6KD86WzAtOWEtZl17MSw0fTopezR9fCg/Oig/OlswLTlhLWZdezEsNH06KXswLDF9WzAtOWEtZl17MSw0fSk/OjooPzpbMC05YS1mXXsxLDR9Oil7M318KD86KD86WzAtOWEtZl17MSw0fTopezAsMn1bMC05YS1mXXsxLDR9KT86Oig/OlswLTlhLWZdezEsNH06KXsyfXwoPzooPzpbMC05YS1mXXsxLDR9Oil7MCwzfVswLTlhLWZdezEsNH0pPzo6WzAtOWEtZl17MSw0fTp8KD86KD86WzAtOWEtZl17MSw0fTopezAsNH1bMC05YS1mXXsxLDR9KT86OikoPzpbMC05YS1mXXsxLDR9OlswLTlhLWZdezEsNH18KD86KD86MjVbMC01XXwyWzAtNF1cXGR8WzAxXT9cXGRcXGQ/KVxcLil7M30oPzoyNVswLTVdfDJbMC00XVxcZHxbMDFdP1xcZFxcZD8pKXwoPzooPzpbMC05YS1mXXsxLDR9Oil7MCw1fVswLTlhLWZdezEsNH0pPzo6WzAtOWEtZl17MSw0fXwoPzooPzpbMC05YS1mXXsxLDR9Oil7MCw2fVswLTlhLWZdezEsNH0pPzo6KXxbVnZdWzAtOWEtZl0rXFwuW2EtejAtOVxcLS5ffiEkJicoKSorLDs9Ol0rKVxcXXwoPzooPzoyNVswLTVdfDJbMC00XVxcZHxbMDFdP1xcZFxcZD8pXFwuKXszfSg/OjI1WzAtNV18MlswLTRdXFxkfFswMV0/XFxkXFxkPyl8KD86W2EtejAtOVxcLS5ffiEkJidcIigpKissOz1dfCVbMC05YS1mXXsyfSkqKSg/OjpcXGQqKT8oPzpcXC8oPzpbYS16MC05XFwtLl9+ISQmJ1wiKCkqKyw7PTpAXXwlWzAtOWEtZl17Mn0pKikqfFxcLyg/Oig/OlthLXowLTlcXC0uX34hJCYnXCIoKSorLDs9OkBdfCVbMC05YS1mXXsyfSkrKD86XFwvKD86W2EtejAtOVxcLS5ffiEkJidcIigpKissOz06QF18JVswLTlhLWZdezJ9KSopKik/fCg/OlthLXowLTlcXC0uX34hJCYnXCIoKSorLDs9OkBdfCVbMC05YS1mXXsyfSkrKD86XFwvKD86W2EtejAtOVxcLS5ffiEkJidcIigpKissOz06QF18JVswLTlhLWZdezJ9KSopKik/KD86XFw/KD86W2EtejAtOVxcLS5ffiEkJidcIigpKissOz06QC8/XXwlWzAtOWEtZl17Mn0pKik/KD86Iyg/OlthLXowLTlcXC0uX34hJCYnXCIoKSorLDs9OkAvP118JVswLTlhLWZdezJ9KSopPyQvaTtcbmNvbnN0IFVSSVRFTVBMQVRFID0gL14oPzooPzpbXlxceDAwLVxceDIwXCInPD4lXFxcXF5ge3x9XXwlWzAtOWEtZl17Mn0pfFxce1srIy4vOz8mPSwhQHxdPyg/OlthLXowLTlfXXwlWzAtOWEtZl17Mn0pKyg/OjpbMS05XVswLTldezAsM318XFwqKT8oPzosKD86W2EtejAtOV9dfCVbMC05YS1mXXsyfSkrKD86OlsxLTldWzAtOV17MCwzfXxcXCopPykqXFx9KSokL2k7XG5jb25zdCBVUkxfID0gL14oPzooPzpodHRwcz98ZnRwKTpcXC9cXC8pKD86XFxTKyg/OjpcXFMqKT9AKT8oPzooPyExMCg/OlxcLlxcZHsxLDN9KXszfSkoPyExMjcoPzpcXC5cXGR7MSwzfSl7M30pKD8hMTY5XFwuMjU0KD86XFwuXFxkezEsM30pezJ9KSg/ITE5MlxcLjE2OCg/OlxcLlxcZHsxLDN9KXsyfSkoPyExNzJcXC4oPzoxWzYtOV18MlxcZHwzWzAtMV0pKD86XFwuXFxkezEsM30pezJ9KSg/OlsxLTldXFxkP3wxXFxkXFxkfDJbMDFdXFxkfDIyWzAtM10pKD86XFwuKD86MT9cXGR7MSwyfXwyWzAtNF1cXGR8MjVbMC01XSkpezJ9KD86XFwuKD86WzEtOV1cXGQ/fDFcXGRcXGR8MlswLTRdXFxkfDI1WzAtNF0pKXwoPzooPzpbYS16XFx1ezAwYTF9LVxcdXtmZmZmfTAtOV0rLT8pKlthLXpcXHV7MDBhMX0tXFx1e2ZmZmZ9MC05XSspKD86XFwuKD86W2EtelxcdXswMGExfS1cXHV7ZmZmZn0wLTldKy0/KSpbYS16XFx1ezAwYTF9LVxcdXtmZmZmfTAtOV0rKSooPzpcXC4oPzpbYS16XFx1ezAwYTF9LVxcdXtmZmZmfV17Mix9KSkpKD86OlxcZHsyLDV9KT8oPzpcXC9bXlxcc10qKT8kL2l1O1xuY29uc3QgVVVJRCA9IC9eKD86dXJuOnV1aWQ6KT9bMC05YS1mXXs4fS0oPzpbMC05YS1mXXs0fS0pezN9WzAtOWEtZl17MTJ9JC9pO1xuY29uc3QgSlNPTl9QT0lOVEVSID0gL14oPzpcXC8oPzpbXn4vXXx+MHx+MSkqKSokLztcbmNvbnN0IEpTT05fUE9JTlRFUl9VUklfRlJBR01FTlQgPSAvXiMoPzpcXC8oPzpbYS16MC05X1xcLS4hJCYnKCkqKyw7Oj1AXXwlWzAtOWEtZl17Mn18fjB8fjEpKikqJC9pO1xuY29uc3QgUkVMQVRJVkVfSlNPTl9QT0lOVEVSID0gL14oPzowfFsxLTldWzAtOV0qKSg/OiN8KD86XFwvKD86W15+L118fjB8fjEpKikqKSQvO1xuY29uc3QgRU1BSUwgPSAoaW5wdXQpID0+IHtcbiAgICBpZiAoaW5wdXRbMF0gPT09ICdcIicpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICBjb25zdCBbbmFtZSwgaG9zdCwgLi4ucmVzdF0gPSBpbnB1dC5zcGxpdCgnQCcpO1xuICAgIGlmICghbmFtZSB8fFxuICAgICAgICAhaG9zdCB8fFxuICAgICAgICByZXN0Lmxlbmd0aCAhPT0gMCB8fFxuICAgICAgICBuYW1lLmxlbmd0aCA+IDY0IHx8XG4gICAgICAgIGhvc3QubGVuZ3RoID4gMjUzKVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgaWYgKG5hbWVbMF0gPT09ICcuJyB8fCBuYW1lLmVuZHNXaXRoKCcuJykgfHwgbmFtZS5pbmNsdWRlcygnLi4nKSlcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIGlmICghL15bYS16MC05Li1dKyQvaS50ZXN0KGhvc3QpIHx8XG4gICAgICAgICEvXlthLXowLTkuISMkJSYnKisvPT9eX2B7fH1+LV0rJC9pLnRlc3QobmFtZSkpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICByZXR1cm4gaG9zdFxuICAgICAgICAuc3BsaXQoJy4nKVxuICAgICAgICAuZXZlcnkocGFydCA9PiAvXlthLXowLTldKFthLXowLTktXXswLDYxfVthLXowLTldKT8kL2kudGVzdChwYXJ0KSk7XG59O1xuY29uc3QgSVBWNCA9IC9eKD86KD86MjVbMC01XXwyWzAtNF1cXGR8WzAxXT9cXGRcXGQ/KVxcLil7M30oPzoyNVswLTVdfDJbMC00XVxcZHxbMDFdP1xcZFxcZD8pJC87XG5jb25zdCBJUFY2ID0gL14oKChbMC05YS1mXXsxLDR9Oil7N30oWzAtOWEtZl17MSw0fXw6KSl8KChbMC05YS1mXXsxLDR9Oil7Nn0oOlswLTlhLWZdezEsNH18KCgyNVswLTVdfDJbMC00XVxcZHwxXFxkXFxkfFsxLTldP1xcZCkoXFwuKDI1WzAtNV18MlswLTRdXFxkfDFcXGRcXGR8WzEtOV0/XFxkKSl7M30pfDopKXwoKFswLTlhLWZdezEsNH06KXs1fSgoKDpbMC05YS1mXXsxLDR9KXsxLDJ9KXw6KCgyNVswLTVdfDJbMC00XVxcZHwxXFxkXFxkfFsxLTldP1xcZCkoXFwuKDI1WzAtNV18MlswLTRdXFxkfDFcXGRcXGR8WzEtOV0/XFxkKSl7M30pfDopKXwoKFswLTlhLWZdezEsNH06KXs0fSgoKDpbMC05YS1mXXsxLDR9KXsxLDN9KXwoKDpbMC05YS1mXXsxLDR9KT86KCgyNVswLTVdfDJbMC00XVxcZHwxXFxkXFxkfFsxLTldP1xcZCkoXFwuKDI1WzAtNV18MlswLTRdXFxkfDFcXGRcXGR8WzEtOV0/XFxkKSl7M30pKXw6KSl8KChbMC05YS1mXXsxLDR9Oil7M30oKCg6WzAtOWEtZl17MSw0fSl7MSw0fSl8KCg6WzAtOWEtZl17MSw0fSl7MCwyfTooKDI1WzAtNV18MlswLTRdXFxkfDFcXGRcXGR8WzEtOV0/XFxkKShcXC4oMjVbMC01XXwyWzAtNF1cXGR8MVxcZFxcZHxbMS05XT9cXGQpKXszfSkpfDopKXwoKFswLTlhLWZdezEsNH06KXsyfSgoKDpbMC05YS1mXXsxLDR9KXsxLDV9KXwoKDpbMC05YS1mXXsxLDR9KXswLDN9OigoMjVbMC01XXwyWzAtNF1cXGR8MVxcZFxcZHxbMS05XT9cXGQpKFxcLigyNVswLTVdfDJbMC00XVxcZHwxXFxkXFxkfFsxLTldP1xcZCkpezN9KSl8OikpfCgoWzAtOWEtZl17MSw0fTopezF9KCgoOlswLTlhLWZdezEsNH0pezEsNn0pfCgoOlswLTlhLWZdezEsNH0pezAsNH06KCgyNVswLTVdfDJbMC00XVxcZHwxXFxkXFxkfFsxLTldP1xcZCkoXFwuKDI1WzAtNV18MlswLTRdXFxkfDFcXGRcXGR8WzEtOV0/XFxkKSl7M30pKXw6KSl8KDooKCg6WzAtOWEtZl17MSw0fSl7MSw3fSl8KCg6WzAtOWEtZl17MSw0fSl7MCw1fTooKDI1WzAtNV18MlswLTRdXFxkfDFcXGRcXGR8WzEtOV0/XFxkKShcXC4oMjVbMC01XXwyWzAtNF1cXGR8MVxcZFxcZHxbMS05XT9cXGQpKXszfSkpfDopKSkkL2k7XG5jb25zdCBEVVJBVElPTiA9IChpbnB1dCkgPT4gaW5wdXQubGVuZ3RoID4gMSAmJlxuICAgIGlucHV0Lmxlbmd0aCA8IDgwICYmXG4gICAgKC9eUFxcZCsoWy4sXVxcZCspP1ckLy50ZXN0KGlucHV0KSB8fFxuICAgICAgICAoL15QW1xcZFlNRFRIU10qKFxcZFsuLF1cXGQrKT9bWU1ESFNdJC8udGVzdChpbnB1dCkgJiZcbiAgICAgICAgICAgIC9eUChbLixcXGRdK1kpPyhbLixcXGRdK00pPyhbLixcXGRdK0QpPyhUKFsuLFxcZF0rSCk/KFsuLFxcZF0rTSk/KFsuLFxcZF0rUyk/KT8kLy50ZXN0KGlucHV0KSkpO1xuZnVuY3Rpb24gYmluZChyKSB7XG4gICAgcmV0dXJuIHIudGVzdC5iaW5kKHIpO1xufVxuZXhwb3J0IGNvbnN0IGZvcm1hdCA9IHtcbiAgICBkYXRlLFxuICAgIHRpbWU6IHRpbWUuYmluZCh1bmRlZmluZWQsIGZhbHNlKSxcbiAgICAnZGF0ZS10aW1lJzogZGF0ZV90aW1lLFxuICAgIGR1cmF0aW9uOiBEVVJBVElPTixcbiAgICB1cmksXG4gICAgJ3VyaS1yZWZlcmVuY2UnOiBiaW5kKFVSSVJFRiksXG4gICAgJ3VyaS10ZW1wbGF0ZSc6IGJpbmQoVVJJVEVNUExBVEUpLFxuICAgIHVybDogYmluZChVUkxfKSxcbiAgICBlbWFpbDogRU1BSUwsXG4gICAgaG9zdG5hbWU6IGJpbmQoSE9TVE5BTUUpLFxuICAgIGlwdjQ6IGJpbmQoSVBWNCksXG4gICAgaXB2NjogYmluZChJUFY2KSxcbiAgICByZWdleDogcmVnZXgsXG4gICAgdXVpZDogYmluZChVVUlEKSxcbiAgICAnanNvbi1wb2ludGVyJzogYmluZChKU09OX1BPSU5URVIpLFxuICAgICdqc29uLXBvaW50ZXItdXJpLWZyYWdtZW50JzogYmluZChKU09OX1BPSU5URVJfVVJJX0ZSQUdNRU5UKSxcbiAgICAncmVsYXRpdmUtanNvbi1wb2ludGVyJzogYmluZChSRUxBVElWRV9KU09OX1BPSU5URVIpXG59O1xuZnVuY3Rpb24gaXNMZWFwWWVhcih5ZWFyKSB7XG4gICAgcmV0dXJuIHllYXIgJSA0ID09PSAwICYmICh5ZWFyICUgMTAwICE9PSAwIHx8IHllYXIgJSA0MDAgPT09IDApO1xufVxuZnVuY3Rpb24gZGF0ZShzdHIpIHtcbiAgICBjb25zdCBtYXRjaGVzID0gc3RyLm1hdGNoKERBVEUpO1xuICAgIGlmICghbWF0Y2hlcylcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIGNvbnN0IHllYXIgPSArbWF0Y2hlc1sxXTtcbiAgICBjb25zdCBtb250aCA9ICttYXRjaGVzWzJdO1xuICAgIGNvbnN0IGRheSA9ICttYXRjaGVzWzNdO1xuICAgIHJldHVybiAobW9udGggPj0gMSAmJlxuICAgICAgICBtb250aCA8PSAxMiAmJlxuICAgICAgICBkYXkgPj0gMSAmJlxuICAgICAgICBkYXkgPD0gKG1vbnRoID09IDIgJiYgaXNMZWFwWWVhcih5ZWFyKSA/IDI5IDogREFZU1ttb250aF0pKTtcbn1cbmZ1bmN0aW9uIHRpbWUoZnVsbCwgc3RyKSB7XG4gICAgY29uc3QgbWF0Y2hlcyA9IHN0ci5tYXRjaChUSU1FKTtcbiAgICBpZiAoIW1hdGNoZXMpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICBjb25zdCBob3VyID0gK21hdGNoZXNbMV07XG4gICAgY29uc3QgbWludXRlID0gK21hdGNoZXNbMl07XG4gICAgY29uc3Qgc2Vjb25kID0gK21hdGNoZXNbM107XG4gICAgY29uc3QgdGltZVpvbmUgPSAhIW1hdGNoZXNbNV07XG4gICAgcmV0dXJuICgoKGhvdXIgPD0gMjMgJiYgbWludXRlIDw9IDU5ICYmIHNlY29uZCA8PSA1OSkgfHxcbiAgICAgICAgKGhvdXIgPT0gMjMgJiYgbWludXRlID09IDU5ICYmIHNlY29uZCA9PSA2MCkpICYmXG4gICAgICAgICghZnVsbCB8fCB0aW1lWm9uZSkpO1xufVxuY29uc3QgREFURV9USU1FX1NFUEFSQVRPUiA9IC90fFxccy9pO1xuZnVuY3Rpb24gZGF0ZV90aW1lKHN0cikge1xuICAgIGNvbnN0IGRhdGVUaW1lID0gc3RyLnNwbGl0KERBVEVfVElNRV9TRVBBUkFUT1IpO1xuICAgIHJldHVybiBkYXRlVGltZS5sZW5ndGggPT0gMiAmJiBkYXRlKGRhdGVUaW1lWzBdKSAmJiB0aW1lKHRydWUsIGRhdGVUaW1lWzFdKTtcbn1cbmNvbnN0IE5PVF9VUklfRlJBR01FTlQgPSAvXFwvfDovO1xuY29uc3QgVVJJX1BBVFRFUk4gPSAvXig/OlthLXpdW2EtejAtOStcXC0uXSo6KSg/OlxcLz9cXC8oPzooPzpbYS16MC05XFwtLl9+ISQmJygpKissOz06XXwlWzAtOWEtZl17Mn0pKkApPyg/OlxcWyg/Oig/Oig/Oig/OlswLTlhLWZdezEsNH06KXs2fXw6Oig/OlswLTlhLWZdezEsNH06KXs1fXwoPzpbMC05YS1mXXsxLDR9KT86Oig/OlswLTlhLWZdezEsNH06KXs0fXwoPzooPzpbMC05YS1mXXsxLDR9Oil7MCwxfVswLTlhLWZdezEsNH0pPzo6KD86WzAtOWEtZl17MSw0fTopezN9fCg/Oig/OlswLTlhLWZdezEsNH06KXswLDJ9WzAtOWEtZl17MSw0fSk/OjooPzpbMC05YS1mXXsxLDR9Oil7Mn18KD86KD86WzAtOWEtZl17MSw0fTopezAsM31bMC05YS1mXXsxLDR9KT86OlswLTlhLWZdezEsNH06fCg/Oig/OlswLTlhLWZdezEsNH06KXswLDR9WzAtOWEtZl17MSw0fSk/OjopKD86WzAtOWEtZl17MSw0fTpbMC05YS1mXXsxLDR9fCg/Oig/OjI1WzAtNV18MlswLTRdXFxkfFswMV0/XFxkXFxkPylcXC4pezN9KD86MjVbMC01XXwyWzAtNF1cXGR8WzAxXT9cXGRcXGQ/KSl8KD86KD86WzAtOWEtZl17MSw0fTopezAsNX1bMC05YS1mXXsxLDR9KT86OlswLTlhLWZdezEsNH18KD86KD86WzAtOWEtZl17MSw0fTopezAsNn1bMC05YS1mXXsxLDR9KT86Oil8W1Z2XVswLTlhLWZdK1xcLlthLXowLTlcXC0uX34hJCYnKCkqKyw7PTpdKylcXF18KD86KD86MjVbMC01XXwyWzAtNF1cXGR8WzAxXT9cXGRcXGQ/KVxcLil7M30oPzoyNVswLTVdfDJbMC00XVxcZHxbMDFdP1xcZFxcZD8pfCg/OlthLXowLTlcXC0uX34hJCYnKCkqKyw7PV18JVswLTlhLWZdezJ9KSopKD86OlxcZCopPyg/OlxcLyg/OlthLXowLTlcXC0uX34hJCYnKCkqKyw7PTpAXXwlWzAtOWEtZl17Mn0pKikqfFxcLyg/Oig/OlthLXowLTlcXC0uX34hJCYnKCkqKyw7PTpAXXwlWzAtOWEtZl17Mn0pKyg/OlxcLyg/OlthLXowLTlcXC0uX34hJCYnKCkqKyw7PTpAXXwlWzAtOWEtZl17Mn0pKikqKT98KD86W2EtejAtOVxcLS5ffiEkJicoKSorLDs9OkBdfCVbMC05YS1mXXsyfSkrKD86XFwvKD86W2EtejAtOVxcLS5ffiEkJicoKSorLDs9OkBdfCVbMC05YS1mXXsyfSkqKSopKD86XFw/KD86W2EtejAtOVxcLS5ffiEkJicoKSorLDs9OkAvP118JVswLTlhLWZdezJ9KSopPyg/OiMoPzpbYS16MC05XFwtLl9+ISQmJygpKissOz06QC8/XXwlWzAtOWEtZl17Mn0pKik/JC9pO1xuZnVuY3Rpb24gdXJpKHN0cikge1xuICAgIHJldHVybiBOT1RfVVJJX0ZSQUdNRU5ULnRlc3Qoc3RyKSAmJiBVUklfUEFUVEVSTi50ZXN0KHN0cik7XG59XG5jb25zdCBaX0FOQ0hPUiA9IC9bXlxcXFxdXFxcXFovO1xuZnVuY3Rpb24gcmVnZXgoc3RyKSB7XG4gICAgaWYgKFpfQU5DSE9SLnRlc3Qoc3RyKSlcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIHRyeSB7XG4gICAgICAgIG5ldyBSZWdFeHAoc3RyLCAndScpO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/format.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutputFormat: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_4__.OutputFormat),\n/* harmony export */   Validator: () => (/* reexport safe */ _validator_js__WEBPACK_IMPORTED_MODULE_7__.Validator),\n/* harmony export */   deepCompareStrict: () => (/* reexport safe */ _deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__.deepCompareStrict),\n/* harmony export */   dereference: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.dereference),\n/* harmony export */   encodePointer: () => (/* reexport safe */ _pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer),\n/* harmony export */   escapePointer: () => (/* reexport safe */ _pointer_js__WEBPACK_IMPORTED_MODULE_3__.escapePointer),\n/* harmony export */   format: () => (/* reexport safe */ _format_js__WEBPACK_IMPORTED_MODULE_2__.format),\n/* harmony export */   ignoredKeyword: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.ignoredKeyword),\n/* harmony export */   initialBaseURI: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.initialBaseURI),\n/* harmony export */   schemaArrayKeyword: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.schemaArrayKeyword),\n/* harmony export */   schemaKeyword: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.schemaKeyword),\n/* harmony export */   schemaMapKeyword: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.schemaMapKeyword),\n/* harmony export */   ucs2length: () => (/* reexport safe */ _ucs2_length_js__WEBPACK_IMPORTED_MODULE_5__.ucs2length),\n/* harmony export */   validate: () => (/* reexport safe */ _validate_js__WEBPACK_IMPORTED_MODULE_6__.validate)\n/* harmony export */ });\n/* harmony import */ var _deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deep-compare-strict.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js\");\n/* harmony import */ var _dereference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js\");\n/* harmony import */ var _format_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./format.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/format.js\");\n/* harmony import */ var _pointer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/types.js\");\n/* harmony import */ var _ucs2_length_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ucs2-length.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js\");\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validate.js\");\n/* harmony import */ var _validator_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./validator.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validator.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBeUM7QUFDUjtBQUNMO0FBQ0M7QUFDRjtBQUNNO0FBQ0g7QUFDQyIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAY2Z3b3JrZXJcXGpzb24tc2NoZW1hXFxkaXN0XFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vZGVlcC1jb21wYXJlLXN0cmljdC5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2RlcmVmZXJlbmNlLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZm9ybWF0LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vcG9pbnRlci5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdWNzMi1sZW5ndGguanMnO1xuZXhwb3J0ICogZnJvbSAnLi92YWxpZGF0ZS5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3ZhbGlkYXRvci5qcyc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js":
/*!****************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/pointer.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodePointer: () => (/* binding */ encodePointer),\n/* harmony export */   escapePointer: () => (/* binding */ escapePointer)\n/* harmony export */ });\nfunction encodePointer(p) {\n    return encodeURI(escapePointer(p));\n}\nfunction escapePointer(p) {\n    return p.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL3BvaW50ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGNmd29ya2VyXFxqc29uLXNjaGVtYVxcZGlzdFxcZXNtXFxwb2ludGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBlbmNvZGVQb2ludGVyKHApIHtcbiAgICByZXR1cm4gZW5jb2RlVVJJKGVzY2FwZVBvaW50ZXIocCkpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGVzY2FwZVBvaW50ZXIocCkge1xuICAgIHJldHVybiBwLnJlcGxhY2UoL34vZywgJ34wJykucmVwbGFjZSgvXFwvL2csICd+MScpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/types.js":
/*!**************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/types.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutputFormat: () => (/* binding */ OutputFormat)\n/* harmony export */ });\nvar OutputFormat;\n(function (OutputFormat) {\n    OutputFormat[OutputFormat[\"Flag\"] = 1] = \"Flag\";\n    OutputFormat[OutputFormat[\"Basic\"] = 2] = \"Basic\";\n    OutputFormat[OutputFormat[\"Detailed\"] = 4] = \"Detailed\";\n})(OutputFormat || (OutputFormat = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0MiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGNmd29ya2VyXFxqc29uLXNjaGVtYVxcZGlzdFxcZXNtXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIE91dHB1dEZvcm1hdDtcbihmdW5jdGlvbiAoT3V0cHV0Rm9ybWF0KSB7XG4gICAgT3V0cHV0Rm9ybWF0W091dHB1dEZvcm1hdFtcIkZsYWdcIl0gPSAxXSA9IFwiRmxhZ1wiO1xuICAgIE91dHB1dEZvcm1hdFtPdXRwdXRGb3JtYXRbXCJCYXNpY1wiXSA9IDJdID0gXCJCYXNpY1wiO1xuICAgIE91dHB1dEZvcm1hdFtPdXRwdXRGb3JtYXRbXCJEZXRhaWxlZFwiXSA9IDRdID0gXCJEZXRhaWxlZFwiO1xufSkoT3V0cHV0Rm9ybWF0IHx8IChPdXRwdXRGb3JtYXQgPSB7fSkpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ucs2length: () => (/* binding */ ucs2length)\n/* harmony export */ });\nfunction ucs2length(s) {\n    let result = 0;\n    let length = s.length;\n    let index = 0;\n    let charCode;\n    while (index < length) {\n        result++;\n        charCode = s.charCodeAt(index++);\n        if (charCode >= 0xd800 && charCode <= 0xdbff && index < length) {\n            charCode = s.charCodeAt(index);\n            if ((charCode & 0xfc00) == 0xdc00) {\n                index++;\n            }\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL3VjczItbGVuZ3RoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBjZndvcmtlclxcanNvbi1zY2hlbWFcXGRpc3RcXGVzbVxcdWNzMi1sZW5ndGguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHVjczJsZW5ndGgocykge1xuICAgIGxldCByZXN1bHQgPSAwO1xuICAgIGxldCBsZW5ndGggPSBzLmxlbmd0aDtcbiAgICBsZXQgaW5kZXggPSAwO1xuICAgIGxldCBjaGFyQ29kZTtcbiAgICB3aGlsZSAoaW5kZXggPCBsZW5ndGgpIHtcbiAgICAgICAgcmVzdWx0Kys7XG4gICAgICAgIGNoYXJDb2RlID0gcy5jaGFyQ29kZUF0KGluZGV4KyspO1xuICAgICAgICBpZiAoY2hhckNvZGUgPj0gMHhkODAwICYmIGNoYXJDb2RlIDw9IDB4ZGJmZiAmJiBpbmRleCA8IGxlbmd0aCkge1xuICAgICAgICAgICAgY2hhckNvZGUgPSBzLmNoYXJDb2RlQXQoaW5kZXgpO1xuICAgICAgICAgICAgaWYgKChjaGFyQ29kZSAmIDB4ZmMwMCkgPT0gMHhkYzAwKSB7XG4gICAgICAgICAgICAgICAgaW5kZXgrKztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validate.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/validate.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/* harmony import */ var _deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deep-compare-strict.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js\");\n/* harmony import */ var _dereference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js\");\n/* harmony import */ var _format_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./format.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/format.js\");\n/* harmony import */ var _pointer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js\");\n/* harmony import */ var _ucs2_length_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ucs2-length.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js\");\n\n\n\n\n\nfunction validate(instance, schema, draft = '2019-09', lookup = (0,_dereference_js__WEBPACK_IMPORTED_MODULE_1__.dereference)(schema), shortCircuit = true, recursiveAnchor = null, instanceLocation = '#', schemaLocation = '#', evaluated = Object.create(null)) {\n    if (schema === true) {\n        return { valid: true, errors: [] };\n    }\n    if (schema === false) {\n        return {\n            valid: false,\n            errors: [\n                {\n                    instanceLocation,\n                    keyword: 'false',\n                    keywordLocation: instanceLocation,\n                    error: 'False boolean schema.'\n                }\n            ]\n        };\n    }\n    const rawInstanceType = typeof instance;\n    let instanceType;\n    switch (rawInstanceType) {\n        case 'boolean':\n        case 'number':\n        case 'string':\n            instanceType = rawInstanceType;\n            break;\n        case 'object':\n            if (instance === null) {\n                instanceType = 'null';\n            }\n            else if (Array.isArray(instance)) {\n                instanceType = 'array';\n            }\n            else {\n                instanceType = 'object';\n            }\n            break;\n        default:\n            throw new Error(`Instances of \"${rawInstanceType}\" type are not supported.`);\n    }\n    const { $ref, $recursiveRef, $recursiveAnchor, type: $type, const: $const, enum: $enum, required: $required, not: $not, anyOf: $anyOf, allOf: $allOf, oneOf: $oneOf, if: $if, then: $then, else: $else, format: $format, properties: $properties, patternProperties: $patternProperties, additionalProperties: $additionalProperties, unevaluatedProperties: $unevaluatedProperties, minProperties: $minProperties, maxProperties: $maxProperties, propertyNames: $propertyNames, dependentRequired: $dependentRequired, dependentSchemas: $dependentSchemas, dependencies: $dependencies, prefixItems: $prefixItems, items: $items, additionalItems: $additionalItems, unevaluatedItems: $unevaluatedItems, contains: $contains, minContains: $minContains, maxContains: $maxContains, minItems: $minItems, maxItems: $maxItems, uniqueItems: $uniqueItems, minimum: $minimum, maximum: $maximum, exclusiveMinimum: $exclusiveMinimum, exclusiveMaximum: $exclusiveMaximum, multipleOf: $multipleOf, minLength: $minLength, maxLength: $maxLength, pattern: $pattern, __absolute_ref__, __absolute_recursive_ref__ } = schema;\n    const errors = [];\n    if ($recursiveAnchor === true && recursiveAnchor === null) {\n        recursiveAnchor = schema;\n    }\n    if ($recursiveRef === '#') {\n        const refSchema = recursiveAnchor === null\n            ? lookup[__absolute_recursive_ref__]\n            : recursiveAnchor;\n        const keywordLocation = `${schemaLocation}/$recursiveRef`;\n        const result = validate(instance, recursiveAnchor === null ? schema : recursiveAnchor, draft, lookup, shortCircuit, refSchema, instanceLocation, keywordLocation, evaluated);\n        if (!result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: '$recursiveRef',\n                keywordLocation,\n                error: 'A subschema had errors.'\n            }, ...result.errors);\n        }\n    }\n    if ($ref !== undefined) {\n        const uri = __absolute_ref__ || $ref;\n        const refSchema = lookup[uri];\n        if (refSchema === undefined) {\n            let message = `Unresolved $ref \"${$ref}\".`;\n            if (__absolute_ref__ && __absolute_ref__ !== $ref) {\n                message += `  Absolute URI \"${__absolute_ref__}\".`;\n            }\n            message += `\\nKnown schemas:\\n- ${Object.keys(lookup).join('\\n- ')}`;\n            throw new Error(message);\n        }\n        const keywordLocation = `${schemaLocation}/$ref`;\n        const result = validate(instance, refSchema, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation, evaluated);\n        if (!result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: '$ref',\n                keywordLocation,\n                error: 'A subschema had errors.'\n            }, ...result.errors);\n        }\n        if (draft === '4' || draft === '7') {\n            return { valid: errors.length === 0, errors };\n        }\n    }\n    if (Array.isArray($type)) {\n        let length = $type.length;\n        let valid = false;\n        for (let i = 0; i < length; i++) {\n            if (instanceType === $type[i] ||\n                ($type[i] === 'integer' &&\n                    instanceType === 'number' &&\n                    instance % 1 === 0 &&\n                    instance === instance)) {\n                valid = true;\n                break;\n            }\n        }\n        if (!valid) {\n            errors.push({\n                instanceLocation,\n                keyword: 'type',\n                keywordLocation: `${schemaLocation}/type`,\n                error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type.join('\", \"')}\".`\n            });\n        }\n    }\n    else if ($type === 'integer') {\n        if (instanceType !== 'number' || instance % 1 || instance !== instance) {\n            errors.push({\n                instanceLocation,\n                keyword: 'type',\n                keywordLocation: `${schemaLocation}/type`,\n                error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type}\".`\n            });\n        }\n    }\n    else if ($type !== undefined && instanceType !== $type) {\n        errors.push({\n            instanceLocation,\n            keyword: 'type',\n            keywordLocation: `${schemaLocation}/type`,\n            error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type}\".`\n        });\n    }\n    if ($const !== undefined) {\n        if (instanceType === 'object' || instanceType === 'array') {\n            if (!(0,_deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__.deepCompareStrict)(instance, $const)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'const',\n                    keywordLocation: `${schemaLocation}/const`,\n                    error: `Instance does not match ${JSON.stringify($const)}.`\n                });\n            }\n        }\n        else if (instance !== $const) {\n            errors.push({\n                instanceLocation,\n                keyword: 'const',\n                keywordLocation: `${schemaLocation}/const`,\n                error: `Instance does not match ${JSON.stringify($const)}.`\n            });\n        }\n    }\n    if ($enum !== undefined) {\n        if (instanceType === 'object' || instanceType === 'array') {\n            if (!$enum.some(value => (0,_deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__.deepCompareStrict)(instance, value))) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'enum',\n                    keywordLocation: `${schemaLocation}/enum`,\n                    error: `Instance does not match any of ${JSON.stringify($enum)}.`\n                });\n            }\n        }\n        else if (!$enum.some(value => instance === value)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'enum',\n                keywordLocation: `${schemaLocation}/enum`,\n                error: `Instance does not match any of ${JSON.stringify($enum)}.`\n            });\n        }\n    }\n    if ($not !== undefined) {\n        const keywordLocation = `${schemaLocation}/not`;\n        const result = validate(instance, $not, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation);\n        if (result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: 'not',\n                keywordLocation,\n                error: 'Instance matched \"not\" schema.'\n            });\n        }\n    }\n    let subEvaluateds = [];\n    if ($anyOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/anyOf`;\n        const errorsLength = errors.length;\n        let anyValid = false;\n        for (let i = 0; i < $anyOf.length; i++) {\n            const subSchema = $anyOf[i];\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            anyValid = anyValid || result.valid;\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n        }\n        if (anyValid) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'anyOf',\n                keywordLocation,\n                error: 'Instance does not match any subschemas.'\n            });\n        }\n    }\n    if ($allOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/allOf`;\n        const errorsLength = errors.length;\n        let allValid = true;\n        for (let i = 0; i < $allOf.length; i++) {\n            const subSchema = $allOf[i];\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            allValid = allValid && result.valid;\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n        }\n        if (allValid) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'allOf',\n                keywordLocation,\n                error: `Instance does not match every subschema.`\n            });\n        }\n    }\n    if ($oneOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/oneOf`;\n        const errorsLength = errors.length;\n        const matches = $oneOf.filter((subSchema, i) => {\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n            return result.valid;\n        }).length;\n        if (matches === 1) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'oneOf',\n                keywordLocation,\n                error: `Instance does not match exactly one subschema (${matches} matches).`\n            });\n        }\n    }\n    if (instanceType === 'object' || instanceType === 'array') {\n        Object.assign(evaluated, ...subEvaluateds);\n    }\n    if ($if !== undefined) {\n        const keywordLocation = `${schemaLocation}/if`;\n        const conditionResult = validate(instance, $if, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation, evaluated).valid;\n        if (conditionResult) {\n            if ($then !== undefined) {\n                const thenResult = validate(instance, $then, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${schemaLocation}/then`, evaluated);\n                if (!thenResult.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'if',\n                        keywordLocation,\n                        error: `Instance does not match \"then\" schema.`\n                    }, ...thenResult.errors);\n                }\n            }\n        }\n        else if ($else !== undefined) {\n            const elseResult = validate(instance, $else, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${schemaLocation}/else`, evaluated);\n            if (!elseResult.valid) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'if',\n                    keywordLocation,\n                    error: `Instance does not match \"else\" schema.`\n                }, ...elseResult.errors);\n            }\n        }\n    }\n    if (instanceType === 'object') {\n        if ($required !== undefined) {\n            for (const key of $required) {\n                if (!(key in instance)) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'required',\n                        keywordLocation: `${schemaLocation}/required`,\n                        error: `Instance does not have required property \"${key}\".`\n                    });\n                }\n            }\n        }\n        const keys = Object.keys(instance);\n        if ($minProperties !== undefined && keys.length < $minProperties) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minProperties',\n                keywordLocation: `${schemaLocation}/minProperties`,\n                error: `Instance does not have at least ${$minProperties} properties.`\n            });\n        }\n        if ($maxProperties !== undefined && keys.length > $maxProperties) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxProperties',\n                keywordLocation: `${schemaLocation}/maxProperties`,\n                error: `Instance does not have at least ${$maxProperties} properties.`\n            });\n        }\n        if ($propertyNames !== undefined) {\n            const keywordLocation = `${schemaLocation}/propertyNames`;\n            for (const key in instance) {\n                const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                const result = validate(key, $propertyNames, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                if (!result.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'propertyNames',\n                        keywordLocation,\n                        error: `Property name \"${key}\" does not match schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        if ($dependentRequired !== undefined) {\n            const keywordLocation = `${schemaLocation}/dependantRequired`;\n            for (const key in $dependentRequired) {\n                if (key in instance) {\n                    const required = $dependentRequired[key];\n                    for (const dependantKey of required) {\n                        if (!(dependantKey in instance)) {\n                            errors.push({\n                                instanceLocation,\n                                keyword: 'dependentRequired',\n                                keywordLocation,\n                                error: `Instance has \"${key}\" but does not have \"${dependantKey}\".`\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if ($dependentSchemas !== undefined) {\n            for (const key in $dependentSchemas) {\n                const keywordLocation = `${schemaLocation}/dependentSchemas`;\n                if (key in instance) {\n                    const result = validate(instance, $dependentSchemas[key], draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${keywordLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`, evaluated);\n                    if (!result.valid) {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'dependentSchemas',\n                            keywordLocation,\n                            error: `Instance has \"${key}\" but does not match dependant schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if ($dependencies !== undefined) {\n            const keywordLocation = `${schemaLocation}/dependencies`;\n            for (const key in $dependencies) {\n                if (key in instance) {\n                    const propsOrSchema = $dependencies[key];\n                    if (Array.isArray(propsOrSchema)) {\n                        for (const dependantKey of propsOrSchema) {\n                            if (!(dependantKey in instance)) {\n                                errors.push({\n                                    instanceLocation,\n                                    keyword: 'dependencies',\n                                    keywordLocation,\n                                    error: `Instance has \"${key}\" but does not have \"${dependantKey}\".`\n                                });\n                            }\n                        }\n                    }\n                    else {\n                        const result = validate(instance, propsOrSchema, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${keywordLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`);\n                        if (!result.valid) {\n                            errors.push({\n                                instanceLocation,\n                                keyword: 'dependencies',\n                                keywordLocation,\n                                error: `Instance has \"${key}\" but does not match dependant schema.`\n                            }, ...result.errors);\n                        }\n                    }\n                }\n            }\n        }\n        const thisEvaluated = Object.create(null);\n        let stop = false;\n        if ($properties !== undefined) {\n            const keywordLocation = `${schemaLocation}/properties`;\n            for (const key in $properties) {\n                if (!(key in instance)) {\n                    continue;\n                }\n                const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                const result = validate(instance[key], $properties[key], draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, `${keywordLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`);\n                if (result.valid) {\n                    evaluated[key] = thisEvaluated[key] = true;\n                }\n                else {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'properties',\n                        keywordLocation,\n                        error: `Property \"${key}\" does not match schema.`\n                    }, ...result.errors);\n                    if (stop)\n                        break;\n                }\n            }\n        }\n        if (!stop && $patternProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/patternProperties`;\n            for (const pattern in $patternProperties) {\n                const regex = new RegExp(pattern, 'u');\n                const subSchema = $patternProperties[pattern];\n                for (const key in instance) {\n                    if (!regex.test(key)) {\n                        continue;\n                    }\n                    const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                    const result = validate(instance[key], subSchema, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, `${keywordLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(pattern)}`);\n                    if (result.valid) {\n                        evaluated[key] = thisEvaluated[key] = true;\n                    }\n                    else {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'patternProperties',\n                            keywordLocation,\n                            error: `Property \"${key}\" matches pattern \"${pattern}\" but does not match associated schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if (!stop && $additionalProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/additionalProperties`;\n            for (const key in instance) {\n                if (thisEvaluated[key]) {\n                    continue;\n                }\n                const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                const result = validate(instance[key], $additionalProperties, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                if (result.valid) {\n                    evaluated[key] = true;\n                }\n                else {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'additionalProperties',\n                        keywordLocation,\n                        error: `Property \"${key}\" does not match additional properties schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        else if (!stop && $unevaluatedProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/unevaluatedProperties`;\n            for (const key in instance) {\n                if (!evaluated[key]) {\n                    const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                    const result = validate(instance[key], $unevaluatedProperties, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                    if (result.valid) {\n                        evaluated[key] = true;\n                    }\n                    else {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'unevaluatedProperties',\n                            keywordLocation,\n                            error: `Property \"${key}\" does not match unevaluated properties schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n    }\n    else if (instanceType === 'array') {\n        if ($maxItems !== undefined && instance.length > $maxItems) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxItems',\n                keywordLocation: `${schemaLocation}/maxItems`,\n                error: `Array has too many items (${instance.length} > ${$maxItems}).`\n            });\n        }\n        if ($minItems !== undefined && instance.length < $minItems) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minItems',\n                keywordLocation: `${schemaLocation}/minItems`,\n                error: `Array has too few items (${instance.length} < ${$minItems}).`\n            });\n        }\n        const length = instance.length;\n        let i = 0;\n        let stop = false;\n        if ($prefixItems !== undefined) {\n            const keywordLocation = `${schemaLocation}/prefixItems`;\n            const length2 = Math.min($prefixItems.length, length);\n            for (; i < length2; i++) {\n                const result = validate(instance[i], $prefixItems[i], draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, `${keywordLocation}/${i}`);\n                evaluated[i] = true;\n                if (!result.valid) {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'prefixItems',\n                        keywordLocation,\n                        error: `Items did not match schema.`\n                    }, ...result.errors);\n                    if (stop)\n                        break;\n                }\n            }\n        }\n        if ($items !== undefined) {\n            const keywordLocation = `${schemaLocation}/items`;\n            if (Array.isArray($items)) {\n                const length2 = Math.min($items.length, length);\n                for (; i < length2; i++) {\n                    const result = validate(instance[i], $items[i], draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, `${keywordLocation}/${i}`);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'items',\n                            keywordLocation,\n                            error: `Items did not match schema.`\n                        }, ...result.errors);\n                        if (stop)\n                            break;\n                    }\n                }\n            }\n            else {\n                for (; i < length; i++) {\n                    const result = validate(instance[i], $items, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'items',\n                            keywordLocation,\n                            error: `Items did not match schema.`\n                        }, ...result.errors);\n                        if (stop)\n                            break;\n                    }\n                }\n            }\n            if (!stop && $additionalItems !== undefined) {\n                const keywordLocation = `${schemaLocation}/additionalItems`;\n                for (; i < length; i++) {\n                    const result = validate(instance[i], $additionalItems, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'additionalItems',\n                            keywordLocation,\n                            error: `Items did not match additional items schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if ($contains !== undefined) {\n            if (length === 0 && $minContains === undefined) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'contains',\n                    keywordLocation: `${schemaLocation}/contains`,\n                    error: `Array is empty. It must contain at least one item matching the schema.`\n                });\n            }\n            else if ($minContains !== undefined && length < $minContains) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minContains',\n                    keywordLocation: `${schemaLocation}/minContains`,\n                    error: `Array has less items (${length}) than minContains (${$minContains}).`\n                });\n            }\n            else {\n                const keywordLocation = `${schemaLocation}/contains`;\n                const errorsLength = errors.length;\n                let contained = 0;\n                for (let j = 0; j < length; j++) {\n                    const result = validate(instance[j], $contains, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${j}`, keywordLocation);\n                    if (result.valid) {\n                        evaluated[j] = true;\n                        contained++;\n                    }\n                    else {\n                        errors.push(...result.errors);\n                    }\n                }\n                if (contained >= ($minContains || 0)) {\n                    errors.length = errorsLength;\n                }\n                if ($minContains === undefined &&\n                    $maxContains === undefined &&\n                    contained === 0) {\n                    errors.splice(errorsLength, 0, {\n                        instanceLocation,\n                        keyword: 'contains',\n                        keywordLocation,\n                        error: `Array does not contain item matching schema.`\n                    });\n                }\n                else if ($minContains !== undefined && contained < $minContains) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'minContains',\n                        keywordLocation: `${schemaLocation}/minContains`,\n                        error: `Array must contain at least ${$minContains} items matching schema. Only ${contained} items were found.`\n                    });\n                }\n                else if ($maxContains !== undefined && contained > $maxContains) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'maxContains',\n                        keywordLocation: `${schemaLocation}/maxContains`,\n                        error: `Array may contain at most ${$maxContains} items matching schema. ${contained} items were found.`\n                    });\n                }\n            }\n        }\n        if (!stop && $unevaluatedItems !== undefined) {\n            const keywordLocation = `${schemaLocation}/unevaluatedItems`;\n            for (i; i < length; i++) {\n                if (evaluated[i]) {\n                    continue;\n                }\n                const result = validate(instance[i], $unevaluatedItems, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                evaluated[i] = true;\n                if (!result.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'unevaluatedItems',\n                        keywordLocation,\n                        error: `Items did not match unevaluated items schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        if ($uniqueItems) {\n            for (let j = 0; j < length; j++) {\n                const a = instance[j];\n                const ao = typeof a === 'object' && a !== null;\n                for (let k = 0; k < length; k++) {\n                    if (j === k) {\n                        continue;\n                    }\n                    const b = instance[k];\n                    const bo = typeof b === 'object' && b !== null;\n                    if (a === b || (ao && bo && (0,_deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__.deepCompareStrict)(a, b))) {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'uniqueItems',\n                            keywordLocation: `${schemaLocation}/uniqueItems`,\n                            error: `Duplicate items at indexes ${j} and ${k}.`\n                        });\n                        j = Number.MAX_SAFE_INTEGER;\n                        k = Number.MAX_SAFE_INTEGER;\n                    }\n                }\n            }\n        }\n    }\n    else if (instanceType === 'number') {\n        if (draft === '4') {\n            if ($minimum !== undefined &&\n                (($exclusiveMinimum === true && instance <= $minimum) ||\n                    instance < $minimum)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minimum',\n                    keywordLocation: `${schemaLocation}/minimum`,\n                    error: `${instance} is less than ${$exclusiveMinimum ? 'or equal to ' : ''} ${$minimum}.`\n                });\n            }\n            if ($maximum !== undefined &&\n                (($exclusiveMaximum === true && instance >= $maximum) ||\n                    instance > $maximum)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'maximum',\n                    keywordLocation: `${schemaLocation}/maximum`,\n                    error: `${instance} is greater than ${$exclusiveMaximum ? 'or equal to ' : ''} ${$maximum}.`\n                });\n            }\n        }\n        else {\n            if ($minimum !== undefined && instance < $minimum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minimum',\n                    keywordLocation: `${schemaLocation}/minimum`,\n                    error: `${instance} is less than ${$minimum}.`\n                });\n            }\n            if ($maximum !== undefined && instance > $maximum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'maximum',\n                    keywordLocation: `${schemaLocation}/maximum`,\n                    error: `${instance} is greater than ${$maximum}.`\n                });\n            }\n            if ($exclusiveMinimum !== undefined && instance <= $exclusiveMinimum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'exclusiveMinimum',\n                    keywordLocation: `${schemaLocation}/exclusiveMinimum`,\n                    error: `${instance} is less than ${$exclusiveMinimum}.`\n                });\n            }\n            if ($exclusiveMaximum !== undefined && instance >= $exclusiveMaximum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'exclusiveMaximum',\n                    keywordLocation: `${schemaLocation}/exclusiveMaximum`,\n                    error: `${instance} is greater than or equal to ${$exclusiveMaximum}.`\n                });\n            }\n        }\n        if ($multipleOf !== undefined) {\n            const remainder = instance % $multipleOf;\n            if (Math.abs(0 - remainder) >= 1.1920929e-7 &&\n                Math.abs($multipleOf - remainder) >= 1.1920929e-7) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'multipleOf',\n                    keywordLocation: `${schemaLocation}/multipleOf`,\n                    error: `${instance} is not a multiple of ${$multipleOf}.`\n                });\n            }\n        }\n    }\n    else if (instanceType === 'string') {\n        const length = $minLength === undefined && $maxLength === undefined\n            ? 0\n            : (0,_ucs2_length_js__WEBPACK_IMPORTED_MODULE_4__.ucs2length)(instance);\n        if ($minLength !== undefined && length < $minLength) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minLength',\n                keywordLocation: `${schemaLocation}/minLength`,\n                error: `String is too short (${length} < ${$minLength}).`\n            });\n        }\n        if ($maxLength !== undefined && length > $maxLength) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxLength',\n                keywordLocation: `${schemaLocation}/maxLength`,\n                error: `String is too long (${length} > ${$maxLength}).`\n            });\n        }\n        if ($pattern !== undefined && !new RegExp($pattern, 'u').test(instance)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'pattern',\n                keywordLocation: `${schemaLocation}/pattern`,\n                error: `String does not match pattern.`\n            });\n        }\n        if ($format !== undefined &&\n            _format_js__WEBPACK_IMPORTED_MODULE_2__.format[$format] &&\n            !_format_js__WEBPACK_IMPORTED_MODULE_2__.format[$format](instance)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'format',\n                keywordLocation: `${schemaLocation}/format`,\n                error: `String does not match format \"${$format}\".`\n            });\n        }\n    }\n    return { valid: errors.length === 0, errors };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validator.js":
/*!******************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/validator.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Validator: () => (/* binding */ Validator)\n/* harmony export */ });\n/* harmony import */ var _dereference_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js\");\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validate.js\");\n\n\nclass Validator {\n    schema;\n    draft;\n    shortCircuit;\n    lookup;\n    constructor(schema, draft = '2019-09', shortCircuit = true) {\n        this.schema = schema;\n        this.draft = draft;\n        this.shortCircuit = shortCircuit;\n        this.lookup = (0,_dereference_js__WEBPACK_IMPORTED_MODULE_0__.dereference)(schema);\n    }\n    validate(instance) {\n        return (0,_validate_js__WEBPACK_IMPORTED_MODULE_1__.validate)(instance, this.schema, this.draft, this.lookup, this.shortCircuit);\n    }\n    addSchema(schema, id) {\n        if (id) {\n            schema = { ...schema, $id: id };\n        }\n        (0,_dereference_js__WEBPACK_IMPORTED_MODULE_0__.dereference)(schema, this.lookup);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL3ZhbGlkYXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDTjtBQUNsQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNERBQVc7QUFDakM7QUFDQTtBQUNBLGVBQWUsc0RBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0EsUUFBUSw0REFBVztBQUNuQjtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBjZndvcmtlclxcanNvbi1zY2hlbWFcXGRpc3RcXGVzbVxcdmFsaWRhdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlcmVmZXJlbmNlIH0gZnJvbSAnLi9kZXJlZmVyZW5jZS5qcyc7XG5pbXBvcnQgeyB2YWxpZGF0ZSB9IGZyb20gJy4vdmFsaWRhdGUuanMnO1xuZXhwb3J0IGNsYXNzIFZhbGlkYXRvciB7XG4gICAgc2NoZW1hO1xuICAgIGRyYWZ0O1xuICAgIHNob3J0Q2lyY3VpdDtcbiAgICBsb29rdXA7XG4gICAgY29uc3RydWN0b3Ioc2NoZW1hLCBkcmFmdCA9ICcyMDE5LTA5Jywgc2hvcnRDaXJjdWl0ID0gdHJ1ZSkge1xuICAgICAgICB0aGlzLnNjaGVtYSA9IHNjaGVtYTtcbiAgICAgICAgdGhpcy5kcmFmdCA9IGRyYWZ0O1xuICAgICAgICB0aGlzLnNob3J0Q2lyY3VpdCA9IHNob3J0Q2lyY3VpdDtcbiAgICAgICAgdGhpcy5sb29rdXAgPSBkZXJlZmVyZW5jZShzY2hlbWEpO1xuICAgIH1cbiAgICB2YWxpZGF0ZShpbnN0YW5jZSkge1xuICAgICAgICByZXR1cm4gdmFsaWRhdGUoaW5zdGFuY2UsIHRoaXMuc2NoZW1hLCB0aGlzLmRyYWZ0LCB0aGlzLmxvb2t1cCwgdGhpcy5zaG9ydENpcmN1aXQpO1xuICAgIH1cbiAgICBhZGRTY2hlbWEoc2NoZW1hLCBpZCkge1xuICAgICAgICBpZiAoaWQpIHtcbiAgICAgICAgICAgIHNjaGVtYSA9IHsgLi4uc2NoZW1hLCAkaWQ6IGlkIH07XG4gICAgICAgIH1cbiAgICAgICAgZGVyZWZlcmVuY2Uoc2NoZW1hLCB0aGlzLmxvb2t1cCk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validator.js\n");

/***/ })

};
;