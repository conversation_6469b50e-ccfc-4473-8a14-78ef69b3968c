import os
import asyncio
import json
import logging
from typing import Literal, Optional
from typing_extensions import Literal
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, AIMessage,HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
from langgraph.prebuilt import Tool<PERSON><PERSON>
from copilotkit import CopilotKitState
from apis import ZinniaXAPI
from langchain_ollama import ChatOllama
from tools import get_schedule_details, get_cancel_details_id, reschedule_case, uncancel_case, validate_schedule_details,validate_cancel_details

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Configuration ---
STAGE_URL = os.environ.get("STAGE_URL")
API_USER_EMAIL = os.environ.get("API_USER_EMAIL")
API_USER_PASSWORD = os.environ.get("API_USER_PASSWORD")


class AgentState(CopilotKitState):
    """
    Here we define the state of the scheduling agent

    In this instance, we're inheriting from CopilotKitState, which will bring in
    the CopilotKitState fields. We're also adding custom fields for scheduling workflow.
    """
    confirmation_needed: bool = False
    deferred_tool_call: Optional[dict] = None
    awaiting_user_response: bool = False


# List of all tools
critical_tools = [
    'get_cancel_details_id',
    'reschedule_case',
    'uncancel_case',
    'get_schedule_details'
]
tools = [
    get_schedule_details,
    get_cancel_details_id,
    reschedule_case,
    uncancel_case
]

def is_critical_tool_call(tool_call):
    """Checks if a tool call is for a critical action that requires confirmation."""
    return tool_call.get("name") in critical_tools

async def chat_node(state: AgentState, config: RunnableConfig) -> dict:
    """
    Main chat node for the agent. It invokes the model and initiates the confirmation workflow for critical tools.
    """
    model = ChatOllama(model="llama3-groq-tool-use", base_url='https://partially-model-boa.ngrok-free.app')
    model_with_tools = model.bind_tools(
        [*state["copilotkit"]["actions"], *tools],
    )
    confirmation_message = None
    system_message = SystemMessage(
        content=(
            "You are ZinniaX Copilot, a professional assistant for scheduling medical cases. "
            "Always use the provided tools for actions. For critical actions like scheduling, canceling, or rescheduling, "
            "you must first present the details to the user and ask for their confirmation before proceeding. "
            "Collect information step-by-step and validate names against the system."
        )
    )

    response = await model_with_tools.ainvoke([system_message, *state["messages"]], config)
    print('#'*50)
    print('chat_node - states', json.dumps(state, default=str, indent=2))
    print('#'*50)
    
    # Check if the response contains tool calls
    if isinstance(response, AIMessage) and response.tool_calls:
        tool_call = response.tool_calls[0]
        print(f'Tool call detected: {tool_call["name"]}')
        
        # For critical tools, always ask for confirmation first
        if is_critical_tool_call(tool_call):
            """Generate a specific confirmation message based on the tool call."""
            args = tool_call.get("args", {})
            
            tool_name = tool_call["name"]
            print("ARGS",args)
            if tool_name == "get_schedule_details":
                flag, details = await validate_schedule_details(
                    args.get('surgeon'),
                    args.get('hospital'),
                    args.get('date'),
                    args.get('time'),
                    args.get('procedure'),
                    args.get('patientName'),
                    args.get('city')
                )
                if flag == False:
                    return {
                        "messages": [AIMessage(content=details)],
                        "confirmation_needed": False,
                        "deferred_tool_call": None,
                        "awaiting_user_response": False
                    }
                if flag == True:
                    tool_call["args"] = details
                    confirmation_message = await generate_confirmation_message(tool_call)
            elif tool_name == "get_cancel_details_id":
                flag, details = await validate_cancel_details(args.get('caseId'))
                if flag == False:
                    return {
                        "messages": [AIMessage(content=details)],
                        "confirmation_needed": False,
                        "deferred_tool_call": None,
                        "awaiting_user_response": False
                    }
                if flag == True:
                    tool_call["args"] = details
                    confirmation_message = await generate_confirmation_message(tool_call)
            return {
                "messages": [AIMessage(content=confirmation_message)],
                "confirmation_needed": True,
                "deferred_tool_call": tool_call,
                "awaiting_user_response": True
            }
        else:
            # For non-critical tools, proceed normally
            return {"messages": [response]}
    
    return {"messages": [response]}

async def generate_confirmation_message(tool_call):
    """Generate a specific confirmation message based on the tool call."""
    args = tool_call.get("args", {})
    tool_name = tool_call["name"]
    print("ARGS",args)
    if tool_name == "get_schedule_details":
        return (
            f"Please confirm: Schedule new case for patient '{args.get('patientName', 'N/A')}' "
            f"with surgeon '{args.get('surgeon')}' at '{args.get('hospital')}' on {args.get('date')} "
            f"at {args.get('time')}. Is this correct? (Type 'yes' to confirm or 'no' to cancel)"
        )
    elif tool_name in ["get_cancel_details_id", "reschedule_case", "uncancel_case"]:
        case_id = args.get("caseId")
        try:
            async with ZinniaXAPI() as api:
                case_details = await api.fetch_case_details(case_id)
            
            if case_details:
                if tool_name == "get_cancel_details_id":
                    return f"Are you sure you want to cancel case #{case_id} for patient '{case_details['patientName']}' with surgeon '{case_details['surgeonName']}'? (Type 'yes' to confirm or 'no' to cancel)"
                elif tool_name == "reschedule_case":
                    return f"Are you sure you want to reschedule case #{case_id} for patient '{case_details['patientName']}' to {args.get('newDate')} at {args.get('newTime')}? (Type 'yes' to confirm or 'no' to cancel)"
                elif tool_name == "uncancel_case":
                    return f"Are you sure you want to un-cancel case #{case_id} for patient '{case_details['patientName']}'? (Type 'yes' to confirm or 'no' to cancel)"
            else:
                return f"I couldn't retrieve the details for case #{case_id}. Are you sure you want to proceed with this action? (Type 'yes' to confirm or 'no' to cancel)"
        except Exception as e:
            logger.error(f"Error fetching case details: {e}")
            return f"Error retrieving case details. Are you sure you want to proceed? (Type 'yes' to confirm or 'no' to cancel)"
    
    return "Are you sure you want to proceed? (Type 'yes' to confirm or 'no' to cancel)"

async def confirmation_node(state: AgentState, config: RunnableConfig) -> dict:
    """
    Handles user confirmation. If confirmed, executes the tool call. If denied, cancels the operation.
    """
    print('*'*60)
    print('confirmation_node - state:', json.dumps(state, default=str, indent=2))
    print('*'*60)
    
    if not state.get("messages"):
        logger.error("No messages in state for confirmation node")
        return {
            "messages": [AIMessage(content="Error: No user response received.")],
            "confirmation_needed": False,
            "deferred_tool_call": None,
            "awaiting_user_response": False
        }

    last_message = state["messages"][-1]
    if not isinstance(last_message, HumanMessage):
        logger.error("Last message is not from user")
        return {
            "messages": [AIMessage(content="Please respond with 'yes' to confirm or 'no' to cancel.")],
            "confirmation_needed": True,
            "awaiting_user_response": True
        }
    
    user_response = last_message.content.lower().strip()
    logger.info(f"User response: '{user_response}'")
    
    # Check for confirmation
    if any(word in user_response for word in ["yes", "confirm", "proceed", "correct", "do it", "y"]):
        logger.info("User confirmed action. Executing the deferred tool call.")
        
        deferred_tool_call = state.get("deferred_tool_call")
        if not deferred_tool_call:
            return {
                "messages": [AIMessage(content="Error: No action to confirm.")],
                "confirmation_needed": False,
                "awaiting_user_response": False
            }
        
        # Execute the confirmed tool call
        try:
            result = await execute_tool_call(deferred_tool_call)
            return {
                "messages": [AIMessage(content=result)],
                "confirmation_needed": False,
                "deferred_tool_call": None,
                "awaiting_user_response": False
            }
        except Exception as e:
            logger.error(f"Error executing tool call: {e}")
            return {
                "messages": [AIMessage(content=f"Error executing action: {str(e)}")],
                "confirmation_needed": False,
                "deferred_tool_call": None,
                "awaiting_user_response": False
            }
    
    # Check for denial
    elif any(word in user_response for word in ["no", "cancel", "abort", "stop", "n"]):
        logger.info("User declined action. Cancelling the operation.")
        return {
            "messages": [AIMessage(content="Okay, I have cancelled the operation. How else can I help you?")],
            "confirmation_needed": False,
            "deferred_tool_call": None,
            "awaiting_user_response": False
        }
    
    # Unclear response - ask again
    else:
        logger.info("User response unclear. Asking for clarification.")
        return {
            "messages": [AIMessage(content="Please respond with 'yes' to confirm the action or 'no' to cancel it.")],
            "confirmation_needed": True,
            "awaiting_user_response": True
        }

async def execute_tool_call(tool_call):
    """Execute a tool call and return the result."""
    async with ZinniaXAPI() as api:
        tool_name = tool_call["name"]
        args = tool_call.get("args", {})
        
        if tool_name == "get_schedule_details":
            
                details = {
                    'surgeon': args['surgeon'],
                    'date': args['date'],
                    'time': args['time'],
                    'hospital': args['hospital'],
                    'procedure': args.get('procedure', ''),
                    'patientName': args.get('patientName', ''),
                    'city': args.get('city', '')
                }
                success, result = await api.schedule_new_case(details)
                if success:
                    return f"Successfully scheduled new case with ID: {result}."
                else:
                    return f"Failed to schedule case: {result}"
        
        elif tool_name == "get_cancel_details_id":
            print("FINALLY CALLING CANCELLATION FUNCTION")
            details = args['caseId']
               
            success, result = await api.cancel_case(details)
            if success:
                return f"Successfully Cancelled case with ID: {result}."
            else:
                return f"Failed to cancel case: {result}"
        
        elif tool_name == "reschedule_case":
            return reschedule_case.invoke(args)
        
        elif tool_name == "uncancel_case":
            return uncancel_case.invoke(args)
        
        else:
            return f"Unknown tool: {tool_name}"

# --- Define the workflow graph ---
workflow = StateGraph(AgentState)

# Add nodes to the graph
workflow.add_node("chat_node", chat_node)
workflow.add_node("tool_node", ToolNode(tools=tools))
workflow.add_node("confirmation_node", confirmation_node)

# Define the entry point router
def route_initial(state: AgentState) -> str:
    """Determine the first node to run."""
    if state.get("confirmation_needed"):
        return "confirmation_node"
    return "chat_node"

workflow.set_conditional_entry_point(
    route_initial,
    {
        "confirmation_node": "confirmation_node",
        "chat_node": "chat_node",
    },
)

# Define the edges
def after_chat(state: AgentState) -> str:
    """Decide where to go after the chat_node."""
    last_message = state["messages"][-1]
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        # If the model chose a critical tool, we asked for confirmation.
        # The confirmation_needed flag is set. We should end the turn to get user input.
        if state.get("confirmation_needed"):
            return "__end__"
        # Otherwise, it's a non-critical tool that can run without confirmation.
        return "tool_node"
    # No tool call, so end the turn.
    return "__end__"

workflow.add_conditional_edges(
    "chat_node",
    after_chat,
    {
        "tool_node": "tool_node",
        "__end__": "__end__",
    },
)

# After confirmation, the action is done. Go back to the chat node to report the result.
workflow.add_edge("confirmation_node", "__end__")
# After a non-critical tool is run, go back to the chat node to report the result.
workflow.add_edge("tool_node", "chat_node")


# Compile the graph
is_langgraph_api = (
    os.environ.get("LANGGRAPH_API", "false").lower() == "true" or
    os.environ.get("LANGGRAPH_API_DIR") is not None
)

if is_langgraph_api:
    graph = workflow.compile()
else:
    from langgraph.checkpoint.memory import MemorySaver
    memory = MemorySaver()
    graph = workflow.compile(checkpointer=memory, interrupt_after=['tool_node'])





# --- FastAPI and CopilotKit Setup ---

import uvicorn
from fastapi import FastAPI
from copilotkit import CopilotKitRemoteEndpoint, LangGraphAgent
from copilotkit.integrations.fastapi import add_fastapi_endpoint


# load_dotenv()

app = FastAPI()

sdk = CopilotKitRemoteEndpoint(
    agents=[
        LangGraphAgent(
            name="scheduling_agent",
            description="A professional assistant for scheduling, canceling, and managing medical cases.",
            graph=graph,  # The graph object from your langgraph agent
        )
    ],
)

# Use CopilotKit's FastAPI integration to add a new endpoint for your LangGraph agents
add_fastapi_endpoint(app, sdk, "/copilotkit", use_thread_pool=False)

# Add a health check route
@app.get("/health")
def health():
    """Health check."""
    return {"status": "ok"}

def main():
    """Run the uvicorn server."""
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(
        "main:app",  # Path to your FastAPI file
        host="0.0.0.0",
        port=port,
        reload=True,
    )

if __name__ == "__main__":
    main()