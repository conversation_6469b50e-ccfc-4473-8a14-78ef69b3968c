/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-xml-parser";
exports.ids = ["vendor-chunks/fast-xml-parser"];
exports.modules = {

/***/ "(rsc)/./node_modules/fast-xml-parser/src/fxp.js":
/*!*************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/fxp.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst validator = __webpack_require__(/*! ./validator */ \"(rsc)/./node_modules/fast-xml-parser/src/validator.js\");\nconst XMLParser = __webpack_require__(/*! ./xmlparser/XMLParser */ \"(rsc)/./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js\");\nconst XMLBuilder = __webpack_require__(/*! ./xmlbuilder/json2xml */ \"(rsc)/./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js\");\n\nmodule.exports = {\n  XMLParser: XMLParser,\n  XMLValidator: validator,\n  XMLBuilder: XMLBuilder\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmFzdC14bWwtcGFyc2VyL3NyYy9meHAuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsa0JBQWtCLG1CQUFPLENBQUMsMEVBQWE7QUFDdkMsa0JBQWtCLG1CQUFPLENBQUMsOEZBQXVCO0FBQ2pELG1CQUFtQixtQkFBTyxDQUFDLDhGQUF1Qjs7QUFFbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxmYXN0LXhtbC1wYXJzZXJcXHNyY1xcZnhwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgdmFsaWRhdG9yID0gcmVxdWlyZSgnLi92YWxpZGF0b3InKTtcbmNvbnN0IFhNTFBhcnNlciA9IHJlcXVpcmUoJy4veG1scGFyc2VyL1hNTFBhcnNlcicpO1xuY29uc3QgWE1MQnVpbGRlciA9IHJlcXVpcmUoJy4veG1sYnVpbGRlci9qc29uMnhtbCcpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgWE1MUGFyc2VyOiBYTUxQYXJzZXIsXG4gIFhNTFZhbGlkYXRvcjogdmFsaWRhdG9yLFxuICBYTUxCdWlsZGVyOiBYTUxCdWlsZGVyXG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/fxp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/util.js":
/*!**************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/util.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nconst nameStartChar = ':A-Za-z_\\\\u00C0-\\\\u00D6\\\\u00D8-\\\\u00F6\\\\u00F8-\\\\u02FF\\\\u0370-\\\\u037D\\\\u037F-\\\\u1FFF\\\\u200C-\\\\u200D\\\\u2070-\\\\u218F\\\\u2C00-\\\\u2FEF\\\\u3001-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFFD';\nconst nameChar = nameStartChar + '\\\\-.\\\\d\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040';\nconst nameRegexp = '[' + nameStartChar + '][' + nameChar + ']*'\nconst regexName = new RegExp('^' + nameRegexp + '$');\n\nconst getAllMatches = function(string, regex) {\n  const matches = [];\n  let match = regex.exec(string);\n  while (match) {\n    const allmatches = [];\n    allmatches.startIndex = regex.lastIndex - match[0].length;\n    const len = match.length;\n    for (let index = 0; index < len; index++) {\n      allmatches.push(match[index]);\n    }\n    matches.push(allmatches);\n    match = regex.exec(string);\n  }\n  return matches;\n};\n\nconst isName = function(string) {\n  const match = regexName.exec(string);\n  return !(match === null || typeof match === 'undefined');\n};\n\nexports.isExist = function(v) {\n  return typeof v !== 'undefined';\n};\n\nexports.isEmptyObject = function(obj) {\n  return Object.keys(obj).length === 0;\n};\n\n/**\n * Copy all the properties of a into b.\n * @param {*} target\n * @param {*} a\n */\nexports.merge = function(target, a, arrayMode) {\n  if (a) {\n    const keys = Object.keys(a); // will return an array of own properties\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      if (arrayMode === 'strict') {\n        target[keys[i]] = [ a[keys[i]] ];\n      } else {\n        target[keys[i]] = a[keys[i]];\n      }\n    }\n  }\n};\n/* exports.merge =function (b,a){\n  return Object.assign(b,a);\n} */\n\nexports.getValue = function(v) {\n  if (exports.isExist(v)) {\n    return v;\n  } else {\n    return '';\n  }\n};\n\n// const fakeCall = function(a) {return a;};\n// const fakeCallNoReturn = function() {};\n\nexports.isName = isName;\nexports.getAllMatches = getAllMatches;\nexports.nameRegexp = nameRegexp;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/validator.js":
/*!*******************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/validator.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nconst util = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/fast-xml-parser/src/util.js\");\n\nconst defaultOptions = {\n  allowBooleanAttributes: false, //A tag can have attributes without any value\n  unpairedTags: []\n};\n\n//const tagsPattern = new RegExp(\"<\\\\/?([\\\\w:\\\\-_\\.]+)\\\\s*\\/?>\",\"g\");\nexports.validate = function (xmlData, options) {\n  options = Object.assign({}, defaultOptions, options);\n\n  //xmlData = xmlData.replace(/(\\r\\n|\\n|\\r)/gm,\"\");//make it single line\n  //xmlData = xmlData.replace(/(^\\s*<\\?xml.*?\\?>)/g,\"\");//Remove XML starting tag\n  //xmlData = xmlData.replace(/(<!DOCTYPE[\\s\\w\\\"\\.\\/\\-\\:]+(\\[.*\\])*\\s*>)/g,\"\");//Remove DOCTYPE\n  const tags = [];\n  let tagFound = false;\n\n  //indicates that the root tag has been closed (aka. depth 0 has been reached)\n  let reachedRoot = false;\n\n  if (xmlData[0] === '\\ufeff') {\n    // check for byte order mark (BOM)\n    xmlData = xmlData.substr(1);\n  }\n  \n  for (let i = 0; i < xmlData.length; i++) {\n\n    if (xmlData[i] === '<' && xmlData[i+1] === '?') {\n      i+=2;\n      i = readPI(xmlData,i);\n      if (i.err) return i;\n    }else if (xmlData[i] === '<') {\n      //starting of tag\n      //read until you reach to '>' avoiding any '>' in attribute value\n      let tagStartPos = i;\n      i++;\n      \n      if (xmlData[i] === '!') {\n        i = readCommentAndCDATA(xmlData, i);\n        continue;\n      } else {\n        let closingTag = false;\n        if (xmlData[i] === '/') {\n          //closing tag\n          closingTag = true;\n          i++;\n        }\n        //read tagname\n        let tagName = '';\n        for (; i < xmlData.length &&\n          xmlData[i] !== '>' &&\n          xmlData[i] !== ' ' &&\n          xmlData[i] !== '\\t' &&\n          xmlData[i] !== '\\n' &&\n          xmlData[i] !== '\\r'; i++\n        ) {\n          tagName += xmlData[i];\n        }\n        tagName = tagName.trim();\n        //console.log(tagName);\n\n        if (tagName[tagName.length - 1] === '/') {\n          //self closing tag without attributes\n          tagName = tagName.substring(0, tagName.length - 1);\n          //continue;\n          i--;\n        }\n        if (!validateTagName(tagName)) {\n          let msg;\n          if (tagName.trim().length === 0) {\n            msg = \"Invalid space after '<'.\";\n          } else {\n            msg = \"Tag '\"+tagName+\"' is an invalid name.\";\n          }\n          return getErrorObject('InvalidTag', msg, getLineNumberForPosition(xmlData, i));\n        }\n\n        const result = readAttributeStr(xmlData, i);\n        if (result === false) {\n          return getErrorObject('InvalidAttr', \"Attributes for '\"+tagName+\"' have open quote.\", getLineNumberForPosition(xmlData, i));\n        }\n        let attrStr = result.value;\n        i = result.index;\n\n        if (attrStr[attrStr.length - 1] === '/') {\n          //self closing tag\n          const attrStrStart = i - attrStr.length;\n          attrStr = attrStr.substring(0, attrStr.length - 1);\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid === true) {\n            tagFound = true;\n            //continue; //text may presents after self closing tag\n          } else {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, attrStrStart + isValid.err.line));\n          }\n        } else if (closingTag) {\n          if (!result.tagClosed) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' doesn't have proper closing.\", getLineNumberForPosition(xmlData, i));\n          } else if (attrStr.trim().length > 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' can't have attributes or invalid starting.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else if (tags.length === 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' has not been opened.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else {\n            const otg = tags.pop();\n            if (tagName !== otg.tagName) {\n              let openPos = getLineNumberForPosition(xmlData, otg.tagStartPos);\n              return getErrorObject('InvalidTag',\n                \"Expected closing tag '\"+otg.tagName+\"' (opened in line \"+openPos.line+\", col \"+openPos.col+\") instead of closing tag '\"+tagName+\"'.\",\n                getLineNumberForPosition(xmlData, tagStartPos));\n            }\n\n            //when there are no more tags, we reached the root level.\n            if (tags.length == 0) {\n              reachedRoot = true;\n            }\n          }\n        } else {\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid !== true) {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, i - attrStr.length + isValid.err.line));\n          }\n\n          //if the root level has been reached before ...\n          if (reachedRoot === true) {\n            return getErrorObject('InvalidXml', 'Multiple possible root nodes found.', getLineNumberForPosition(xmlData, i));\n          } else if(options.unpairedTags.indexOf(tagName) !== -1){\n            //don't push into stack\n          } else {\n            tags.push({tagName, tagStartPos});\n          }\n          tagFound = true;\n        }\n\n        //skip tag text value\n        //It may include comments and CDATA value\n        for (i++; i < xmlData.length; i++) {\n          if (xmlData[i] === '<') {\n            if (xmlData[i + 1] === '!') {\n              //comment or CADATA\n              i++;\n              i = readCommentAndCDATA(xmlData, i);\n              continue;\n            } else if (xmlData[i+1] === '?') {\n              i = readPI(xmlData, ++i);\n              if (i.err) return i;\n            } else{\n              break;\n            }\n          } else if (xmlData[i] === '&') {\n            const afterAmp = validateAmpersand(xmlData, i);\n            if (afterAmp == -1)\n              return getErrorObject('InvalidChar', \"char '&' is not expected.\", getLineNumberForPosition(xmlData, i));\n            i = afterAmp;\n          }else{\n            if (reachedRoot === true && !isWhiteSpace(xmlData[i])) {\n              return getErrorObject('InvalidXml', \"Extra text at the end\", getLineNumberForPosition(xmlData, i));\n            }\n          }\n        } //end of reading tag text value\n        if (xmlData[i] === '<') {\n          i--;\n        }\n      }\n    } else {\n      if ( isWhiteSpace(xmlData[i])) {\n        continue;\n      }\n      return getErrorObject('InvalidChar', \"char '\"+xmlData[i]+\"' is not expected.\", getLineNumberForPosition(xmlData, i));\n    }\n  }\n\n  if (!tagFound) {\n    return getErrorObject('InvalidXml', 'Start tag expected.', 1);\n  }else if (tags.length == 1) {\n      return getErrorObject('InvalidTag', \"Unclosed tag '\"+tags[0].tagName+\"'.\", getLineNumberForPosition(xmlData, tags[0].tagStartPos));\n  }else if (tags.length > 0) {\n      return getErrorObject('InvalidXml', \"Invalid '\"+\n          JSON.stringify(tags.map(t => t.tagName), null, 4).replace(/\\r?\\n/g, '')+\n          \"' found.\", {line: 1, col: 1});\n  }\n\n  return true;\n};\n\nfunction isWhiteSpace(char){\n  return char === ' ' || char === '\\t' || char === '\\n'  || char === '\\r';\n}\n/**\n * Read Processing insstructions and skip\n * @param {*} xmlData\n * @param {*} i\n */\nfunction readPI(xmlData, i) {\n  const start = i;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] == '?' || xmlData[i] == ' ') {\n      //tagname\n      const tagname = xmlData.substr(start, i - start);\n      if (i > 5 && tagname === 'xml') {\n        return getErrorObject('InvalidXml', 'XML declaration allowed only at the start of the document.', getLineNumberForPosition(xmlData, i));\n      } else if (xmlData[i] == '?' && xmlData[i + 1] == '>') {\n        //check if valid attribut string\n        i++;\n        break;\n      } else {\n        continue;\n      }\n    }\n  }\n  return i;\n}\n\nfunction readCommentAndCDATA(xmlData, i) {\n  if (xmlData.length > i + 5 && xmlData[i + 1] === '-' && xmlData[i + 2] === '-') {\n    //comment\n    for (i += 3; i < xmlData.length; i++) {\n      if (xmlData[i] === '-' && xmlData[i + 1] === '-' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  } else if (\n    xmlData.length > i + 8 &&\n    xmlData[i + 1] === 'D' &&\n    xmlData[i + 2] === 'O' &&\n    xmlData[i + 3] === 'C' &&\n    xmlData[i + 4] === 'T' &&\n    xmlData[i + 5] === 'Y' &&\n    xmlData[i + 6] === 'P' &&\n    xmlData[i + 7] === 'E'\n  ) {\n    let angleBracketsCount = 1;\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === '<') {\n        angleBracketsCount++;\n      } else if (xmlData[i] === '>') {\n        angleBracketsCount--;\n        if (angleBracketsCount === 0) {\n          break;\n        }\n      }\n    }\n  } else if (\n    xmlData.length > i + 9 &&\n    xmlData[i + 1] === '[' &&\n    xmlData[i + 2] === 'C' &&\n    xmlData[i + 3] === 'D' &&\n    xmlData[i + 4] === 'A' &&\n    xmlData[i + 5] === 'T' &&\n    xmlData[i + 6] === 'A' &&\n    xmlData[i + 7] === '['\n  ) {\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === ']' && xmlData[i + 1] === ']' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  }\n\n  return i;\n}\n\nconst doubleQuote = '\"';\nconst singleQuote = \"'\";\n\n/**\n * Keep reading xmlData until '<' is found outside the attribute value.\n * @param {string} xmlData\n * @param {number} i\n */\nfunction readAttributeStr(xmlData, i) {\n  let attrStr = '';\n  let startChar = '';\n  let tagClosed = false;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === doubleQuote || xmlData[i] === singleQuote) {\n      if (startChar === '') {\n        startChar = xmlData[i];\n      } else if (startChar !== xmlData[i]) {\n        //if vaue is enclosed with double quote then single quotes are allowed inside the value and vice versa\n      } else {\n        startChar = '';\n      }\n    } else if (xmlData[i] === '>') {\n      if (startChar === '') {\n        tagClosed = true;\n        break;\n      }\n    }\n    attrStr += xmlData[i];\n  }\n  if (startChar !== '') {\n    return false;\n  }\n\n  return {\n    value: attrStr,\n    index: i,\n    tagClosed: tagClosed\n  };\n}\n\n/**\n * Select all the attributes whether valid or invalid.\n */\nconst validAttrStrRegxp = new RegExp('(\\\\s*)([^\\\\s=]+)(\\\\s*=)?(\\\\s*([\\'\"])(([\\\\s\\\\S])*?)\\\\5)?', 'g');\n\n//attr, =\"sd\", a=\"amit's\", a=\"sd\"b=\"saf\", ab  cd=\"\"\n\nfunction validateAttributeString(attrStr, options) {\n  //console.log(\"start:\"+attrStr+\":end\");\n\n  //if(attrStr.trim().length === 0) return true; //empty string\n\n  const matches = util.getAllMatches(attrStr, validAttrStrRegxp);\n  const attrNames = {};\n\n  for (let i = 0; i < matches.length; i++) {\n    if (matches[i][1].length === 0) {\n      //nospace before attribute name: a=\"sd\"b=\"saf\"\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' has no space in starting.\", getPositionFromMatch(matches[i]))\n    } else if (matches[i][3] !== undefined && matches[i][4] === undefined) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' is without value.\", getPositionFromMatch(matches[i]));\n    } else if (matches[i][3] === undefined && !options.allowBooleanAttributes) {\n      //independent attribute: ab\n      return getErrorObject('InvalidAttr', \"boolean attribute '\"+matches[i][2]+\"' is not allowed.\", getPositionFromMatch(matches[i]));\n    }\n    /* else if(matches[i][6] === undefined){//attribute without value: ab=\n                    return { err: { code:\"InvalidAttr\",msg:\"attribute \" + matches[i][2] + \" has no value assigned.\"}};\n                } */\n    const attrName = matches[i][2];\n    if (!validateAttrName(attrName)) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is an invalid name.\", getPositionFromMatch(matches[i]));\n    }\n    if (!attrNames.hasOwnProperty(attrName)) {\n      //check for duplicate attribute.\n      attrNames[attrName] = 1;\n    } else {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is repeated.\", getPositionFromMatch(matches[i]));\n    }\n  }\n\n  return true;\n}\n\nfunction validateNumberAmpersand(xmlData, i) {\n  let re = /\\d/;\n  if (xmlData[i] === 'x') {\n    i++;\n    re = /[\\da-fA-F]/;\n  }\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === ';')\n      return i;\n    if (!xmlData[i].match(re))\n      break;\n  }\n  return -1;\n}\n\nfunction validateAmpersand(xmlData, i) {\n  // https://www.w3.org/TR/xml/#dt-charref\n  i++;\n  if (xmlData[i] === ';')\n    return -1;\n  if (xmlData[i] === '#') {\n    i++;\n    return validateNumberAmpersand(xmlData, i);\n  }\n  let count = 0;\n  for (; i < xmlData.length; i++, count++) {\n    if (xmlData[i].match(/\\w/) && count < 20)\n      continue;\n    if (xmlData[i] === ';')\n      break;\n    return -1;\n  }\n  return i;\n}\n\nfunction getErrorObject(code, message, lineNumber) {\n  return {\n    err: {\n      code: code,\n      msg: message,\n      line: lineNumber.line || lineNumber,\n      col: lineNumber.col,\n    },\n  };\n}\n\nfunction validateAttrName(attrName) {\n  return util.isName(attrName);\n}\n\n// const startsWithXML = /^xml/i;\n\nfunction validateTagName(tagname) {\n  return util.isName(tagname) /* && !tagname.match(startsWithXML) */;\n}\n\n//this function returns the line number for the character at the given index\nfunction getLineNumberForPosition(xmlData, index) {\n  const lines = xmlData.substring(0, index).split(/\\r?\\n/);\n  return {\n    line: lines.length,\n\n    // column number is last line's length + 1, because column numbering starts at 1:\n    col: lines[lines.length - 1].length + 1\n  };\n}\n\n//this function returns the position of the first character of match within attrStr\nfunction getPositionFromMatch(match) {\n  return match.startIndex + match[1].length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/validator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n//parse Empty Node as self closing node\nconst buildFromOrderedJs = __webpack_require__(/*! ./orderedJs2Xml */ \"(rsc)/./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js\");\n\nconst defaultOptions = {\n  attributeNamePrefix: '@_',\n  attributesGroupName: false,\n  textNodeName: '#text',\n  ignoreAttributes: true,\n  cdataPropName: false,\n  format: false,\n  indentBy: '  ',\n  suppressEmptyNode: false,\n  suppressUnpairedNode: true,\n  suppressBooleanAttributes: true,\n  tagValueProcessor: function(key, a) {\n    return a;\n  },\n  attributeValueProcessor: function(attrName, a) {\n    return a;\n  },\n  preserveOrder: false,\n  commentPropName: false,\n  unpairedTags: [],\n  entities: [\n    { regex: new RegExp(\"&\", \"g\"), val: \"&amp;\" },//it must be on top\n    { regex: new RegExp(\">\", \"g\"), val: \"&gt;\" },\n    { regex: new RegExp(\"<\", \"g\"), val: \"&lt;\" },\n    { regex: new RegExp(\"\\'\", \"g\"), val: \"&apos;\" },\n    { regex: new RegExp(\"\\\"\", \"g\"), val: \"&quot;\" }\n  ],\n  processEntities: true,\n  stopNodes: [],\n  // transformTagName: false,\n  // transformAttributeName: false,\n  oneListGroup: false\n};\n\nfunction Builder(options) {\n  this.options = Object.assign({}, defaultOptions, options);\n  if (this.options.ignoreAttributes || this.options.attributesGroupName) {\n    this.isAttribute = function(/*a*/) {\n      return false;\n    };\n  } else {\n    this.attrPrefixLen = this.options.attributeNamePrefix.length;\n    this.isAttribute = isAttribute;\n  }\n\n  this.processTextOrObjNode = processTextOrObjNode\n\n  if (this.options.format) {\n    this.indentate = indentate;\n    this.tagEndChar = '>\\n';\n    this.newLine = '\\n';\n  } else {\n    this.indentate = function() {\n      return '';\n    };\n    this.tagEndChar = '>';\n    this.newLine = '';\n  }\n}\n\nBuilder.prototype.build = function(jObj) {\n  if(this.options.preserveOrder){\n    return buildFromOrderedJs(jObj, this.options);\n  }else {\n    if(Array.isArray(jObj) && this.options.arrayNodeName && this.options.arrayNodeName.length > 1){\n      jObj = {\n        [this.options.arrayNodeName] : jObj\n      }\n    }\n    return this.j2x(jObj, 0).val;\n  }\n};\n\nBuilder.prototype.j2x = function(jObj, level) {\n  let attrStr = '';\n  let val = '';\n  for (let key in jObj) {\n    if(!Object.prototype.hasOwnProperty.call(jObj, key)) continue;\n    if (typeof jObj[key] === 'undefined') {\n      // supress undefined node only if it is not an attribute\n      if (this.isAttribute(key)) {\n        val += '';\n      }\n    } else if (jObj[key] === null) {\n      // null attribute should be ignored by the attribute list, but should not cause the tag closing\n      if (this.isAttribute(key)) {\n        val += '';\n      } else if (key[0] === '?') {\n        val += this.indentate(level) + '<' + key + '?' + this.tagEndChar;\n      } else {\n        val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n      }\n      // val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n    } else if (jObj[key] instanceof Date) {\n      val += this.buildTextValNode(jObj[key], key, '', level);\n    } else if (typeof jObj[key] !== 'object') {\n      //premitive type\n      const attr = this.isAttribute(key);\n      if (attr) {\n        attrStr += this.buildAttrPairStr(attr, '' + jObj[key]);\n      }else {\n        //tag value\n        if (key === this.options.textNodeName) {\n          let newval = this.options.tagValueProcessor(key, '' + jObj[key]);\n          val += this.replaceEntitiesValue(newval);\n        } else {\n          val += this.buildTextValNode(jObj[key], key, '', level);\n        }\n      }\n    } else if (Array.isArray(jObj[key])) {\n      //repeated nodes\n      const arrLen = jObj[key].length;\n      let listTagVal = \"\";\n      let listTagAttr = \"\";\n      for (let j = 0; j < arrLen; j++) {\n        const item = jObj[key][j];\n        if (typeof item === 'undefined') {\n          // supress undefined node\n        } else if (item === null) {\n          if(key[0] === \"?\") val += this.indentate(level) + '<' + key + '?' + this.tagEndChar;\n          else val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n          // val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n        } else if (typeof item === 'object') {\n          if(this.options.oneListGroup){\n            const result = this.j2x(item, level + 1);\n            listTagVal += result.val;\n            if (this.options.attributesGroupName && item.hasOwnProperty(this.options.attributesGroupName)) {\n              listTagAttr += result.attrStr\n            }\n          }else{\n            listTagVal += this.processTextOrObjNode(item, key, level)\n          }\n        } else {\n          if (this.options.oneListGroup) {\n            let textValue = this.options.tagValueProcessor(key, item);\n            textValue = this.replaceEntitiesValue(textValue);\n            listTagVal += textValue;\n          } else {\n            listTagVal += this.buildTextValNode(item, key, '', level);\n          }\n        }\n      }\n      if(this.options.oneListGroup){\n        listTagVal = this.buildObjectNode(listTagVal, key, listTagAttr, level);\n      }\n      val += listTagVal;\n    } else {\n      //nested node\n      if (this.options.attributesGroupName && key === this.options.attributesGroupName) {\n        const Ks = Object.keys(jObj[key]);\n        const L = Ks.length;\n        for (let j = 0; j < L; j++) {\n          attrStr += this.buildAttrPairStr(Ks[j], '' + jObj[key][Ks[j]]);\n        }\n      } else {\n        val += this.processTextOrObjNode(jObj[key], key, level)\n      }\n    }\n  }\n  return {attrStr: attrStr, val: val};\n};\n\nBuilder.prototype.buildAttrPairStr = function(attrName, val){\n  val = this.options.attributeValueProcessor(attrName, '' + val);\n  val = this.replaceEntitiesValue(val);\n  if (this.options.suppressBooleanAttributes && val === \"true\") {\n    return ' ' + attrName;\n  } else return ' ' + attrName + '=\"' + val + '\"';\n}\n\nfunction processTextOrObjNode (object, key, level) {\n  const result = this.j2x(object, level + 1);\n  if (object[this.options.textNodeName] !== undefined && Object.keys(object).length === 1) {\n    return this.buildTextValNode(object[this.options.textNodeName], key, result.attrStr, level);\n  } else {\n    return this.buildObjectNode(result.val, key, result.attrStr, level);\n  }\n}\n\nBuilder.prototype.buildObjectNode = function(val, key, attrStr, level) {\n  if(val === \"\"){\n    if(key[0] === \"?\") return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar;\n    else {\n      return this.indentate(level) + '<' + key + attrStr + this.closeTag(key) + this.tagEndChar;\n    }\n  }else{\n\n    let tagEndExp = '</' + key + this.tagEndChar;\n    let piClosingChar = \"\";\n    \n    if(key[0] === \"?\") {\n      piClosingChar = \"?\";\n      tagEndExp = \"\";\n    }\n  \n    // attrStr is an empty string in case the attribute came as undefined or null\n    if ((attrStr || attrStr === '') && val.indexOf('<') === -1) {\n      return ( this.indentate(level) + '<' +  key + attrStr + piClosingChar + '>' + val + tagEndExp );\n    } else if (this.options.commentPropName !== false && key === this.options.commentPropName && piClosingChar.length === 0) {\n      return this.indentate(level) + `<!--${val}-->` + this.newLine;\n    }else {\n      return (\n        this.indentate(level) + '<' + key + attrStr + piClosingChar + this.tagEndChar +\n        val +\n        this.indentate(level) + tagEndExp    );\n    }\n  }\n}\n\nBuilder.prototype.closeTag = function(key){\n  let closeTag = \"\";\n  if(this.options.unpairedTags.indexOf(key) !== -1){ //unpaired\n    if(!this.options.suppressUnpairedNode) closeTag = \"/\"\n  }else if(this.options.suppressEmptyNode){ //empty\n    closeTag = \"/\";\n  }else{\n    closeTag = `></${key}`\n  }\n  return closeTag;\n}\n\nfunction buildEmptyObjNode(val, key, attrStr, level) {\n  if (val !== '') {\n    return this.buildObjectNode(val, key, attrStr, level);\n  } else {\n    if(key[0] === \"?\") return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar;\n    else {\n      return  this.indentate(level) + '<' + key + attrStr + '/' + this.tagEndChar;\n      // return this.buildTagStr(level,key, attrStr);\n    }\n  }\n}\n\nBuilder.prototype.buildTextValNode = function(val, key, attrStr, level) {\n  if (this.options.cdataPropName !== false && key === this.options.cdataPropName) {\n    return this.indentate(level) + `<![CDATA[${val}]]>` +  this.newLine;\n  }else if (this.options.commentPropName !== false && key === this.options.commentPropName) {\n    return this.indentate(level) + `<!--${val}-->` +  this.newLine;\n  }else if(key[0] === \"?\") {//PI tag\n    return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar; \n  }else{\n    let textValue = this.options.tagValueProcessor(key, val);\n    textValue = this.replaceEntitiesValue(textValue);\n  \n    if( textValue === ''){\n      return this.indentate(level) + '<' + key + attrStr + this.closeTag(key) + this.tagEndChar;\n    }else{\n      return this.indentate(level) + '<' + key + attrStr + '>' +\n         textValue +\n        '</' + key + this.tagEndChar;\n    }\n  }\n}\n\nBuilder.prototype.replaceEntitiesValue = function(textValue){\n  if(textValue && textValue.length > 0 && this.options.processEntities){\n    for (let i=0; i<this.options.entities.length; i++) {\n      const entity = this.options.entities[i];\n      textValue = textValue.replace(entity.regex, entity.val);\n    }\n  }\n  return textValue;\n}\n\nfunction indentate(level) {\n  return this.options.indentBy.repeat(level);\n}\n\nfunction isAttribute(name /*, options*/) {\n  if (name.startsWith(this.options.attributeNamePrefix) && name !== this.options.textNodeName) {\n    return name.substr(this.attrPrefixLen);\n  } else {\n    return false;\n  }\n}\n\nmodule.exports = Builder;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmFzdC14bWwtcGFyc2VyL3NyYy94bWxidWlsZGVyL2pzb24yeG1sLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSwyQkFBMkIsbUJBQU8sQ0FBQyw2RkFBaUI7O0FBRXBEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLHdDQUF3QyxHQUFHO0FBQ2pELE1BQU0sdUNBQXVDLEdBQUc7QUFDaEQsTUFBTSx1Q0FBdUMsR0FBRztBQUNoRCxNQUFNLDBDQUEwQyxHQUFHO0FBQ25ELE1BQU0sMENBQTBDO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixZQUFZO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixPQUFPO0FBQy9CO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLDRDQUE0QyxJQUFJO0FBQ2hELEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EscURBQXFEO0FBQ3JEO0FBQ0EsR0FBRyx5Q0FBeUM7QUFDNUM7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLElBQUk7QUFDekI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsK0NBQStDLElBQUk7QUFDbkQsR0FBRztBQUNILDBDQUEwQyxJQUFJO0FBQzlDLEdBQUcseUJBQXlCO0FBQzVCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxrQkFBa0IsZ0NBQWdDO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxmYXN0LXhtbC1wYXJzZXJcXHNyY1xceG1sYnVpbGRlclxcanNvbjJ4bWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuLy9wYXJzZSBFbXB0eSBOb2RlIGFzIHNlbGYgY2xvc2luZyBub2RlXG5jb25zdCBidWlsZEZyb21PcmRlcmVkSnMgPSByZXF1aXJlKCcuL29yZGVyZWRKczJYbWwnKTtcblxuY29uc3QgZGVmYXVsdE9wdGlvbnMgPSB7XG4gIGF0dHJpYnV0ZU5hbWVQcmVmaXg6ICdAXycsXG4gIGF0dHJpYnV0ZXNHcm91cE5hbWU6IGZhbHNlLFxuICB0ZXh0Tm9kZU5hbWU6ICcjdGV4dCcsXG4gIGlnbm9yZUF0dHJpYnV0ZXM6IHRydWUsXG4gIGNkYXRhUHJvcE5hbWU6IGZhbHNlLFxuICBmb3JtYXQ6IGZhbHNlLFxuICBpbmRlbnRCeTogJyAgJyxcbiAgc3VwcHJlc3NFbXB0eU5vZGU6IGZhbHNlLFxuICBzdXBwcmVzc1VucGFpcmVkTm9kZTogdHJ1ZSxcbiAgc3VwcHJlc3NCb29sZWFuQXR0cmlidXRlczogdHJ1ZSxcbiAgdGFnVmFsdWVQcm9jZXNzb3I6IGZ1bmN0aW9uKGtleSwgYSkge1xuICAgIHJldHVybiBhO1xuICB9LFxuICBhdHRyaWJ1dGVWYWx1ZVByb2Nlc3NvcjogZnVuY3Rpb24oYXR0ck5hbWUsIGEpIHtcbiAgICByZXR1cm4gYTtcbiAgfSxcbiAgcHJlc2VydmVPcmRlcjogZmFsc2UsXG4gIGNvbW1lbnRQcm9wTmFtZTogZmFsc2UsXG4gIHVucGFpcmVkVGFnczogW10sXG4gIGVudGl0aWVzOiBbXG4gICAgeyByZWdleDogbmV3IFJlZ0V4cChcIiZcIiwgXCJnXCIpLCB2YWw6IFwiJmFtcDtcIiB9LC8vaXQgbXVzdCBiZSBvbiB0b3BcbiAgICB7IHJlZ2V4OiBuZXcgUmVnRXhwKFwiPlwiLCBcImdcIiksIHZhbDogXCImZ3Q7XCIgfSxcbiAgICB7IHJlZ2V4OiBuZXcgUmVnRXhwKFwiPFwiLCBcImdcIiksIHZhbDogXCImbHQ7XCIgfSxcbiAgICB7IHJlZ2V4OiBuZXcgUmVnRXhwKFwiXFwnXCIsIFwiZ1wiKSwgdmFsOiBcIiZhcG9zO1wiIH0sXG4gICAgeyByZWdleDogbmV3IFJlZ0V4cChcIlxcXCJcIiwgXCJnXCIpLCB2YWw6IFwiJnF1b3Q7XCIgfVxuICBdLFxuICBwcm9jZXNzRW50aXRpZXM6IHRydWUsXG4gIHN0b3BOb2RlczogW10sXG4gIC8vIHRyYW5zZm9ybVRhZ05hbWU6IGZhbHNlLFxuICAvLyB0cmFuc2Zvcm1BdHRyaWJ1dGVOYW1lOiBmYWxzZSxcbiAgb25lTGlzdEdyb3VwOiBmYWxzZVxufTtcblxuZnVuY3Rpb24gQnVpbGRlcihvcHRpb25zKSB7XG4gIHRoaXMub3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oe30sIGRlZmF1bHRPcHRpb25zLCBvcHRpb25zKTtcbiAgaWYgKHRoaXMub3B0aW9ucy5pZ25vcmVBdHRyaWJ1dGVzIHx8IHRoaXMub3B0aW9ucy5hdHRyaWJ1dGVzR3JvdXBOYW1lKSB7XG4gICAgdGhpcy5pc0F0dHJpYnV0ZSA9IGZ1bmN0aW9uKC8qYSovKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfTtcbiAgfSBlbHNlIHtcbiAgICB0aGlzLmF0dHJQcmVmaXhMZW4gPSB0aGlzLm9wdGlvbnMuYXR0cmlidXRlTmFtZVByZWZpeC5sZW5ndGg7XG4gICAgdGhpcy5pc0F0dHJpYnV0ZSA9IGlzQXR0cmlidXRlO1xuICB9XG5cbiAgdGhpcy5wcm9jZXNzVGV4dE9yT2JqTm9kZSA9IHByb2Nlc3NUZXh0T3JPYmpOb2RlXG5cbiAgaWYgKHRoaXMub3B0aW9ucy5mb3JtYXQpIHtcbiAgICB0aGlzLmluZGVudGF0ZSA9IGluZGVudGF0ZTtcbiAgICB0aGlzLnRhZ0VuZENoYXIgPSAnPlxcbic7XG4gICAgdGhpcy5uZXdMaW5lID0gJ1xcbic7XG4gIH0gZWxzZSB7XG4gICAgdGhpcy5pbmRlbnRhdGUgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiAnJztcbiAgICB9O1xuICAgIHRoaXMudGFnRW5kQ2hhciA9ICc+JztcbiAgICB0aGlzLm5ld0xpbmUgPSAnJztcbiAgfVxufVxuXG5CdWlsZGVyLnByb3RvdHlwZS5idWlsZCA9IGZ1bmN0aW9uKGpPYmopIHtcbiAgaWYodGhpcy5vcHRpb25zLnByZXNlcnZlT3JkZXIpe1xuICAgIHJldHVybiBidWlsZEZyb21PcmRlcmVkSnMoak9iaiwgdGhpcy5vcHRpb25zKTtcbiAgfWVsc2Uge1xuICAgIGlmKEFycmF5LmlzQXJyYXkoak9iaikgJiYgdGhpcy5vcHRpb25zLmFycmF5Tm9kZU5hbWUgJiYgdGhpcy5vcHRpb25zLmFycmF5Tm9kZU5hbWUubGVuZ3RoID4gMSl7XG4gICAgICBqT2JqID0ge1xuICAgICAgICBbdGhpcy5vcHRpb25zLmFycmF5Tm9kZU5hbWVdIDogak9ialxuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdGhpcy5qMngoak9iaiwgMCkudmFsO1xuICB9XG59O1xuXG5CdWlsZGVyLnByb3RvdHlwZS5qMnggPSBmdW5jdGlvbihqT2JqLCBsZXZlbCkge1xuICBsZXQgYXR0clN0ciA9ICcnO1xuICBsZXQgdmFsID0gJyc7XG4gIGZvciAobGV0IGtleSBpbiBqT2JqKSB7XG4gICAgaWYoIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChqT2JqLCBrZXkpKSBjb250aW51ZTtcbiAgICBpZiAodHlwZW9mIGpPYmpba2V5XSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIC8vIHN1cHJlc3MgdW5kZWZpbmVkIG5vZGUgb25seSBpZiBpdCBpcyBub3QgYW4gYXR0cmlidXRlXG4gICAgICBpZiAodGhpcy5pc0F0dHJpYnV0ZShrZXkpKSB7XG4gICAgICAgIHZhbCArPSAnJztcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKGpPYmpba2V5XSA9PT0gbnVsbCkge1xuICAgICAgLy8gbnVsbCBhdHRyaWJ1dGUgc2hvdWxkIGJlIGlnbm9yZWQgYnkgdGhlIGF0dHJpYnV0ZSBsaXN0LCBidXQgc2hvdWxkIG5vdCBjYXVzZSB0aGUgdGFnIGNsb3NpbmdcbiAgICAgIGlmICh0aGlzLmlzQXR0cmlidXRlKGtleSkpIHtcbiAgICAgICAgdmFsICs9ICcnO1xuICAgICAgfSBlbHNlIGlmIChrZXlbMF0gPT09ICc/Jykge1xuICAgICAgICB2YWwgKz0gdGhpcy5pbmRlbnRhdGUobGV2ZWwpICsgJzwnICsga2V5ICsgJz8nICsgdGhpcy50YWdFbmRDaGFyO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdmFsICs9IHRoaXMuaW5kZW50YXRlKGxldmVsKSArICc8JyArIGtleSArICcvJyArIHRoaXMudGFnRW5kQ2hhcjtcbiAgICAgIH1cbiAgICAgIC8vIHZhbCArPSB0aGlzLmluZGVudGF0ZShsZXZlbCkgKyAnPCcgKyBrZXkgKyAnLycgKyB0aGlzLnRhZ0VuZENoYXI7XG4gICAgfSBlbHNlIGlmIChqT2JqW2tleV0gaW5zdGFuY2VvZiBEYXRlKSB7XG4gICAgICB2YWwgKz0gdGhpcy5idWlsZFRleHRWYWxOb2RlKGpPYmpba2V5XSwga2V5LCAnJywgbGV2ZWwpO1xuICAgIH0gZWxzZSBpZiAodHlwZW9mIGpPYmpba2V5XSAhPT0gJ29iamVjdCcpIHtcbiAgICAgIC8vcHJlbWl0aXZlIHR5cGVcbiAgICAgIGNvbnN0IGF0dHIgPSB0aGlzLmlzQXR0cmlidXRlKGtleSk7XG4gICAgICBpZiAoYXR0cikge1xuICAgICAgICBhdHRyU3RyICs9IHRoaXMuYnVpbGRBdHRyUGFpclN0cihhdHRyLCAnJyArIGpPYmpba2V5XSk7XG4gICAgICB9ZWxzZSB7XG4gICAgICAgIC8vdGFnIHZhbHVlXG4gICAgICAgIGlmIChrZXkgPT09IHRoaXMub3B0aW9ucy50ZXh0Tm9kZU5hbWUpIHtcbiAgICAgICAgICBsZXQgbmV3dmFsID0gdGhpcy5vcHRpb25zLnRhZ1ZhbHVlUHJvY2Vzc29yKGtleSwgJycgKyBqT2JqW2tleV0pO1xuICAgICAgICAgIHZhbCArPSB0aGlzLnJlcGxhY2VFbnRpdGllc1ZhbHVlKG5ld3ZhbCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdmFsICs9IHRoaXMuYnVpbGRUZXh0VmFsTm9kZShqT2JqW2tleV0sIGtleSwgJycsIGxldmVsKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShqT2JqW2tleV0pKSB7XG4gICAgICAvL3JlcGVhdGVkIG5vZGVzXG4gICAgICBjb25zdCBhcnJMZW4gPSBqT2JqW2tleV0ubGVuZ3RoO1xuICAgICAgbGV0IGxpc3RUYWdWYWwgPSBcIlwiO1xuICAgICAgbGV0IGxpc3RUYWdBdHRyID0gXCJcIjtcbiAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgYXJyTGVuOyBqKyspIHtcbiAgICAgICAgY29uc3QgaXRlbSA9IGpPYmpba2V5XVtqXTtcbiAgICAgICAgaWYgKHR5cGVvZiBpdGVtID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgIC8vIHN1cHJlc3MgdW5kZWZpbmVkIG5vZGVcbiAgICAgICAgfSBlbHNlIGlmIChpdGVtID09PSBudWxsKSB7XG4gICAgICAgICAgaWYoa2V5WzBdID09PSBcIj9cIikgdmFsICs9IHRoaXMuaW5kZW50YXRlKGxldmVsKSArICc8JyArIGtleSArICc/JyArIHRoaXMudGFnRW5kQ2hhcjtcbiAgICAgICAgICBlbHNlIHZhbCArPSB0aGlzLmluZGVudGF0ZShsZXZlbCkgKyAnPCcgKyBrZXkgKyAnLycgKyB0aGlzLnRhZ0VuZENoYXI7XG4gICAgICAgICAgLy8gdmFsICs9IHRoaXMuaW5kZW50YXRlKGxldmVsKSArICc8JyArIGtleSArICcvJyArIHRoaXMudGFnRW5kQ2hhcjtcbiAgICAgICAgfSBlbHNlIGlmICh0eXBlb2YgaXRlbSA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgICBpZih0aGlzLm9wdGlvbnMub25lTGlzdEdyb3VwKXtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMuajJ4KGl0ZW0sIGxldmVsICsgMSk7XG4gICAgICAgICAgICBsaXN0VGFnVmFsICs9IHJlc3VsdC52YWw7XG4gICAgICAgICAgICBpZiAodGhpcy5vcHRpb25zLmF0dHJpYnV0ZXNHcm91cE5hbWUgJiYgaXRlbS5oYXNPd25Qcm9wZXJ0eSh0aGlzLm9wdGlvbnMuYXR0cmlidXRlc0dyb3VwTmFtZSkpIHtcbiAgICAgICAgICAgICAgbGlzdFRhZ0F0dHIgKz0gcmVzdWx0LmF0dHJTdHJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9ZWxzZXtcbiAgICAgICAgICAgIGxpc3RUYWdWYWwgKz0gdGhpcy5wcm9jZXNzVGV4dE9yT2JqTm9kZShpdGVtLCBrZXksIGxldmVsKVxuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpZiAodGhpcy5vcHRpb25zLm9uZUxpc3RHcm91cCkge1xuICAgICAgICAgICAgbGV0IHRleHRWYWx1ZSA9IHRoaXMub3B0aW9ucy50YWdWYWx1ZVByb2Nlc3NvcihrZXksIGl0ZW0pO1xuICAgICAgICAgICAgdGV4dFZhbHVlID0gdGhpcy5yZXBsYWNlRW50aXRpZXNWYWx1ZSh0ZXh0VmFsdWUpO1xuICAgICAgICAgICAgbGlzdFRhZ1ZhbCArPSB0ZXh0VmFsdWU7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGxpc3RUYWdWYWwgKz0gdGhpcy5idWlsZFRleHRWYWxOb2RlKGl0ZW0sIGtleSwgJycsIGxldmVsKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGlmKHRoaXMub3B0aW9ucy5vbmVMaXN0R3JvdXApe1xuICAgICAgICBsaXN0VGFnVmFsID0gdGhpcy5idWlsZE9iamVjdE5vZGUobGlzdFRhZ1ZhbCwga2V5LCBsaXN0VGFnQXR0ciwgbGV2ZWwpO1xuICAgICAgfVxuICAgICAgdmFsICs9IGxpc3RUYWdWYWw7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vbmVzdGVkIG5vZGVcbiAgICAgIGlmICh0aGlzLm9wdGlvbnMuYXR0cmlidXRlc0dyb3VwTmFtZSAmJiBrZXkgPT09IHRoaXMub3B0aW9ucy5hdHRyaWJ1dGVzR3JvdXBOYW1lKSB7XG4gICAgICAgIGNvbnN0IEtzID0gT2JqZWN0LmtleXMoak9ialtrZXldKTtcbiAgICAgICAgY29uc3QgTCA9IEtzLmxlbmd0aDtcbiAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCBMOyBqKyspIHtcbiAgICAgICAgICBhdHRyU3RyICs9IHRoaXMuYnVpbGRBdHRyUGFpclN0cihLc1tqXSwgJycgKyBqT2JqW2tleV1bS3Nbal1dKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdmFsICs9IHRoaXMucHJvY2Vzc1RleHRPck9iak5vZGUoak9ialtrZXldLCBrZXksIGxldmVsKVxuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4ge2F0dHJTdHI6IGF0dHJTdHIsIHZhbDogdmFsfTtcbn07XG5cbkJ1aWxkZXIucHJvdG90eXBlLmJ1aWxkQXR0clBhaXJTdHIgPSBmdW5jdGlvbihhdHRyTmFtZSwgdmFsKXtcbiAgdmFsID0gdGhpcy5vcHRpb25zLmF0dHJpYnV0ZVZhbHVlUHJvY2Vzc29yKGF0dHJOYW1lLCAnJyArIHZhbCk7XG4gIHZhbCA9IHRoaXMucmVwbGFjZUVudGl0aWVzVmFsdWUodmFsKTtcbiAgaWYgKHRoaXMub3B0aW9ucy5zdXBwcmVzc0Jvb2xlYW5BdHRyaWJ1dGVzICYmIHZhbCA9PT0gXCJ0cnVlXCIpIHtcbiAgICByZXR1cm4gJyAnICsgYXR0ck5hbWU7XG4gIH0gZWxzZSByZXR1cm4gJyAnICsgYXR0ck5hbWUgKyAnPVwiJyArIHZhbCArICdcIic7XG59XG5cbmZ1bmN0aW9uIHByb2Nlc3NUZXh0T3JPYmpOb2RlIChvYmplY3QsIGtleSwgbGV2ZWwpIHtcbiAgY29uc3QgcmVzdWx0ID0gdGhpcy5qMngob2JqZWN0LCBsZXZlbCArIDEpO1xuICBpZiAob2JqZWN0W3RoaXMub3B0aW9ucy50ZXh0Tm9kZU5hbWVdICE9PSB1bmRlZmluZWQgJiYgT2JqZWN0LmtleXMob2JqZWN0KS5sZW5ndGggPT09IDEpIHtcbiAgICByZXR1cm4gdGhpcy5idWlsZFRleHRWYWxOb2RlKG9iamVjdFt0aGlzLm9wdGlvbnMudGV4dE5vZGVOYW1lXSwga2V5LCByZXN1bHQuYXR0clN0ciwgbGV2ZWwpO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiB0aGlzLmJ1aWxkT2JqZWN0Tm9kZShyZXN1bHQudmFsLCBrZXksIHJlc3VsdC5hdHRyU3RyLCBsZXZlbCk7XG4gIH1cbn1cblxuQnVpbGRlci5wcm90b3R5cGUuYnVpbGRPYmplY3ROb2RlID0gZnVuY3Rpb24odmFsLCBrZXksIGF0dHJTdHIsIGxldmVsKSB7XG4gIGlmKHZhbCA9PT0gXCJcIil7XG4gICAgaWYoa2V5WzBdID09PSBcIj9cIikgcmV0dXJuICB0aGlzLmluZGVudGF0ZShsZXZlbCkgKyAnPCcgKyBrZXkgKyBhdHRyU3RyKyAnPycgKyB0aGlzLnRhZ0VuZENoYXI7XG4gICAgZWxzZSB7XG4gICAgICByZXR1cm4gdGhpcy5pbmRlbnRhdGUobGV2ZWwpICsgJzwnICsga2V5ICsgYXR0clN0ciArIHRoaXMuY2xvc2VUYWcoa2V5KSArIHRoaXMudGFnRW5kQ2hhcjtcbiAgICB9XG4gIH1lbHNle1xuXG4gICAgbGV0IHRhZ0VuZEV4cCA9ICc8LycgKyBrZXkgKyB0aGlzLnRhZ0VuZENoYXI7XG4gICAgbGV0IHBpQ2xvc2luZ0NoYXIgPSBcIlwiO1xuICAgIFxuICAgIGlmKGtleVswXSA9PT0gXCI/XCIpIHtcbiAgICAgIHBpQ2xvc2luZ0NoYXIgPSBcIj9cIjtcbiAgICAgIHRhZ0VuZEV4cCA9IFwiXCI7XG4gICAgfVxuICBcbiAgICAvLyBhdHRyU3RyIGlzIGFuIGVtcHR5IHN0cmluZyBpbiBjYXNlIHRoZSBhdHRyaWJ1dGUgY2FtZSBhcyB1bmRlZmluZWQgb3IgbnVsbFxuICAgIGlmICgoYXR0clN0ciB8fCBhdHRyU3RyID09PSAnJykgJiYgdmFsLmluZGV4T2YoJzwnKSA9PT0gLTEpIHtcbiAgICAgIHJldHVybiAoIHRoaXMuaW5kZW50YXRlKGxldmVsKSArICc8JyArICBrZXkgKyBhdHRyU3RyICsgcGlDbG9zaW5nQ2hhciArICc+JyArIHZhbCArIHRhZ0VuZEV4cCApO1xuICAgIH0gZWxzZSBpZiAodGhpcy5vcHRpb25zLmNvbW1lbnRQcm9wTmFtZSAhPT0gZmFsc2UgJiYga2V5ID09PSB0aGlzLm9wdGlvbnMuY29tbWVudFByb3BOYW1lICYmIHBpQ2xvc2luZ0NoYXIubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gdGhpcy5pbmRlbnRhdGUobGV2ZWwpICsgYDwhLS0ke3ZhbH0tLT5gICsgdGhpcy5uZXdMaW5lO1xuICAgIH1lbHNlIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIHRoaXMuaW5kZW50YXRlKGxldmVsKSArICc8JyArIGtleSArIGF0dHJTdHIgKyBwaUNsb3NpbmdDaGFyICsgdGhpcy50YWdFbmRDaGFyICtcbiAgICAgICAgdmFsICtcbiAgICAgICAgdGhpcy5pbmRlbnRhdGUobGV2ZWwpICsgdGFnRW5kRXhwICAgICk7XG4gICAgfVxuICB9XG59XG5cbkJ1aWxkZXIucHJvdG90eXBlLmNsb3NlVGFnID0gZnVuY3Rpb24oa2V5KXtcbiAgbGV0IGNsb3NlVGFnID0gXCJcIjtcbiAgaWYodGhpcy5vcHRpb25zLnVucGFpcmVkVGFncy5pbmRleE9mKGtleSkgIT09IC0xKXsgLy91bnBhaXJlZFxuICAgIGlmKCF0aGlzLm9wdGlvbnMuc3VwcHJlc3NVbnBhaXJlZE5vZGUpIGNsb3NlVGFnID0gXCIvXCJcbiAgfWVsc2UgaWYodGhpcy5vcHRpb25zLnN1cHByZXNzRW1wdHlOb2RlKXsgLy9lbXB0eVxuICAgIGNsb3NlVGFnID0gXCIvXCI7XG4gIH1lbHNle1xuICAgIGNsb3NlVGFnID0gYD48LyR7a2V5fWBcbiAgfVxuICByZXR1cm4gY2xvc2VUYWc7XG59XG5cbmZ1bmN0aW9uIGJ1aWxkRW1wdHlPYmpOb2RlKHZhbCwga2V5LCBhdHRyU3RyLCBsZXZlbCkge1xuICBpZiAodmFsICE9PSAnJykge1xuICAgIHJldHVybiB0aGlzLmJ1aWxkT2JqZWN0Tm9kZSh2YWwsIGtleSwgYXR0clN0ciwgbGV2ZWwpO1xuICB9IGVsc2Uge1xuICAgIGlmKGtleVswXSA9PT0gXCI/XCIpIHJldHVybiAgdGhpcy5pbmRlbnRhdGUobGV2ZWwpICsgJzwnICsga2V5ICsgYXR0clN0cisgJz8nICsgdGhpcy50YWdFbmRDaGFyO1xuICAgIGVsc2Uge1xuICAgICAgcmV0dXJuICB0aGlzLmluZGVudGF0ZShsZXZlbCkgKyAnPCcgKyBrZXkgKyBhdHRyU3RyICsgJy8nICsgdGhpcy50YWdFbmRDaGFyO1xuICAgICAgLy8gcmV0dXJuIHRoaXMuYnVpbGRUYWdTdHIobGV2ZWwsa2V5LCBhdHRyU3RyKTtcbiAgICB9XG4gIH1cbn1cblxuQnVpbGRlci5wcm90b3R5cGUuYnVpbGRUZXh0VmFsTm9kZSA9IGZ1bmN0aW9uKHZhbCwga2V5LCBhdHRyU3RyLCBsZXZlbCkge1xuICBpZiAodGhpcy5vcHRpb25zLmNkYXRhUHJvcE5hbWUgIT09IGZhbHNlICYmIGtleSA9PT0gdGhpcy5vcHRpb25zLmNkYXRhUHJvcE5hbWUpIHtcbiAgICByZXR1cm4gdGhpcy5pbmRlbnRhdGUobGV2ZWwpICsgYDwhW0NEQVRBWyR7dmFsfV1dPmAgKyAgdGhpcy5uZXdMaW5lO1xuICB9ZWxzZSBpZiAodGhpcy5vcHRpb25zLmNvbW1lbnRQcm9wTmFtZSAhPT0gZmFsc2UgJiYga2V5ID09PSB0aGlzLm9wdGlvbnMuY29tbWVudFByb3BOYW1lKSB7XG4gICAgcmV0dXJuIHRoaXMuaW5kZW50YXRlKGxldmVsKSArIGA8IS0tJHt2YWx9LS0+YCArICB0aGlzLm5ld0xpbmU7XG4gIH1lbHNlIGlmKGtleVswXSA9PT0gXCI/XCIpIHsvL1BJIHRhZ1xuICAgIHJldHVybiAgdGhpcy5pbmRlbnRhdGUobGV2ZWwpICsgJzwnICsga2V5ICsgYXR0clN0cisgJz8nICsgdGhpcy50YWdFbmRDaGFyOyBcbiAgfWVsc2V7XG4gICAgbGV0IHRleHRWYWx1ZSA9IHRoaXMub3B0aW9ucy50YWdWYWx1ZVByb2Nlc3NvcihrZXksIHZhbCk7XG4gICAgdGV4dFZhbHVlID0gdGhpcy5yZXBsYWNlRW50aXRpZXNWYWx1ZSh0ZXh0VmFsdWUpO1xuICBcbiAgICBpZiggdGV4dFZhbHVlID09PSAnJyl7XG4gICAgICByZXR1cm4gdGhpcy5pbmRlbnRhdGUobGV2ZWwpICsgJzwnICsga2V5ICsgYXR0clN0ciArIHRoaXMuY2xvc2VUYWcoa2V5KSArIHRoaXMudGFnRW5kQ2hhcjtcbiAgICB9ZWxzZXtcbiAgICAgIHJldHVybiB0aGlzLmluZGVudGF0ZShsZXZlbCkgKyAnPCcgKyBrZXkgKyBhdHRyU3RyICsgJz4nICtcbiAgICAgICAgIHRleHRWYWx1ZSArXG4gICAgICAgICc8LycgKyBrZXkgKyB0aGlzLnRhZ0VuZENoYXI7XG4gICAgfVxuICB9XG59XG5cbkJ1aWxkZXIucHJvdG90eXBlLnJlcGxhY2VFbnRpdGllc1ZhbHVlID0gZnVuY3Rpb24odGV4dFZhbHVlKXtcbiAgaWYodGV4dFZhbHVlICYmIHRleHRWYWx1ZS5sZW5ndGggPiAwICYmIHRoaXMub3B0aW9ucy5wcm9jZXNzRW50aXRpZXMpe1xuICAgIGZvciAobGV0IGk9MDsgaTx0aGlzLm9wdGlvbnMuZW50aXRpZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IGVudGl0eSA9IHRoaXMub3B0aW9ucy5lbnRpdGllc1tpXTtcbiAgICAgIHRleHRWYWx1ZSA9IHRleHRWYWx1ZS5yZXBsYWNlKGVudGl0eS5yZWdleCwgZW50aXR5LnZhbCk7XG4gICAgfVxuICB9XG4gIHJldHVybiB0ZXh0VmFsdWU7XG59XG5cbmZ1bmN0aW9uIGluZGVudGF0ZShsZXZlbCkge1xuICByZXR1cm4gdGhpcy5vcHRpb25zLmluZGVudEJ5LnJlcGVhdChsZXZlbCk7XG59XG5cbmZ1bmN0aW9uIGlzQXR0cmlidXRlKG5hbWUgLyosIG9wdGlvbnMqLykge1xuICBpZiAobmFtZS5zdGFydHNXaXRoKHRoaXMub3B0aW9ucy5hdHRyaWJ1dGVOYW1lUHJlZml4KSAmJiBuYW1lICE9PSB0aGlzLm9wdGlvbnMudGV4dE5vZGVOYW1lKSB7XG4gICAgcmV0dXJuIG5hbWUuc3Vic3RyKHRoaXMuYXR0clByZWZpeExlbik7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gQnVpbGRlcjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js":
/*!**********************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("const EOL = \"\\n\";\n\n/**\n * \n * @param {array} jArray \n * @param {any} options \n * @returns \n */\nfunction toXml(jArray, options) {\n    let indentation = \"\";\n    if (options.format && options.indentBy.length > 0) {\n        indentation = EOL;\n    }\n    return arrToStr(jArray, options, \"\", indentation);\n}\n\nfunction arrToStr(arr, options, jPath, indentation) {\n    let xmlStr = \"\";\n    let isPreviousElementTag = false;\n\n    for (let i = 0; i < arr.length; i++) {\n        const tagObj = arr[i];\n        const tagName = propName(tagObj);\n        if(tagName === undefined) continue;\n\n        let newJPath = \"\";\n        if (jPath.length === 0) newJPath = tagName\n        else newJPath = `${jPath}.${tagName}`;\n\n        if (tagName === options.textNodeName) {\n            let tagText = tagObj[tagName];\n            if (!isStopNode(newJPath, options)) {\n                tagText = options.tagValueProcessor(tagName, tagText);\n                tagText = replaceEntitiesValue(tagText, options);\n            }\n            if (isPreviousElementTag) {\n                xmlStr += indentation;\n            }\n            xmlStr += tagText;\n            isPreviousElementTag = false;\n            continue;\n        } else if (tagName === options.cdataPropName) {\n            if (isPreviousElementTag) {\n                xmlStr += indentation;\n            }\n            xmlStr += `<![CDATA[${tagObj[tagName][0][options.textNodeName]}]]>`;\n            isPreviousElementTag = false;\n            continue;\n        } else if (tagName === options.commentPropName) {\n            xmlStr += indentation + `<!--${tagObj[tagName][0][options.textNodeName]}-->`;\n            isPreviousElementTag = true;\n            continue;\n        } else if (tagName[0] === \"?\") {\n            const attStr = attr_to_str(tagObj[\":@\"], options);\n            const tempInd = tagName === \"?xml\" ? \"\" : indentation;\n            let piTextNodeName = tagObj[tagName][0][options.textNodeName];\n            piTextNodeName = piTextNodeName.length !== 0 ? \" \" + piTextNodeName : \"\"; //remove extra spacing\n            xmlStr += tempInd + `<${tagName}${piTextNodeName}${attStr}?>`;\n            isPreviousElementTag = true;\n            continue;\n        }\n        let newIdentation = indentation;\n        if (newIdentation !== \"\") {\n            newIdentation += options.indentBy;\n        }\n        const attStr = attr_to_str(tagObj[\":@\"], options);\n        const tagStart = indentation + `<${tagName}${attStr}`;\n        const tagValue = arrToStr(tagObj[tagName], options, newJPath, newIdentation);\n        if (options.unpairedTags.indexOf(tagName) !== -1) {\n            if (options.suppressUnpairedNode) xmlStr += tagStart + \">\";\n            else xmlStr += tagStart + \"/>\";\n        } else if ((!tagValue || tagValue.length === 0) && options.suppressEmptyNode) {\n            xmlStr += tagStart + \"/>\";\n        } else if (tagValue && tagValue.endsWith(\">\")) {\n            xmlStr += tagStart + `>${tagValue}${indentation}</${tagName}>`;\n        } else {\n            xmlStr += tagStart + \">\";\n            if (tagValue && indentation !== \"\" && (tagValue.includes(\"/>\") || tagValue.includes(\"</\"))) {\n                xmlStr += indentation + options.indentBy + tagValue + indentation;\n            } else {\n                xmlStr += tagValue;\n            }\n            xmlStr += `</${tagName}>`;\n        }\n        isPreviousElementTag = true;\n    }\n\n    return xmlStr;\n}\n\nfunction propName(obj) {\n    const keys = Object.keys(obj);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        if(!obj.hasOwnProperty(key)) continue;\n        if (key !== \":@\") return key;\n    }\n}\n\nfunction attr_to_str(attrMap, options) {\n    let attrStr = \"\";\n    if (attrMap && !options.ignoreAttributes) {\n        for (let attr in attrMap) {\n            if(!attrMap.hasOwnProperty(attr)) continue;\n            let attrVal = options.attributeValueProcessor(attr, attrMap[attr]);\n            attrVal = replaceEntitiesValue(attrVal, options);\n            if (attrVal === true && options.suppressBooleanAttributes) {\n                attrStr += ` ${attr.substr(options.attributeNamePrefix.length)}`;\n            } else {\n                attrStr += ` ${attr.substr(options.attributeNamePrefix.length)}=\"${attrVal}\"`;\n            }\n        }\n    }\n    return attrStr;\n}\n\nfunction isStopNode(jPath, options) {\n    jPath = jPath.substr(0, jPath.length - options.textNodeName.length - 1);\n    let tagName = jPath.substr(jPath.lastIndexOf(\".\") + 1);\n    for (let index in options.stopNodes) {\n        if (options.stopNodes[index] === jPath || options.stopNodes[index] === \"*.\" + tagName) return true;\n    }\n    return false;\n}\n\nfunction replaceEntitiesValue(textValue, options) {\n    if (textValue && textValue.length > 0 && options.processEntities) {\n        for (let i = 0; i < options.entities.length; i++) {\n            const entity = options.entities[i];\n            textValue = textValue.replace(entity.regex, entity.val);\n        }\n    }\n    return textValue;\n}\nmodule.exports = toXml;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmFzdC14bWwtcGFyc2VyL3NyYy94bWxidWlsZGVyL29yZGVyZWRKczJYbWwuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQixXQUFXLEtBQUs7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsMkJBQTJCLE1BQU0sR0FBRyxRQUFROztBQUU1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLHlDQUF5QztBQUMzRTtBQUNBO0FBQ0EsVUFBVTtBQUNWLDJDQUEyQyx5Q0FBeUM7QUFDcEY7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxzRkFBc0Y7QUFDdEYsb0NBQW9DLFFBQVEsRUFBRSxlQUFlLEVBQUUsT0FBTztBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFFBQVEsRUFBRSxPQUFPO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWLHFDQUFxQyxTQUFTLEVBQUUsWUFBWSxJQUFJLFFBQVE7QUFDeEUsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsMkJBQTJCLFFBQVE7QUFDbkM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9CQUFvQixpQkFBaUI7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLGdEQUFnRDtBQUMvRSxjQUFjO0FBQ2QsK0JBQStCLGdEQUFnRCxJQUFJLFFBQVE7QUFDM0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSx3QkFBd0IsNkJBQTZCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGZhc3QteG1sLXBhcnNlclxcc3JjXFx4bWxidWlsZGVyXFxvcmRlcmVkSnMyWG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEVPTCA9IFwiXFxuXCI7XG5cbi8qKlxuICogXG4gKiBAcGFyYW0ge2FycmF5fSBqQXJyYXkgXG4gKiBAcGFyYW0ge2FueX0gb3B0aW9ucyBcbiAqIEByZXR1cm5zIFxuICovXG5mdW5jdGlvbiB0b1htbChqQXJyYXksIG9wdGlvbnMpIHtcbiAgICBsZXQgaW5kZW50YXRpb24gPSBcIlwiO1xuICAgIGlmIChvcHRpb25zLmZvcm1hdCAmJiBvcHRpb25zLmluZGVudEJ5Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgaW5kZW50YXRpb24gPSBFT0w7XG4gICAgfVxuICAgIHJldHVybiBhcnJUb1N0cihqQXJyYXksIG9wdGlvbnMsIFwiXCIsIGluZGVudGF0aW9uKTtcbn1cblxuZnVuY3Rpb24gYXJyVG9TdHIoYXJyLCBvcHRpb25zLCBqUGF0aCwgaW5kZW50YXRpb24pIHtcbiAgICBsZXQgeG1sU3RyID0gXCJcIjtcbiAgICBsZXQgaXNQcmV2aW91c0VsZW1lbnRUYWcgPSBmYWxzZTtcblxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYXJyLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHRhZ09iaiA9IGFycltpXTtcbiAgICAgICAgY29uc3QgdGFnTmFtZSA9IHByb3BOYW1lKHRhZ09iaik7XG4gICAgICAgIGlmKHRhZ05hbWUgPT09IHVuZGVmaW5lZCkgY29udGludWU7XG5cbiAgICAgICAgbGV0IG5ld0pQYXRoID0gXCJcIjtcbiAgICAgICAgaWYgKGpQYXRoLmxlbmd0aCA9PT0gMCkgbmV3SlBhdGggPSB0YWdOYW1lXG4gICAgICAgIGVsc2UgbmV3SlBhdGggPSBgJHtqUGF0aH0uJHt0YWdOYW1lfWA7XG5cbiAgICAgICAgaWYgKHRhZ05hbWUgPT09IG9wdGlvbnMudGV4dE5vZGVOYW1lKSB7XG4gICAgICAgICAgICBsZXQgdGFnVGV4dCA9IHRhZ09ialt0YWdOYW1lXTtcbiAgICAgICAgICAgIGlmICghaXNTdG9wTm9kZShuZXdKUGF0aCwgb3B0aW9ucykpIHtcbiAgICAgICAgICAgICAgICB0YWdUZXh0ID0gb3B0aW9ucy50YWdWYWx1ZVByb2Nlc3Nvcih0YWdOYW1lLCB0YWdUZXh0KTtcbiAgICAgICAgICAgICAgICB0YWdUZXh0ID0gcmVwbGFjZUVudGl0aWVzVmFsdWUodGFnVGV4dCwgb3B0aW9ucyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNQcmV2aW91c0VsZW1lbnRUYWcpIHtcbiAgICAgICAgICAgICAgICB4bWxTdHIgKz0gaW5kZW50YXRpb247XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB4bWxTdHIgKz0gdGFnVGV4dDtcbiAgICAgICAgICAgIGlzUHJldmlvdXNFbGVtZW50VGFnID0gZmFsc2U7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfSBlbHNlIGlmICh0YWdOYW1lID09PSBvcHRpb25zLmNkYXRhUHJvcE5hbWUpIHtcbiAgICAgICAgICAgIGlmIChpc1ByZXZpb3VzRWxlbWVudFRhZykge1xuICAgICAgICAgICAgICAgIHhtbFN0ciArPSBpbmRlbnRhdGlvbjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHhtbFN0ciArPSBgPCFbQ0RBVEFbJHt0YWdPYmpbdGFnTmFtZV1bMF1bb3B0aW9ucy50ZXh0Tm9kZU5hbWVdfV1dPmA7XG4gICAgICAgICAgICBpc1ByZXZpb3VzRWxlbWVudFRhZyA9IGZhbHNlO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH0gZWxzZSBpZiAodGFnTmFtZSA9PT0gb3B0aW9ucy5jb21tZW50UHJvcE5hbWUpIHtcbiAgICAgICAgICAgIHhtbFN0ciArPSBpbmRlbnRhdGlvbiArIGA8IS0tJHt0YWdPYmpbdGFnTmFtZV1bMF1bb3B0aW9ucy50ZXh0Tm9kZU5hbWVdfS0tPmA7XG4gICAgICAgICAgICBpc1ByZXZpb3VzRWxlbWVudFRhZyA9IHRydWU7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfSBlbHNlIGlmICh0YWdOYW1lWzBdID09PSBcIj9cIikge1xuICAgICAgICAgICAgY29uc3QgYXR0U3RyID0gYXR0cl90b19zdHIodGFnT2JqW1wiOkBcIl0sIG9wdGlvbnMpO1xuICAgICAgICAgICAgY29uc3QgdGVtcEluZCA9IHRhZ05hbWUgPT09IFwiP3htbFwiID8gXCJcIiA6IGluZGVudGF0aW9uO1xuICAgICAgICAgICAgbGV0IHBpVGV4dE5vZGVOYW1lID0gdGFnT2JqW3RhZ05hbWVdWzBdW29wdGlvbnMudGV4dE5vZGVOYW1lXTtcbiAgICAgICAgICAgIHBpVGV4dE5vZGVOYW1lID0gcGlUZXh0Tm9kZU5hbWUubGVuZ3RoICE9PSAwID8gXCIgXCIgKyBwaVRleHROb2RlTmFtZSA6IFwiXCI7IC8vcmVtb3ZlIGV4dHJhIHNwYWNpbmdcbiAgICAgICAgICAgIHhtbFN0ciArPSB0ZW1wSW5kICsgYDwke3RhZ05hbWV9JHtwaVRleHROb2RlTmFtZX0ke2F0dFN0cn0/PmA7XG4gICAgICAgICAgICBpc1ByZXZpb3VzRWxlbWVudFRhZyA9IHRydWU7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBsZXQgbmV3SWRlbnRhdGlvbiA9IGluZGVudGF0aW9uO1xuICAgICAgICBpZiAobmV3SWRlbnRhdGlvbiAhPT0gXCJcIikge1xuICAgICAgICAgICAgbmV3SWRlbnRhdGlvbiArPSBvcHRpb25zLmluZGVudEJ5O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGF0dFN0ciA9IGF0dHJfdG9fc3RyKHRhZ09ialtcIjpAXCJdLCBvcHRpb25zKTtcbiAgICAgICAgY29uc3QgdGFnU3RhcnQgPSBpbmRlbnRhdGlvbiArIGA8JHt0YWdOYW1lfSR7YXR0U3RyfWA7XG4gICAgICAgIGNvbnN0IHRhZ1ZhbHVlID0gYXJyVG9TdHIodGFnT2JqW3RhZ05hbWVdLCBvcHRpb25zLCBuZXdKUGF0aCwgbmV3SWRlbnRhdGlvbik7XG4gICAgICAgIGlmIChvcHRpb25zLnVucGFpcmVkVGFncy5pbmRleE9mKHRhZ05hbWUpICE9PSAtMSkge1xuICAgICAgICAgICAgaWYgKG9wdGlvbnMuc3VwcHJlc3NVbnBhaXJlZE5vZGUpIHhtbFN0ciArPSB0YWdTdGFydCArIFwiPlwiO1xuICAgICAgICAgICAgZWxzZSB4bWxTdHIgKz0gdGFnU3RhcnQgKyBcIi8+XCI7XG4gICAgICAgIH0gZWxzZSBpZiAoKCF0YWdWYWx1ZSB8fCB0YWdWYWx1ZS5sZW5ndGggPT09IDApICYmIG9wdGlvbnMuc3VwcHJlc3NFbXB0eU5vZGUpIHtcbiAgICAgICAgICAgIHhtbFN0ciArPSB0YWdTdGFydCArIFwiLz5cIjtcbiAgICAgICAgfSBlbHNlIGlmICh0YWdWYWx1ZSAmJiB0YWdWYWx1ZS5lbmRzV2l0aChcIj5cIikpIHtcbiAgICAgICAgICAgIHhtbFN0ciArPSB0YWdTdGFydCArIGA+JHt0YWdWYWx1ZX0ke2luZGVudGF0aW9ufTwvJHt0YWdOYW1lfT5gO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgeG1sU3RyICs9IHRhZ1N0YXJ0ICsgXCI+XCI7XG4gICAgICAgICAgICBpZiAodGFnVmFsdWUgJiYgaW5kZW50YXRpb24gIT09IFwiXCIgJiYgKHRhZ1ZhbHVlLmluY2x1ZGVzKFwiLz5cIikgfHwgdGFnVmFsdWUuaW5jbHVkZXMoXCI8L1wiKSkpIHtcbiAgICAgICAgICAgICAgICB4bWxTdHIgKz0gaW5kZW50YXRpb24gKyBvcHRpb25zLmluZGVudEJ5ICsgdGFnVmFsdWUgKyBpbmRlbnRhdGlvbjtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgeG1sU3RyICs9IHRhZ1ZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgeG1sU3RyICs9IGA8LyR7dGFnTmFtZX0+YDtcbiAgICAgICAgfVxuICAgICAgICBpc1ByZXZpb3VzRWxlbWVudFRhZyA9IHRydWU7XG4gICAgfVxuXG4gICAgcmV0dXJuIHhtbFN0cjtcbn1cblxuZnVuY3Rpb24gcHJvcE5hbWUob2JqKSB7XG4gICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKG9iaik7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBrZXlzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGtleSA9IGtleXNbaV07XG4gICAgICAgIGlmKCFvYmouaGFzT3duUHJvcGVydHkoa2V5KSkgY29udGludWU7XG4gICAgICAgIGlmIChrZXkgIT09IFwiOkBcIikgcmV0dXJuIGtleTtcbiAgICB9XG59XG5cbmZ1bmN0aW9uIGF0dHJfdG9fc3RyKGF0dHJNYXAsIG9wdGlvbnMpIHtcbiAgICBsZXQgYXR0clN0ciA9IFwiXCI7XG4gICAgaWYgKGF0dHJNYXAgJiYgIW9wdGlvbnMuaWdub3JlQXR0cmlidXRlcykge1xuICAgICAgICBmb3IgKGxldCBhdHRyIGluIGF0dHJNYXApIHtcbiAgICAgICAgICAgIGlmKCFhdHRyTWFwLmhhc093blByb3BlcnR5KGF0dHIpKSBjb250aW51ZTtcbiAgICAgICAgICAgIGxldCBhdHRyVmFsID0gb3B0aW9ucy5hdHRyaWJ1dGVWYWx1ZVByb2Nlc3NvcihhdHRyLCBhdHRyTWFwW2F0dHJdKTtcbiAgICAgICAgICAgIGF0dHJWYWwgPSByZXBsYWNlRW50aXRpZXNWYWx1ZShhdHRyVmFsLCBvcHRpb25zKTtcbiAgICAgICAgICAgIGlmIChhdHRyVmFsID09PSB0cnVlICYmIG9wdGlvbnMuc3VwcHJlc3NCb29sZWFuQXR0cmlidXRlcykge1xuICAgICAgICAgICAgICAgIGF0dHJTdHIgKz0gYCAke2F0dHIuc3Vic3RyKG9wdGlvbnMuYXR0cmlidXRlTmFtZVByZWZpeC5sZW5ndGgpfWA7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGF0dHJTdHIgKz0gYCAke2F0dHIuc3Vic3RyKG9wdGlvbnMuYXR0cmlidXRlTmFtZVByZWZpeC5sZW5ndGgpfT1cIiR7YXR0clZhbH1cImA7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGF0dHJTdHI7XG59XG5cbmZ1bmN0aW9uIGlzU3RvcE5vZGUoalBhdGgsIG9wdGlvbnMpIHtcbiAgICBqUGF0aCA9IGpQYXRoLnN1YnN0cigwLCBqUGF0aC5sZW5ndGggLSBvcHRpb25zLnRleHROb2RlTmFtZS5sZW5ndGggLSAxKTtcbiAgICBsZXQgdGFnTmFtZSA9IGpQYXRoLnN1YnN0cihqUGF0aC5sYXN0SW5kZXhPZihcIi5cIikgKyAxKTtcbiAgICBmb3IgKGxldCBpbmRleCBpbiBvcHRpb25zLnN0b3BOb2Rlcykge1xuICAgICAgICBpZiAob3B0aW9ucy5zdG9wTm9kZXNbaW5kZXhdID09PSBqUGF0aCB8fCBvcHRpb25zLnN0b3BOb2Rlc1tpbmRleF0gPT09IFwiKi5cIiArIHRhZ05hbWUpIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59XG5cbmZ1bmN0aW9uIHJlcGxhY2VFbnRpdGllc1ZhbHVlKHRleHRWYWx1ZSwgb3B0aW9ucykge1xuICAgIGlmICh0ZXh0VmFsdWUgJiYgdGV4dFZhbHVlLmxlbmd0aCA+IDAgJiYgb3B0aW9ucy5wcm9jZXNzRW50aXRpZXMpIHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBvcHRpb25zLmVudGl0aWVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCBlbnRpdHkgPSBvcHRpb25zLmVudGl0aWVzW2ldO1xuICAgICAgICAgICAgdGV4dFZhbHVlID0gdGV4dFZhbHVlLnJlcGxhY2UoZW50aXR5LnJlZ2V4LCBlbnRpdHkudmFsKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdGV4dFZhbHVlO1xufVxubW9kdWxlLmV4cG9ydHMgPSB0b1htbDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js":
/*!*********************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! ../util */ \"(rsc)/./node_modules/fast-xml-parser/src/util.js\");\n\n//TODO: handle comments\nfunction readDocType(xmlData, i){\n    \n    const entities = {};\n    if( xmlData[i + 3] === 'O' &&\n         xmlData[i + 4] === 'C' &&\n         xmlData[i + 5] === 'T' &&\n         xmlData[i + 6] === 'Y' &&\n         xmlData[i + 7] === 'P' &&\n         xmlData[i + 8] === 'E')\n    {    \n        i = i+9;\n        let angleBracketsCount = 1;\n        let hasBody = false, comment = false;\n        let exp = \"\";\n        for(;i<xmlData.length;i++){\n            if (xmlData[i] === '<' && !comment) { //Determine the tag type\n                if( hasBody && isEntity(xmlData, i)){\n                    i += 7; \n                    [entityName, val,i] = readEntityExp(xmlData,i+1);\n                    if(val.indexOf(\"&\") === -1) //Parameter entities are not supported\n                        entities[ validateEntityName(entityName) ] = {\n                            regx : RegExp( `&${entityName};`,\"g\"),\n                            val: val\n                        };\n                }\n                else if( hasBody && isElement(xmlData, i))  i += 8;//Not supported\n                else if( hasBody && isAttlist(xmlData, i))  i += 8;//Not supported\n                else if( hasBody && isNotation(xmlData, i)) i += 9;//Not supported\n                else if( isComment)                         comment = true;\n                else                                        throw new Error(\"Invalid DOCTYPE\");\n\n                angleBracketsCount++;\n                exp = \"\";\n            } else if (xmlData[i] === '>') { //Read tag content\n                if(comment){\n                    if( xmlData[i - 1] === \"-\" && xmlData[i - 2] === \"-\"){\n                        comment = false;\n                        angleBracketsCount--;\n                    }\n                }else{\n                    angleBracketsCount--;\n                }\n                if (angleBracketsCount === 0) {\n                  break;\n                }\n            }else if( xmlData[i] === '['){\n                hasBody = true;\n            }else{\n                exp += xmlData[i];\n            }\n        }\n        if(angleBracketsCount !== 0){\n            throw new Error(`Unclosed DOCTYPE`);\n        }\n    }else{\n        throw new Error(`Invalid Tag instead of DOCTYPE`);\n    }\n    return {entities, i};\n}\n\nfunction readEntityExp(xmlData,i){\n    //External entities are not supported\n    //    <!ENTITY ext SYSTEM \"http://normal-website.com\" >\n\n    //Parameter entities are not supported\n    //    <!ENTITY entityname \"&anotherElement;\">\n\n    //Internal entities are supported\n    //    <!ENTITY entityname \"replacement text\">\n    \n    //read EntityName\n    let entityName = \"\";\n    for (; i < xmlData.length && (xmlData[i] !== \"'\" && xmlData[i] !== '\"' ); i++) {\n        // if(xmlData[i] === \" \") continue;\n        // else \n        entityName += xmlData[i];\n    }\n    entityName = entityName.trim();\n    if(entityName.indexOf(\" \") !== -1) throw new Error(\"External entites are not supported\");\n\n    //read Entity Value\n    const startChar = xmlData[i++];\n    let val = \"\"\n    for (; i < xmlData.length && xmlData[i] !== startChar ; i++) {\n        val += xmlData[i];\n    }\n    return [entityName, val, i];\n}\n\nfunction isComment(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === '-' &&\n    xmlData[i+3] === '-') return true\n    return false\n}\nfunction isEntity(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === 'E' &&\n    xmlData[i+3] === 'N' &&\n    xmlData[i+4] === 'T' &&\n    xmlData[i+5] === 'I' &&\n    xmlData[i+6] === 'T' &&\n    xmlData[i+7] === 'Y') return true\n    return false\n}\nfunction isElement(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === 'E' &&\n    xmlData[i+3] === 'L' &&\n    xmlData[i+4] === 'E' &&\n    xmlData[i+5] === 'M' &&\n    xmlData[i+6] === 'E' &&\n    xmlData[i+7] === 'N' &&\n    xmlData[i+8] === 'T') return true\n    return false\n}\n\nfunction isAttlist(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === 'A' &&\n    xmlData[i+3] === 'T' &&\n    xmlData[i+4] === 'T' &&\n    xmlData[i+5] === 'L' &&\n    xmlData[i+6] === 'I' &&\n    xmlData[i+7] === 'S' &&\n    xmlData[i+8] === 'T') return true\n    return false\n}\nfunction isNotation(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === 'N' &&\n    xmlData[i+3] === 'O' &&\n    xmlData[i+4] === 'T' &&\n    xmlData[i+5] === 'A' &&\n    xmlData[i+6] === 'T' &&\n    xmlData[i+7] === 'I' &&\n    xmlData[i+8] === 'O' &&\n    xmlData[i+9] === 'N') return true\n    return false\n}\n\nfunction validateEntityName(name){\n    if (util.isName(name))\n\treturn name;\n    else\n        throw new Error(`Invalid entity name ${name}`);\n}\n\nmodule.exports = readDocType;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js":
/*!**********************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nconst defaultOptions = {\n    preserveOrder: false,\n    attributeNamePrefix: '@_',\n    attributesGroupName: false,\n    textNodeName: '#text',\n    ignoreAttributes: true,\n    removeNSPrefix: false, // remove NS from tag name or attribute name if true\n    allowBooleanAttributes: false, //a tag can have attributes without any value\n    //ignoreRootElement : false,\n    parseTagValue: true,\n    parseAttributeValue: false,\n    trimValues: true, //Trim string values of tag and attributes\n    cdataPropName: false,\n    numberParseOptions: {\n      hex: true,\n      leadingZeros: true,\n      eNotation: true\n    },\n    tagValueProcessor: function(tagName, val) {\n      return val;\n    },\n    attributeValueProcessor: function(attrName, val) {\n      return val;\n    },\n    stopNodes: [], //nested tags will not be parsed even for errors\n    alwaysCreateTextNode: false,\n    isArray: () => false,\n    commentPropName: false,\n    unpairedTags: [],\n    processEntities: true,\n    htmlEntities: false,\n    ignoreDeclaration: false,\n    ignorePiTags: false,\n    transformTagName: false,\n    transformAttributeName: false,\n    updateTag: function(tagName, jPath, attrs){\n      return tagName\n    },\n    // skipEmptyListItem: false\n};\n   \nconst buildOptions = function(options) {\n    return Object.assign({}, defaultOptions, options);\n};\n\nexports.buildOptions = buildOptions;\nexports.defaultOptions = defaultOptions;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js":
/*!************************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n///@ts-check\n\nconst util = __webpack_require__(/*! ../util */ \"(rsc)/./node_modules/fast-xml-parser/src/util.js\");\nconst xmlNode = __webpack_require__(/*! ./xmlNode */ \"(rsc)/./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js\");\nconst readDocType = __webpack_require__(/*! ./DocTypeReader */ \"(rsc)/./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js\");\nconst toNumber = __webpack_require__(/*! strnum */ \"(rsc)/./node_modules/strnum/strnum.js\");\n\n// const regx =\n//   '<((!\\\\[CDATA\\\\[([\\\\s\\\\S]*?)(]]>))|((NAME:)?(NAME))([^>]*)>|((\\\\/)(NAME)\\\\s*>))([^<]*)'\n//   .replace(/NAME/g, util.nameRegexp);\n\n//const tagsRegx = new RegExp(\"<(\\\\/?[\\\\w:\\\\-\\._]+)([^>]*)>(\\\\s*\"+cdataRegx+\")*([^<]+)?\",\"g\");\n//const tagsRegx = new RegExp(\"<(\\\\/?)((\\\\w*:)?([\\\\w:\\\\-\\._]+))([^>]*)>([^<]*)(\"+cdataRegx+\"([^<]*))*([^<]+)?\",\"g\");\n\nclass OrderedObjParser{\n  constructor(options){\n    this.options = options;\n    this.currentNode = null;\n    this.tagsNodeStack = [];\n    this.docTypeEntities = {};\n    this.lastEntities = {\n      \"apos\" : { regex: /&(apos|#39|#x27);/g, val : \"'\"},\n      \"gt\" : { regex: /&(gt|#62|#x3E);/g, val : \">\"},\n      \"lt\" : { regex: /&(lt|#60|#x3C);/g, val : \"<\"},\n      \"quot\" : { regex: /&(quot|#34|#x22);/g, val : \"\\\"\"},\n    };\n    this.ampEntity = { regex: /&(amp|#38|#x26);/g, val : \"&\"};\n    this.htmlEntities = {\n      \"space\": { regex: /&(nbsp|#160);/g, val: \" \" },\n      // \"lt\" : { regex: /&(lt|#60);/g, val: \"<\" },\n      // \"gt\" : { regex: /&(gt|#62);/g, val: \">\" },\n      // \"amp\" : { regex: /&(amp|#38);/g, val: \"&\" },\n      // \"quot\" : { regex: /&(quot|#34);/g, val: \"\\\"\" },\n      // \"apos\" : { regex: /&(apos|#39);/g, val: \"'\" },\n      \"cent\" : { regex: /&(cent|#162);/g, val: \"¢\" },\n      \"pound\" : { regex: /&(pound|#163);/g, val: \"£\" },\n      \"yen\" : { regex: /&(yen|#165);/g, val: \"¥\" },\n      \"euro\" : { regex: /&(euro|#8364);/g, val: \"€\" },\n      \"copyright\" : { regex: /&(copy|#169);/g, val: \"©\" },\n      \"reg\" : { regex: /&(reg|#174);/g, val: \"®\" },\n      \"inr\" : { regex: /&(inr|#8377);/g, val: \"₹\" },\n      \"num_dec\": { regex: /&#([0-9]{1,7});/g, val : (_, str) => String.fromCharCode(Number.parseInt(str, 10)) },\n      \"num_hex\": { regex: /&#x([0-9a-fA-F]{1,6});/g, val : (_, str) => String.fromCharCode(Number.parseInt(str, 16)) },\n    };\n    this.addExternalEntities = addExternalEntities;\n    this.parseXml = parseXml;\n    this.parseTextData = parseTextData;\n    this.resolveNameSpace = resolveNameSpace;\n    this.buildAttributesMap = buildAttributesMap;\n    this.isItStopNode = isItStopNode;\n    this.replaceEntitiesValue = replaceEntitiesValue;\n    this.readStopNodeData = readStopNodeData;\n    this.saveTextToParentTag = saveTextToParentTag;\n    this.addChild = addChild;\n  }\n\n}\n\nfunction addExternalEntities(externalEntities){\n  const entKeys = Object.keys(externalEntities);\n  for (let i = 0; i < entKeys.length; i++) {\n    const ent = entKeys[i];\n    this.lastEntities[ent] = {\n       regex: new RegExp(\"&\"+ent+\";\",\"g\"),\n       val : externalEntities[ent]\n    }\n  }\n}\n\n/**\n * @param {string} val\n * @param {string} tagName\n * @param {string} jPath\n * @param {boolean} dontTrim\n * @param {boolean} hasAttributes\n * @param {boolean} isLeafNode\n * @param {boolean} escapeEntities\n */\nfunction parseTextData(val, tagName, jPath, dontTrim, hasAttributes, isLeafNode, escapeEntities) {\n  if (val !== undefined) {\n    if (this.options.trimValues && !dontTrim) {\n      val = val.trim();\n    }\n    if(val.length > 0){\n      if(!escapeEntities) val = this.replaceEntitiesValue(val);\n      \n      const newval = this.options.tagValueProcessor(tagName, val, jPath, hasAttributes, isLeafNode);\n      if(newval === null || newval === undefined){\n        //don't parse\n        return val;\n      }else if(typeof newval !== typeof val || newval !== val){\n        //overwrite\n        return newval;\n      }else if(this.options.trimValues){\n        return parseValue(val, this.options.parseTagValue, this.options.numberParseOptions);\n      }else{\n        const trimmedVal = val.trim();\n        if(trimmedVal === val){\n          return parseValue(val, this.options.parseTagValue, this.options.numberParseOptions);\n        }else{\n          return val;\n        }\n      }\n    }\n  }\n}\n\nfunction resolveNameSpace(tagname) {\n  if (this.options.removeNSPrefix) {\n    const tags = tagname.split(':');\n    const prefix = tagname.charAt(0) === '/' ? '/' : '';\n    if (tags[0] === 'xmlns') {\n      return '';\n    }\n    if (tags.length === 2) {\n      tagname = prefix + tags[1];\n    }\n  }\n  return tagname;\n}\n\n//TODO: change regex to capture NS\n//const attrsRegx = new RegExp(\"([\\\\w\\\\-\\\\.\\\\:]+)\\\\s*=\\\\s*(['\\\"])((.|\\n)*?)\\\\2\",\"gm\");\nconst attrsRegx = new RegExp('([^\\\\s=]+)\\\\s*(=\\\\s*([\\'\"])([\\\\s\\\\S]*?)\\\\3)?', 'gm');\n\nfunction buildAttributesMap(attrStr, jPath, tagName) {\n  if (!this.options.ignoreAttributes && typeof attrStr === 'string') {\n    // attrStr = attrStr.replace(/\\r?\\n/g, ' ');\n    //attrStr = attrStr || attrStr.trim();\n\n    const matches = util.getAllMatches(attrStr, attrsRegx);\n    const len = matches.length; //don't make it inline\n    const attrs = {};\n    for (let i = 0; i < len; i++) {\n      const attrName = this.resolveNameSpace(matches[i][1]);\n      let oldVal = matches[i][4];\n      let aName = this.options.attributeNamePrefix + attrName;\n      if (attrName.length) {\n        if (this.options.transformAttributeName) {\n          aName = this.options.transformAttributeName(aName);\n        }\n        if(aName === \"__proto__\") aName  = \"#__proto__\";\n        if (oldVal !== undefined) {\n          if (this.options.trimValues) {\n            oldVal = oldVal.trim();\n          }\n          oldVal = this.replaceEntitiesValue(oldVal);\n          const newVal = this.options.attributeValueProcessor(attrName, oldVal, jPath);\n          if(newVal === null || newVal === undefined){\n            //don't parse\n            attrs[aName] = oldVal;\n          }else if(typeof newVal !== typeof oldVal || newVal !== oldVal){\n            //overwrite\n            attrs[aName] = newVal;\n          }else{\n            //parse\n            attrs[aName] = parseValue(\n              oldVal,\n              this.options.parseAttributeValue,\n              this.options.numberParseOptions\n            );\n          }\n        } else if (this.options.allowBooleanAttributes) {\n          attrs[aName] = true;\n        }\n      }\n    }\n    if (!Object.keys(attrs).length) {\n      return;\n    }\n    if (this.options.attributesGroupName) {\n      const attrCollection = {};\n      attrCollection[this.options.attributesGroupName] = attrs;\n      return attrCollection;\n    }\n    return attrs\n  }\n}\n\nconst parseXml = function(xmlData) {\n  xmlData = xmlData.replace(/\\r\\n?/g, \"\\n\"); //TODO: remove this line\n  const xmlObj = new xmlNode('!xml');\n  let currentNode = xmlObj;\n  let textData = \"\";\n  let jPath = \"\";\n  for(let i=0; i< xmlData.length; i++){//for each char in XML data\n    const ch = xmlData[i];\n    if(ch === '<'){\n      // const nextIndex = i+1;\n      // const _2ndChar = xmlData[nextIndex];\n      if( xmlData[i+1] === '/') {//Closing Tag\n        const closeIndex = findClosingIndex(xmlData, \">\", i, \"Closing Tag is not closed.\")\n        let tagName = xmlData.substring(i+2,closeIndex).trim();\n\n        if(this.options.removeNSPrefix){\n          const colonIndex = tagName.indexOf(\":\");\n          if(colonIndex !== -1){\n            tagName = tagName.substr(colonIndex+1);\n          }\n        }\n\n        if(this.options.transformTagName) {\n          tagName = this.options.transformTagName(tagName);\n        }\n\n        if(currentNode){\n          textData = this.saveTextToParentTag(textData, currentNode, jPath);\n        }\n\n        //check if last tag of nested tag was unpaired tag\n        const lastTagName = jPath.substring(jPath.lastIndexOf(\".\")+1);\n        if(tagName && this.options.unpairedTags.indexOf(tagName) !== -1 ){\n          throw new Error(`Unpaired tag can not be used as closing tag: </${tagName}>`);\n        }\n        let propIndex = 0\n        if(lastTagName && this.options.unpairedTags.indexOf(lastTagName) !== -1 ){\n          propIndex = jPath.lastIndexOf('.', jPath.lastIndexOf('.')-1)\n          this.tagsNodeStack.pop();\n        }else{\n          propIndex = jPath.lastIndexOf(\".\");\n        }\n        jPath = jPath.substring(0, propIndex);\n\n        currentNode = this.tagsNodeStack.pop();//avoid recursion, set the parent tag scope\n        textData = \"\";\n        i = closeIndex;\n      } else if( xmlData[i+1] === '?') {\n\n        let tagData = readTagExp(xmlData,i, false, \"?>\");\n        if(!tagData) throw new Error(\"Pi Tag is not closed.\");\n\n        textData = this.saveTextToParentTag(textData, currentNode, jPath);\n        if( (this.options.ignoreDeclaration && tagData.tagName === \"?xml\") || this.options.ignorePiTags){\n\n        }else{\n  \n          const childNode = new xmlNode(tagData.tagName);\n          childNode.add(this.options.textNodeName, \"\");\n          \n          if(tagData.tagName !== tagData.tagExp && tagData.attrExpPresent){\n            childNode[\":@\"] = this.buildAttributesMap(tagData.tagExp, jPath, tagData.tagName);\n          }\n          this.addChild(currentNode, childNode, jPath)\n\n        }\n\n\n        i = tagData.closeIndex + 1;\n      } else if(xmlData.substr(i + 1, 3) === '!--') {\n        const endIndex = findClosingIndex(xmlData, \"-->\", i+4, \"Comment is not closed.\")\n        if(this.options.commentPropName){\n          const comment = xmlData.substring(i + 4, endIndex - 2);\n\n          textData = this.saveTextToParentTag(textData, currentNode, jPath);\n\n          currentNode.add(this.options.commentPropName, [ { [this.options.textNodeName] : comment } ]);\n        }\n        i = endIndex;\n      } else if( xmlData.substr(i + 1, 2) === '!D') {\n        const result = readDocType(xmlData, i);\n        this.docTypeEntities = result.entities;\n        i = result.i;\n      }else if(xmlData.substr(i + 1, 2) === '![') {\n        const closeIndex = findClosingIndex(xmlData, \"]]>\", i, \"CDATA is not closed.\") - 2;\n        const tagExp = xmlData.substring(i + 9,closeIndex);\n\n        textData = this.saveTextToParentTag(textData, currentNode, jPath);\n\n        let val = this.parseTextData(tagExp, currentNode.tagname, jPath, true, false, true, true);\n        if(val == undefined) val = \"\";\n\n        //cdata should be set even if it is 0 length string\n        if(this.options.cdataPropName){\n          currentNode.add(this.options.cdataPropName, [ { [this.options.textNodeName] : tagExp } ]);\n        }else{\n          currentNode.add(this.options.textNodeName, val);\n        }\n        \n        i = closeIndex + 2;\n      }else {//Opening tag\n        let result = readTagExp(xmlData,i, this.options.removeNSPrefix);\n        let tagName= result.tagName;\n        const rawTagName = result.rawTagName;\n        let tagExp = result.tagExp;\n        let attrExpPresent = result.attrExpPresent;\n        let closeIndex = result.closeIndex;\n\n        if (this.options.transformTagName) {\n          tagName = this.options.transformTagName(tagName);\n        }\n        \n        //save text as child node\n        if (currentNode && textData) {\n          if(currentNode.tagname !== '!xml'){\n            //when nested tag is found\n            textData = this.saveTextToParentTag(textData, currentNode, jPath, false);\n          }\n        }\n\n        //check if last tag was unpaired tag\n        const lastTag = currentNode;\n        if(lastTag && this.options.unpairedTags.indexOf(lastTag.tagname) !== -1 ){\n          currentNode = this.tagsNodeStack.pop();\n          jPath = jPath.substring(0, jPath.lastIndexOf(\".\"));\n        }\n        if(tagName !== xmlObj.tagname){\n          jPath += jPath ? \".\" + tagName : tagName;\n        }\n        if (this.isItStopNode(this.options.stopNodes, jPath, tagName)) {\n          let tagContent = \"\";\n          //self-closing tag\n          if(tagExp.length > 0 && tagExp.lastIndexOf(\"/\") === tagExp.length - 1){\n            if(tagName[tagName.length - 1] === \"/\"){ //remove trailing '/'\n              tagName = tagName.substr(0, tagName.length - 1);\n              jPath = jPath.substr(0, jPath.length - 1);\n              tagExp = tagName;\n            }else{\n              tagExp = tagExp.substr(0, tagExp.length - 1);\n            }\n            i = result.closeIndex;\n          }\n          //unpaired tag\n          else if(this.options.unpairedTags.indexOf(tagName) !== -1){\n            \n            i = result.closeIndex;\n          }\n          //normal tag\n          else{\n            //read until closing tag is found\n            const result = this.readStopNodeData(xmlData, rawTagName, closeIndex + 1);\n            if(!result) throw new Error(`Unexpected end of ${rawTagName}`);\n            i = result.i;\n            tagContent = result.tagContent;\n          }\n\n          const childNode = new xmlNode(tagName);\n          if(tagName !== tagExp && attrExpPresent){\n            childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n          }\n          if(tagContent) {\n            tagContent = this.parseTextData(tagContent, tagName, jPath, true, attrExpPresent, true, true);\n          }\n          \n          jPath = jPath.substr(0, jPath.lastIndexOf(\".\"));\n          childNode.add(this.options.textNodeName, tagContent);\n          \n          this.addChild(currentNode, childNode, jPath)\n        }else{\n  //selfClosing tag\n          if(tagExp.length > 0 && tagExp.lastIndexOf(\"/\") === tagExp.length - 1){\n            if(tagName[tagName.length - 1] === \"/\"){ //remove trailing '/'\n              tagName = tagName.substr(0, tagName.length - 1);\n              jPath = jPath.substr(0, jPath.length - 1);\n              tagExp = tagName;\n            }else{\n              tagExp = tagExp.substr(0, tagExp.length - 1);\n            }\n            \n            if(this.options.transformTagName) {\n              tagName = this.options.transformTagName(tagName);\n            }\n\n            const childNode = new xmlNode(tagName);\n            if(tagName !== tagExp && attrExpPresent){\n              childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n            }\n            this.addChild(currentNode, childNode, jPath)\n            jPath = jPath.substr(0, jPath.lastIndexOf(\".\"));\n          }\n    //opening tag\n          else{\n            const childNode = new xmlNode( tagName);\n            this.tagsNodeStack.push(currentNode);\n            \n            if(tagName !== tagExp && attrExpPresent){\n              childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n            }\n            this.addChild(currentNode, childNode, jPath)\n            currentNode = childNode;\n          }\n          textData = \"\";\n          i = closeIndex;\n        }\n      }\n    }else{\n      textData += xmlData[i];\n    }\n  }\n  return xmlObj.child;\n}\n\nfunction addChild(currentNode, childNode, jPath){\n  const result = this.options.updateTag(childNode.tagname, jPath, childNode[\":@\"])\n  if(result === false){\n  }else if(typeof result === \"string\"){\n    childNode.tagname = result\n    currentNode.addChild(childNode);\n  }else{\n    currentNode.addChild(childNode);\n  }\n}\n\nconst replaceEntitiesValue = function(val){\n\n  if(this.options.processEntities){\n    for(let entityName in this.docTypeEntities){\n      const entity = this.docTypeEntities[entityName];\n      val = val.replace( entity.regx, entity.val);\n    }\n    for(let entityName in this.lastEntities){\n      const entity = this.lastEntities[entityName];\n      val = val.replace( entity.regex, entity.val);\n    }\n    if(this.options.htmlEntities){\n      for(let entityName in this.htmlEntities){\n        const entity = this.htmlEntities[entityName];\n        val = val.replace( entity.regex, entity.val);\n      }\n    }\n    val = val.replace( this.ampEntity.regex, this.ampEntity.val);\n  }\n  return val;\n}\nfunction saveTextToParentTag(textData, currentNode, jPath, isLeafNode) {\n  if (textData) { //store previously collected data as textNode\n    if(isLeafNode === undefined) isLeafNode = Object.keys(currentNode.child).length === 0\n    \n    textData = this.parseTextData(textData,\n      currentNode.tagname,\n      jPath,\n      false,\n      currentNode[\":@\"] ? Object.keys(currentNode[\":@\"]).length !== 0 : false,\n      isLeafNode);\n\n    if (textData !== undefined && textData !== \"\")\n      currentNode.add(this.options.textNodeName, textData);\n    textData = \"\";\n  }\n  return textData;\n}\n\n//TODO: use jPath to simplify the logic\n/**\n * \n * @param {string[]} stopNodes \n * @param {string} jPath\n * @param {string} currentTagName \n */\nfunction isItStopNode(stopNodes, jPath, currentTagName){\n  const allNodesExp = \"*.\" + currentTagName;\n  for (const stopNodePath in stopNodes) {\n    const stopNodeExp = stopNodes[stopNodePath];\n    if( allNodesExp === stopNodeExp || jPath === stopNodeExp  ) return true;\n  }\n  return false;\n}\n\n/**\n * Returns the tag Expression and where it is ending handling single-double quotes situation\n * @param {string} xmlData \n * @param {number} i starting index\n * @returns \n */\nfunction tagExpWithClosingIndex(xmlData, i, closingChar = \">\"){\n  let attrBoundary;\n  let tagExp = \"\";\n  for (let index = i; index < xmlData.length; index++) {\n    let ch = xmlData[index];\n    if (attrBoundary) {\n        if (ch === attrBoundary) attrBoundary = \"\";//reset\n    } else if (ch === '\"' || ch === \"'\") {\n        attrBoundary = ch;\n    } else if (ch === closingChar[0]) {\n      if(closingChar[1]){\n        if(xmlData[index + 1] === closingChar[1]){\n          return {\n            data: tagExp,\n            index: index\n          }\n        }\n      }else{\n        return {\n          data: tagExp,\n          index: index\n        }\n      }\n    } else if (ch === '\\t') {\n      ch = \" \"\n    }\n    tagExp += ch;\n  }\n}\n\nfunction findClosingIndex(xmlData, str, i, errMsg){\n  const closingIndex = xmlData.indexOf(str, i);\n  if(closingIndex === -1){\n    throw new Error(errMsg)\n  }else{\n    return closingIndex + str.length - 1;\n  }\n}\n\nfunction readTagExp(xmlData,i, removeNSPrefix, closingChar = \">\"){\n  const result = tagExpWithClosingIndex(xmlData, i+1, closingChar);\n  if(!result) return;\n  let tagExp = result.data;\n  const closeIndex = result.index;\n  const separatorIndex = tagExp.search(/\\s/);\n  let tagName = tagExp;\n  let attrExpPresent = true;\n  if(separatorIndex !== -1){//separate tag name and attributes expression\n    tagName = tagExp.substring(0, separatorIndex);\n    tagExp = tagExp.substring(separatorIndex + 1).trimStart();\n  }\n\n  const rawTagName = tagName;\n  if(removeNSPrefix){\n    const colonIndex = tagName.indexOf(\":\");\n    if(colonIndex !== -1){\n      tagName = tagName.substr(colonIndex+1);\n      attrExpPresent = tagName !== result.data.substr(colonIndex + 1);\n    }\n  }\n\n  return {\n    tagName: tagName,\n    tagExp: tagExp,\n    closeIndex: closeIndex,\n    attrExpPresent: attrExpPresent,\n    rawTagName: rawTagName,\n  }\n}\n/**\n * find paired tag for a stop node\n * @param {string} xmlData \n * @param {string} tagName \n * @param {number} i \n */\nfunction readStopNodeData(xmlData, tagName, i){\n  const startIndex = i;\n  // Starting at 1 since we already have an open tag\n  let openTagCount = 1;\n\n  for (; i < xmlData.length; i++) {\n    if( xmlData[i] === \"<\"){ \n      if (xmlData[i+1] === \"/\") {//close tag\n          const closeIndex = findClosingIndex(xmlData, \">\", i, `${tagName} is not closed`);\n          let closeTagName = xmlData.substring(i+2,closeIndex).trim();\n          if(closeTagName === tagName){\n            openTagCount--;\n            if (openTagCount === 0) {\n              return {\n                tagContent: xmlData.substring(startIndex, i),\n                i : closeIndex\n              }\n            }\n          }\n          i=closeIndex;\n        } else if(xmlData[i+1] === '?') { \n          const closeIndex = findClosingIndex(xmlData, \"?>\", i+1, \"StopNode is not closed.\")\n          i=closeIndex;\n        } else if(xmlData.substr(i + 1, 3) === '!--') { \n          const closeIndex = findClosingIndex(xmlData, \"-->\", i+3, \"StopNode is not closed.\")\n          i=closeIndex;\n        } else if(xmlData.substr(i + 1, 2) === '![') { \n          const closeIndex = findClosingIndex(xmlData, \"]]>\", i, \"StopNode is not closed.\") - 2;\n          i=closeIndex;\n        } else {\n          const tagData = readTagExp(xmlData, i, '>')\n\n          if (tagData) {\n            const openTagName = tagData && tagData.tagName;\n            if (openTagName === tagName && tagData.tagExp[tagData.tagExp.length-1] !== \"/\") {\n              openTagCount++;\n            }\n            i=tagData.closeIndex;\n          }\n        }\n      }\n  }//end for loop\n}\n\nfunction parseValue(val, shouldParse, options) {\n  if (shouldParse && typeof val === 'string') {\n    //console.log(options)\n    const newval = val.trim();\n    if(newval === 'true' ) return true;\n    else if(newval === 'false' ) return false;\n    else return toNumber(val, options);\n  } else {\n    if (util.isExist(val)) {\n      return val;\n    } else {\n      return '';\n    }\n  }\n}\n\n\nmodule.exports = OrderedObjParser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { buildOptions} = __webpack_require__(/*! ./OptionsBuilder */ \"(rsc)/./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js\");\nconst OrderedObjParser = __webpack_require__(/*! ./OrderedObjParser */ \"(rsc)/./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js\");\nconst { prettify} = __webpack_require__(/*! ./node2json */ \"(rsc)/./node_modules/fast-xml-parser/src/xmlparser/node2json.js\");\nconst validator = __webpack_require__(/*! ../validator */ \"(rsc)/./node_modules/fast-xml-parser/src/validator.js\");\n\nclass XMLParser{\n    \n    constructor(options){\n        this.externalEntities = {};\n        this.options = buildOptions(options);\n        \n    }\n    /**\n     * Parse XML dats to JS object \n     * @param {string|Buffer} xmlData \n     * @param {boolean|Object} validationOption \n     */\n    parse(xmlData,validationOption){\n        if(typeof xmlData === \"string\"){\n        }else if( xmlData.toString){\n            xmlData = xmlData.toString();\n        }else{\n            throw new Error(\"XML data is accepted in String or Bytes[] form.\")\n        }\n        if( validationOption){\n            if(validationOption === true) validationOption = {}; //validate with default options\n            \n            const result = validator.validate(xmlData, validationOption);\n            if (result !== true) {\n              throw Error( `${result.err.msg}:${result.err.line}:${result.err.col}` )\n            }\n          }\n        const orderedObjParser = new OrderedObjParser(this.options);\n        orderedObjParser.addExternalEntities(this.externalEntities);\n        const orderedResult = orderedObjParser.parseXml(xmlData);\n        if(this.options.preserveOrder || orderedResult === undefined) return orderedResult;\n        else return prettify(orderedResult, this.options);\n    }\n\n    /**\n     * Add Entity which is not by default supported by this library\n     * @param {string} key \n     * @param {string} value \n     */\n    addEntity(key, value){\n        if(value.indexOf(\"&\") !== -1){\n            throw new Error(\"Entity value can't have '&'\")\n        }else if(key.indexOf(\"&\") !== -1 || key.indexOf(\";\") !== -1){\n            throw new Error(\"An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'\")\n        }else if(value === \"&\"){\n            throw new Error(\"An entity with value '&' is not permitted\");\n        }else{\n            this.externalEntities[key] = value;\n        }\n    }\n}\n\nmodule.exports = XMLParser;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/xmlparser/node2json.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/node2json.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\n/**\n * \n * @param {array} node \n * @param {any} options \n * @returns \n */\nfunction prettify(node, options){\n  return compress( node, options);\n}\n\n/**\n * \n * @param {array} arr \n * @param {object} options \n * @param {string} jPath \n * @returns object\n */\nfunction compress(arr, options, jPath){\n  let text;\n  const compressedObj = {};\n  for (let i = 0; i < arr.length; i++) {\n    const tagObj = arr[i];\n    const property = propName(tagObj);\n    let newJpath = \"\";\n    if(jPath === undefined) newJpath = property;\n    else newJpath = jPath + \".\" + property;\n\n    if(property === options.textNodeName){\n      if(text === undefined) text = tagObj[property];\n      else text += \"\" + tagObj[property];\n    }else if(property === undefined){\n      continue;\n    }else if(tagObj[property]){\n      \n      let val = compress(tagObj[property], options, newJpath);\n      const isLeaf = isLeafTag(val, options);\n\n      if(tagObj[\":@\"]){\n        assignAttributes( val, tagObj[\":@\"], newJpath, options);\n      }else if(Object.keys(val).length === 1 && val[options.textNodeName] !== undefined && !options.alwaysCreateTextNode){\n        val = val[options.textNodeName];\n      }else if(Object.keys(val).length === 0){\n        if(options.alwaysCreateTextNode) val[options.textNodeName] = \"\";\n        else val = \"\";\n      }\n\n      if(compressedObj[property] !== undefined && compressedObj.hasOwnProperty(property)) {\n        if(!Array.isArray(compressedObj[property])) {\n            compressedObj[property] = [ compressedObj[property] ];\n        }\n        compressedObj[property].push(val);\n      }else{\n        //TODO: if a node is not an array, then check if it should be an array\n        //also determine if it is a leaf node\n        if (options.isArray(property, newJpath, isLeaf )) {\n          compressedObj[property] = [val];\n        }else{\n          compressedObj[property] = val;\n        }\n      }\n    }\n    \n  }\n  // if(text && text.length > 0) compressedObj[options.textNodeName] = text;\n  if(typeof text === \"string\"){\n    if(text.length > 0) compressedObj[options.textNodeName] = text;\n  }else if(text !== undefined) compressedObj[options.textNodeName] = text;\n  return compressedObj;\n}\n\nfunction propName(obj){\n  const keys = Object.keys(obj);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    if(key !== \":@\") return key;\n  }\n}\n\nfunction assignAttributes(obj, attrMap, jpath, options){\n  if (attrMap) {\n    const keys = Object.keys(attrMap);\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      const atrrName = keys[i];\n      if (options.isArray(atrrName, jpath + \".\" + atrrName, true, true)) {\n        obj[atrrName] = [ attrMap[atrrName] ];\n      } else {\n        obj[atrrName] = attrMap[atrrName];\n      }\n    }\n  }\n}\n\nfunction isLeafTag(obj, options){\n  const { textNodeName } = options;\n  const propCount = Object.keys(obj).length;\n  \n  if (propCount === 0) {\n    return true;\n  }\n\n  if (\n    propCount === 1 &&\n    (obj[textNodeName] || typeof obj[textNodeName] === \"boolean\" || obj[textNodeName] === 0)\n  ) {\n    return true;\n  }\n\n  return false;\n}\nexports.prettify = prettify;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/xmlparser/node2json.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js":
/*!***************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nclass XmlNode{\n  constructor(tagname) {\n    this.tagname = tagname;\n    this.child = []; //nested tags, text, cdata, comments in order\n    this[\":@\"] = {}; //attributes map\n  }\n  add(key,val){\n    // this.child.push( {name : key, val: val, isCdata: isCdata });\n    if(key === \"__proto__\") key = \"#__proto__\";\n    this.child.push( {[key]: val });\n  }\n  addChild(node) {\n    if(node.tagname === \"__proto__\") node.tagname = \"#__proto__\";\n    if(node[\":@\"] && Object.keys(node[\":@\"]).length > 0){\n      this.child.push( { [node.tagname]: node.child, [\":@\"]: node[\":@\"] });\n    }else{\n      this.child.push( { [node.tagname]: node.child });\n    }\n  };\n};\n\n\nmodule.exports = XmlNode;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmFzdC14bWwtcGFyc2VyL3NyYy94bWxwYXJzZXIveG1sTm9kZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSx5QkFBeUIsd0NBQXdDO0FBQ2pFO0FBQ0Esc0JBQXNCLFlBQVk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsZ0RBQWdEO0FBQ3pFLEtBQUs7QUFDTCx5QkFBeUIsNEJBQTRCO0FBQ3JEO0FBQ0E7QUFDQTs7O0FBR0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcZmFzdC14bWwtcGFyc2VyXFxzcmNcXHhtbHBhcnNlclxceG1sTm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNsYXNzIFhtbE5vZGV7XG4gIGNvbnN0cnVjdG9yKHRhZ25hbWUpIHtcbiAgICB0aGlzLnRhZ25hbWUgPSB0YWduYW1lO1xuICAgIHRoaXMuY2hpbGQgPSBbXTsgLy9uZXN0ZWQgdGFncywgdGV4dCwgY2RhdGEsIGNvbW1lbnRzIGluIG9yZGVyXG4gICAgdGhpc1tcIjpAXCJdID0ge307IC8vYXR0cmlidXRlcyBtYXBcbiAgfVxuICBhZGQoa2V5LHZhbCl7XG4gICAgLy8gdGhpcy5jaGlsZC5wdXNoKCB7bmFtZSA6IGtleSwgdmFsOiB2YWwsIGlzQ2RhdGE6IGlzQ2RhdGEgfSk7XG4gICAgaWYoa2V5ID09PSBcIl9fcHJvdG9fX1wiKSBrZXkgPSBcIiNfX3Byb3RvX19cIjtcbiAgICB0aGlzLmNoaWxkLnB1c2goIHtba2V5XTogdmFsIH0pO1xuICB9XG4gIGFkZENoaWxkKG5vZGUpIHtcbiAgICBpZihub2RlLnRhZ25hbWUgPT09IFwiX19wcm90b19fXCIpIG5vZGUudGFnbmFtZSA9IFwiI19fcHJvdG9fX1wiO1xuICAgIGlmKG5vZGVbXCI6QFwiXSAmJiBPYmplY3Qua2V5cyhub2RlW1wiOkBcIl0pLmxlbmd0aCA+IDApe1xuICAgICAgdGhpcy5jaGlsZC5wdXNoKCB7IFtub2RlLnRhZ25hbWVdOiBub2RlLmNoaWxkLCBbXCI6QFwiXTogbm9kZVtcIjpAXCJdIH0pO1xuICAgIH1lbHNle1xuICAgICAgdGhpcy5jaGlsZC5wdXNoKCB7IFtub2RlLnRhZ25hbWVdOiBub2RlLmNoaWxkIH0pO1xuICAgIH1cbiAgfTtcbn07XG5cblxubW9kdWxlLmV4cG9ydHMgPSBYbWxOb2RlOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js\n");

/***/ })

};
;