import re
from typing import Optional
import requests
import os
import difflib
import json
from datetime import datetime

STAGE_URL = 'https://stage.ionm.zinniax.com/api'
CASES_FILTER_API_URL = "https://stage.ionm.zinniax.com/api/cases/filter"
DOCTORS_API_URL = "https://stage.ionm.zinniax.com/api/doctors/cases?isIncludeInactive=false"

import jwt


import threading

THREAD_USER_FILE = "thread_to_user.json"
USER_USAGE_FILE = "user_token_usage.json"
usage_lock = threading.Lock()

# Ensure required JSON files exist
REQUIRED_JSON_FILES = {
    "thread_to_user.json": {},
    "user_token_usage.json": {},
    "tokens.json": {},
    "langgraph.json": {}
}

def ensure_json_files_exist():
    for filename, default_content in REQUIRED_JSON_FILES.items():
        if not os.path.exists(filename):
            with open(filename, "w") as f:
                json.dump(default_content, f, indent=2)

# Call at import time
ensure_json_files_exist()

def load_json_file(path):
    if os.path.exists(path):
        with open(path, "r") as f:
            return json.load(f)
    return {}

def save_json_file(path, data):
    with usage_lock:
        with open(path, "w") as f:
            json.dump(data, f, indent=2)

def bind_thread_to_user(thread_id, username):
    mapping = load_json_file(THREAD_USER_FILE)
    mapping[thread_id] = username
    save_json_file(THREAD_USER_FILE, mapping)

def get_username_for_thread(thread_id):
    mapping = load_json_file(THREAD_USER_FILE)
    return mapping.get(thread_id)

def add_token_usage_for_user(username, tokens_used):
    usage = load_json_file(USER_USAGE_FILE)
    usage[username] = usage.get(username, 0) + tokens_used
    save_json_file(USER_USAGE_FILE, usage)


def decode_jwt_no_secret(token):
    try:
        print('token received', token)
        # decode without signature verification
        decoded = jwt.decode(token, options={"verify_signature": False}, algorithms=["HS512"])
        return decoded['sub']
    except jwt.DecodeError:
        return "Failed to decode token."
    except Exception as e:
        return f"Error: {str(e)}"

# --- Utility Functions ---

# def get_auth_token():
#     """
#     Placeholder for your actual authentication mechanism.
#     Replace this with your robust token retrieval logic.
#     """
#     print("DEBUG: helper.py - Using placeholder get_auth_token()")
#     api_user_email = '<EMAIL>'
#     api_user_password = 'Sunflower@101'

#     if not STAGE_URL or not api_user_email or not api_user_password:
#         print("ERROR: STAGE_URL or API credentials for auth not configured in environment variables.")
#         return None

#     auth_url = f"{STAGE_URL}/authenticate"
#     try:
#         payload = json.dumps({"email": api_user_email, "password": api_user_password})
#         headers = {'Content-Type': 'application/json'}
#         response = requests.post(auth_url, headers=headers, data=payload, timeout=10)
#         response.raise_for_status()
#         token = response.json().get('id_token')
#         if not token:
#             print(f"ERROR: Authentication successful but no id_token in response from {auth_url}")
#             return None
#         return token
#     except requests.exceptions.RequestException as e:
#         print(f"ERROR: Authentication request failed to {auth_url}: {str(e)}")
#         if hasattr(e, 'response') and e.response is not None:
#             print(f"DEBUG: Auth API Error response: {e.response.text}")
#         return None
#     except Exception as e:
#         print(f"ERROR: Unexpected error during authentication: {str(e)}")
#         return None
    
def convert_to_24_hour_format_with_period(time_str: str) -> tuple[str, str]:
    """Convert time string to 24-hour format and also return 'AM' or 'PM'."""

    time_str = time_str.strip().lower()
    time_regex = r'^(\d{1,2})(?::(\d{2}))?\s*(am|pm)?$'
    match = re.match(time_regex, time_str)

    if not match:
        raise ValueError('Invalid time format. Please use formats like "4 pm", "4:30 pm", "16:00", etc.')

    hours, minutes, period = match.groups()
    hour_num = int(hours)

    # AM/PM conversion logic
    if period:
        if period == 'pm' and hour_num != 12:
            hour_num += 12
        elif period == 'am' and hour_num == 12:
            hour_num = 0

    minutes = minutes or '00'
    time_24h = f"{hour_num:02d}:{minutes}"

    # Now, infer AM/PM from 24-hour time
    period_result = "AM" if 0 <= hour_num < 12 else "PM"

    return time_24h, period_result


def convert_to_12_hour_format_with_period(time_str: str) -> tuple[str, str]:
    """Convert time string to 12-hour format with 'AM' or 'PM'."""
    time_str = time_str.strip().lower()
    # Detect input: 12-hour (with am/pm) or 24-hour (no am/pm)
    time_regex = r"^(\d{1,2})(?::(\d{2}))?\s*(am|pm)?$"
    match = re.match(time_regex, time_str)

    if not match:
        raise ValueError('Invalid time format. Please use formats like "16:00", "4 pm", or "4:30 pm".')

    hours, minutes, period = match.groups()
    hour_num = int(hours)
    minutes = minutes or "00"

    if period:  # Input is already 12-hour
        period = period.upper()
        hour_12 = hour_num if 1 <= hour_num <= 12 else ((hour_num - 1) % 12 + 1)
        formatted = f"{hour_12:02d}:{minutes}"
        return formatted, period
    else:  # 24-hour input, need to convert
        # Parse as 24-hour time and convert using datetime
        time_dt = datetime.strptime(f"{hour_num:02d}:{minutes}", "%H:%M")
        time_12hr = time_dt.strftime("%I:%M %p")
        hour, minute, period = time_12hr.split(":")[0], time_12hr.split(":")[1].split()[0], time_12hr.split()[1]
        formatted = f"{hour}:{minute}"
        return formatted, period

def extract_integer_from_text(input_str: str) -> Optional[int]:
    """Extract integer from text response."""
    numbers = re.findall(r'\d+', input_str)
    if numbers:
        return int(numbers[0])
    return None

def clean_surgeon_name(name: str) -> str:
    """Clean surgeon name by removing commas."""
    return name.replace(',', ' ')

def normalize_time(s: str) -> str:
    """Normalize time strings for flexible comparison."""
    if not s:
        return ""
    t = s.strip().upper().replace(' ', '')
    match = re.match(r"^(\d{1,2}):(\d{2})(AM|PM)$", t, re.IGNORECASE)
    if match:
        hour, minute, period = match.groups()
        hour = hour.zfill(2)
        return f"{hour}:{minute}{period}"
    return t

def normalize_name(name, item_type="doctor"):
    if not name: return ""
    
    if item_type == "doctor":
        name_lower = name.strip().lower()
        # Remove prefixes like 'Dr.', 'Dr', 'Doctor', etc. case-insensitively
        normalized = re.sub(r'^(dr\.?\s*|doctor\s*)', '', name_lower, flags=re.IGNORECASE)
        return normalized.strip()
    elif item_type == "hospital":
        # Pass hospital name as it is, just strip whitespace
        return name.strip()
    else:
        # Default behavior for other types (if any) or if item_type is not specified
        return name.strip().lower()

def fetch_doctors_list(token):
    
    api_url =DOCTORS_API_URL
    # token=  get_auth_token()
    headers = {'Authorization': f'Bearer {token}', 'Accept': 'application/json'}
    try:
        response = requests.get(api_url, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()
        if isinstance(data, list):
            return data
        if isinstance(data, dict) and 'content' in data and isinstance(data['content'], list):
            return data['content']
        print(f"WARN: Doctors list API did not return a list. Response: {data}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Error fetching doctors list: {str(e)}")
        return None

def get_hospital_list(token):
    api_url = STAGE_URL + '/hospitals'
    # print(api_url)
    # token = get_auth_token()
    print("hos token",token)
    headers = {'Authorization': f'Bearer {token}', 'Accept': 'application/json'}
    try:
        response = requests.get(api_url, headers=headers)
        print(response)
        response.raise_for_status()
        data = response.json()
        if isinstance(data, list):
            return data
        if isinstance(data, dict) and 'content' in data and isinstance(data['content'], list):
            return data['content']
        print(f"WARN: Doctors list API did not return a list. Response: {data}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Error fetching hospital list: {str(e)}")
        return None

def extract_integer_from_text(input_str):
    # Mapping of spelled-out numbers to digits
    word_to_digit = {
        'zero': '0',
        'one': '1',
        'two': '2',
        'three': '3',
        'four': '4',
        'five': '5',
        'six': '6',
        'seven': '7',
        'eight': '8',
        'nine': '9'
    }

    # Extract words that represent numbers (handling cases like "one...", "zero...")
    words = re.findall(r'\b(\w+)\b', input_str.lower())  # Split into words, ignoring punctuation
    digits = []

    for word in words:
        # Remove trailing non-alphabetic characters (like '...')
        cleaned_word = re.sub(r'[^a-z]', '', word)
        if cleaned_word in word_to_digit:
            digits.append(word_to_digit[cleaned_word])

    # Combine digits into a single integer
    if digits:
        return int(''.join(digits))
    else:
        return None

def fetch_hospital_details(hospital_id, token):

    """
    Fetch detailed information for a specific hospital using its ID.
    
    Args:
        hospital_id (int): The ID of the hospital
        token (str): Authentication token
    
    Returns:
        dict: Hospital details or None if request fails
    """
    headers = {'Authorization': f'Bearer {token}', 'Accept': 'application/json'}
    try:
        print("hospital_idss",hospital_id)
        response = requests.get(f"{STAGE_URL}/hospitals/{hospital_id}", headers=headers, timeout=10)
        print("responsee",response)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching hospital details for ID {hospital_id}: {str(e)}")
        return None


def parse_time_string(input_str, api_token, format=None):
    """
    Parses a human-friendly time string using the time parsing API or returns
    the date string as is. Supports optional output date formatting.

    Args:
        input_str (str): The text to parse (e.g., 'today', '2025-07-25', '12/8/25').
        api_token (str): Bearer token for API authorization.
        format (str, optional): Optional strftime format for output date, e.g. "%Y-%m-%d".

    Returns:
        str or None: Date string either in original or formatted form,
                     or None if parsing fails.
    """

    def format_date(date_str):
        if format:
            possible_input_formats = [
                "%Y-%m-%d",
                "%d/%m/%y", "%m/%d/%y",
                "%d-%m-%Y", "%d-%m-%y",
                "%Y/%m/%d",
                "%Y.%m.%d"
            ]
            for fmt in possible_input_formats:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime(format)
                except ValueError:
                    continue
            # No format succeeded
            return None
        else:
            return date_str

    # If input contains any alphabets, call the time parsing API
    if re.search("[a-zA-Z]", input_str):
        url = "https://8cjacxsnt4.execute-api.us-east-2.amazonaws.com/parse"
        payload = f"locale=en_GB&text={input_str}&timezone=Europe%2FLondon"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Bearer {api_token}"
        }
        try:
            response = requests.post(url, headers=headers, data=payload)
            response.raise_for_status()
            res_json = response.json()

            # Response should be a list with at least one item with "value"
            if res_json and isinstance(res_json, list) and "value" in res_json[0]:
                # Extract date string (YYYY-MM-DD) before 'T'
                date_val = res_json[0]["value"]["value"].split("T")[0]
                return format_date(date_val)
            else:
                return None
        except Exception as e:
            print(f"API error: {e}")
            return None
    else:
        # Input string does not have alphabets, probably already a date
        return format_date(input_str)
     
def find_top_matches(input_name, items_list, item_type="doctor", cutoff_score=0.7, max_matches=5, city=None):
    """
    Find matches for a given input name from a list of items using word-based searching.
    Removes common hospital-related words and searches for the main word in names.
    Optionally filters results by location (city, address, postal code, state, region).
    Uses fuzzy matching for location comparison when exact matches aren't found.
    Returns only the best match when location filtering is applied.
    
    Args:
        input_name (str): The name to search for
        items_list (list): List of items to search through
        item_type (str): Type of item being searched (default: "doctor")
        cutoff_score (float): Minimum similarity score for fuzzy matching (default: 0.7)
        max_matches (int): Maximum number of matches to return (default: 5)
        city (str): Optional location to filter results by (can be city, state, region, etc.)
    
    Returns:
        list: List containing the best matched item with its details
    """
    if not items_list or not isinstance(items_list, list) or not input_name:
        print(f"DEBUG: helper.py - find_top_matches: Invalid input_name or items_list for {item_type}.")
        return []

    # List of common hospital-related words to remove
    common_words = {
        'hospital', 'hospitals', 'medical', 'center', 'centers', 'clinic', 'clinics',
        'health', 'healthcare', 'care', 'institute', 'institution', 'facility',
        'facilities', 'medical center', 'health center', 'healthcare center'
    }

    def clean_name(name):
        
        if isinstance(name , list):
            return name
        """Remove common words and clean the name for searching"""
        if not name:
            return ""
        name_lower = name.lower().strip()
        # Remove common words
        for word in common_words:
            name_lower = name_lower.replace(word, '')
        # Remove extra spaces and punctuation
        name_lower = ' '.join(name_lower.split())
        return name_lower

    def clean_location(location):
        """Clean location string for comparison"""
        if not location:
            return ""
        return location.lower().strip()

    def is_location_match(search_location, field_value, use_fuzzy=True):
        """
        Check if location matches using exact or fuzzy matching
        
        Args:
            search_location (str): Location to search for
            field_value (str): Field value to compare against
            use_fuzzy (bool): Whether to use fuzzy matching if exact match fails
        
        Returns:
            tuple: (bool, float) - (is_match, similarity_ratio)
        """
        if not search_location or not field_value:
            return False, 0.0
            
        search_location = clean_location(search_location)
        field_value = clean_location(field_value)
        
        # Try exact match first
        if search_location in field_value or field_value in search_location:
            return True, 1.0
            
        # If exact match fails and fuzzy matching is enabled, try fuzzy match
        if use_fuzzy:
            # Split into words for better matching
            search_words = search_location.split()
            field_words = field_value.split()
            
            # Try matching individual words
            for search_word in search_words:
                matches = difflib.get_close_matches(search_word, field_words, n=1, cutoff=cutoff_score)
                if matches:
                    ratio = difflib.SequenceMatcher(None, search_word, matches[0]).ratio()
                    if ratio >= cutoff_score:
                        return True, ratio
            
            # Try matching the whole string
            ratio = difflib.SequenceMatcher(None, search_location, field_value).ratio()
            if ratio >= cutoff_score:
                return True, ratio
                
        return False, 0.0

    # Clean the input name
    cleaned_input = clean_name(input_name)
    if not cleaned_input:
        print(f"DEBUG: helper.py - find_top_matches: Cleaned input name is empty for '{input_name}'.")
        return []

    matches = []
    best_match = None
    best_match_score = 0

    for item in items_list:
        if not isinstance(item, dict) or not item.get('name') or item.get('id') is None:
            continue
        hospital_id = item['id']
        original_name = item['name']
        cleaned_name = clean_name(original_name)

        # Check if any word from cleaned input exists in the cleaned name
        input_words = set(cleaned_input.split())
        name_words = set(cleaned_name.split())
        
        # Find matching words
        matching_words = input_words.intersection(name_words)
        
        if matching_words:
            # If location filter is provided, check all location fields
            if city:
                city = clean_location(city)
                location_fields = {
                    'city': item.get('city', ''),
                    'address1': item.get('address1', ''),
                    'address2': item.get('address2', ''),
                    'postalCode': item.get('postalCode', ''),
                    'stateName': item.get('state', ''),
                    'regionName': item.get('hospitalRegion', ''),
                    'callRegionName': item.get('callRegion', '')
                }
                
                # Check each location field for matches
                field_match = None
                best_ratio = 0.0
                
                for field_name, field_value in location_fields.items():
                    if field_value:  # Only check non-empty fields
                        is_match, ratio = is_location_match(city, field_value)
                        if is_match and ratio > best_ratio:
                            field_match = field_name
                            best_ratio = ratio
                
                if field_match:
                    # Calculate overall match score (combining name match and location match)
                    name_match_score = len(matching_words) / len(input_words)
                    overall_score = (name_match_score + best_ratio) / 2
                    
                    if overall_score > best_match_score:
                        best_match_score = overall_score
                        best_match = {
                            'id': item['id'],
                            'name': original_name,
                            'matching_words': list(matching_words),
                            'location': {
                                'city': item.get('city'),
                                'address1': item.get('address1'),
                                'address2': item.get('address2'),
                                'postalCode': item.get('postalCode'),
                                'stateName': item.get('state'),
                                'regionName': item.get('hospitalRegion'),
                                'callRegionName': item.get('callRegion')
                            },
                            'match_details': {
                                'matched_field': field_match,
                                'similarity_ratio': best_ratio,
                                'overall_score': overall_score
                            }
                        }
                        print(f'Found better match in {field_match} with ratio {best_ratio:.2f} and overall score {overall_score:.2f}')
            else:
                matches.append({
                    'id': item['id'],
                    'name': original_name,
                    'matching_words': list(matching_words),
                    'city' : item.get('city', ''),
                    'address': item.get('address1','') ,
                    'region' : item.get('callRegionName','')

                })

    # If we have a location filter, return only the best match
    if city and best_match:
        print(f"DEBUG: Returning best match with overall score {best_match['match_details']['overall_score']:.2f}")
        return [best_match]
    
    # If no location filter, return top N matches
    matches.sort(key=lambda x: len(x['matching_words']), reverse=True)
    print("matches 101",matches)
    return matches[:max_matches]

def fetch_hospital_doctors(hospital_id, token):
    """
    Fetch the list of doctors for a specific hospital.
    
    Args:
        hospital_id (int): The ID of the hospital
        token (str): Authentication token
    
    Returns:
        list: List of dictionaries containing doctor information, or empty list if request fails
    """
    headers = {'Authorization': f'Bearer {token}', 'Accept': 'application/json'}
    try:
        response = requests.get(f"{STAGE_URL}/doctors/hospital/{hospital_id}", headers=headers, timeout=30)
        response.raise_for_status()
        doctors = response.json()
        # print(doctors)
        # Filter out inactive doctors and format the response
        active_doctors = []
        for doctor in doctors:
            if not doctor.get('isActive', False):  # Only include active doctors
                active_doctors.append({
                    'id': doctor.get('id'),
                    'name': doctor.get('name'),
                    'isOnPto': doctor.get('isOnPto', False)
                })
        
        if active_doctors:
            print(f"DEBUG: Found {len(active_doctors)} active doctors for hospital ID {hospital_id}")
        else:
            print(f"DEBUG: No active doctors found for hospital ID {hospital_id}")
            
        return active_doctors
    except requests.exceptions.RequestException as e:
        print(f"Error fetching doctors list for hospital ID {hospital_id}: {str(e)}")
        return []

def extract_first_name(full_name):
   
    if not full_name or not isinstance(full_name, str):
        print("DEBUG: Invalid name provided")
        return None
        
    # Clean the name
    name = full_name.strip().lower()
    
    # Handle comma-separated names (e.g., "Smith, John")
    if ',' in name:
        parts = [part.strip() for part in name.split(',')]
        if len(parts) == 2:
            # If format is "Last, First", return the second part
            return parts[1]
        # If format is "First, Last", return the first part
        return parts[0]
    
    # Handle space-separated names (e.g., "John Smith")
    parts = name.split()
    if parts:
        return parts[0]
    
    return None

def find_hospital_by_initials(hospital_list, user_initials):
    """
    Find a hospital by matching initials from its name.
    
    Args:
        hospital_list (list): List of hospital dictionaries containing 'name' key
        user_initials (str): User provided initials to match against
    
    Returns:
        dict: Hospital dictionary if match found, None otherwise
    """
    if not hospital_list or not user_initials:
        print("DEBUG: Invalid hospital list or initials")
        return None
        
    # Clean and normalize user initials
    user_initials = user_initials.strip().upper()
    
    for hospital in hospital_list:
        if not isinstance(hospital, dict) or 'name' not in hospital:
            continue
            
        hospital_name = hospital['name']
        # Split name into words and get first letter of each word
        words = hospital_name.split()
        if len(words) > 1:
            # Get initials from words
            name_initials = ''.join(word[0].upper() for word in words if word)
            
            # Check if user initials match the name initials
            if name_initials == user_initials:
                print(f"DEBUG: Matched initials '{user_initials}' to hospital '{hospital_name}'")
                return hospital['name']
    
    print(f"DEBUG: No match found for initials '{user_initials}'")
    return user_initials 

def find_doctors_by_name_final(doctor_name, doctors_list):
    """
    Find all doctors whose names contain the given search term.
    Performs case-insensitive matching and returns all matches.
    
    Args:
        doctor_name (str): The name or part of name to search for
        doctors_list (list): List of doctor dictionaries with 'id' and 'name' keys
    
    Returns:
        list: List of dictionaries containing matched doctors' id and name
    """
    if not doctor_name or not doctors_list:
        print("DEBUG: Invalid doctor name or empty doctors list")
        return []

    # Clean and normalize the input name
    search_term = doctor_name.strip().lower()
    
    # List to store all matches
    matches = []
    
    for doctor in doctors_list:
        # print(doctor)
        doctor_full_name = doctor['name'].lower()
        
        # Check if search term exists in doctor's name
        if search_term in doctor_full_name:
            matches.append({
                'id': doctor['id'],
                'name': doctor['name']
            })
    
    if matches:
        print(f"DEBUG: Found {len(matches)} matches for '{doctor_name}'")
    else:
        print(f"DEBUG: No matches found for '{doctor_name}'")
        
    return matches

def find_doctor_by_name(doctor_name, doctors_list):
    """
    Find a doctor by name from the list of doctors.
    Performs case-insensitive matching, handles different name formats,
    and uses fuzzy matching for better accuracy.
    
    Args:
        doctor_name (str): The name of the doctor to find
        doctors_list (list): List of doctor dictionaries with 'id' and 'name' keys
    
    Returns:
        list: List containing matched doctor's id and name, or None if no match found
    """
    if not doctor_name or not doctors_list:
        print("DEBUG: Invalid doctor name or empty doctors list")
        return None
        
    get_doc_list = find_doctors_by_name_final(doctor_name, doctors_list)
    if len(get_doc_list) > 0:
        print("get_doc_list worked",get_doc_list)
        return get_doc_list
        
    # Clean and normalize the input name
    doctor_name = doctor_name.strip().lower()

    # Try different name formats
    name_variations = [
        doctor_name,  # Original input
        doctor_name.replace(',', ''),  # Remove commas
        ' '.join(doctor_name.split()[::-1]),  # Reverse first and last name
        ' '.join(doctor_name.split()),  # Normalize spaces
        doctor_name.replace(' ', '')  # Remove all spaces
    ]
    
    # Get all doctor names for fuzzy matching
    doctor_names = [doctor['name'].lower() for doctor in doctors_list]
    
    # Try exact matches first
    for doctor in doctors_list:
        doctor_full_name = doctor['name'].lower()
        
        # Check if any name variation matches exactly
        for name_var in name_variations:
            if name_var in doctor_full_name or doctor_full_name in name_var:
                print(f"DEBUG: Exact match found: '{doctor_name}' to doctor '{doctor['name']}'")
                return [{
                    'id': doctor['id'],
                    'name': doctor['name']
                }]
    
    # If no exact match, try fuzzy matching
    best_match = None
    highest_ratio = 0
    
    for name_var in name_variations:
        # Get fuzzy matches for this name variation
        matches = difflib.get_close_matches(name_var, doctor_names, n=1, cutoff=0.6)
        if matches:
            match_ratio = difflib.SequenceMatcher(None, name_var, matches[0]).ratio()
            if match_ratio > highest_ratio:
                highest_ratio = match_ratio
                # Find the original doctor entry
                for doctor in doctors_list:
                    if doctor['name'].lower() == matches[0]:
                        best_match = doctor
                        break
    
    if best_match and highest_ratio >= 0.6:
        print(f"DEBUG: Fuzzy match found (ratio: {highest_ratio:.2f}): '{doctor_name}' to doctor '{best_match['name']}'")
        return [{
            'id': best_match['id'],
            'name': best_match['name']
        }]
    
    print(f"DEBUG: No match found for doctor name '{doctor_name}'")
    return []
def find_closest_doctor(input_name, doctors_list):
    normalized_input = normalize_name(input_name)
    names = [doctor['name'] for doctor in doctors_list]
    normalized_names = [normalize_name(name) for name in names]
    closest_matches = difflib.get_close_matches(normalized_input, normalized_names, n=1, cutoff=0.5)
    if closest_matches:
        closest_normalized = closest_matches[0]
        for doctor, norm_name in zip(doctors_list, normalized_names):
            if norm_name == closest_normalized:
                return {'id': doctor['id'], 'name': doctor['name']}
    return None

def find_closest_item(input_name, items_list, item_type="doctor", cutoff_score=0.7):
    if not items_list or not isinstance(items_list, list) or not input_name:
        print(f"DEBUG: helper.py - find_closest_item: Invalid input_name or items_list for {item_type}.")
        return None

    normalized_input = normalize_name(input_name, item_type) # Uses the updated normalize_name
    if not normalized_input:
        print(f"DEBUG: helper.py - find_closest_item: Normalized input name is empty for '{input_name}'.")
        return None

    api_item_details = []
    for item in items_list:
        if isinstance(item, dict) and item.get('name') and item.get('id') is not None:
            original_api_name = item['name']
            normalized_api_name = normalize_name(original_api_name, item_type)
            if normalized_api_name:
                 api_item_details.append({'original': original_api_name, 
                                          'normalized': normalized_api_name, 
                                          'id': item['id']})
        else:
            print(f"DEBUG: helper.py - Skipping item due to missing name/id or unexpected format: {item}")
    
    if not api_item_details:
        print(f"DEBUG: helper.py - No valid items with names to match against in API list for {item_type}.")
        return None

    normalized_api_names_for_difflib = [detail['normalized'] for detail in api_item_details]
    
    closest_normalized_matches = difflib.get_close_matches(normalized_input, normalized_api_names_for_difflib, n=1, cutoff=cutoff_score)

    if closest_normalized_matches:
        matched_normalized_name = closest_normalized_matches[0]
        for detail in api_item_details:
            if detail['normalized'] == matched_normalized_name:
                print(f"DEBUG: helper.py - Matched '{normalized_input}' (from user input '{input_name}') "
                      f"to API's normalized '{matched_normalized_name}' (original: '{detail['original']}') "
                      f"with ID {detail['id']} for {item_type}.")
                return {'id': detail['id'], 'name': detail['original']}
    
    print(f"DEBUG: helper.py - No close match found for '{normalized_input}' (from user input '{input_name}') in {item_type} list using cutoff {cutoff_score}.")
    return None

def find_hospitals_by_name(hospital_list,hospital_name):
    """
    Find all hospitals whose names contain the given search term.
    Performs case-insensitive matching and returns all matches.
    
    Args:
        hospital_name (str): The name or part of name to search for
        hospital_list (list): List of hospital dictionaries with 'id' and 'name' keys
    
    Returns:
        list: List of dictionaries containing matched hospitals' id and name
    """
    if not hospital_name or not hospital_list:
        print("DEBUG: Invalid hospital name or empty hospital list")
        return []

    # Clean and normalize the input name
    search_term = hospital_name.strip().lower()
    
    # List to store all matches
    matches = []
    
    for hospital in hospital_list:
        hospital_full_name = hospital['name'].lower()
        
        # Check if search term exists in hospital's name
        if search_term in hospital_full_name:
            matches.append({
                'id': hospital['id'],
                'name': hospital['name']
            })
    
    if matches:
        print(f"DEBUG: Found {len(matches)} matches for '{hospital_name}'")
    else:
        print(f"DEBUG: No matches found for '{hospital_name}'")
        
    return matches


def get_doctor_name_accurately(doctor_name,hospital_name,token,city=None):
    # token = get_auth_token()

    hospital_name = hospital_name.split(';')[0]
    hospital_list = get_hospital_list(token=token)
    hospital_id = find_top_matches(hospital_name, hospital_list, item_type="hospital", city=city)
    
    
    
    if hospital_id:
        hospital_id = hospital_id[0]['id']
        print('HOSPITAL-ID', hospital_id)
        doctors_list = fetch_hospital_doctors(hospital_id, token)
        print('DOCTOR-LIST',doctors_list)
        doctor_name = normalize_name(doctor_name, item_type="doctor")
        print("filtered doctor name",doctor_name)
        return find_doctor_by_name(doctor_name, doctors_list)
    

def get_hospital_accurately(hospital_name,hospital_list,city=None):
    # print('CITY',city)
    if len(find_top_matches(hospital_name, hospital_list, item_type="hospital", city=city)) > 1:
        data = find_top_matches(hospital_name, hospital_list, item_type="hospital", city=city)
        print('DATA',data)
        options = data
        result = options
        return 'Multiple',result
    elif len(find_top_matches(hospital_name, hospital_list, item_type="hospital", city=city)) == 0:
        return False,f"The provided hospital name '{hospital_name}' seems invalid. Please provide a valid hospital's name."
    else:
        data = find_top_matches(hospital_name, hospital_list, item_type="hospital", city=city)
        print('DATA',data)
        if len(data) > 0 and 'location' in data[0] :
            city = data[0]['location']['city']
        else:
            city = None
        if city:
            return (True,data[0]['id']),data[0]['name'] + ';' + city
        else:
            return (True,data[0]['id']),data[0]['name']

def _fetch_cases_for_filters(hospitalId,doctor_id, hospital_name_str, date_str,token):
    
    
    # token = get_auth_token()
    headers = {
        'accept': 'application/json, text/plain, */*',
        'authorization': f'Bearer {token}',
        'content-type': 'application/json',
        'isdaylight': 'true', 
        'user-timezone': 'Asia/Calcutta' 
    }
    
    # Build the payload - make hospital name optional
    payload = {
        "status": ["Requested", "Pending", "Confirmed"], 
        "doctorIds": [doctor_id] if doctor_id else [],
        # "hospitals": [hospital_name_str] if hospital_name_str else [],  # Only include hospital if provided
        "hospitals": [hospitalId],  # Only include hospital if provided
        "surgeryFromDate": date_str,
        "surgeryToDate": None,
        "readerOversights":[] # Usually filter for a single day for cancellations like this
    }
    
    print(f"DEBUG: Fetching cases with payload: {json.dumps(payload)}")
    try:
        response = requests.post(CASES_FILTER_API_URL, headers=headers, json=payload, timeout=15)
        response.raise_for_status()
        api_response = response.json()
        cases = api_response.get("content", [])


        print(f"DEBUG: Found {len(cases)} cases from API for filters.")

        simplified_cases = [
            {
                "id": case.get("id"),
                "caseNo": case.get("caseNo"),
                "status": case.get("status"),
                "procedure": case.get("procedure"),
                "dateOfSurgery": case.get("dateOfSurgery"),
                "hospitalName": case.get("hospitalName"),
                "doctorName": case.get("doctorName"),
                "hospitalRegionName": case.get("hospitalRegionName"),
                "hospitalStateName": case.get("hospitalStateName"),
                "patientInOut": case.get("patientInOut")
            }
            for case in cases
        ]
        return simplified_cases
    except requests.exceptions.RequestException as e:
        print(f"Error fetching cases for filters: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"DEBUG: API Error response for fetching cases: {e.response.text}")
        return None
    
# if __name__ == "__main__":
#     # print(decode_jwt_no_secret(token='eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aOVHTNP8sc4D0Qtp1afy7n_fsBq63h07PYDA46sPB8iBid6EJqS81g2xzWJrpfT_PwBLAapXA1_0FGimZtKohg'))
#     # list_name = get_hospital_list()
#     # print(get_hospital_accurately('emory university', list_name))
#     # print(find_top_matches('emory university', list_name, item_type="hospital"))
# # print(find_top_matches('Morgan  Stanley Hospital', list_name,item_type="hospital",))

#     # print(find_hospitals_by_name(list_name, 'emory hospital'))
#     token = 'eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VHy_6og-3qvV_mJkVqzFMjDKrL_eNrRh2lXIhmDaKzMMcfGDRGRbOGboz7di2Yrz8suVg0u_Wayh5xNNOs_Lbg'
#     print(get_hospital_list(token=token))
