import { useCoAgentStateRender, useCopilotChat } from "@copilotkit/react-core";
import { TextMessage, Role } from "@copilotkit/runtime-client-gql";
import { useState, useEffect } from "react";
import CaseListDisplay from "./CaseListDisplay";
import ConfirmationButtons from "./static/ConfirmationButtons";
import ReactMarkdown from "react-markdown";
import HospitalListDisplay from "./HospitalListDisplay";
import DoctorListDisplay from "./DoctorListDisplay";

interface CaseInfo {
  caseNo: number;
  doctorName: string;
}

interface HospitalInfo {
  id: string;
  name: string;
  city: string;
  address: string;
  region: string;
}


interface DoctorInfo {
  id: string;
  name: string;
}


interface AgentState {
  showCase?: CaseInfo[];
  confirmation_needed?: boolean;
  messages?: any[];
  confirmation_message?: string;
  frontend_tool?: string;
  showHospitals?: HospitalInfo[];
  showDoctors?: DoctorInfo[];
}


export default function ChatArea() {
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [selectedHospitals, setSelectedHospitals] = useState<string[]>([]);
  const [selectedDoctors, setSelectedDoctors] = useState<string[]>([]);
  const [processedItems, setProcessedItems] = useState<Set<string>>(new Set());
  
  const { appendMessage } = useCopilotChat();

  async function handleCancelCases(selected: string[], caseKey: string) {
    try {
      const caseIds = selected.map(caseStr => caseStr.split(" - ").pop()?.trim()).filter(Boolean);
      appendMessage(new TextMessage({ content: `${caseIds}`, role: Role.User }));
      setSelectedCases([]);
      setProcessedItems(prev => new Set([...prev, caseKey]));
    } catch (err: any) {
      appendMessage(new TextMessage({ content: `Error: ${err.message}`, role: Role.User }));
    }
  }

  async function handleSelectedHospitals(selected: HospitalInfo[], hospitalKey: string) {
    try {
      const selectedHospital = selected[0];

      const displayMessage = `hospitalId=${selectedHospital.id} \ncity=${selectedHospital.city}`;
      appendMessage(new TextMessage({ content: displayMessage, role: Role.User }));

      setSelectedHospitals(selected.map((item) => item.id));
      setProcessedItems(prev => new Set([...prev, hospitalKey]));
    } catch (err: any) {
      appendMessage(new TextMessage({ content: `Error: ${err.message}`, role: Role.User }));
    }
  }

   async function handleSelectedDoctor(selected: DoctorInfo[], DoctorKey: string) {
    try {
      const selectedDoctor = selected[0];

      const displayMessage = `doctorId = ${selectedDoctor.id} \nDoctor name = ${selectedDoctor.name}`;
      appendMessage(new TextMessage({ content: displayMessage, role: Role.User }));

      setSelectedDoctors(selected.map((item) => item.id));
      setProcessedItems(prev => new Set([...prev, DoctorKey]));
    } catch (err: any) {
      appendMessage(new TextMessage({ content: `Error: ${err.message}`, role: Role.User }));
    }
  }

  const rendered = useCoAgentStateRender<AgentState>({
    name: "scheduling_agent",
    render: ({ state, status }) => {
      if (status === "inProgress") return <div>Loading...</div>;

      // Create unique keys for current items
      const hospitalKey = state.showHospitals ? 
        `hospitals_${state.showHospitals.map(h => h.id).join('_')}` : '';
      const caseKey = state.showCase ? 
        `cases_${state.showCase.map(c => c.caseNo).join('_')}` : '';
      const confirmationKey = state.confirmation_needed ? 
        `confirmation_${state.confirmation_message}` : '';

      return (
        <div>
          {/* Show Hospitals */}
          {state.frontend_tool == "showHospitals" && state.showHospitals?.length ? (
            <div>
              <ReactMarkdown>
                {state.confirmation_message || "Please select the hospital."}
              </ReactMarkdown>
              <HospitalListDisplay
                items={state.showHospitals}
                selectedIdxs={selectedHospitals.map((id) =>
                  state.showHospitals!.findIndex((item) => item.id === id)
                )}
                onSubmit={(selected) => handleSelectedHospitals(selected, hospitalKey)}
                disabled={processedItems.has(hospitalKey)}
              />
            </div>
          ) : null}

          {state.frontend_tool == "showDoctors" && state.showDoctors?.length ? (
            <div>
              <ReactMarkdown>
                {state.confirmation_message || "Please select the hospital."}
              </ReactMarkdown>
              <DoctorListDisplay
                items={state.showDoctors}
                selectedIdxs={selectedDoctors.map((id) =>
                  state.showDoctors!.findIndex((item) => item.id === id)
                )}
                onSubmit={(selected) => handleSelectedDoctor(selected, hospitalKey)}
                disabled={processedItems.has(hospitalKey)}
              />
            </div>
          ) : null}

          {/* Show Cases */}
          {state.frontend_tool == "showCase" && state.showCase ? (
            <CaseListDisplay
              items={state.showCase.map(item => `${item.doctorName} - ${item.caseNo}`)}
              selectedIdxs={selectedCases.map((caseStr) =>
                state.showCase!.findIndex(
                  (item) => `${item.doctorName} - ${item.caseNo}` === caseStr
                )
              )}
              onSubmit={async (selected) => {
                setSelectedCases(selected);
                await handleCancelCases(selected, caseKey);
              }}
              disabled={processedItems.has(caseKey)}
            />
          ) : null}

          {/* Confirmation Buttons */}
          {state.frontend_tool == "confirmation" && state.confirmation_needed == true && (
            <div style={{ marginTop: 24 }}>
              <ReactMarkdown>{state.confirmation_message}</ReactMarkdown>
              <ConfirmationButtons
                onYes={() => {
                  appendMessage(new TextMessage({ content: "yes", role: Role.User }));
                  setProcessedItems(prev => new Set([...prev, confirmationKey]));
                }}
                onNo={() => {
                  appendMessage(new TextMessage({ content: "no", role: Role.User }));
                  setProcessedItems(prev => new Set([...prev, confirmationKey]));
                }}
                disabled={processedItems.has(confirmationKey)}
              />
            </div>
          )}
        </div>
      );
    },
  });

  return rendered ?? null;
}