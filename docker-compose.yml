version: "3.9"

services:
  backend:
    build:
      context: .
      dockerfile: agent/Dockerfile
    ports:
      - "8001:8001"
    networks:
      - app-network
    volumes:
      - ./logs/backend:/app/logs
    environment:
      - LOG_PATH=/app/logs/backend.log


  frontend:
    build:
      context: .
      dockerfile: ui/Dockerfile
    ports:
      - "3000:3000"
    networks:
      - app-network
    depends_on:
      - backend
    volumes:
      - ./logs/frontend:/app/logs
    environment:
      - NEXT_PUBLIC_BACKEND_URL=https://stage.ionm.copilot.zinniax.com
      - OPENAI_API_KEY=''
networks:
  app-network:
    driver: bridge
