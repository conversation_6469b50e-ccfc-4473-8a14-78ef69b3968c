"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@graphql-yoga";
exports.ids = ["vendor-chunks/@graphql-yoga"];
exports.modules = {

/***/ "(rsc)/./node_modules/@graphql-yoga/logger/cjs/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@graphql-yoga/logger/cjs/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable no-console */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createLogger = exports.debugPrefix = exports.errorPrefix = exports.infoPrefix = exports.warnPrefix = void 0;\nconst ansiCodes = {\n    red: '\\x1b[31m',\n    yellow: '\\x1b[33m',\n    magenta: '\\x1b[35m',\n    cyan: '\\x1b[36m',\n    reset: '\\x1b[0m',\n};\nexports.warnPrefix = ansiCodes.yellow + 'WARN' + ansiCodes.reset;\nexports.infoPrefix = ansiCodes.cyan + 'INFO' + ansiCodes.reset;\nexports.errorPrefix = ansiCodes.red + 'ERR' + ansiCodes.reset;\nexports.debugPrefix = ansiCodes.magenta + 'DEBUG' + ansiCodes.reset;\nconst logLevelScores = {\n    debug: 0,\n    info: 1,\n    warn: 2,\n    error: 3,\n    silent: 4,\n};\nconst noop = () => { };\nconst consoleLog = (prefix) => (...args) => console.log(prefix, ...args);\nconst debugLog = console.debug\n    ? (...args) => console.debug(exports.debugPrefix, ...args)\n    : consoleLog(exports.debugPrefix);\nconst infoLog = console.info\n    ? (...args) => console.info(exports.infoPrefix, ...args)\n    : consoleLog(exports.infoPrefix);\nconst warnLog = console.warn\n    ? (...args) => console.warn(exports.warnPrefix, ...args)\n    : consoleLog(exports.warnPrefix);\nconst errorLog = console.error\n    ? (...args) => console.error(exports.errorPrefix, ...args)\n    : consoleLog(exports.errorPrefix);\nconst createLogger = (logLevel = globalThis.process?.env['DEBUG'] === '1' ? 'debug' : 'info') => {\n    const score = logLevelScores[logLevel];\n    return {\n        debug: score > logLevelScores.debug ? noop : debugLog,\n        info: score > logLevelScores.info ? noop : infoLog,\n        warn: score > logLevelScores.warn ? noop : warnLog,\n        error: score > logLevelScores.error ? noop : errorLog,\n    };\n};\nexports.createLogger = createLogger;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/logger/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/index.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@graphql-yoga/plugin-defer-stream/cjs/index.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useDeferStream = useDeferStream;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nconst defer_stream_directive_label_js_1 = __webpack_require__(/*! ./validations/defer-stream-directive-label.js */ \"(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-label.js\");\nconst defer_stream_directive_on_root_field_js_1 = __webpack_require__(/*! ./validations/defer-stream-directive-on-root-field.js */ \"(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-on-root-field.js\");\nconst overlapping_fields_can_be_merged_js_1 = __webpack_require__(/*! ./validations/overlapping-fields-can-be-merged.js */ \"(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/overlapping-fields-can-be-merged.js\");\nconst stream_directive_on_list_field_js_1 = __webpack_require__(/*! ./validations/stream-directive-on-list-field.js */ \"(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/stream-directive-on-list-field.js\");\nfunction useDeferStream() {\n    return {\n        onSchemaChange: ({ schema, replaceSchema, }) => {\n            const directives = [];\n            const deferInSchema = schema.getDirective('defer');\n            if (deferInSchema == null) {\n                directives.push(utils_1.GraphQLDeferDirective);\n            }\n            const streamInSchema = schema.getDirective('stream');\n            if (streamInSchema == null) {\n                directives.push(utils_1.GraphQLStreamDirective);\n            }\n            if (directives.length) {\n                replaceSchema(new graphql_1.GraphQLSchema({\n                    ...schema.toConfig(),\n                    directives: [...schema.getDirectives(), ...directives],\n                }));\n            }\n        },\n        onValidate: ({ params, addValidationRule, }) => {\n            // Just to make TS happy because rules are always defined by useEngine.\n            params.rules ||= [];\n            params.rules = params.rules.filter(rule => rule.name !== 'OverlappingFieldsCanBeMergedRule');\n            addValidationRule(overlapping_fields_can_be_merged_js_1.OverlappingFieldsCanBeMergedRule);\n            addValidationRule(defer_stream_directive_label_js_1.DeferStreamDirectiveLabelRule);\n            addValidationRule(defer_stream_directive_on_root_field_js_1.DeferStreamDirectiveOnRootFieldRule);\n            addValidationRule(stream_directive_on_list_field_js_1.StreamDirectiveOnListFieldRule);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-label.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-label.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeferStreamDirectiveLabelRule = DeferStreamDirectiveLabelRule;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst graphql_yoga_1 = __webpack_require__(/*! graphql-yoga */ \"(rsc)/./node_modules/graphql-yoga/cjs/index.js\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Stream directive on list field\n *\n * A GraphQL document is only valid if defer and stream directives' label argument is static and unique.\n */\nfunction DeferStreamDirectiveLabelRule(context) {\n    const knownLabels = Object.create(null);\n    return {\n        Directive(node) {\n            if (node.name.value === utils_1.GraphQLDeferDirective.name ||\n                node.name.value === utils_1.GraphQLStreamDirective.name) {\n                const labelArgument = node.arguments?.find(arg => arg.name.value === 'label');\n                const labelValue = labelArgument?.value;\n                if (!labelValue) {\n                    return;\n                }\n                if (labelValue.kind !== graphql_1.Kind.STRING) {\n                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Directive \"${node.name.value}\"'s label argument must be a static string.`, { nodes: node }));\n                }\n                else if (knownLabels[labelValue.value]) {\n                    context.reportError((0, graphql_yoga_1.createGraphQLError)('Defer/Stream directive label argument must be unique.', {\n                        nodes: [knownLabels[labelValue.value], node],\n                    }));\n                }\n                else {\n                    knownLabels[labelValue.value] = node;\n                }\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-label.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-on-root-field.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-on-root-field.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeferStreamDirectiveOnRootFieldRule = DeferStreamDirectiveOnRootFieldRule;\nconst graphql_yoga_1 = __webpack_require__(/*! graphql-yoga */ \"(rsc)/./node_modules/graphql-yoga/cjs/index.js\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Stream directive on list field\n *\n * A GraphQL document is only valid if defer directives are not used on root mutation or subscription types.\n */\nfunction DeferStreamDirectiveOnRootFieldRule(context) {\n    return {\n        Directive(node) {\n            const mutationType = context.getSchema().getMutationType();\n            const subscriptionType = context.getSchema().getSubscriptionType();\n            const parentType = context.getParentType();\n            if (parentType && node.name.value === utils_1.GraphQLDeferDirective.name) {\n                if (mutationType && parentType === mutationType) {\n                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Defer directive cannot be used on root mutation type \"${parentType.name}\".`, { nodes: node }));\n                }\n                if (subscriptionType && parentType === subscriptionType) {\n                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Defer directive cannot be used on root subscription type \"${parentType.name}\".`, { nodes: node }));\n                }\n            }\n            if (parentType && node.name.value === utils_1.GraphQLStreamDirective.name) {\n                if (mutationType && parentType === mutationType) {\n                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Stream directive cannot be used on root mutation type \"${parentType.name}\".`, { nodes: node }));\n                }\n                if (subscriptionType && parentType === subscriptionType) {\n                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Stream directive cannot be used on root subscription type \"${parentType.name}\".`, { nodes: node }));\n                }\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-on-root-field.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/overlapping-fields-can-be-merged.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/overlapping-fields-can-be-merged.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.naturalCompare = naturalCompare;\nexports.OverlappingFieldsCanBeMergedRule = OverlappingFieldsCanBeMergedRule;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst graphql_yoga_1 = __webpack_require__(/*! graphql-yoga */ \"(rsc)/./node_modules/graphql-yoga/cjs/index.js\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Returns a number indicating whether a reference string comes before, or after,\n * or is the same as the given string in natural sort order.\n *\n * See: https://en.wikipedia.org/wiki/Natural_sort_order\n *\n */\nfunction naturalCompare(aStr, bStr) {\n    let aIndex = 0;\n    let bIndex = 0;\n    while (aIndex < aStr.length && bIndex < bStr.length) {\n        let aChar = aStr.charCodeAt(aIndex);\n        let bChar = bStr.charCodeAt(bIndex);\n        if (isDigit(aChar) && isDigit(bChar)) {\n            let aNum = 0;\n            do {\n                ++aIndex;\n                aNum = aNum * 10 + aChar - DIGIT_0;\n                aChar = aStr.charCodeAt(aIndex);\n            } while (isDigit(aChar) && aNum > 0);\n            let bNum = 0;\n            do {\n                ++bIndex;\n                bNum = bNum * 10 + bChar - DIGIT_0;\n                bChar = bStr.charCodeAt(bIndex);\n            } while (isDigit(bChar) && bNum > 0);\n            if (aNum < bNum) {\n                return -1;\n            }\n            if (aNum > bNum) {\n                return 1;\n            }\n        }\n        else {\n            if (aChar < bChar) {\n                return -1;\n            }\n            if (aChar > bChar) {\n                return 1;\n            }\n            ++aIndex;\n            ++bIndex;\n        }\n    }\n    return aStr.length - bStr.length;\n}\nconst DIGIT_0 = 48;\nconst DIGIT_9 = 57;\nfunction isDigit(code) {\n    return !Number.isNaN(code) && DIGIT_0 <= code && code <= DIGIT_9;\n}\nfunction sortValueNode(valueNode) {\n    switch (valueNode.kind) {\n        case graphql_1.Kind.OBJECT:\n            return {\n                ...valueNode,\n                fields: sortFields(valueNode.fields),\n            };\n        case graphql_1.Kind.LIST:\n            return {\n                ...valueNode,\n                values: valueNode.values.map(sortValueNode),\n            };\n        case graphql_1.Kind.INT:\n        case graphql_1.Kind.FLOAT:\n        case graphql_1.Kind.STRING:\n        case graphql_1.Kind.BOOLEAN:\n        case graphql_1.Kind.NULL:\n        case graphql_1.Kind.ENUM:\n        case graphql_1.Kind.VARIABLE:\n            return valueNode;\n    }\n}\nfunction sortFields(fields) {\n    return fields\n        .map(fieldNode => ({\n        ...fieldNode,\n        value: sortValueNode(fieldNode.value),\n    }))\n        .sort((fieldA, fieldB) => naturalCompare(fieldA.name.value, fieldB.name.value));\n}\nfunction reasonMessage(reason) {\n    if (Array.isArray(reason)) {\n        return reason\n            .map(([responseName, subReason]) => `subfields \"${responseName}\" conflict because ` + reasonMessage(subReason))\n            .join(' and ');\n    }\n    return reason;\n}\n/**\n * Overlapping fields can be merged\n *\n * A selection set is only valid if all fields (including spreading any\n * fragments) either correspond to distinct response names or can be merged\n * without ambiguity.\n *\n * See https://spec.graphql.org/draft/#sec-Field-Selection-Merging\n */\nfunction OverlappingFieldsCanBeMergedRule(context) {\n    // A memoization for when two fragments are compared \"between\" each other for\n    // conflicts. Two fragments may be compared many times, so memoizing this can\n    // dramatically improve the performance of this validator.\n    const comparedFragmentPairs = new PairSet();\n    // A cache for the \"field map\" and list of fragment names found in any given\n    // selection set. Selection sets may be asked for this information multiple\n    // times, so this improves the performance of this validator.\n    const cachedFieldsAndFragmentNames = new Map();\n    return {\n        SelectionSet(selectionSet) {\n            const conflicts = findConflictsWithinSelectionSet(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, context.getParentType(), selectionSet);\n            for (const [[responseName, reason], fields1, fields2] of conflicts) {\n                const reasonMsg = reasonMessage(reason);\n                context.reportError((0, graphql_yoga_1.createGraphQLError)(`Fields \"${responseName}\" conflict because ${reasonMsg}. Use different aliases on the fields to fetch both if this was intentional.`, { nodes: fields1.concat(fields2) }));\n            }\n        },\n    };\n}\n/**\n * Algorithm:\n *\n * Conflicts occur when two fields exist in a query which will produce the same\n * response name, but represent differing values, thus creating a conflict.\n * The algorithm below finds all conflicts via making a series of comparisons\n * between fields. In order to compare as few fields as possible, this makes\n * a series of comparisons \"within\" sets of fields and \"between\" sets of fields.\n *\n * Given any selection set, a collection produces both a set of fields by\n * also including all inline fragments, as well as a list of fragments\n * referenced by fragment spreads.\n *\n * A) Each selection set represented in the document first compares \"within\" its\n * collected set of fields, finding any conflicts between every pair of\n * overlapping fields.\n * Note: This is the *only time* that a the fields \"within\" a set are compared\n * to each other. After this only fields \"between\" sets are compared.\n *\n * B) Also, if any fragment is referenced in a selection set, then a\n * comparison is made \"between\" the original set of fields and the\n * referenced fragment.\n *\n * C) Also, if multiple fragments are referenced, then comparisons\n * are made \"between\" each referenced fragment.\n *\n * D) When comparing \"between\" a set of fields and a referenced fragment, first\n * a comparison is made between each field in the original set of fields and\n * each field in the the referenced set of fields.\n *\n * E) Also, if any fragment is referenced in the referenced selection set,\n * then a comparison is made \"between\" the original set of fields and the\n * referenced fragment (recursively referring to step D).\n *\n * F) When comparing \"between\" two fragments, first a comparison is made between\n * each field in the first referenced set of fields and each field in the the\n * second referenced set of fields.\n *\n * G) Also, any fragments referenced by the first must be compared to the\n * second, and any fragments referenced by the second must be compared to the\n * first (recursively referring to step F).\n *\n * H) When comparing two fields, if both have selection sets, then a comparison\n * is made \"between\" both selection sets, first comparing the set of fields in\n * the first selection set with the set of fields in the second.\n *\n * I) Also, if any fragment is referenced in either selection set, then a\n * comparison is made \"between\" the other set of fields and the\n * referenced fragment.\n *\n * J) Also, if two fragments are referenced in both selection sets, then a\n * comparison is made \"between\" the two fragments.\n *\n */\n// Find all conflicts found \"within\" a selection set, including those found\n// via spreading in fragments. Called when visiting each SelectionSet in the\n// GraphQL Document.\nfunction findConflictsWithinSelectionSet(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentType, selectionSet) {\n    const conflicts = [];\n    const [fieldMap, fragmentNames] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType, selectionSet);\n    // (A) Find find all conflicts \"within\" the fields of this selection set.\n    // Note: this is the *only place* `collectConflictsWithin` is called.\n    collectConflictsWithin(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, fieldMap);\n    if (fragmentNames.length !== 0) {\n        // (B) Then collect conflicts between these fields and those represented by\n        // each spread fragment name found.\n        for (let i = 0; i < fragmentNames.length; i++) {\n            collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, false, fieldMap, fragmentNames[i]);\n            // (C) Then compare this fragment with all other fragments found in this\n            // selection set to collect conflicts between fragments spread together.\n            // This compares each item in the list of fragment names to every other\n            // item in that same list (except for itself).\n            for (let j = i + 1; j < fragmentNames.length; j++) {\n                collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, false, fragmentNames[i], fragmentNames[j]);\n            }\n        }\n    }\n    return conflicts;\n}\n// Collect all conflicts found between a set of fields and a fragment reference\n// including via spreading in any nested fragments.\nfunction collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, fragmentName) {\n    const fragment = context.getFragment(fragmentName);\n    if (!fragment) {\n        return;\n    }\n    const [fieldMap2, referencedFragmentNames] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment);\n    // Do not compare a fragment's fieldMap to itself.\n    if (fieldMap === fieldMap2) {\n        return;\n    }\n    // (D) First collect any conflicts between the provided collection of fields\n    // and the collection of fields represented by the given fragment.\n    collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, fieldMap2);\n    // (E) Then collect any conflicts between the provided collection of fields\n    // and any fragment names found in the given fragment.\n    for (const referencedFragmentName of referencedFragmentNames) {\n        // Memoize so two fragments are not compared for conflicts more than once.\n        if (comparedFragmentPairs.has(referencedFragmentName, fragmentName, areMutuallyExclusive)) {\n            continue;\n        }\n        comparedFragmentPairs.add(referencedFragmentName, fragmentName, areMutuallyExclusive);\n        collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, referencedFragmentName);\n    }\n}\n// Collect all conflicts found between two fragments, including via spreading in\n// any nested fragments.\nfunction collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, fragmentName2) {\n    // No need to compare a fragment to itself.\n    if (fragmentName1 === fragmentName2) {\n        return;\n    }\n    // Memoize so two fragments are not compared for conflicts more than once.\n    if (comparedFragmentPairs.has(fragmentName1, fragmentName2, areMutuallyExclusive)) {\n        return;\n    }\n    comparedFragmentPairs.add(fragmentName1, fragmentName2, areMutuallyExclusive);\n    const fragment1 = context.getFragment(fragmentName1);\n    const fragment2 = context.getFragment(fragmentName2);\n    if (!fragment1 || !fragment2) {\n        return;\n    }\n    const [fieldMap1, referencedFragmentNames1] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment1);\n    const [fieldMap2, referencedFragmentNames2] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment2);\n    // (F) First, collect all conflicts between these two collections of fields\n    // (not including any nested fragments).\n    collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fieldMap2);\n    // (G) Then collect conflicts between the first fragment and any nested\n    // fragments spread in the second fragment.\n    for (const referencedFragmentName2 of referencedFragmentNames2) {\n        collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, referencedFragmentName2);\n    }\n    // (G) Then collect conflicts between the second fragment and any nested\n    // fragments spread in the first fragment.\n    for (const referencedFragmentName1 of referencedFragmentNames1) {\n        collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, referencedFragmentName1, fragmentName2);\n    }\n}\n// Find all conflicts found between two selection sets, including those found\n// via spreading in fragments. Called when determining if conflicts exist\n// between the sub-fields of two overlapping fields.\nfunction findConflictsBetweenSubSelectionSets(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, parentType1, selectionSet1, parentType2, selectionSet2) {\n    const conflicts = [];\n    const [fieldMap1, fragmentNames1] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType1, selectionSet1);\n    const [fieldMap2, fragmentNames2] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType2, selectionSet2);\n    // (H) First, collect all conflicts between these two collections of field.\n    collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fieldMap2);\n    // (I) Then collect conflicts between the first collection of fields and\n    // those referenced by each fragment name associated with the second.\n    for (const fragmentName2 of fragmentNames2) {\n        collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fragmentName2);\n    }\n    // (I) Then collect conflicts between the second collection of fields and\n    // those referenced by each fragment name associated with the first.\n    for (const fragmentName1 of fragmentNames1) {\n        collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap2, fragmentName1);\n    }\n    // (J) Also collect conflicts between any fragment names by the first and\n    // fragment names by the second. This compares each item in the first set of\n    // names to each item in the second set of names.\n    for (const fragmentName1 of fragmentNames1) {\n        for (const fragmentName2 of fragmentNames2) {\n            collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, fragmentName2);\n        }\n    }\n    return conflicts;\n}\n// Collect all Conflicts \"within\" one collection of fields.\nfunction collectConflictsWithin(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, fieldMap) {\n    // A field map is a keyed collection, where each key represents a response\n    // name and the value at that key is a list of all fields which provide that\n    // response name. For every response name, if there are multiple fields, they\n    // must be compared to find a potential conflict.\n    for (const [responseName, fields] of Object.entries(fieldMap)) {\n        // This compares every field in the list to every other field in this list\n        // (except to itself). If the list only has one item, nothing needs to\n        // be compared.\n        if (fields.length > 1) {\n            for (let i = 0; i < fields.length; i++) {\n                for (let j = i + 1; j < fields.length; j++) {\n                    const conflict = findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, false, // within one collection is never mutually exclusive\n                    responseName, fields[i], fields[j]);\n                    if (conflict) {\n                        conflicts.push(conflict);\n                    }\n                }\n            }\n        }\n    }\n}\n// Collect all Conflicts between two collections of fields. This is similar to,\n// but different from the `collectConflictsWithin` function above. This check\n// assumes that `collectConflictsWithin` has already been called on each\n// provided collection of fields. This is true because this validator traverses\n// each individual selection set.\nfunction collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, fieldMap1, fieldMap2) {\n    // A field map is a keyed collection, where each key represents a response\n    // name and the value at that key is a list of all fields which provide that\n    // response name. For any response name which appears in both provided field\n    // maps, each field from the first field map must be compared to every field\n    // in the second field map to find potential conflicts.\n    for (const [responseName, fields1] of Object.entries(fieldMap1)) {\n        const fields2 = fieldMap2[responseName];\n        if (fields2) {\n            for (const field1 of fields1) {\n                for (const field2 of fields2) {\n                    const conflict = findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, responseName, field1, field2);\n                    if (conflict) {\n                        conflicts.push(conflict);\n                    }\n                }\n            }\n        }\n    }\n}\n// Determines if there is a conflict between two particular fields, including\n// comparing their sub-fields.\nfunction findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, responseName, field1, field2) {\n    const [parentType1, node1, def1] = field1;\n    const [parentType2, node2, def2] = field2;\n    // If it is known that two fields could not possibly apply at the same\n    // time, due to the parent types, then it is safe to permit them to diverge\n    // in aliased field or arguments used as they will not present any ambiguity\n    // by differing.\n    // It is known that two parent types could never overlap if they are\n    // different Object types. Interface or Union types might overlap - if not\n    // in the current state of the schema, then perhaps in some future version,\n    // thus may not safely diverge.\n    const areMutuallyExclusive = parentFieldsAreMutuallyExclusive ||\n        (parentType1 !== parentType2 && (0, graphql_1.isObjectType)(parentType1) && (0, graphql_1.isObjectType)(parentType2));\n    if (!areMutuallyExclusive) {\n        // Two aliases must refer to the same field.\n        const name1 = node1.name.value;\n        const name2 = node2.name.value;\n        if (name1 !== name2) {\n            return [[responseName, `\"${name1}\" and \"${name2}\" are different fields`], [node1], [node2]];\n        }\n        // Two field calls must have the same arguments.\n        if (stringifyArguments(node1) !== stringifyArguments(node2)) {\n            return [[responseName, 'they have differing arguments'], [node1], [node2]];\n        }\n    }\n    // FIXME https://github.com/graphql/graphql-js/issues/2203\n    const directives1 = /* c8 ignore next */ node1.directives ?? [];\n    const directives2 = /* c8 ignore next */ node2.directives ?? [];\n    if (!sameStreams(directives1, directives2)) {\n        return [[responseName, 'they have differing stream directives'], [node1], [node2]];\n    }\n    // The return type for each field.\n    const type1 = def1?.type;\n    const type2 = def2?.type;\n    if (type1 && type2 && doTypesConflict(type1, type2)) {\n        return [\n            [responseName, `they return conflicting types \"${(0, utils_1.inspect)(type1)}\" and \"${(0, utils_1.inspect)(type2)}\"`],\n            [node1],\n            [node2],\n        ];\n    }\n    // Collect and compare sub-fields. Use the same \"visited fragment names\" list\n    // for both collections so fields in a fragment reference are never\n    // compared to themselves.\n    const selectionSet1 = node1.selectionSet;\n    const selectionSet2 = node2.selectionSet;\n    if (selectionSet1 && selectionSet2) {\n        const conflicts = findConflictsBetweenSubSelectionSets(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, (0, graphql_1.getNamedType)(type1), selectionSet1, (0, graphql_1.getNamedType)(type2), selectionSet2);\n        return subfieldConflicts(conflicts, responseName, node1, node2);\n    }\n    return;\n}\nfunction stringifyArguments(fieldNode) {\n    // FIXME https://github.com/graphql/graphql-js/issues/2203\n    const args = /* c8 ignore next */ fieldNode.arguments ?? [];\n    const inputObjectWithArgs = {\n        kind: graphql_1.Kind.OBJECT,\n        fields: args.map(argNode => ({\n            kind: graphql_1.Kind.OBJECT_FIELD,\n            name: argNode.name,\n            value: argNode.value,\n        })),\n    };\n    return (0, graphql_1.print)(sortValueNode(inputObjectWithArgs));\n}\nfunction getStreamDirective(directives) {\n    return directives.find(directive => directive.name.value === 'stream');\n}\nfunction sameStreams(directives1, directives2) {\n    const stream1 = getStreamDirective(directives1);\n    const stream2 = getStreamDirective(directives2);\n    if (!stream1 && !stream2) {\n        // both fields do not have streams\n        return true;\n    }\n    if (stream1 && stream2) {\n        // check if both fields have equivalent streams\n        return stringifyArguments(stream1) === stringifyArguments(stream2);\n    }\n    // fields have a mix of stream and no stream\n    return false;\n}\n// Two types conflict if both types could not apply to a value simultaneously.\n// Composite types are ignored as their individual field types will be compared\n// later recursively. However List and Non-Null types must match.\nfunction doTypesConflict(type1, type2) {\n    if ((0, graphql_1.isListType)(type1)) {\n        return (0, graphql_1.isListType)(type2) ? doTypesConflict(type1.ofType, type2.ofType) : true;\n    }\n    if ((0, graphql_1.isListType)(type2)) {\n        return true;\n    }\n    if ((0, graphql_1.isNonNullType)(type1)) {\n        return (0, graphql_1.isNonNullType)(type2) ? doTypesConflict(type1.ofType, type2.ofType) : true;\n    }\n    if ((0, graphql_1.isNonNullType)(type2)) {\n        return true;\n    }\n    if ((0, graphql_1.isLeafType)(type1) || (0, graphql_1.isLeafType)(type2)) {\n        return type1 !== type2;\n    }\n    return false;\n}\n// Given a selection set, return the collection of fields (a mapping of response\n// name to field nodes and definitions) as well as a list of fragment names\n// referenced via fragment spreads.\nfunction getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType, selectionSet) {\n    const cached = cachedFieldsAndFragmentNames.get(selectionSet);\n    if (cached) {\n        return cached;\n    }\n    const nodeAndDefs = Object.create(null);\n    const fragmentNames = new Set();\n    _collectFieldsAndFragmentNames(context, parentType, selectionSet, nodeAndDefs, fragmentNames);\n    const result = [nodeAndDefs, [...fragmentNames]];\n    cachedFieldsAndFragmentNames.set(selectionSet, result);\n    return result;\n}\n// Given a reference to a fragment, return the represented collection of fields\n// as well as a list of nested fragment names referenced via fragment spreads.\nfunction getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment) {\n    // Short-circuit building a type from the node if possible.\n    const cached = cachedFieldsAndFragmentNames.get(fragment.selectionSet);\n    if (cached) {\n        return cached;\n    }\n    const fragmentType = (0, graphql_1.typeFromAST)(context.getSchema(), fragment.typeCondition);\n    return getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragmentType, fragment.selectionSet);\n}\nfunction _collectFieldsAndFragmentNames(context, parentType, selectionSet, nodeAndDefs, fragmentNames) {\n    for (const selection of selectionSet.selections) {\n        switch (selection.kind) {\n            case graphql_1.Kind.FIELD: {\n                const fieldName = selection.name.value;\n                let fieldDef;\n                if ((0, graphql_1.isObjectType)(parentType) || (0, graphql_1.isInterfaceType)(parentType)) {\n                    fieldDef = parentType.getFields()[fieldName];\n                }\n                const responseName = selection.alias ? selection.alias.value : fieldName;\n                nodeAndDefs[responseName] ||= [];\n                nodeAndDefs[responseName].push([parentType, selection, fieldDef]);\n                break;\n            }\n            case graphql_1.Kind.FRAGMENT_SPREAD:\n                fragmentNames.add(selection.name.value);\n                break;\n            case graphql_1.Kind.INLINE_FRAGMENT: {\n                const typeCondition = selection.typeCondition;\n                const inlineFragmentType = typeCondition\n                    ? (0, graphql_1.typeFromAST)(context.getSchema(), typeCondition)\n                    : parentType;\n                _collectFieldsAndFragmentNames(context, inlineFragmentType, selection.selectionSet, nodeAndDefs, fragmentNames);\n                break;\n            }\n        }\n    }\n}\n// Given a series of Conflicts which occurred between two sub-fields, generate\n// a single Conflict.\nfunction subfieldConflicts(conflicts, responseName, node1, node2) {\n    if (conflicts.length > 0) {\n        return [\n            [responseName, conflicts.map(([reason]) => reason)],\n            [node1, ...conflicts.map(([, fields1]) => fields1).flat()],\n            [node2, ...conflicts.map(([, , fields2]) => fields2).flat()],\n        ];\n    }\n    return;\n}\n/**\n * A way to keep track of pairs of things when the ordering of the pair does not matter.\n */\nclass PairSet {\n    _data;\n    constructor() {\n        this._data = new Map();\n    }\n    has(a, b, areMutuallyExclusive) {\n        const [key1, key2] = a < b ? [a, b] : [b, a];\n        const result = this._data.get(key1)?.get(key2);\n        if (result === undefined) {\n            return false;\n        }\n        // areMutuallyExclusive being false is a superset of being true, hence if\n        // we want to know if this PairSet \"has\" these two with no exclusivity,\n        // we have to ensure it was added as such.\n        return areMutuallyExclusive ? true : areMutuallyExclusive === result;\n    }\n    add(a, b, areMutuallyExclusive) {\n        const [key1, key2] = a < b ? [a, b] : [b, a];\n        const map = this._data.get(key1);\n        if (map === undefined) {\n            this._data.set(key1, new Map([[key2, areMutuallyExclusive]]));\n        }\n        else {\n            map.set(key2, areMutuallyExclusive);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/overlapping-fields-can-be-merged.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/stream-directive-on-list-field.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/stream-directive-on-list-field.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.StreamDirectiveOnListFieldRule = StreamDirectiveOnListFieldRule;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst graphql_yoga_1 = __webpack_require__(/*! graphql-yoga */ \"(rsc)/./node_modules/graphql-yoga/cjs/index.js\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Stream directive on list field\n *\n * A GraphQL document is only valid if stream directives are used on list fields.\n */\nfunction StreamDirectiveOnListFieldRule(context) {\n    return {\n        Directive(node) {\n            const fieldDef = context.getFieldDef();\n            const parentType = context.getParentType();\n            if (fieldDef &&\n                parentType &&\n                node.name.value === utils_1.GraphQLStreamDirective.name &&\n                !((0, graphql_1.isListType)(fieldDef.type) ||\n                    ((0, graphql_1.isWrappingType)(fieldDef.type) && (0, graphql_1.isListType)(fieldDef.type.ofType)))) {\n                context.reportError((0, graphql_yoga_1.createGraphQLError)(`Stream directive cannot be used on non-list field \"${fieldDef.name}\" on type \"${parentType.name}\".`, { nodes: node }));\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/stream-directive-on-list-field.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/subscription/cjs/create-pub-sub.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@graphql-yoga/subscription/cjs/create-pub-sub.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createPubSub = void 0;\nconst repeater_1 = __webpack_require__(/*! @repeaterjs/repeater */ \"(rsc)/./node_modules/@repeaterjs/repeater/cjs/repeater.js\");\nconst events_1 = __webpack_require__(/*! @whatwg-node/events */ \"(rsc)/./node_modules/@whatwg-node/events/cjs/index.js\");\n/**\n * Utility for publishing and subscribing to events.\n */\nconst createPubSub = (config) => {\n    const target = config?.eventTarget ?? new EventTarget();\n    return {\n        publish(routingKey, ...args) {\n            const payload = args[1] ?? args[0] ?? null;\n            const topic = args[1] === undefined ? routingKey : `${routingKey}:${args[0]}`;\n            const event = new events_1.CustomEvent(topic, {\n                detail: payload,\n            });\n            target.dispatchEvent(event);\n        },\n        subscribe(...[routingKey, id]) {\n            const topic = id === undefined ? routingKey : `${routingKey}:${id}`;\n            return new repeater_1.Repeater(function subscriptionRepeater(next, stop) {\n                stop.then(function subscriptionRepeaterStopHandler() {\n                    target.removeEventListener(topic, pubsubEventListener);\n                });\n                target.addEventListener(topic, pubsubEventListener);\n                function pubsubEventListener(event) {\n                    next(event.detail);\n                }\n            });\n        },\n    };\n};\nexports.createPubSub = createPubSub;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/subscription/cjs/create-pub-sub.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/subscription/cjs/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@graphql-yoga/subscription/cjs/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Repeater = exports.pipe = exports.map = exports.filter = exports.createPubSub = void 0;\nvar create_pub_sub_js_1 = __webpack_require__(/*! ./create-pub-sub.js */ \"(rsc)/./node_modules/@graphql-yoga/subscription/cjs/create-pub-sub.js\");\nObject.defineProperty(exports, \"createPubSub\", ({ enumerable: true, get: function () { return create_pub_sub_js_1.createPubSub; } }));\nvar filter_js_1 = __webpack_require__(/*! ./operator/filter.js */ \"(rsc)/./node_modules/@graphql-yoga/subscription/cjs/operator/filter.js\");\nObject.defineProperty(exports, \"filter\", ({ enumerable: true, get: function () { return filter_js_1.filter; } }));\nvar map_js_1 = __webpack_require__(/*! ./operator/map.js */ \"(rsc)/./node_modules/@graphql-yoga/subscription/cjs/operator/map.js\");\nObject.defineProperty(exports, \"map\", ({ enumerable: true, get: function () { return map_js_1.map; } }));\nvar pipe_js_1 = __webpack_require__(/*! ./utils/pipe.js */ \"(rsc)/./node_modules/@graphql-yoga/subscription/cjs/utils/pipe.js\");\nObject.defineProperty(exports, \"pipe\", ({ enumerable: true, get: function () { return pipe_js_1.pipe; } }));\nvar repeater_1 = __webpack_require__(/*! @repeaterjs/repeater */ \"(rsc)/./node_modules/@repeaterjs/repeater/cjs/repeater.js\");\nObject.defineProperty(exports, \"Repeater\", ({ enumerable: true, get: function () { return repeater_1.Repeater; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/subscription/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/subscription/cjs/operator/filter.js":
/*!************************************************************************!*\
  !*** ./node_modules/@graphql-yoga/subscription/cjs/operator/filter.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.filter = filter;\nconst repeater_1 = __webpack_require__(/*! @repeaterjs/repeater */ \"(rsc)/./node_modules/@repeaterjs/repeater/cjs/repeater.js\");\nfunction filter(filter) {\n    return (source) => new repeater_1.Repeater(async (push, stop) => {\n        const iterable = source[Symbol.asyncIterator]();\n        stop.then(() => {\n            iterable.return?.();\n        });\n        let latest;\n        while ((latest = await iterable.next()).done === false) {\n            if (await filter(latest.value)) {\n                await push(latest.value);\n            }\n        }\n        stop();\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGdyYXBocWwteW9nYS9zdWJzY3JpcHRpb24vY2pzL29wZXJhdG9yL2ZpbHRlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxjQUFjO0FBQ2QsbUJBQW1CLG1CQUFPLENBQUMsdUZBQXNCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGdyYXBocWwteW9nYVxcc3Vic2NyaXB0aW9uXFxjanNcXG9wZXJhdG9yXFxmaWx0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmZpbHRlciA9IGZpbHRlcjtcbmNvbnN0IHJlcGVhdGVyXzEgPSByZXF1aXJlKFwiQHJlcGVhdGVyanMvcmVwZWF0ZXJcIik7XG5mdW5jdGlvbiBmaWx0ZXIoZmlsdGVyKSB7XG4gICAgcmV0dXJuIChzb3VyY2UpID0+IG5ldyByZXBlYXRlcl8xLlJlcGVhdGVyKGFzeW5jIChwdXNoLCBzdG9wKSA9PiB7XG4gICAgICAgIGNvbnN0IGl0ZXJhYmxlID0gc291cmNlW1N5bWJvbC5hc3luY0l0ZXJhdG9yXSgpO1xuICAgICAgICBzdG9wLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgaXRlcmFibGUucmV0dXJuPy4oKTtcbiAgICAgICAgfSk7XG4gICAgICAgIGxldCBsYXRlc3Q7XG4gICAgICAgIHdoaWxlICgobGF0ZXN0ID0gYXdhaXQgaXRlcmFibGUubmV4dCgpKS5kb25lID09PSBmYWxzZSkge1xuICAgICAgICAgICAgaWYgKGF3YWl0IGZpbHRlcihsYXRlc3QudmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgcHVzaChsYXRlc3QudmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHN0b3AoKTtcbiAgICB9KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/subscription/cjs/operator/filter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/subscription/cjs/operator/map.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@graphql-yoga/subscription/cjs/operator/map.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.map = void 0;\nconst repeater_1 = __webpack_require__(/*! @repeaterjs/repeater */ \"(rsc)/./node_modules/@repeaterjs/repeater/cjs/repeater.js\");\n/**\n * Utility for mapping an event stream.\n */\nconst map = (mapper) => (source) => new repeater_1.Repeater(async (push, stop) => {\n    const iterable = source[Symbol.asyncIterator]();\n    stop.then(() => {\n        iterable.return?.();\n    });\n    let latest;\n    while ((latest = await iterable.next()).done === false) {\n        await push(await mapper(latest.value));\n    }\n    stop();\n});\nexports.map = map;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGdyYXBocWwteW9nYS9zdWJzY3JpcHRpb24vY2pzL29wZXJhdG9yL21hcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxXQUFXO0FBQ1gsbUJBQW1CLG1CQUFPLENBQUMsdUZBQXNCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsV0FBVyIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAZ3JhcGhxbC15b2dhXFxzdWJzY3JpcHRpb25cXGNqc1xcb3BlcmF0b3JcXG1hcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMubWFwID0gdm9pZCAwO1xuY29uc3QgcmVwZWF0ZXJfMSA9IHJlcXVpcmUoXCJAcmVwZWF0ZXJqcy9yZXBlYXRlclwiKTtcbi8qKlxuICogVXRpbGl0eSBmb3IgbWFwcGluZyBhbiBldmVudCBzdHJlYW0uXG4gKi9cbmNvbnN0IG1hcCA9IChtYXBwZXIpID0+IChzb3VyY2UpID0+IG5ldyByZXBlYXRlcl8xLlJlcGVhdGVyKGFzeW5jIChwdXNoLCBzdG9wKSA9PiB7XG4gICAgY29uc3QgaXRlcmFibGUgPSBzb3VyY2VbU3ltYm9sLmFzeW5jSXRlcmF0b3JdKCk7XG4gICAgc3RvcC50aGVuKCgpID0+IHtcbiAgICAgICAgaXRlcmFibGUucmV0dXJuPy4oKTtcbiAgICB9KTtcbiAgICBsZXQgbGF0ZXN0O1xuICAgIHdoaWxlICgobGF0ZXN0ID0gYXdhaXQgaXRlcmFibGUubmV4dCgpKS5kb25lID09PSBmYWxzZSkge1xuICAgICAgICBhd2FpdCBwdXNoKGF3YWl0IG1hcHBlcihsYXRlc3QudmFsdWUpKTtcbiAgICB9XG4gICAgc3RvcCgpO1xufSk7XG5leHBvcnRzLm1hcCA9IG1hcDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/subscription/cjs/operator/map.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@graphql-yoga/subscription/cjs/utils/pipe.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@graphql-yoga/subscription/cjs/utils/pipe.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/* eslint-disable @typescript-eslint/no-unsafe-function-type */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.pipe = pipe;\nfunction pipe(a, ab, bc, cd, de, ef, fg, gh, hi) {\n    switch (arguments.length) {\n        case 1:\n            return a;\n        case 2:\n            return ab(a);\n        case 3:\n            return bc(ab(a));\n        case 4:\n            return cd(bc(ab(a)));\n        case 5:\n            return de(cd(bc(ab(a))));\n        case 6:\n            return ef(de(cd(bc(ab(a)))));\n        case 7:\n            return fg(ef(de(cd(bc(ab(a))))));\n        case 8:\n            return gh(fg(ef(de(cd(bc(ab(a)))))));\n        case 9:\n            return hi(gh(fg(ef(de(cd(bc(ab(a))))))));\n        default:\n            // eslint-disable-next-line no-case-declarations, prefer-rest-params\n            let ret = arguments[0];\n            for (let i = 1; i < arguments.length; i++) {\n                // eslint-disable-next-line prefer-rest-params\n                ret = arguments[i](ret);\n            }\n            return ret;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@graphql-yoga/subscription/cjs/utils/pipe.js\n");

/***/ })

};
;