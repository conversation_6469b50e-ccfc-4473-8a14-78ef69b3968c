'use strict';
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/base.proto
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.Vectors =
  exports.GeoCoordinatesFilter =
  exports.FilterTarget =
  exports.FilterReferenceCount =
  exports.FilterReferenceMultiTarget =
  exports.FilterReferenceSingleTarget =
  exports.Filters =
  exports.BooleanArray =
  exports.NumberArray =
  exports.IntArray =
  exports.TextArray =
  exports.ObjectProperties =
  exports.ObjectArrayProperties =
  exports.ObjectPropertiesValue =
  exports.BooleanArrayProperties =
  exports.TextArrayProperties =
  exports.IntArrayProperties =
  exports.NumberArrayProperties =
  exports.vectors_VectorTypeToJSON =
  exports.vectors_VectorTypeFromJSON =
  exports.Vectors_VectorType =
  exports.filters_OperatorToJSON =
  exports.filters_OperatorFromJSON =
  exports.Filters_Operator =
  exports.consistencyLevelToJSON =
  exports.consistencyLevelFromJSON =
  exports.ConsistencyLevel =
  exports.protobufPackage =
    void 0;
/* eslint-disable */
const long_1 = __importDefault(require('long'));
const minimal_js_1 = __importDefault(require('protobufjs/minimal.js'));
const struct_js_1 = require('../google/protobuf/struct.js');
exports.protobufPackage = 'weaviate.v1';
var ConsistencyLevel;
(function (ConsistencyLevel) {
  ConsistencyLevel[(ConsistencyLevel['CONSISTENCY_LEVEL_UNSPECIFIED'] = 0)] = 'CONSISTENCY_LEVEL_UNSPECIFIED';
  ConsistencyLevel[(ConsistencyLevel['CONSISTENCY_LEVEL_ONE'] = 1)] = 'CONSISTENCY_LEVEL_ONE';
  ConsistencyLevel[(ConsistencyLevel['CONSISTENCY_LEVEL_QUORUM'] = 2)] = 'CONSISTENCY_LEVEL_QUORUM';
  ConsistencyLevel[(ConsistencyLevel['CONSISTENCY_LEVEL_ALL'] = 3)] = 'CONSISTENCY_LEVEL_ALL';
  ConsistencyLevel[(ConsistencyLevel['UNRECOGNIZED'] = -1)] = 'UNRECOGNIZED';
})(ConsistencyLevel || (exports.ConsistencyLevel = ConsistencyLevel = {}));
function consistencyLevelFromJSON(object) {
  switch (object) {
    case 0:
    case 'CONSISTENCY_LEVEL_UNSPECIFIED':
      return ConsistencyLevel.CONSISTENCY_LEVEL_UNSPECIFIED;
    case 1:
    case 'CONSISTENCY_LEVEL_ONE':
      return ConsistencyLevel.CONSISTENCY_LEVEL_ONE;
    case 2:
    case 'CONSISTENCY_LEVEL_QUORUM':
      return ConsistencyLevel.CONSISTENCY_LEVEL_QUORUM;
    case 3:
    case 'CONSISTENCY_LEVEL_ALL':
      return ConsistencyLevel.CONSISTENCY_LEVEL_ALL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ConsistencyLevel.UNRECOGNIZED;
  }
}
exports.consistencyLevelFromJSON = consistencyLevelFromJSON;
function consistencyLevelToJSON(object) {
  switch (object) {
    case ConsistencyLevel.CONSISTENCY_LEVEL_UNSPECIFIED:
      return 'CONSISTENCY_LEVEL_UNSPECIFIED';
    case ConsistencyLevel.CONSISTENCY_LEVEL_ONE:
      return 'CONSISTENCY_LEVEL_ONE';
    case ConsistencyLevel.CONSISTENCY_LEVEL_QUORUM:
      return 'CONSISTENCY_LEVEL_QUORUM';
    case ConsistencyLevel.CONSISTENCY_LEVEL_ALL:
      return 'CONSISTENCY_LEVEL_ALL';
    case ConsistencyLevel.UNRECOGNIZED:
    default:
      return 'UNRECOGNIZED';
  }
}
exports.consistencyLevelToJSON = consistencyLevelToJSON;
var Filters_Operator;
(function (Filters_Operator) {
  Filters_Operator[(Filters_Operator['OPERATOR_UNSPECIFIED'] = 0)] = 'OPERATOR_UNSPECIFIED';
  Filters_Operator[(Filters_Operator['OPERATOR_EQUAL'] = 1)] = 'OPERATOR_EQUAL';
  Filters_Operator[(Filters_Operator['OPERATOR_NOT_EQUAL'] = 2)] = 'OPERATOR_NOT_EQUAL';
  Filters_Operator[(Filters_Operator['OPERATOR_GREATER_THAN'] = 3)] = 'OPERATOR_GREATER_THAN';
  Filters_Operator[(Filters_Operator['OPERATOR_GREATER_THAN_EQUAL'] = 4)] = 'OPERATOR_GREATER_THAN_EQUAL';
  Filters_Operator[(Filters_Operator['OPERATOR_LESS_THAN'] = 5)] = 'OPERATOR_LESS_THAN';
  Filters_Operator[(Filters_Operator['OPERATOR_LESS_THAN_EQUAL'] = 6)] = 'OPERATOR_LESS_THAN_EQUAL';
  Filters_Operator[(Filters_Operator['OPERATOR_AND'] = 7)] = 'OPERATOR_AND';
  Filters_Operator[(Filters_Operator['OPERATOR_OR'] = 8)] = 'OPERATOR_OR';
  Filters_Operator[(Filters_Operator['OPERATOR_WITHIN_GEO_RANGE'] = 9)] = 'OPERATOR_WITHIN_GEO_RANGE';
  Filters_Operator[(Filters_Operator['OPERATOR_LIKE'] = 10)] = 'OPERATOR_LIKE';
  Filters_Operator[(Filters_Operator['OPERATOR_IS_NULL'] = 11)] = 'OPERATOR_IS_NULL';
  Filters_Operator[(Filters_Operator['OPERATOR_CONTAINS_ANY'] = 12)] = 'OPERATOR_CONTAINS_ANY';
  Filters_Operator[(Filters_Operator['OPERATOR_CONTAINS_ALL'] = 13)] = 'OPERATOR_CONTAINS_ALL';
  Filters_Operator[(Filters_Operator['UNRECOGNIZED'] = -1)] = 'UNRECOGNIZED';
})(Filters_Operator || (exports.Filters_Operator = Filters_Operator = {}));
function filters_OperatorFromJSON(object) {
  switch (object) {
    case 0:
    case 'OPERATOR_UNSPECIFIED':
      return Filters_Operator.OPERATOR_UNSPECIFIED;
    case 1:
    case 'OPERATOR_EQUAL':
      return Filters_Operator.OPERATOR_EQUAL;
    case 2:
    case 'OPERATOR_NOT_EQUAL':
      return Filters_Operator.OPERATOR_NOT_EQUAL;
    case 3:
    case 'OPERATOR_GREATER_THAN':
      return Filters_Operator.OPERATOR_GREATER_THAN;
    case 4:
    case 'OPERATOR_GREATER_THAN_EQUAL':
      return Filters_Operator.OPERATOR_GREATER_THAN_EQUAL;
    case 5:
    case 'OPERATOR_LESS_THAN':
      return Filters_Operator.OPERATOR_LESS_THAN;
    case 6:
    case 'OPERATOR_LESS_THAN_EQUAL':
      return Filters_Operator.OPERATOR_LESS_THAN_EQUAL;
    case 7:
    case 'OPERATOR_AND':
      return Filters_Operator.OPERATOR_AND;
    case 8:
    case 'OPERATOR_OR':
      return Filters_Operator.OPERATOR_OR;
    case 9:
    case 'OPERATOR_WITHIN_GEO_RANGE':
      return Filters_Operator.OPERATOR_WITHIN_GEO_RANGE;
    case 10:
    case 'OPERATOR_LIKE':
      return Filters_Operator.OPERATOR_LIKE;
    case 11:
    case 'OPERATOR_IS_NULL':
      return Filters_Operator.OPERATOR_IS_NULL;
    case 12:
    case 'OPERATOR_CONTAINS_ANY':
      return Filters_Operator.OPERATOR_CONTAINS_ANY;
    case 13:
    case 'OPERATOR_CONTAINS_ALL':
      return Filters_Operator.OPERATOR_CONTAINS_ALL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Filters_Operator.UNRECOGNIZED;
  }
}
exports.filters_OperatorFromJSON = filters_OperatorFromJSON;
function filters_OperatorToJSON(object) {
  switch (object) {
    case Filters_Operator.OPERATOR_UNSPECIFIED:
      return 'OPERATOR_UNSPECIFIED';
    case Filters_Operator.OPERATOR_EQUAL:
      return 'OPERATOR_EQUAL';
    case Filters_Operator.OPERATOR_NOT_EQUAL:
      return 'OPERATOR_NOT_EQUAL';
    case Filters_Operator.OPERATOR_GREATER_THAN:
      return 'OPERATOR_GREATER_THAN';
    case Filters_Operator.OPERATOR_GREATER_THAN_EQUAL:
      return 'OPERATOR_GREATER_THAN_EQUAL';
    case Filters_Operator.OPERATOR_LESS_THAN:
      return 'OPERATOR_LESS_THAN';
    case Filters_Operator.OPERATOR_LESS_THAN_EQUAL:
      return 'OPERATOR_LESS_THAN_EQUAL';
    case Filters_Operator.OPERATOR_AND:
      return 'OPERATOR_AND';
    case Filters_Operator.OPERATOR_OR:
      return 'OPERATOR_OR';
    case Filters_Operator.OPERATOR_WITHIN_GEO_RANGE:
      return 'OPERATOR_WITHIN_GEO_RANGE';
    case Filters_Operator.OPERATOR_LIKE:
      return 'OPERATOR_LIKE';
    case Filters_Operator.OPERATOR_IS_NULL:
      return 'OPERATOR_IS_NULL';
    case Filters_Operator.OPERATOR_CONTAINS_ANY:
      return 'OPERATOR_CONTAINS_ANY';
    case Filters_Operator.OPERATOR_CONTAINS_ALL:
      return 'OPERATOR_CONTAINS_ALL';
    case Filters_Operator.UNRECOGNIZED:
    default:
      return 'UNRECOGNIZED';
  }
}
exports.filters_OperatorToJSON = filters_OperatorToJSON;
var Vectors_VectorType;
(function (Vectors_VectorType) {
  Vectors_VectorType[(Vectors_VectorType['VECTOR_TYPE_UNSPECIFIED'] = 0)] = 'VECTOR_TYPE_UNSPECIFIED';
  Vectors_VectorType[(Vectors_VectorType['VECTOR_TYPE_SINGLE_FP32'] = 1)] = 'VECTOR_TYPE_SINGLE_FP32';
  Vectors_VectorType[(Vectors_VectorType['VECTOR_TYPE_MULTI_FP32'] = 2)] = 'VECTOR_TYPE_MULTI_FP32';
  Vectors_VectorType[(Vectors_VectorType['UNRECOGNIZED'] = -1)] = 'UNRECOGNIZED';
})(Vectors_VectorType || (exports.Vectors_VectorType = Vectors_VectorType = {}));
function vectors_VectorTypeFromJSON(object) {
  switch (object) {
    case 0:
    case 'VECTOR_TYPE_UNSPECIFIED':
      return Vectors_VectorType.VECTOR_TYPE_UNSPECIFIED;
    case 1:
    case 'VECTOR_TYPE_SINGLE_FP32':
      return Vectors_VectorType.VECTOR_TYPE_SINGLE_FP32;
    case 2:
    case 'VECTOR_TYPE_MULTI_FP32':
      return Vectors_VectorType.VECTOR_TYPE_MULTI_FP32;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Vectors_VectorType.UNRECOGNIZED;
  }
}
exports.vectors_VectorTypeFromJSON = vectors_VectorTypeFromJSON;
function vectors_VectorTypeToJSON(object) {
  switch (object) {
    case Vectors_VectorType.VECTOR_TYPE_UNSPECIFIED:
      return 'VECTOR_TYPE_UNSPECIFIED';
    case Vectors_VectorType.VECTOR_TYPE_SINGLE_FP32:
      return 'VECTOR_TYPE_SINGLE_FP32';
    case Vectors_VectorType.VECTOR_TYPE_MULTI_FP32:
      return 'VECTOR_TYPE_MULTI_FP32';
    case Vectors_VectorType.UNRECOGNIZED:
    default:
      return 'UNRECOGNIZED';
  }
}
exports.vectors_VectorTypeToJSON = vectors_VectorTypeToJSON;
function createBaseNumberArrayProperties() {
  return { values: [], propName: '', valuesBytes: new Uint8Array(0) };
}
exports.NumberArrayProperties = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.double(v);
    }
    writer.ldelim();
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    if (message.valuesBytes.length !== 0) {
      writer.uint32(26).bytes(message.valuesBytes);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNumberArrayProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 9) {
            message.values.push(reader.double());
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.double());
            }
            continue;
          }
          break;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.valuesBytes = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.Number(e))
        : [],
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
      valuesBytes: isSet(object.valuesBytes) ? bytesFromBase64(object.valuesBytes) : new Uint8Array(0),
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    if (message.valuesBytes.length !== 0) {
      obj.valuesBytes = base64FromBytes(message.valuesBytes);
    }
    return obj;
  },
  create(base) {
    return exports.NumberArrayProperties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseNumberArrayProperties();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.propName = (_b = object.propName) !== null && _b !== void 0 ? _b : '';
    message.valuesBytes = (_c = object.valuesBytes) !== null && _c !== void 0 ? _c : new Uint8Array(0);
    return message;
  },
};
function createBaseIntArrayProperties() {
  return { values: [], propName: '' };
}
exports.IntArrayProperties = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.int64(v);
    }
    writer.ldelim();
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntArrayProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(longToNumber(reader.int64()));
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(longToNumber(reader.int64()));
            }
            continue;
          }
          break;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.Number(e))
        : [],
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values.map((e) => Math.round(e));
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    return obj;
  },
  create(base) {
    return exports.IntArrayProperties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseIntArrayProperties();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.propName = (_b = object.propName) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function createBaseTextArrayProperties() {
  return { values: [], propName: '' };
}
exports.TextArrayProperties = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextArrayProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    return obj;
  },
  create(base) {
    return exports.TextArrayProperties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseTextArrayProperties();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.propName = (_b = object.propName) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function createBaseBooleanArrayProperties() {
  return { values: [], propName: '' };
}
exports.BooleanArrayProperties = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.bool(v);
    }
    writer.ldelim();
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBooleanArrayProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(reader.bool());
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.bool());
            }
            continue;
          }
          break;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.Boolean(e))
        : [],
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    return obj;
  },
  create(base) {
    return exports.BooleanArrayProperties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseBooleanArrayProperties();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.propName = (_b = object.propName) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function createBaseObjectPropertiesValue() {
  return {
    nonRefProperties: undefined,
    numberArrayProperties: [],
    intArrayProperties: [],
    textArrayProperties: [],
    booleanArrayProperties: [],
    objectProperties: [],
    objectArrayProperties: [],
    emptyListProps: [],
  };
}
exports.ObjectPropertiesValue = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.nonRefProperties !== undefined) {
      struct_js_1.Struct.encode(
        struct_js_1.Struct.wrap(message.nonRefProperties),
        writer.uint32(10).fork()
      ).ldelim();
    }
    for (const v of message.numberArrayProperties) {
      exports.NumberArrayProperties.encode(v, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.intArrayProperties) {
      exports.IntArrayProperties.encode(v, writer.uint32(26).fork()).ldelim();
    }
    for (const v of message.textArrayProperties) {
      exports.TextArrayProperties.encode(v, writer.uint32(34).fork()).ldelim();
    }
    for (const v of message.booleanArrayProperties) {
      exports.BooleanArrayProperties.encode(v, writer.uint32(42).fork()).ldelim();
    }
    for (const v of message.objectProperties) {
      exports.ObjectProperties.encode(v, writer.uint32(50).fork()).ldelim();
    }
    for (const v of message.objectArrayProperties) {
      exports.ObjectArrayProperties.encode(v, writer.uint32(58).fork()).ldelim();
    }
    for (const v of message.emptyListProps) {
      writer.uint32(82).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectPropertiesValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.nonRefProperties = struct_js_1.Struct.unwrap(
            struct_js_1.Struct.decode(reader, reader.uint32())
          );
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.numberArrayProperties.push(exports.NumberArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.intArrayProperties.push(exports.IntArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.textArrayProperties.push(exports.TextArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.booleanArrayProperties.push(exports.BooleanArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.objectProperties.push(exports.ObjectProperties.decode(reader, reader.uint32()));
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.objectArrayProperties.push(exports.ObjectArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.emptyListProps.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      nonRefProperties: isObject(object.nonRefProperties) ? object.nonRefProperties : undefined,
      numberArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.numberArrayProperties
      )
        ? object.numberArrayProperties.map((e) => exports.NumberArrayProperties.fromJSON(e))
        : [],
      intArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.intArrayProperties
      )
        ? object.intArrayProperties.map((e) => exports.IntArrayProperties.fromJSON(e))
        : [],
      textArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.textArrayProperties
      )
        ? object.textArrayProperties.map((e) => exports.TextArrayProperties.fromJSON(e))
        : [],
      booleanArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.booleanArrayProperties
      )
        ? object.booleanArrayProperties.map((e) => exports.BooleanArrayProperties.fromJSON(e))
        : [],
      objectProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.objectProperties
      )
        ? object.objectProperties.map((e) => exports.ObjectProperties.fromJSON(e))
        : [],
      objectArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.objectArrayProperties
      )
        ? object.objectArrayProperties.map((e) => exports.ObjectArrayProperties.fromJSON(e))
        : [],
      emptyListProps: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.emptyListProps
      )
        ? object.emptyListProps.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b, _c, _d, _e, _f, _g;
    const obj = {};
    if (message.nonRefProperties !== undefined) {
      obj.nonRefProperties = message.nonRefProperties;
    }
    if ((_a = message.numberArrayProperties) === null || _a === void 0 ? void 0 : _a.length) {
      obj.numberArrayProperties = message.numberArrayProperties.map((e) =>
        exports.NumberArrayProperties.toJSON(e)
      );
    }
    if ((_b = message.intArrayProperties) === null || _b === void 0 ? void 0 : _b.length) {
      obj.intArrayProperties = message.intArrayProperties.map((e) => exports.IntArrayProperties.toJSON(e));
    }
    if ((_c = message.textArrayProperties) === null || _c === void 0 ? void 0 : _c.length) {
      obj.textArrayProperties = message.textArrayProperties.map((e) => exports.TextArrayProperties.toJSON(e));
    }
    if ((_d = message.booleanArrayProperties) === null || _d === void 0 ? void 0 : _d.length) {
      obj.booleanArrayProperties = message.booleanArrayProperties.map((e) =>
        exports.BooleanArrayProperties.toJSON(e)
      );
    }
    if ((_e = message.objectProperties) === null || _e === void 0 ? void 0 : _e.length) {
      obj.objectProperties = message.objectProperties.map((e) => exports.ObjectProperties.toJSON(e));
    }
    if ((_f = message.objectArrayProperties) === null || _f === void 0 ? void 0 : _f.length) {
      obj.objectArrayProperties = message.objectArrayProperties.map((e) =>
        exports.ObjectArrayProperties.toJSON(e)
      );
    }
    if ((_g = message.emptyListProps) === null || _g === void 0 ? void 0 : _g.length) {
      obj.emptyListProps = message.emptyListProps;
    }
    return obj;
  },
  create(base) {
    return exports.ObjectPropertiesValue.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const message = createBaseObjectPropertiesValue();
    message.nonRefProperties = (_a = object.nonRefProperties) !== null && _a !== void 0 ? _a : undefined;
    message.numberArrayProperties =
      ((_b = object.numberArrayProperties) === null || _b === void 0
        ? void 0
        : _b.map((e) => exports.NumberArrayProperties.fromPartial(e))) || [];
    message.intArrayProperties =
      ((_c = object.intArrayProperties) === null || _c === void 0
        ? void 0
        : _c.map((e) => exports.IntArrayProperties.fromPartial(e))) || [];
    message.textArrayProperties =
      ((_d = object.textArrayProperties) === null || _d === void 0
        ? void 0
        : _d.map((e) => exports.TextArrayProperties.fromPartial(e))) || [];
    message.booleanArrayProperties =
      ((_e = object.booleanArrayProperties) === null || _e === void 0
        ? void 0
        : _e.map((e) => exports.BooleanArrayProperties.fromPartial(e))) || [];
    message.objectProperties =
      ((_f = object.objectProperties) === null || _f === void 0
        ? void 0
        : _f.map((e) => exports.ObjectProperties.fromPartial(e))) || [];
    message.objectArrayProperties =
      ((_g = object.objectArrayProperties) === null || _g === void 0
        ? void 0
        : _g.map((e) => exports.ObjectArrayProperties.fromPartial(e))) || [];
    message.emptyListProps =
      ((_h = object.emptyListProps) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
    return message;
  },
};
function createBaseObjectArrayProperties() {
  return { values: [], propName: '' };
}
exports.ObjectArrayProperties = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.values) {
      exports.ObjectPropertiesValue.encode(v, writer.uint32(10).fork()).ldelim();
    }
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectArrayProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(exports.ObjectPropertiesValue.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => exports.ObjectPropertiesValue.fromJSON(e))
        : [],
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values.map((e) => exports.ObjectPropertiesValue.toJSON(e));
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    return obj;
  },
  create(base) {
    return exports.ObjectArrayProperties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseObjectArrayProperties();
    message.values =
      ((_a = object.values) === null || _a === void 0
        ? void 0
        : _a.map((e) => exports.ObjectPropertiesValue.fromPartial(e))) || [];
    message.propName = (_b = object.propName) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function createBaseObjectProperties() {
  return { value: undefined, propName: '' };
}
exports.ObjectProperties = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.value !== undefined) {
      exports.ObjectPropertiesValue.encode(message.value, writer.uint32(10).fork()).ldelim();
    }
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.value = exports.ObjectPropertiesValue.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      value: isSet(object.value) ? exports.ObjectPropertiesValue.fromJSON(object.value) : undefined,
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.value !== undefined) {
      obj.value = exports.ObjectPropertiesValue.toJSON(message.value);
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    return obj;
  },
  create(base) {
    return exports.ObjectProperties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseObjectProperties();
    message.value =
      object.value !== undefined && object.value !== null
        ? exports.ObjectPropertiesValue.fromPartial(object.value)
        : undefined;
    message.propName = (_a = object.propName) !== null && _a !== void 0 ? _a : '';
    return message;
  },
};
function createBaseTextArray() {
  return { values: [] };
}
exports.TextArray = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextArray();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return exports.TextArray.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseTextArray();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseIntArray() {
  return { values: [] };
}
exports.IntArray = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.int64(v);
    }
    writer.ldelim();
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntArray();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(longToNumber(reader.int64()));
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(longToNumber(reader.int64()));
            }
            continue;
          }
          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.Number(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values.map((e) => Math.round(e));
    }
    return obj;
  },
  create(base) {
    return exports.IntArray.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseIntArray();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseNumberArray() {
  return { values: [] };
}
exports.NumberArray = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.double(v);
    }
    writer.ldelim();
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNumberArray();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 9) {
            message.values.push(reader.double());
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.double());
            }
            continue;
          }
          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.Number(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return exports.NumberArray.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseNumberArray();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseBooleanArray() {
  return { values: [] };
}
exports.BooleanArray = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.bool(v);
    }
    writer.ldelim();
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBooleanArray();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(reader.bool());
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.bool());
            }
            continue;
          }
          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.Boolean(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return exports.BooleanArray.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseBooleanArray();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseFilters() {
  return {
    operator: 0,
    on: [],
    filters: [],
    valueText: undefined,
    valueInt: undefined,
    valueBoolean: undefined,
    valueNumber: undefined,
    valueTextArray: undefined,
    valueIntArray: undefined,
    valueBooleanArray: undefined,
    valueNumberArray: undefined,
    valueGeo: undefined,
    target: undefined,
  };
}
exports.Filters = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.operator !== 0) {
      writer.uint32(8).int32(message.operator);
    }
    for (const v of message.on) {
      writer.uint32(18).string(v);
    }
    for (const v of message.filters) {
      exports.Filters.encode(v, writer.uint32(26).fork()).ldelim();
    }
    if (message.valueText !== undefined) {
      writer.uint32(34).string(message.valueText);
    }
    if (message.valueInt !== undefined) {
      writer.uint32(40).int64(message.valueInt);
    }
    if (message.valueBoolean !== undefined) {
      writer.uint32(48).bool(message.valueBoolean);
    }
    if (message.valueNumber !== undefined) {
      writer.uint32(57).double(message.valueNumber);
    }
    if (message.valueTextArray !== undefined) {
      exports.TextArray.encode(message.valueTextArray, writer.uint32(74).fork()).ldelim();
    }
    if (message.valueIntArray !== undefined) {
      exports.IntArray.encode(message.valueIntArray, writer.uint32(82).fork()).ldelim();
    }
    if (message.valueBooleanArray !== undefined) {
      exports.BooleanArray.encode(message.valueBooleanArray, writer.uint32(90).fork()).ldelim();
    }
    if (message.valueNumberArray !== undefined) {
      exports.NumberArray.encode(message.valueNumberArray, writer.uint32(98).fork()).ldelim();
    }
    if (message.valueGeo !== undefined) {
      exports.GeoCoordinatesFilter.encode(message.valueGeo, writer.uint32(106).fork()).ldelim();
    }
    if (message.target !== undefined) {
      exports.FilterTarget.encode(message.target, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.operator = reader.int32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.on.push(reader.string());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.filters.push(exports.Filters.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.valueText = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.valueInt = longToNumber(reader.int64());
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.valueBoolean = reader.bool();
          continue;
        case 7:
          if (tag !== 57) {
            break;
          }
          message.valueNumber = reader.double();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.valueTextArray = exports.TextArray.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.valueIntArray = exports.IntArray.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.valueBooleanArray = exports.BooleanArray.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }
          message.valueNumberArray = exports.NumberArray.decode(reader, reader.uint32());
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }
          message.valueGeo = exports.GeoCoordinatesFilter.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }
          message.target = exports.FilterTarget.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      operator: isSet(object.operator) ? filters_OperatorFromJSON(object.operator) : 0,
      on: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.on)
        ? object.on.map((e) => globalThis.String(e))
        : [],
      filters: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.filters)
        ? object.filters.map((e) => exports.Filters.fromJSON(e))
        : [],
      valueText: isSet(object.valueText) ? globalThis.String(object.valueText) : undefined,
      valueInt: isSet(object.valueInt) ? globalThis.Number(object.valueInt) : undefined,
      valueBoolean: isSet(object.valueBoolean) ? globalThis.Boolean(object.valueBoolean) : undefined,
      valueNumber: isSet(object.valueNumber) ? globalThis.Number(object.valueNumber) : undefined,
      valueTextArray: isSet(object.valueTextArray)
        ? exports.TextArray.fromJSON(object.valueTextArray)
        : undefined,
      valueIntArray: isSet(object.valueIntArray)
        ? exports.IntArray.fromJSON(object.valueIntArray)
        : undefined,
      valueBooleanArray: isSet(object.valueBooleanArray)
        ? exports.BooleanArray.fromJSON(object.valueBooleanArray)
        : undefined,
      valueNumberArray: isSet(object.valueNumberArray)
        ? exports.NumberArray.fromJSON(object.valueNumberArray)
        : undefined,
      valueGeo: isSet(object.valueGeo) ? exports.GeoCoordinatesFilter.fromJSON(object.valueGeo) : undefined,
      target: isSet(object.target) ? exports.FilterTarget.fromJSON(object.target) : undefined,
    };
  },
  toJSON(message) {
    var _a, _b;
    const obj = {};
    if (message.operator !== 0) {
      obj.operator = filters_OperatorToJSON(message.operator);
    }
    if ((_a = message.on) === null || _a === void 0 ? void 0 : _a.length) {
      obj.on = message.on;
    }
    if ((_b = message.filters) === null || _b === void 0 ? void 0 : _b.length) {
      obj.filters = message.filters.map((e) => exports.Filters.toJSON(e));
    }
    if (message.valueText !== undefined) {
      obj.valueText = message.valueText;
    }
    if (message.valueInt !== undefined) {
      obj.valueInt = Math.round(message.valueInt);
    }
    if (message.valueBoolean !== undefined) {
      obj.valueBoolean = message.valueBoolean;
    }
    if (message.valueNumber !== undefined) {
      obj.valueNumber = message.valueNumber;
    }
    if (message.valueTextArray !== undefined) {
      obj.valueTextArray = exports.TextArray.toJSON(message.valueTextArray);
    }
    if (message.valueIntArray !== undefined) {
      obj.valueIntArray = exports.IntArray.toJSON(message.valueIntArray);
    }
    if (message.valueBooleanArray !== undefined) {
      obj.valueBooleanArray = exports.BooleanArray.toJSON(message.valueBooleanArray);
    }
    if (message.valueNumberArray !== undefined) {
      obj.valueNumberArray = exports.NumberArray.toJSON(message.valueNumberArray);
    }
    if (message.valueGeo !== undefined) {
      obj.valueGeo = exports.GeoCoordinatesFilter.toJSON(message.valueGeo);
    }
    if (message.target !== undefined) {
      obj.target = exports.FilterTarget.toJSON(message.target);
    }
    return obj;
  },
  create(base) {
    return exports.Filters.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g;
    const message = createBaseFilters();
    message.operator = (_a = object.operator) !== null && _a !== void 0 ? _a : 0;
    message.on = ((_b = object.on) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    message.filters =
      ((_c = object.filters) === null || _c === void 0
        ? void 0
        : _c.map((e) => exports.Filters.fromPartial(e))) || [];
    message.valueText = (_d = object.valueText) !== null && _d !== void 0 ? _d : undefined;
    message.valueInt = (_e = object.valueInt) !== null && _e !== void 0 ? _e : undefined;
    message.valueBoolean = (_f = object.valueBoolean) !== null && _f !== void 0 ? _f : undefined;
    message.valueNumber = (_g = object.valueNumber) !== null && _g !== void 0 ? _g : undefined;
    message.valueTextArray =
      object.valueTextArray !== undefined && object.valueTextArray !== null
        ? exports.TextArray.fromPartial(object.valueTextArray)
        : undefined;
    message.valueIntArray =
      object.valueIntArray !== undefined && object.valueIntArray !== null
        ? exports.IntArray.fromPartial(object.valueIntArray)
        : undefined;
    message.valueBooleanArray =
      object.valueBooleanArray !== undefined && object.valueBooleanArray !== null
        ? exports.BooleanArray.fromPartial(object.valueBooleanArray)
        : undefined;
    message.valueNumberArray =
      object.valueNumberArray !== undefined && object.valueNumberArray !== null
        ? exports.NumberArray.fromPartial(object.valueNumberArray)
        : undefined;
    message.valueGeo =
      object.valueGeo !== undefined && object.valueGeo !== null
        ? exports.GeoCoordinatesFilter.fromPartial(object.valueGeo)
        : undefined;
    message.target =
      object.target !== undefined && object.target !== null
        ? exports.FilterTarget.fromPartial(object.target)
        : undefined;
    return message;
  },
};
function createBaseFilterReferenceSingleTarget() {
  return { on: '', target: undefined };
}
exports.FilterReferenceSingleTarget = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.on !== '') {
      writer.uint32(10).string(message.on);
    }
    if (message.target !== undefined) {
      exports.FilterTarget.encode(message.target, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFilterReferenceSingleTarget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.on = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.target = exports.FilterTarget.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      on: isSet(object.on) ? globalThis.String(object.on) : '',
      target: isSet(object.target) ? exports.FilterTarget.fromJSON(object.target) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.on !== '') {
      obj.on = message.on;
    }
    if (message.target !== undefined) {
      obj.target = exports.FilterTarget.toJSON(message.target);
    }
    return obj;
  },
  create(base) {
    return exports.FilterReferenceSingleTarget.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseFilterReferenceSingleTarget();
    message.on = (_a = object.on) !== null && _a !== void 0 ? _a : '';
    message.target =
      object.target !== undefined && object.target !== null
        ? exports.FilterTarget.fromPartial(object.target)
        : undefined;
    return message;
  },
};
function createBaseFilterReferenceMultiTarget() {
  return { on: '', target: undefined, targetCollection: '' };
}
exports.FilterReferenceMultiTarget = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.on !== '') {
      writer.uint32(10).string(message.on);
    }
    if (message.target !== undefined) {
      exports.FilterTarget.encode(message.target, writer.uint32(18).fork()).ldelim();
    }
    if (message.targetCollection !== '') {
      writer.uint32(26).string(message.targetCollection);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFilterReferenceMultiTarget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.on = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.target = exports.FilterTarget.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.targetCollection = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      on: isSet(object.on) ? globalThis.String(object.on) : '',
      target: isSet(object.target) ? exports.FilterTarget.fromJSON(object.target) : undefined,
      targetCollection: isSet(object.targetCollection) ? globalThis.String(object.targetCollection) : '',
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.on !== '') {
      obj.on = message.on;
    }
    if (message.target !== undefined) {
      obj.target = exports.FilterTarget.toJSON(message.target);
    }
    if (message.targetCollection !== '') {
      obj.targetCollection = message.targetCollection;
    }
    return obj;
  },
  create(base) {
    return exports.FilterReferenceMultiTarget.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseFilterReferenceMultiTarget();
    message.on = (_a = object.on) !== null && _a !== void 0 ? _a : '';
    message.target =
      object.target !== undefined && object.target !== null
        ? exports.FilterTarget.fromPartial(object.target)
        : undefined;
    message.targetCollection = (_b = object.targetCollection) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function createBaseFilterReferenceCount() {
  return { on: '' };
}
exports.FilterReferenceCount = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.on !== '') {
      writer.uint32(10).string(message.on);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFilterReferenceCount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.on = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { on: isSet(object.on) ? globalThis.String(object.on) : '' };
  },
  toJSON(message) {
    const obj = {};
    if (message.on !== '') {
      obj.on = message.on;
    }
    return obj;
  },
  create(base) {
    return exports.FilterReferenceCount.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseFilterReferenceCount();
    message.on = (_a = object.on) !== null && _a !== void 0 ? _a : '';
    return message;
  },
};
function createBaseFilterTarget() {
  return { property: undefined, singleTarget: undefined, multiTarget: undefined, count: undefined };
}
exports.FilterTarget = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.property !== undefined) {
      writer.uint32(10).string(message.property);
    }
    if (message.singleTarget !== undefined) {
      exports.FilterReferenceSingleTarget.encode(message.singleTarget, writer.uint32(18).fork()).ldelim();
    }
    if (message.multiTarget !== undefined) {
      exports.FilterReferenceMultiTarget.encode(message.multiTarget, writer.uint32(26).fork()).ldelim();
    }
    if (message.count !== undefined) {
      exports.FilterReferenceCount.encode(message.count, writer.uint32(34).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFilterTarget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.property = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.singleTarget = exports.FilterReferenceSingleTarget.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.multiTarget = exports.FilterReferenceMultiTarget.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.count = exports.FilterReferenceCount.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      property: isSet(object.property) ? globalThis.String(object.property) : undefined,
      singleTarget: isSet(object.singleTarget)
        ? exports.FilterReferenceSingleTarget.fromJSON(object.singleTarget)
        : undefined,
      multiTarget: isSet(object.multiTarget)
        ? exports.FilterReferenceMultiTarget.fromJSON(object.multiTarget)
        : undefined,
      count: isSet(object.count) ? exports.FilterReferenceCount.fromJSON(object.count) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.property !== undefined) {
      obj.property = message.property;
    }
    if (message.singleTarget !== undefined) {
      obj.singleTarget = exports.FilterReferenceSingleTarget.toJSON(message.singleTarget);
    }
    if (message.multiTarget !== undefined) {
      obj.multiTarget = exports.FilterReferenceMultiTarget.toJSON(message.multiTarget);
    }
    if (message.count !== undefined) {
      obj.count = exports.FilterReferenceCount.toJSON(message.count);
    }
    return obj;
  },
  create(base) {
    return exports.FilterTarget.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseFilterTarget();
    message.property = (_a = object.property) !== null && _a !== void 0 ? _a : undefined;
    message.singleTarget =
      object.singleTarget !== undefined && object.singleTarget !== null
        ? exports.FilterReferenceSingleTarget.fromPartial(object.singleTarget)
        : undefined;
    message.multiTarget =
      object.multiTarget !== undefined && object.multiTarget !== null
        ? exports.FilterReferenceMultiTarget.fromPartial(object.multiTarget)
        : undefined;
    message.count =
      object.count !== undefined && object.count !== null
        ? exports.FilterReferenceCount.fromPartial(object.count)
        : undefined;
    return message;
  },
};
function createBaseGeoCoordinatesFilter() {
  return { latitude: 0, longitude: 0, distance: 0 };
}
exports.GeoCoordinatesFilter = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.latitude !== 0) {
      writer.uint32(13).float(message.latitude);
    }
    if (message.longitude !== 0) {
      writer.uint32(21).float(message.longitude);
    }
    if (message.distance !== 0) {
      writer.uint32(29).float(message.distance);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGeoCoordinatesFilter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.latitude = reader.float();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }
          message.longitude = reader.float();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }
          message.distance = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      latitude: isSet(object.latitude) ? globalThis.Number(object.latitude) : 0,
      longitude: isSet(object.longitude) ? globalThis.Number(object.longitude) : 0,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.latitude !== 0) {
      obj.latitude = message.latitude;
    }
    if (message.longitude !== 0) {
      obj.longitude = message.longitude;
    }
    if (message.distance !== 0) {
      obj.distance = message.distance;
    }
    return obj;
  },
  create(base) {
    return exports.GeoCoordinatesFilter.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGeoCoordinatesFilter();
    message.latitude = (_a = object.latitude) !== null && _a !== void 0 ? _a : 0;
    message.longitude = (_b = object.longitude) !== null && _b !== void 0 ? _b : 0;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : 0;
    return message;
  },
};
function createBaseVectors() {
  return { name: '', index: 0, vectorBytes: new Uint8Array(0), type: 0 };
}
exports.Vectors = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.name !== '') {
      writer.uint32(10).string(message.name);
    }
    if (message.index !== 0) {
      writer.uint32(16).uint64(message.index);
    }
    if (message.vectorBytes.length !== 0) {
      writer.uint32(26).bytes(message.vectorBytes);
    }
    if (message.type !== 0) {
      writer.uint32(32).int32(message.type);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVectors();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.index = longToNumber(reader.uint64());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.vectorBytes = reader.bytes();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.type = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      index: isSet(object.index) ? globalThis.Number(object.index) : 0,
      vectorBytes: isSet(object.vectorBytes) ? bytesFromBase64(object.vectorBytes) : new Uint8Array(0),
      type: isSet(object.type) ? vectors_VectorTypeFromJSON(object.type) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.name !== '') {
      obj.name = message.name;
    }
    if (message.index !== 0) {
      obj.index = Math.round(message.index);
    }
    if (message.vectorBytes.length !== 0) {
      obj.vectorBytes = base64FromBytes(message.vectorBytes);
    }
    if (message.type !== 0) {
      obj.type = vectors_VectorTypeToJSON(message.type);
    }
    return obj;
  },
  create(base) {
    return exports.Vectors.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseVectors();
    message.name = (_a = object.name) !== null && _a !== void 0 ? _a : '';
    message.index = (_b = object.index) !== null && _b !== void 0 ? _b : 0;
    message.vectorBytes = (_c = object.vectorBytes) !== null && _c !== void 0 ? _c : new Uint8Array(0);
    message.type = (_d = object.type) !== null && _d !== void 0 ? _d : 0;
    return message;
  },
};
function bytesFromBase64(b64) {
  if (globalThis.Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}
function base64FromBytes(arr) {
  if (globalThis.Buffer) {
    return globalThis.Buffer.from(arr).toString('base64');
  } else {
    const bin = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(''));
  }
}
function longToNumber(long) {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER');
  }
  return long.toNumber();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
  minimal_js_1.default.util.Long = long_1.default;
  minimal_js_1.default.configure();
}
function isObject(value) {
  return typeof value === 'object' && value !== null;
}
function isSet(value) {
  return value !== null && value !== undefined;
}
