/** @type {Construct} */
export const htmlFlow: Construct
export type Code = import('micromark-util-types').Code
export type Construct = import('micromark-util-types').Construct
export type Resolver = import('micromark-util-types').Resolver
export type State = import('micromark-util-types').State
export type TokenizeContext = import('micromark-util-types').TokenizeContext
export type Tokenizer = import('micromark-util-types').Tokenizer
