/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api2/copilotkit/route";
exports.ids = ["app/api2/copilotkit/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@whatwg-node/fetch/dist sync recursive":
/*!****************************************************!*\
  !*** ./node_modules/@whatwg-node/fetch/dist/ sync ***!
  \****************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@whatwg-node/fetch/dist sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:fs/promises":
/*!***********************************!*\
  !*** external "node:fs/promises" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs/promises");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/promises":
/*!***************************************!*\
  !*** external "node:stream/promises" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/promises");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi2%2Fcopilotkit%2Froute&page=%2Fapi2%2Fcopilotkit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi2%2Fcopilotkit%2Froute.ts&appDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi2%2Fcopilotkit%2Froute&page=%2Fapi2%2Fcopilotkit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi2%2Fcopilotkit%2Froute.ts&appDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_zinniax_copilot_zinniax_copilot_ui_app_api2_copilotkit_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api2/copilotkit/route.ts */ \"(rsc)/./app/api2/copilotkit/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api2/copilotkit/route\",\n        pathname: \"/api2/copilotkit\",\n        filename: \"route\",\n        bundlePath: \"app/api2/copilotkit/route\"\n    },\n    resolvedPagePath: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\api2\\\\copilotkit\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_zinniax_copilot_zinniax_copilot_ui_app_api2_copilotkit_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi2%2Fcopilotkit%2Froute&page=%2Fapi2%2Fcopilotkit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi2%2Fcopilotkit%2Froute.ts&appDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api2/copilotkit/route.ts":
/*!**************************************!*\
  !*** ./app/api2/copilotkit/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @copilotkit/runtime */ \"(rsc)/./node_modules/@copilotkit/runtime/dist/index.js\");\n/* harmony import */ var _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_copilotkit_runtime__WEBPACK_IMPORTED_MODULE_0__);\n// route.ts\n\n// const REMOTE_ACTION_URL = 'https://stage.ionm.copilot.zinniax.com';\nconst REMOTE_ACTION_URL = 'http://localhost:8001';\nconsole.log(\"REMOTE_ACTION_URL_ROUTE2222\", REMOTE_ACTION_URL);\nconst serviceAdapter = new _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_0__.EmptyAdapter();\n// Use trailing slash to match your working configuration\nconst link = REMOTE_ACTION_URL + \"/api/copilotkit\";\nconst runtime = new _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_0__.CopilotRuntime({\n    remoteEndpoints: [\n        {\n            url: link\n        }\n    ]\n});\nconst POST = async (req)=>{\n    console.log(\"BACKEND REQUEST RECEIVED...\", req);\n    const { handleRequest } = (0,_copilotkit_runtime__WEBPACK_IMPORTED_MODULE_0__.copilotRuntimeNextJSAppRouterEndpoint)({\n        runtime,\n        serviceAdapter,\n        endpoint: \"/api2/copilotkit\"\n    });\n    console.log(\"POST /copilotkit\", req);\n    return handleRequest(req);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api2/copilotkit/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@aws-sdk","vendor-chunks/@smithy","vendor-chunks/next","vendor-chunks/zod","vendor-chunks/@copilotkit","vendor-chunks/graphql","vendor-chunks/debug","vendor-chunks/untruncate-json","vendor-chunks/extend","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/uuid","vendor-chunks/@langchain","vendor-chunks/rxjs","vendor-chunks/@ag-ui","vendor-chunks/class-validator","vendor-chunks/validator","vendor-chunks/@graphql-tools","vendor-chunks/type-graphql","vendor-chunks/openai","vendor-chunks/graphql-scalars","vendor-chunks/zod-to-json-schema","vendor-chunks/langsmith","vendor-chunks/semver","vendor-chunks/node-forge","vendor-chunks/@whatwg-node","vendor-chunks/@segment","vendor-chunks/libphonenumber-js","vendor-chunks/graphql-yoga","vendor-chunks/google-auth-library","vendor-chunks/jose","vendor-chunks/pino-pretty","vendor-chunks/readable-stream","vendor-chunks/@envelop","vendor-chunks/groq-sdk","vendor-chunks/@anthropic-ai","vendor-chunks/@cfworker","vendor-chunks/class-transformer","vendor-chunks/pino","vendor-chunks/@fastify","vendor-chunks/formdata-node","vendor-chunks/form-data-encoder","vendor-chunks/@graphql-yoga","vendor-chunks/fast-redact","vendor-chunks/pino-std-serializers","vendor-chunks/whatwg-url","vendor-chunks/jws","vendor-chunks/@aws-crypto","vendor-chunks/fast-json-patch","vendor-chunks/@bufbuild","vendor-chunks/thread-stream","vendor-chunks/gaxios","vendor-chunks/chalk","vendor-chunks/agentkeepalive","vendor-chunks/retry","vendor-chunks/p-queue","vendor-chunks/json-bigint","vendor-chunks/https-proxy-agent","vendor-chunks/dset","vendor-chunks/color-convert","vendor-chunks/urlpattern-polyfill","vendor-chunks/langchain","vendor-chunks/yallist","vendor-chunks/tr46","vendor-chunks/partial-json","vendor-chunks/gcp-metadata","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/agent-base","vendor-chunks/tslib","vendor-chunks/node-fetch","vendor-chunks/@lukeed","vendor-chunks/sonic-boom","vendor-chunks/reflect-metadata","vendor-chunks/js-tiktoken","vendor-chunks/fast-copy","vendor-chunks/cross-inspect","vendor-chunks/colorette","vendor-chunks/@repeaterjs","vendor-chunks/wrappy","vendor-chunks/webidl-conversions","vendor-chunks/web-streams-polyfill","vendor-chunks/string_decoder","vendor-chunks/split2","vendor-chunks/secure-json-parse","vendor-chunks/safe-stable-stringify","vendor-chunks/safe-buffer","vendor-chunks/quick-format-unescaped","vendor-chunks/pump","vendor-chunks/process","vendor-chunks/pino-abstract-transport","vendor-chunks/p-timeout","vendor-chunks/p-retry","vendor-chunks/p-finally","vendor-chunks/once","vendor-chunks/on-exit-leak-free","vendor-chunks/node-domexception","vendor-chunks/lru-cache","vendor-chunks/jwa","vendor-chunks/is-stream","vendor-chunks/humanize-ms","vendor-chunks/gtoken","vendor-chunks/google-p12-pem","vendor-chunks/fast-text-encoding","vendor-chunks/fast-safe-stringify","vendor-chunks/eventemitter3","vendor-chunks/event-target-shim","vendor-chunks/end-of-stream","vendor-chunks/decamelize","vendor-chunks/dateformat","vendor-chunks/color-name","vendor-chunks/camelcase","vendor-chunks/buffer-equal-constant-time","vendor-chunks/bignumber.js","vendor-chunks/base64-js","vendor-chunks/atomic-sleep","vendor-chunks/arrify","vendor-chunks/ansi-styles","vendor-chunks/abort-controller"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi2%2Fcopilotkit%2Froute&page=%2Fapi2%2Fcopilotkit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi2%2Fcopilotkit%2Froute.ts&appDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Czinniax-copilot%5Czinniax-copilot%5Cui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();