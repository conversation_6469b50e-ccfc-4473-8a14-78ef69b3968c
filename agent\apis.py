import os
import aiohttp
import logging
import re
from typing import Optional, Dict, Any

from helper import convert_to_12_hour_format_with_period, clean_surgeon_name

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# --- Configuration ---
STAGE_URL = 'https://stage.ionm.zinniax.com/api'


# --- API Client Functions ---


class ZinniaXAPI:
    def __init__(self):
        self.session = None
        # self.auth_token = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    async def fetch_case_details_pro(self, case_id: str, action: str = "general", token=None):
        """Fetch case details from ZinniaX API."""
        print(
            f"Fetching details for case #{case_id} with action: {action}")

        if not case_id:
            logger.error("Case ID is missing")
            return None
        print("TOKEN IN FETCH CASE DETAILS" , token)
        case_id_number = int(case_id)
        api_url = f"{STAGE_URL}/cases/filter"
        auth_token = token

        if not auth_token:
            logger.error("Failed to get authentication token")
            return None

        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.7',
            'authorization': f'Bearer {auth_token}',
            'content-type': 'application/json',
            'user-timezone': 'Asia/Calcutta',
            'origin': 'https://stage.ionm.zinniax.com',
            'referer': 'https://stage.ionm.zinniax.com/cases'
        }

        
        try:
            async with self.session.post(
                api_url,
                json={"regionIds": [], "status": [], "stateIds": [], "doctorIds": [], "hospitals": [], "readerIds": [], "techIds": [], "insuranceTypeIds": [], "procedureTypeIds": [], "preCertStatus": [], "patientFirstName": None, "patientLastName": None, "caseNo": case_id_number, "withLabelIds": [
                ], "withOutLabelIds": [], "withDocumentTypeIds": [], "withOutDocumentTypeIds": [], "noteTypeIds": [], "noteActionLabelIds": [], "readerOversights": [], "isShowNaCases": False, "surgeryFromDate": None, "surgeryToDate": None, "patientDOBFromDate": None, "patientDOBToDate": None},
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    content = data.get('content', [])

                    if not content:
                        logger.error(f"No case found with Case # {case_id}")
                        return None

                    case_info = content[0]

                    return {
                        'caseId': case_id_number,
                        'patientName': case_info.get('patientName', 'Unknown'),
                        'surgeonName': case_info.get('doctorName', 'Unknown'),
                        'hospital': case_info.get('hospitalName', 'Unknown'),
                        'procedure': case_info.get('procedure', 'Unknown'),
                        'status': case_info.get('status', 'Unknown'),
                        'scheduledDate': case_info.get('scheduledDate', 'Unknown'),
                        'scheduledTime': case_info.get('scheduledTime', 'Unknown'),
                        'dateOfSurgeryHospitalTime': case_info.get('dateOfSurgeryHospitalTime', 'Unknown')
                    }
                else:
                    logger.error(
                        f"API request failed with status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching case details: {e}")
            return None
    async def fetch_case_details(self, case_id: str, action: str = "general", token=None) -> Optional[Dict[str, Any]]:
        """Fetch case details from ZinniaX API."""
        print(
            f"Fetching details for case #{case_id} with action: {action}")

        if not case_id:
            logger.error("Case ID is missing")
            return None

        case_id_number = int(case_id)
        api_url = f"{STAGE_URL}/cases/filter"
        auth_token = token

        if not auth_token:
            logger.error("Failed to get authentication token")
            return None

        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.7',
            'authorization': f'Bearer {auth_token}',
            'content-type': 'application/json',
            'user-timezone': 'Asia/Calcutta',
            'origin': 'https://stage.ionm.zinniax.com',
            'referer': 'https://stage.ionm.zinniax.com/cases'
        }
        
        try:
            async with self.session.post(
                api_url,
                json={"caseNo":case_id_number,"readerOversights": []},
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    content = data.get('content', [])

                    if not content:
                        logger.error(f"No case found with Case # {case_id}")
                        return None
                    # print('cancel content', content)
                    case_info = content[0]
                    
                    return {
                    'caseId': case_info.get('id', 'Unknown'),
                    'patientName': case_info.get('patientName', 'Unknown'),
                    'surgeonName': case_info.get('doctorName', 'Unknown'),
                    'hospital': case_info.get('hospitalName', 'Unknown'),
                    'procedure': case_info.get('procedure', 'Unknown'),
                    'status': case_info.get('status', 'Unknown'),
                    'scheduledDate': case_info.get('scheduledDate', 'Unknown'),
                    'scheduledTime': case_info.get('scheduledTime', 'Unknown'),
                    'dateOfSurgeryHospitalTime': case_info.get('dateOfSurgeryHospitalTime', 'Unknown')
                    }
                else:
                    logger.error(
                        f"API request failed with status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching case details: {e}")
            return None
        
    async def make_put_request(self, data,action='cancel',reschedule_data=None,token=None):
        url = f"{STAGE_URL}/cases"
        if action == 'reschedule':
            data["note"] = "Rescheduled by ZinniaX Copilot"
            data["surgeryDate"] =  reschedule_data['new_date']
            data["surgeryTime"] =  reschedule_data['new_time']
            data["surgeryAmpm"] = reschedule_data['period']
            
            # print('put reschedule data', data)
           
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.7',
                'authorization': f'Bearer {token}',
                'content-type': 'application/json',
                'user-timezone': 'Asia/Calcutta',
                'origin': 'https://stage.ionm.zinniax.com',
                'referer': 'https://stage.ionm.zinniax.com/cases'
            }
            async with self.session.put(url, json=data, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return 'rescheduled',True
                else:
                    logger.error(
                        f"API request failed with status in put request while rescheduling: {response.status}")
                    return 'rescheduled',False

        elif action == 'uncancel':
            data["note"] = "Reopened by ZinniaX Copilot"
            data["status"] = "Pending"
            
            # print('put uncancel data', data)
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.7',
                'authorization': f'Bearer {token}',
                'content-type': 'application/json',
                'user-timezone': 'Asia/Calcutta',
                'origin': 'https://stage.ionm.zinniax.com',
                'referer': 'https://stage.ionm.zinniax.com/cases'
            }
            async with self.session.put(url, json=data, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return 'uncanceled',True
                else:
                    logger.error(
                        f"API request failed with status in put request while uncanceling a case: {response.status}")
                    return 'uncanceled',False

        else:
            data["note"] = "Canceled by ZinniaX Copilot"
            data["cancelReason"] = "Canceled by ZinniaX Copilot"
            data["status"] = "Canceled"
            
            print('put data', data)
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.7',
                'authorization': f'Bearer {token}',
                'content-type': 'application/json',
                'user-timezone': 'Asia/Calcutta',
                'origin': 'https://stage.ionm.zinniax.com',
                'referer': 'https://stage.ionm.zinniax.com/cases'
            }

            async with self.session.put(url, json=data, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return 'canceled',True
                else:
                    logger.error(
                        f"API request failed with status in put request while cancelling a case: {response.status}")
                    return 'canceled',False
    async def fetch_single_case_details(self, caseno: str, token=None) -> Optional[Dict[str, Any]]:
        """Fetch case details from ZinniaX API."""
        fetch_single_case_api = f"{STAGE_URL}/cases/{caseno}"
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {token}'
            }

            async with self.session.get(fetch_single_case_api, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logger.error(
                        f"API request for fetch single case failed with status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching case details: {e}")
            return None
        






    async def schedule_new_case(self, details: Dict[str, Any], token=None) -> tuple[bool, str]:
        """Schedule a new case in ZinniaX."""
        url = f"{STAGE_URL}/cases"

        # Replace any None or "None" values in details with " "
        details = {k: (v if v is not None and v != "None" else "")
                   for k, v in details.items()}
        print("&"*80)
        print("SCHEDULE" , details)
        print("&"*80)
        patient_name = details.get('patientName', '').strip()
        if patient_name != '':
            # Default to empty
            first_name = ''
            last_name = ''

            if patient_name:
                name_parts = patient_name.split()
                if len(name_parts) == 1:
                    first_name = name_parts[0]
                    last_name = ''
                elif len(name_parts) >= 2:
                    first_name = name_parts[0]
                    last_name = name_parts[1]
            else:
                first_name = ''
                last_name = ''
            patient_name = {
                        "id": '',
                        "firstName": first_name,
                        "lastName": last_name,
                        "dateOfBirth": ''
                    }
        else :
            patient_name = None
            
        try:
            time_24h,period = convert_to_12_hour_format_with_period(details['time'])
            surgeon_name = clean_surgeon_name(details['surgeon'])

            hospital_name = details['hospital']
            if details.get('city') != '':
                # print('APPENDED CITY')
                hospital_name = f"{hospital_name}; {details['city']}"
            payload = {
                    "surgeryDate": details['date'],
                    "surgeryTime": time_24h,
                    "surgeryAmpm": period,
                    "durationOfProcedureHours": "03:00",
                    "procedure": details.get('procedure', ''),
                    "hospitalId": details.get("hospitalId",''),
                    "hospitalName": details.get('hospital', ''),
                    "doctorId": details.get('doctorId', ''),
                    "patient": patient_name,
                    "readerOversight": "REMOTE",
                    "status": "Pending",
                    "patientInOut": "IN",
                    "requestedBy": "Robert",
                    "note": "Scheduled by Zinniax Copilot.",
                    "surgeonName": surgeon_name
                }
            
            # print('payload', payload)
            # payload = {
            #     "name": "schedule",
            #     "call": {
            #         "agent_id": "copilot"},
            #     "copilot": True,
            #     "args": {

            #         "date_time_wordings": f"{details['date']} {time_24h}",
            #         "procedure": details.get('procedure', ''),
            #         "callerName": "Copilot",
            #         "patientName": details.get('patientName', ''),
            #         "surgeonName": surgeon_name,
            #         "hospitalName": hospital_name
            #     }
            # }

            print(f"payload: {payload}")
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {token}'
            }

            async with self.session.post(url, json=payload, headers=headers) as response:
                if response.status == 200 or 201:
                    data = await response.json()
                    print('scheduling response', data)
                    result = data['caseNo']
                    caseID = data['id']
               

                    if 'Error!' in str(result):
                        return False, result, None

                    # case_id = extract_integer_from_text(result)
                    return True, result, caseID
                else:
                    return False, f"Failed to schedule case. Status: {response.status}", None
        except Exception as e:
            logger.error(f"Error scheduling case: {e}"), None
            return False, f"Error scheduling case: {str(e)}", None

    async def cancel_case(self, case_id: str, token=None) -> tuple[bool, str]:
        """Cancel a case in ZinniaX."""
        print("cancle case here ",case_id)

        caseno = await self.fetch_case_details(case_id, token=token,action='cancel')
        print('cancel case no',caseno['caseId'])
        payload = await self.fetch_single_case_details(caseno['caseId'], token=token)
        action,result = await self.make_put_request(payload,action='cancel',token=token)

        if result == True:
            return True, f"Case #{case_id} has been successfully canceled."
        else:
            return False, f"Failed to cancel case."
        

    async def reschedule_case(self, case_id: str, new_date: str, new_time: str, token=None) -> bool:
        """Reschedule a case in ZinniaX."""

        # try:

            
        time_24h,period = convert_to_12_hour_format_with_period(new_time)
        reschedule_data = {}
        reschedule_data['new_date'] = new_date
        reschedule_data['new_time'] = time_24h
        reschedule_data['period'] = period
        caseno = await self.fetch_case_details(case_id, token=token,action='cancel')
        print('reschedule case no',caseno['caseId'])
        payload = await self.fetch_single_case_details(caseno['caseId'], token=token)
        action,result = await self.make_put_request(payload,action='reschedule',reschedule_data=reschedule_data,token=token)

      

        if result == True:
            return True, f"Case #{case_id} has been successfully Rescheduled to {new_date} at {new_time}."
        else:
            return False, f"Failed to reschedule case."

    async def uncancel_case(self, case_id: str, token=None) -> bool:
        """Uncancel a case in ZinniaX."""



        print("uncancle case here ",case_id)

        caseno = await self.fetch_case_details(case_id, token=token,action='uncancel')
        print('cancel case no',caseno['caseId'])
        payload = await self.fetch_single_case_details(caseno['caseId'], token=token)
        action,result = await self.make_put_request(payload,action='uncancel',token=token)
        if result == True:
            return True, f"Case #{case_id} has been successfully canceled."
        else:
            return False, f"Failed to uncancel case."
