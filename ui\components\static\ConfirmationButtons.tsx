import React, { useState } from "react";

interface ConfirmationButtonsProps {
  onYes: () => void;
  onNo: () => void;
  loading?: boolean;
  disabled?: boolean;
}

const baseButtonStyle: React.CSSProperties = {
  border: "2px solid #1976d2",
  background: "none",
  color: "#1976d2",
  borderRadius: "999px",
  padding: "5px",
  fontSize: 18,
  fontWeight: 600,
  cursor: "pointer",
  margin: "5px",
  outline: "none",
  transition: "background 0.2s, color 0.2s",
  minWidth: 70,
  minHeight: 10,
};

const hoverStyle: React.CSSProperties = {
  background: "#e3f2fd",
};

const activeStyle: React.CSSProperties = {
  background: "#1976d2",
  color: "#fff",
};

export const ConfirmationButtons: React.FC<ConfirmationButtonsProps> = ({
  onYes,
  onNo,
  loading = false,
  disabled
}) => {
  const [hovered, setHovered] = useState<"yes" | "no" | null>(null);
  const [clickedButton, setClickedButton] = useState<"yes" | "no" | null>(null);

  disabled = loading || clickedButton !== null;

  const handleClick = (type: "yes" | "no", callback: () => void) => {
    setClickedButton(type);
    callback();
  };

  return (
    <div style={{ display: "flex", justifyContent: "left", marginTop: 24 }}>
      <button
        type="button"
        style={{
          ...baseButtonStyle,
          ...(hovered === "yes" ? hoverStyle : {}),
          ...(clickedButton === "yes" ? activeStyle : {}),
          ...(disabled ? { cursor: "not-allowed", opacity: 0.6 } : {}),
        }}
        disabled={disabled}
        onMouseEnter={() => setHovered("yes")}
        onMouseLeave={() => setHovered(null)}
        onClick={() => handleClick("yes", onYes)}
      >
        Yes
      </button>
      <button
        type="button"
        style={{
          ...baseButtonStyle,
          ...(hovered === "no" ? hoverStyle : {}),
          ...(clickedButton === "no" ? activeStyle : {}),
          ...(disabled ? { cursor: "not-allowed", opacity: 0.6 } : {}),
        }}
        disabled={disabled}
        onMouseEnter={() => setHovered("no")}
        onMouseLeave={() => setHovered(null)}
        onClick={() => handleClick("no", onNo)}
      >
        No
      </button>
    </div>
  );
};

export default ConfirmationButtons;

 