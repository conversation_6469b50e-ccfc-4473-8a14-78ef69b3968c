"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gtoken";
exports.ids = ["vendor-chunks/gtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/gtoken/build/src/index.js":
/*!************************************************!*\
  !*** ./node_modules/gtoken/build/src/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GoogleToken = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst readFile = fs.readFile\n    ? (0, util_1.promisify)(fs.readFile)\n    : async () => {\n        // if running in the web-browser, fs.readFile may not have been shimmed.\n        throw new ErrorWithCode('use key rather than keyFile.', 'MISSING_CREDENTIALS');\n    };\nconst GOOGLE_TOKEN_URL = 'https://www.googleapis.com/oauth2/v4/token';\nconst GOOGLE_REVOKE_TOKEN_URL = 'https://accounts.google.com/o/oauth2/revoke?token=';\nclass ErrorWithCode extends Error {\n    constructor(message, code) {\n        super(message);\n        this.code = code;\n    }\n}\nlet getPem;\nclass GoogleToken {\n    /**\n     * Create a GoogleToken.\n     *\n     * @param options  Configuration object.\n     */\n    constructor(options) {\n        this.transporter = {\n            request: opts => (0, gaxios_1.request)(opts),\n        };\n        this.configure(options);\n    }\n    get accessToken() {\n        return this.rawToken ? this.rawToken.access_token : undefined;\n    }\n    get idToken() {\n        return this.rawToken ? this.rawToken.id_token : undefined;\n    }\n    get tokenType() {\n        return this.rawToken ? this.rawToken.token_type : undefined;\n    }\n    get refreshToken() {\n        return this.rawToken ? this.rawToken.refresh_token : undefined;\n    }\n    /**\n     * Returns whether the token has expired.\n     *\n     * @return true if the token has expired, false otherwise.\n     */\n    hasExpired() {\n        const now = new Date().getTime();\n        if (this.rawToken && this.expiresAt) {\n            return now >= this.expiresAt;\n        }\n        else {\n            return true;\n        }\n    }\n    /**\n     * Returns whether the token will expire within eagerRefreshThresholdMillis\n     *\n     * @return true if the token will be expired within eagerRefreshThresholdMillis, false otherwise.\n     */\n    isTokenExpiring() {\n        var _a;\n        const now = new Date().getTime();\n        const eagerRefreshThresholdMillis = (_a = this.eagerRefreshThresholdMillis) !== null && _a !== void 0 ? _a : 0;\n        if (this.rawToken && this.expiresAt) {\n            return this.expiresAt <= now + eagerRefreshThresholdMillis;\n        }\n        else {\n            return true;\n        }\n    }\n    getToken(callback, opts = {}) {\n        if (typeof callback === 'object') {\n            opts = callback;\n            callback = undefined;\n        }\n        opts = Object.assign({\n            forceRefresh: false,\n        }, opts);\n        if (callback) {\n            const cb = callback;\n            this.getTokenAsync(opts).then(t => cb(null, t), callback);\n            return;\n        }\n        return this.getTokenAsync(opts);\n    }\n    /**\n     * Given a keyFile, extract the key and client email if available\n     * @param keyFile Path to a json, pem, or p12 file that contains the key.\n     * @returns an object with privateKey and clientEmail properties\n     */\n    async getCredentials(keyFile) {\n        const ext = path.extname(keyFile);\n        switch (ext) {\n            case '.json': {\n                const key = await readFile(keyFile, 'utf8');\n                const body = JSON.parse(key);\n                const privateKey = body.private_key;\n                const clientEmail = body.client_email;\n                if (!privateKey || !clientEmail) {\n                    throw new ErrorWithCode('private_key and client_email are required.', 'MISSING_CREDENTIALS');\n                }\n                return { privateKey, clientEmail };\n            }\n            case '.der':\n            case '.crt':\n            case '.pem': {\n                const privateKey = await readFile(keyFile, 'utf8');\n                return { privateKey };\n            }\n            case '.p12':\n            case '.pfx': {\n                // NOTE:  The loading of `google-p12-pem` is deferred for performance\n                // reasons.  The `node-forge` npm module in `google-p12-pem` adds a fair\n                // bit time to overall module loading, and is likely not frequently\n                // used.  In a future release, p12 support will be entirely removed.\n                if (!getPem) {\n                    getPem = (await Promise.resolve().then(() => __webpack_require__(/*! google-p12-pem */ \"(rsc)/./node_modules/google-p12-pem/build/src/index.js\"))).getPem;\n                }\n                const privateKey = await getPem(keyFile);\n                return { privateKey };\n            }\n            default:\n                throw new ErrorWithCode('Unknown certificate type. Type is determined based on file extension. ' +\n                    'Current supported extensions are *.json, *.pem, and *.p12.', 'UNKNOWN_CERTIFICATE_TYPE');\n        }\n    }\n    async getTokenAsync(opts) {\n        if (this.inFlightRequest && !opts.forceRefresh) {\n            return this.inFlightRequest;\n        }\n        try {\n            return await (this.inFlightRequest = this.getTokenAsyncInner(opts));\n        }\n        finally {\n            this.inFlightRequest = undefined;\n        }\n    }\n    async getTokenAsyncInner(opts) {\n        if (this.isTokenExpiring() === false && opts.forceRefresh === false) {\n            return Promise.resolve(this.rawToken);\n        }\n        if (!this.key && !this.keyFile) {\n            throw new Error('No key or keyFile set.');\n        }\n        if (!this.key && this.keyFile) {\n            const creds = await this.getCredentials(this.keyFile);\n            this.key = creds.privateKey;\n            this.iss = creds.clientEmail || this.iss;\n            if (!creds.clientEmail) {\n                this.ensureEmail();\n            }\n        }\n        return this.requestToken();\n    }\n    ensureEmail() {\n        if (!this.iss) {\n            throw new ErrorWithCode('email is required.', 'MISSING_CREDENTIALS');\n        }\n    }\n    revokeToken(callback) {\n        if (callback) {\n            this.revokeTokenAsync().then(() => callback(), callback);\n            return;\n        }\n        return this.revokeTokenAsync();\n    }\n    async revokeTokenAsync() {\n        if (!this.accessToken) {\n            throw new Error('No token to revoke.');\n        }\n        const url = GOOGLE_REVOKE_TOKEN_URL + this.accessToken;\n        await this.transporter.request({ url });\n        this.configure({\n            email: this.iss,\n            sub: this.sub,\n            key: this.key,\n            keyFile: this.keyFile,\n            scope: this.scope,\n            additionalClaims: this.additionalClaims,\n        });\n    }\n    /**\n     * Configure the GoogleToken for re-use.\n     * @param  {object} options Configuration object.\n     */\n    configure(options = {}) {\n        this.keyFile = options.keyFile;\n        this.key = options.key;\n        this.rawToken = undefined;\n        this.iss = options.email || options.iss;\n        this.sub = options.sub;\n        this.additionalClaims = options.additionalClaims;\n        if (typeof options.scope === 'object') {\n            this.scope = options.scope.join(' ');\n        }\n        else {\n            this.scope = options.scope;\n        }\n        this.eagerRefreshThresholdMillis = options.eagerRefreshThresholdMillis;\n        if (options.transporter) {\n            this.transporter = options.transporter;\n        }\n    }\n    /**\n     * Request the token from Google.\n     */\n    async requestToken() {\n        var _a, _b;\n        const iat = Math.floor(new Date().getTime() / 1000);\n        const additionalClaims = this.additionalClaims || {};\n        const payload = Object.assign({\n            iss: this.iss,\n            scope: this.scope,\n            aud: GOOGLE_TOKEN_URL,\n            exp: iat + 3600,\n            iat,\n            sub: this.sub,\n        }, additionalClaims);\n        const signedJWT = jws.sign({\n            header: { alg: 'RS256' },\n            payload,\n            secret: this.key,\n        });\n        try {\n            const r = await this.transporter.request({\n                method: 'POST',\n                url: GOOGLE_TOKEN_URL,\n                data: {\n                    grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',\n                    assertion: signedJWT,\n                },\n                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n                responseType: 'json',\n            });\n            this.rawToken = r.data;\n            this.expiresAt =\n                r.data.expires_in === null || r.data.expires_in === undefined\n                    ? undefined\n                    : (iat + r.data.expires_in) * 1000;\n            return this.rawToken;\n        }\n        catch (e) {\n            this.rawToken = undefined;\n            this.tokenExpires = undefined;\n            const body = e.response && ((_a = e.response) === null || _a === void 0 ? void 0 : _a.data)\n                ? (_b = e.response) === null || _b === void 0 ? void 0 : _b.data\n                : {};\n            if (body.error) {\n                const desc = body.error_description\n                    ? `: ${body.error_description}`\n                    : '';\n                e.message = `${body.error}${desc}`;\n            }\n            throw e;\n        }\n    }\n}\nexports.GoogleToken = GoogleToken;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gtoken/build/src/index.js\n");

/***/ })

};
;