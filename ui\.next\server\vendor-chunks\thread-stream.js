"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/thread-stream";
exports.ids = ["vendor-chunks/thread-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/thread-stream/index.js":
/*!*********************************************!*\
  !*** ./node_modules/thread-stream/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { version } = __webpack_require__(/*! ./package.json */ \"(rsc)/./node_modules/thread-stream/package.json\")\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst { Worker } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst { join } = __webpack_require__(/*! path */ \"path\")\nconst { pathToFileURL } = __webpack_require__(/*! url */ \"url\")\nconst { wait } = __webpack_require__(/*! ./lib/wait */ \"(rsc)/./node_modules/thread-stream/lib/wait.js\")\nconst {\n  WRITE_INDEX,\n  READ_INDEX\n} = __webpack_require__(/*! ./lib/indexes */ \"(rsc)/./node_modules/thread-stream/lib/indexes.js\")\nconst buffer = __webpack_require__(/*! buffer */ \"buffer\")\nconst assert = __webpack_require__(/*! assert */ \"assert\")\n\nconst kImpl = Symbol('kImpl')\n\n// V8 limit for string size\nconst MAX_STRING = buffer.constants.MAX_STRING_LENGTH\n\nclass FakeWeakRef {\n  constructor (value) {\n    this._value = value\n  }\n\n  deref () {\n    return this._value\n  }\n}\n\nclass FakeFinalizationRegistry {\n  register () {}\n\n  unregister () {}\n}\n\n// Currently using FinalizationRegistry with code coverage breaks the world\n// Ref: https://github.com/nodejs/node/issues/49344\nconst FinalizationRegistry = process.env.NODE_V8_COVERAGE ? FakeFinalizationRegistry : global.FinalizationRegistry || FakeFinalizationRegistry\nconst WeakRef = process.env.NODE_V8_COVERAGE ? FakeWeakRef : global.WeakRef || FakeWeakRef\n\nconst registry = new FinalizationRegistry((worker) => {\n  if (worker.exited) {\n    return\n  }\n  worker.terminate()\n})\n\nfunction createWorker (stream, opts) {\n  const { filename, workerData } = opts\n\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n  const toExecute = bundlerOverrides['thread-stream-worker'] || join(__dirname, 'lib', 'worker.js')\n\n  const worker = new Worker(toExecute, {\n    ...opts.workerOpts,\n    trackUnmanagedFds: false,\n    workerData: {\n      filename: filename.indexOf('file://') === 0\n        ? filename\n        : pathToFileURL(filename).href,\n      dataBuf: stream[kImpl].dataBuf,\n      stateBuf: stream[kImpl].stateBuf,\n      workerData: {\n        $context: {\n          threadStreamVersion: version\n        },\n        ...workerData\n      }\n    }\n  })\n\n  // We keep a strong reference for now,\n  // we need to start writing first\n  worker.stream = new FakeWeakRef(stream)\n\n  worker.on('message', onWorkerMessage)\n  worker.on('exit', onWorkerExit)\n  registry.register(stream, worker)\n\n  return worker\n}\n\nfunction drain (stream) {\n  assert(!stream[kImpl].sync)\n  if (stream[kImpl].needDrain) {\n    stream[kImpl].needDrain = false\n    stream.emit('drain')\n  }\n}\n\nfunction nextFlush (stream) {\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  let leftover = stream[kImpl].data.length - writeIndex\n\n  if (leftover > 0) {\n    if (stream[kImpl].buf.length === 0) {\n      stream[kImpl].flushing = false\n\n      if (stream[kImpl].ending) {\n        end(stream)\n      } else if (stream[kImpl].needDrain) {\n        process.nextTick(drain, stream)\n      }\n\n      return\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, nextFlush.bind(null, stream))\n    } else {\n      // multi-byte utf-8\n      stream.flush(() => {\n        // err is already handled in flush()\n        if (stream.destroyed) {\n          return\n        }\n\n        Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n        Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n        // Find a toWrite length that fits the buffer\n        // it must exists as the buffer is at least 4 bytes length\n        // and the max utf-8 length for a char is 4 bytes.\n        while (toWriteBytes > stream[kImpl].data.length) {\n          leftover = leftover / 2\n          toWrite = stream[kImpl].buf.slice(0, leftover)\n          toWriteBytes = Buffer.byteLength(toWrite)\n        }\n        stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n        write(stream, toWrite, nextFlush.bind(null, stream))\n      })\n    }\n  } else if (leftover === 0) {\n    if (writeIndex === 0 && stream[kImpl].buf.length === 0) {\n      // we had a flushSync in the meanwhile\n      return\n    }\n    stream.flush(() => {\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      nextFlush(stream)\n    })\n  } else {\n    // This should never happen\n    destroy(stream, new Error('overwritten'))\n  }\n}\n\nfunction onWorkerMessage (msg) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    this.exited = true\n    // Terminate the worker.\n    this.terminate()\n    return\n  }\n\n  switch (msg.code) {\n    case 'READY':\n      // Replace the FakeWeakRef with a\n      // proper one.\n      this.stream = new WeakRef(stream)\n\n      stream.flush(() => {\n        stream[kImpl].ready = true\n        stream.emit('ready')\n      })\n      break\n    case 'ERROR':\n      destroy(stream, msg.err)\n      break\n    case 'EVENT':\n      if (Array.isArray(msg.args)) {\n        stream.emit(msg.name, ...msg.args)\n      } else {\n        stream.emit(msg.name, msg.args)\n      }\n      break\n    case 'WARNING':\n      process.emitWarning(msg.err)\n      break\n    default:\n      destroy(stream, new Error('this should not happen: ' + msg.code))\n  }\n}\n\nfunction onWorkerExit (code) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    // Nothing to do, the worker already exit\n    return\n  }\n  registry.unregister(stream)\n  stream.worker.exited = true\n  stream.worker.off('exit', onWorkerExit)\n  destroy(stream, code !== 0 ? new Error('the worker thread exited') : null)\n}\n\nclass ThreadStream extends EventEmitter {\n  constructor (opts = {}) {\n    super()\n\n    if (opts.bufferSize < 4) {\n      throw new Error('bufferSize must at least fit a 4-byte utf-8 char')\n    }\n\n    this[kImpl] = {}\n    this[kImpl].stateBuf = new SharedArrayBuffer(128)\n    this[kImpl].state = new Int32Array(this[kImpl].stateBuf)\n    this[kImpl].dataBuf = new SharedArrayBuffer(opts.bufferSize || 4 * 1024 * 1024)\n    this[kImpl].data = Buffer.from(this[kImpl].dataBuf)\n    this[kImpl].sync = opts.sync || false\n    this[kImpl].ending = false\n    this[kImpl].ended = false\n    this[kImpl].needDrain = false\n    this[kImpl].destroyed = false\n    this[kImpl].flushing = false\n    this[kImpl].ready = false\n    this[kImpl].finished = false\n    this[kImpl].errored = null\n    this[kImpl].closed = false\n    this[kImpl].buf = ''\n\n    // TODO (fix): Make private?\n    this.worker = createWorker(this, opts) // TODO (fix): make private\n    this.on('message', (message, transferList) => {\n      this.worker.postMessage(message, transferList)\n    })\n  }\n\n  write (data) {\n    if (this[kImpl].destroyed) {\n      error(this, new Error('the worker has exited'))\n      return false\n    }\n\n    if (this[kImpl].ending) {\n      error(this, new Error('the worker is ending'))\n      return false\n    }\n\n    if (this[kImpl].flushing && this[kImpl].buf.length + data.length >= MAX_STRING) {\n      try {\n        writeSync(this)\n        this[kImpl].flushing = true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    this[kImpl].buf += data\n\n    if (this[kImpl].sync) {\n      try {\n        writeSync(this)\n        return true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    if (!this[kImpl].flushing) {\n      this[kImpl].flushing = true\n      setImmediate(nextFlush, this)\n    }\n\n    this[kImpl].needDrain = this[kImpl].data.length - this[kImpl].buf.length - Atomics.load(this[kImpl].state, WRITE_INDEX) <= 0\n    return !this[kImpl].needDrain\n  }\n\n  end () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    this[kImpl].ending = true\n    end(this)\n  }\n\n  flush (cb) {\n    if (this[kImpl].destroyed) {\n      if (typeof cb === 'function') {\n        process.nextTick(cb, new Error('the worker has exited'))\n      }\n      return\n    }\n\n    // TODO write all .buf\n    const writeIndex = Atomics.load(this[kImpl].state, WRITE_INDEX)\n    // process._rawDebug(`(flush) readIndex (${Atomics.load(this.state, READ_INDEX)}) writeIndex (${Atomics.load(this.state, WRITE_INDEX)})`)\n    wait(this[kImpl].state, READ_INDEX, writeIndex, Infinity, (err, res) => {\n      if (err) {\n        destroy(this, err)\n        process.nextTick(cb, err)\n        return\n      }\n      if (res === 'not-equal') {\n        // TODO handle deadlock\n        this.flush(cb)\n        return\n      }\n      process.nextTick(cb)\n    })\n  }\n\n  flushSync () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    writeSync(this)\n    flushSync(this)\n  }\n\n  unref () {\n    this.worker.unref()\n  }\n\n  ref () {\n    this.worker.ref()\n  }\n\n  get ready () {\n    return this[kImpl].ready\n  }\n\n  get destroyed () {\n    return this[kImpl].destroyed\n  }\n\n  get closed () {\n    return this[kImpl].closed\n  }\n\n  get writable () {\n    return !this[kImpl].destroyed && !this[kImpl].ending\n  }\n\n  get writableEnded () {\n    return this[kImpl].ending\n  }\n\n  get writableFinished () {\n    return this[kImpl].finished\n  }\n\n  get writableNeedDrain () {\n    return this[kImpl].needDrain\n  }\n\n  get writableObjectMode () {\n    return false\n  }\n\n  get writableErrored () {\n    return this[kImpl].errored\n  }\n}\n\nfunction error (stream, err) {\n  setImmediate(() => {\n    stream.emit('error', err)\n  })\n}\n\nfunction destroy (stream, err) {\n  if (stream[kImpl].destroyed) {\n    return\n  }\n  stream[kImpl].destroyed = true\n\n  if (err) {\n    stream[kImpl].errored = err\n    error(stream, err)\n  }\n\n  if (!stream.worker.exited) {\n    stream.worker.terminate()\n      .catch(() => {})\n      .then(() => {\n        stream[kImpl].closed = true\n        stream.emit('close')\n      })\n  } else {\n    setImmediate(() => {\n      stream[kImpl].closed = true\n      stream.emit('close')\n    })\n  }\n}\n\nfunction write (stream, data, cb) {\n  // data is smaller than the shared buffer length\n  const current = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  const length = Buffer.byteLength(data)\n  stream[kImpl].data.write(data, current)\n  Atomics.store(stream[kImpl].state, WRITE_INDEX, current + length)\n  Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n  cb()\n  return true\n}\n\nfunction end (stream) {\n  if (stream[kImpl].ended || !stream[kImpl].ending || stream[kImpl].flushing) {\n    return\n  }\n  stream[kImpl].ended = true\n\n  try {\n    stream.flushSync()\n\n    let readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    // process._rawDebug('writing index')\n    Atomics.store(stream[kImpl].state, WRITE_INDEX, -1)\n    // process._rawDebug(`(end) readIndex (${Atomics.load(stream.state, READ_INDEX)}) writeIndex (${Atomics.load(stream.state, WRITE_INDEX)})`)\n    Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n\n    // Wait for the process to complete\n    let spins = 0\n    while (readIndex !== -1) {\n      // process._rawDebug(`read = ${read}`)\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n      readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n      if (readIndex === -2) {\n        destroy(stream, new Error('end() failed'))\n        return\n      }\n\n      if (++spins === 10) {\n        destroy(stream, new Error('end() took too long (10s)'))\n        return\n      }\n    }\n\n    process.nextTick(() => {\n      stream[kImpl].finished = true\n      stream.emit('finish')\n    })\n  } catch (err) {\n    destroy(stream, err)\n  }\n  // process._rawDebug('end finished...')\n}\n\nfunction writeSync (stream) {\n  const cb = () => {\n    if (stream[kImpl].ending) {\n      end(stream)\n    } else if (stream[kImpl].needDrain) {\n      process.nextTick(drain, stream)\n    }\n  }\n  stream[kImpl].flushing = false\n\n  while (stream[kImpl].buf.length !== 0) {\n    const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n    let leftover = stream[kImpl].data.length - writeIndex\n    if (leftover === 0) {\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      continue\n    } else if (leftover < 0) {\n      // stream should never happen\n      throw new Error('overwritten')\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, cb)\n    } else {\n      // multi-byte utf-8\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n      // Find a toWrite length that fits the buffer\n      // it must exists as the buffer is at least 4 bytes length\n      // and the max utf-8 length for a char is 4 bytes.\n      while (toWriteBytes > stream[kImpl].buf.length) {\n        leftover = leftover / 2\n        toWrite = stream[kImpl].buf.slice(0, leftover)\n        toWriteBytes = Buffer.byteLength(toWrite)\n      }\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      write(stream, toWrite, cb)\n    }\n  }\n}\n\nfunction flushSync (stream) {\n  if (stream[kImpl].flushing) {\n    throw new Error('unable to flush while flushing')\n  }\n\n  // process._rawDebug('flushSync started')\n\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n\n  let spins = 0\n\n  // TODO handle deadlock\n  while (true) {\n    const readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    if (readIndex === -2) {\n      throw Error('_flushSync failed')\n    }\n\n    // process._rawDebug(`(flushSync) readIndex (${readIndex}) writeIndex (${writeIndex})`)\n    if (readIndex !== writeIndex) {\n      // TODO stream timeouts for some reason.\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n    } else {\n      break\n    }\n\n    if (++spins === 10) {\n      throw new Error('_flushSync took too long (10s)')\n    }\n  }\n  // process._rawDebug('flushSync finished')\n}\n\nmodule.exports = ThreadStream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/thread-stream/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/thread-stream/lib/indexes.js":
/*!***************************************************!*\
  !*** ./node_modules/thread-stream/lib/indexes.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nconst WRITE_INDEX = 4\nconst READ_INDEX = 8\n\nmodule.exports = {\n  WRITE_INDEX,\n  READ_INDEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdGhyZWFkLXN0cmVhbS9saWIvaW5kZXhlcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXHRocmVhZC1zdHJlYW1cXGxpYlxcaW5kZXhlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgV1JJVEVfSU5ERVggPSA0XG5jb25zdCBSRUFEX0lOREVYID0gOFxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgV1JJVEVfSU5ERVgsXG4gIFJFQURfSU5ERVhcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/thread-stream/lib/indexes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/thread-stream/lib/wait.js":
/*!************************************************!*\
  !*** ./node_modules/thread-stream/lib/wait.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n\nconst MAX_TIMEOUT = 1000\n\nfunction wait (state, index, expected, timeout, done) {\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current === expected) {\n    done(null, 'ok')\n    return\n  }\n  let prior = current\n  const check = (backoff) => {\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        prior = current\n        current = Atomics.load(state, index)\n        if (current === prior) {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        } else {\n          if (current === expected) done(null, 'ok')\n          else done(null, 'not-equal')\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\n// let waitDiffCount = 0\nfunction waitDiff (state, index, expected, timeout, done) {\n  // const id = waitDiffCount++\n  // process._rawDebug(`>>> waitDiff ${id}`)\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current !== expected) {\n    done(null, 'ok')\n    return\n  }\n  const check = (backoff) => {\n    // process._rawDebug(`${id} ${index} current ${current} expected ${expected}`)\n    // process._rawDebug('' + backoff)\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        current = Atomics.load(state, index)\n        if (current !== expected) {\n          done(null, 'ok')\n        } else {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\nmodule.exports = { wait, waitDiff }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/thread-stream/lib/wait.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/thread-stream/package.json":
/*!*************************************************!*\
  !*** ./node_modules/thread-stream/package.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"thread-stream","version":"3.1.0","description":"A streaming way to send data to a Node.js Worker Thread","main":"index.js","types":"index.d.ts","dependencies":{"real-require":"^0.2.0"},"devDependencies":{"@types/node":"^20.1.0","@types/tap":"^15.0.0","@yao-pkg/pkg":"^5.11.5","desm":"^1.3.0","fastbench":"^1.0.1","husky":"^9.0.6","pino-elasticsearch":"^8.0.0","sonic-boom":"^4.0.1","standard":"^17.0.0","tap":"^16.2.0","ts-node":"^10.8.0","typescript":"^5.3.2","why-is-node-running":"^2.2.2"},"scripts":{"build":"tsc --noEmit","test":"standard && npm run build && npm run transpile && tap \\"test/**/*.test.*js\\" && tap --ts test/*.test.*ts","test:ci":"standard && npm run transpile && npm run test:ci:js && npm run test:ci:ts","test:ci:js":"tap --no-check-coverage --timeout=120 --coverage-report=lcovonly \\"test/**/*.test.*js\\"","test:ci:ts":"tap --ts --no-check-coverage --coverage-report=lcovonly \\"test/**/*.test.*ts\\"","test:yarn":"npm run transpile && tap \\"test/**/*.test.js\\" --no-check-coverage","transpile":"sh ./test/ts/transpile.sh","prepare":"husky install"},"standard":{"ignore":["test/ts/**/*","test/syntax-error.mjs"]},"repository":{"type":"git","url":"git+https://github.com/mcollina/thread-stream.git"},"keywords":["worker","thread","threads","stream"],"author":"Matteo Collina <<EMAIL>>","license":"MIT","bugs":{"url":"https://github.com/mcollina/thread-stream/issues"},"homepage":"https://github.com/mcollina/thread-stream#readme"}');

/***/ })

};
;