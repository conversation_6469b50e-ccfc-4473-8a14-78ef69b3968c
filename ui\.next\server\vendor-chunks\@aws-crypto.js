"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-crypto";
exports.ids = ["vendor-chunks/@aws-crypto"];
exports.modules = {

/***/ "(rsc)/./node_modules/@aws-crypto/util/build/module/convertToBuffer.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/convertToBuffer.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertToBuffer: () => (/* binding */ convertToBuffer)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/@smithy/util-utf8/dist-es/index.js\");\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\n// Quick polyfill\nvar fromUtf8 = typeof Buffer !== \"undefined\" && Buffer.from\n    ? function (input) { return Buffer.from(input, \"utf8\"); }\n    : _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_0__.fromUtf8;\nfunction convertToBuffer(data) {\n    // Already a Uint8, do nothing\n    if (data instanceof Uint8Array)\n        return data;\n    if (typeof data === \"string\") {\n        return fromUtf8(data);\n    }\n    if (ArrayBuffer.isView(data)) {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n    }\n    return new Uint8Array(data);\n}\n//# sourceMappingURL=convertToBuffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvY29udmVydFRvQnVmZmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNnRTtBQUNoRTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLE1BQU0sdURBQWU7QUFDZDtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYXdzLWNyeXB0b1xcdXRpbFxcYnVpbGRcXG1vZHVsZVxcY29udmVydFRvQnVmZmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCBBbWF6b24uY29tIEluYy4gb3IgaXRzIGFmZmlsaWF0ZXMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4vLyBTUERYLUxpY2Vuc2UtSWRlbnRpZmllcjogQXBhY2hlLTIuMFxuaW1wb3J0IHsgZnJvbVV0ZjggYXMgZnJvbVV0ZjhCcm93c2VyIH0gZnJvbSBcIkBzbWl0aHkvdXRpbC11dGY4XCI7XG4vLyBRdWljayBwb2x5ZmlsbFxudmFyIGZyb21VdGY4ID0gdHlwZW9mIEJ1ZmZlciAhPT0gXCJ1bmRlZmluZWRcIiAmJiBCdWZmZXIuZnJvbVxuICAgID8gZnVuY3Rpb24gKGlucHV0KSB7IHJldHVybiBCdWZmZXIuZnJvbShpbnB1dCwgXCJ1dGY4XCIpOyB9XG4gICAgOiBmcm9tVXRmOEJyb3dzZXI7XG5leHBvcnQgZnVuY3Rpb24gY29udmVydFRvQnVmZmVyKGRhdGEpIHtcbiAgICAvLyBBbHJlYWR5IGEgVWludDgsIGRvIG5vdGhpbmdcbiAgICBpZiAoZGF0YSBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpXG4gICAgICAgIHJldHVybiBkYXRhO1xuICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICByZXR1cm4gZnJvbVV0ZjgoZGF0YSk7XG4gICAgfVxuICAgIGlmIChBcnJheUJ1ZmZlci5pc1ZpZXcoZGF0YSkpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGRhdGEuYnVmZmVyLCBkYXRhLmJ5dGVPZmZzZXQsIGRhdGEuYnl0ZUxlbmd0aCAvIFVpbnQ4QXJyYXkuQllURVNfUEVSX0VMRU1FTlQpO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoZGF0YSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb252ZXJ0VG9CdWZmZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@aws-crypto/util/build/module/convertToBuffer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@aws-crypto/util/build/module/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertToBuffer: () => (/* reexport safe */ _convertToBuffer__WEBPACK_IMPORTED_MODULE_0__.convertToBuffer),\n/* harmony export */   isEmptyData: () => (/* reexport safe */ _isEmptyData__WEBPACK_IMPORTED_MODULE_1__.isEmptyData),\n/* harmony export */   numToUint8: () => (/* reexport safe */ _numToUint8__WEBPACK_IMPORTED_MODULE_2__.numToUint8),\n/* harmony export */   uint32ArrayFrom: () => (/* reexport safe */ _uint32ArrayFrom__WEBPACK_IMPORTED_MODULE_3__.uint32ArrayFrom)\n/* harmony export */ });\n/* harmony import */ var _convertToBuffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./convertToBuffer */ \"(rsc)/./node_modules/@aws-crypto/util/build/module/convertToBuffer.js\");\n/* harmony import */ var _isEmptyData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isEmptyData */ \"(rsc)/./node_modules/@aws-crypto/util/build/module/isEmptyData.js\");\n/* harmony import */ var _numToUint8__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./numToUint8 */ \"(rsc)/./node_modules/@aws-crypto/util/build/module/numToUint8.js\");\n/* harmony import */ var _uint32ArrayFrom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./uint32ArrayFrom */ \"(rsc)/./node_modules/@aws-crypto/util/build/module/uint32ArrayFrom.js\");\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ29EO0FBQ1I7QUFDRjtBQUNVO0FBQ3BEIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBhd3MtY3J5cHRvXFx1dGlsXFxidWlsZFxcbW9kdWxlXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgQW1hem9uLmNvbSBJbmMuIG9yIGl0cyBhZmZpbGlhdGVzLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuLy8gU1BEWC1MaWNlbnNlLUlkZW50aWZpZXI6IEFwYWNoZS0yLjBcbmV4cG9ydCB7IGNvbnZlcnRUb0J1ZmZlciB9IGZyb20gXCIuL2NvbnZlcnRUb0J1ZmZlclwiO1xuZXhwb3J0IHsgaXNFbXB0eURhdGEgfSBmcm9tIFwiLi9pc0VtcHR5RGF0YVwiO1xuZXhwb3J0IHsgbnVtVG9VaW50OCB9IGZyb20gXCIuL251bVRvVWludDhcIjtcbmV4cG9ydCB7IHVpbnQzMkFycmF5RnJvbSB9IGZyb20gJy4vdWludDMyQXJyYXlGcm9tJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@aws-crypto/util/build/module/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@aws-crypto/util/build/module/isEmptyData.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/isEmptyData.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isEmptyData: () => (/* binding */ isEmptyData)\n/* harmony export */ });\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction isEmptyData(data) {\n    if (typeof data === \"string\") {\n        return data.length === 0;\n    }\n    return data.byteLength === 0;\n}\n//# sourceMappingURL=isEmptyData.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvaXNFbXB0eURhdGEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYXdzLWNyeXB0b1xcdXRpbFxcYnVpbGRcXG1vZHVsZVxcaXNFbXB0eURhdGEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IEFtYXpvbi5jb20gSW5jLiBvciBpdHMgYWZmaWxpYXRlcy4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbi8vIFNQRFgtTGljZW5zZS1JZGVudGlmaWVyOiBBcGFjaGUtMi4wXG5leHBvcnQgZnVuY3Rpb24gaXNFbXB0eURhdGEoZGF0YSkge1xuICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICByZXR1cm4gZGF0YS5sZW5ndGggPT09IDA7XG4gICAgfVxuICAgIHJldHVybiBkYXRhLmJ5dGVMZW5ndGggPT09IDA7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pc0VtcHR5RGF0YS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@aws-crypto/util/build/module/isEmptyData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@aws-crypto/util/build/module/numToUint8.js":
/*!******************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/numToUint8.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   numToUint8: () => (/* binding */ numToUint8)\n/* harmony export */ });\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction numToUint8(num) {\n    return new Uint8Array([\n        (num & 0xff000000) >> 24,\n        (num & 0x00ff0000) >> 16,\n        (num & 0x0000ff00) >> 8,\n        num & 0x000000ff,\n    ]);\n}\n//# sourceMappingURL=numToUint8.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvbnVtVG9VaW50OC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYXdzLWNyeXB0b1xcdXRpbFxcYnVpbGRcXG1vZHVsZVxcbnVtVG9VaW50OC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgQW1hem9uLmNvbSBJbmMuIG9yIGl0cyBhZmZpbGlhdGVzLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuLy8gU1BEWC1MaWNlbnNlLUlkZW50aWZpZXI6IEFwYWNoZS0yLjBcbmV4cG9ydCBmdW5jdGlvbiBudW1Ub1VpbnQ4KG51bSkge1xuICAgIHJldHVybiBuZXcgVWludDhBcnJheShbXG4gICAgICAgIChudW0gJiAweGZmMDAwMDAwKSA+PiAyNCxcbiAgICAgICAgKG51bSAmIDB4MDBmZjAwMDApID4+IDE2LFxuICAgICAgICAobnVtICYgMHgwMDAwZmYwMCkgPj4gOCxcbiAgICAgICAgbnVtICYgMHgwMDAwMDBmZixcbiAgICBdKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW51bVRvVWludDguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@aws-crypto/util/build/module/numToUint8.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@aws-crypto/util/build/module/uint32ArrayFrom.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/uint32ArrayFrom.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uint32ArrayFrom: () => (/* binding */ uint32ArrayFrom)\n/* harmony export */ });\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// IE 11 does not support Array.from, so we do it manually\nfunction uint32ArrayFrom(a_lookUpTable) {\n    if (!Uint32Array.from) {\n        var return_array = new Uint32Array(a_lookUpTable.length);\n        var a_index = 0;\n        while (a_index < a_lookUpTable.length) {\n            return_array[a_index] = a_lookUpTable[a_index];\n            a_index += 1;\n        }\n        return return_array;\n    }\n    return Uint32Array.from(a_lookUpTable);\n}\n//# sourceMappingURL=uint32ArrayFrom.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvdWludDMyQXJyYXlGcm9tLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYXdzLWNyeXB0b1xcdXRpbFxcYnVpbGRcXG1vZHVsZVxcdWludDMyQXJyYXlGcm9tLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCBBbWF6b24uY29tIEluYy4gb3IgaXRzIGFmZmlsaWF0ZXMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4vLyBTUERYLUxpY2Vuc2UtSWRlbnRpZmllcjogQXBhY2hlLTIuMFxuLy8gSUUgMTEgZG9lcyBub3Qgc3VwcG9ydCBBcnJheS5mcm9tLCBzbyB3ZSBkbyBpdCBtYW51YWxseVxuZXhwb3J0IGZ1bmN0aW9uIHVpbnQzMkFycmF5RnJvbShhX2xvb2tVcFRhYmxlKSB7XG4gICAgaWYgKCFVaW50MzJBcnJheS5mcm9tKSB7XG4gICAgICAgIHZhciByZXR1cm5fYXJyYXkgPSBuZXcgVWludDMyQXJyYXkoYV9sb29rVXBUYWJsZS5sZW5ndGgpO1xuICAgICAgICB2YXIgYV9pbmRleCA9IDA7XG4gICAgICAgIHdoaWxlIChhX2luZGV4IDwgYV9sb29rVXBUYWJsZS5sZW5ndGgpIHtcbiAgICAgICAgICAgIHJldHVybl9hcnJheVthX2luZGV4XSA9IGFfbG9va1VwVGFibGVbYV9pbmRleF07XG4gICAgICAgICAgICBhX2luZGV4ICs9IDE7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJldHVybl9hcnJheTtcbiAgICB9XG4gICAgcmV0dXJuIFVpbnQzMkFycmF5LmZyb20oYV9sb29rVXBUYWJsZSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11aW50MzJBcnJheUZyb20uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@aws-crypto/util/build/module/uint32ArrayFrom.js\n");

/***/ })

};
;