"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/google-p12-pem";
exports.ids = ["vendor-chunks/google-p12-pem"];
exports.modules = {

/***/ "(rsc)/./node_modules/google-p12-pem/build/src/index.js":
/*!********************************************************!*\
  !*** ./node_modules/google-p12-pem/build/src/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPem = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst forge = __webpack_require__(/*! node-forge */ \"(rsc)/./node_modules/node-forge/lib/index.js\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst readFile = (0, util_1.promisify)(fs.readFile);\nfunction getPem(filename, callback) {\n    if (callback) {\n        getPemAsync(filename)\n            .then(pem => callback(null, pem))\n            .catch(err => callback(err, null));\n    }\n    else {\n        return getPemAsync(filename);\n    }\n}\nexports.getPem = getPem;\nfunction getPemAsync(filename) {\n    return readFile(filename, { encoding: 'base64' }).then(keyp12 => {\n        return convertToPem(keyp12);\n    });\n}\n/**\n * Converts a P12 in base64 encoding to a pem.\n * @param p12base64 String containing base64 encoded p12.\n * @returns a string containing the pem.\n */\nfunction convertToPem(p12base64) {\n    const p12Der = forge.util.decode64(p12base64);\n    const p12Asn1 = forge.asn1.fromDer(p12Der);\n    const p12 = forge.pkcs12.pkcs12FromAsn1(p12Asn1, 'notasecret');\n    const bags = p12.getBags({ friendlyName: 'privatekey' });\n    if (bags.friendlyName) {\n        const privateKey = bags.friendlyName[0].key;\n        const pem = forge.pki.privateKeyToPem(privateKey);\n        return pem.replace(/\\r\\n/g, '\\n');\n    }\n    else {\n        throw new Error('Unable to get friendly name.');\n    }\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-p12-pem/build/src/index.js\n");

/***/ })

};
;