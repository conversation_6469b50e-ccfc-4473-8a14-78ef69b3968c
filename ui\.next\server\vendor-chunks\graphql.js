"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/graphql";
exports.ids = ["vendor-chunks/graphql"];
exports.modules = {

/***/ "(ssr)/./node_modules/graphql/error/GraphQLError.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/graphql/error/GraphQLError.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLError: () => (/* binding */ GraphQLError),\n/* harmony export */   formatError: () => (/* binding */ formatError),\n/* harmony export */   printError: () => (/* binding */ printError)\n/* harmony export */ });\n/* harmony import */ var _jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/isObjectLike.mjs */ \"(ssr)/./node_modules/graphql/jsutils/isObjectLike.mjs\");\n/* harmony import */ var _language_location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../language/location.mjs */ \"(ssr)/./node_modules/graphql/language/location.mjs\");\n/* harmony import */ var _language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../language/printLocation.mjs */ \"(ssr)/./node_modules/graphql/language/printLocation.mjs\");\n\n\n\n\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5],\n    };\n  }\n\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nclass GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n\n    const { nodes, source, positions, path, originalError, extensions } =\n      toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError =\n      originalError !== null && originalError !== void 0\n        ? originalError\n        : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(\n      Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined,\n    );\n    const nodeLocations = undefinedIfEmpty(\n      (_this$nodes = this.nodes) === null || _this$nodes === void 0\n        ? void 0\n        : _this$nodes.map((node) => node.loc).filter((loc) => loc != null),\n    ); // Compute locations in the source for the given nodes/positions.\n\n    this.source =\n      source !== null && source !== void 0\n        ? source\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : (_nodeLocations$ = nodeLocations[0]) === null ||\n          _nodeLocations$ === void 0\n        ? void 0\n        : _nodeLocations$.source;\n    this.positions =\n      positions !== null && positions !== void 0\n        ? positions\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => loc.start);\n    this.locations =\n      positions && source\n        ? positions.map((pos) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(source, pos))\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(loc.source, loc.start));\n    const originalExtensions = (0,_jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__.isObjectLike)(\n      originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions,\n    )\n      ? originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions\n      : undefined;\n    this.extensions =\n      (_ref =\n        extensions !== null && extensions !== void 0\n          ? extensions\n          : originalExtensions) !== null && _ref !== void 0\n        ? _ref\n        : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true,\n      },\n      name: {\n        enumerable: false,\n      },\n      nodes: {\n        enumerable: false,\n      },\n      source: {\n        enumerable: false,\n      },\n      positions: {\n        enumerable: false,\n      },\n      originalError: {\n        enumerable: false,\n      },\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (\n      originalError !== null &&\n      originalError !== void 0 &&\n      originalError.stack\n    ) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true,\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true,\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n\n  toString() {\n    let output = this.message;\n\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printLocation)(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printSourceLocation)(this.source, location);\n      }\n    }\n\n    return output;\n  }\n\n  toJSON() {\n    const formattedError = {\n      message: this.message,\n    };\n\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n\n    return formattedError;\n  }\n}\n\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nfunction printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nfunction formatError(error) {\n  return error.toJSON();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/error/GraphQLError.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/graphql/jsutils/invariant.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/jsutils/invariant.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* binding */ invariant)\n/* harmony export */ });\nfunction invariant(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(\n      message != null ? message : 'Unexpected invariant triggered.',\n    );\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2ludmFyaWFudC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGdyYXBocWxcXGpzdXRpbHNcXGludmFyaWFudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGludmFyaWFudChjb25kaXRpb24sIG1lc3NhZ2UpIHtcbiAgY29uc3QgYm9vbGVhbkNvbmRpdGlvbiA9IEJvb2xlYW4oY29uZGl0aW9uKTtcblxuICBpZiAoIWJvb2xlYW5Db25kaXRpb24pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBtZXNzYWdlICE9IG51bGwgPyBtZXNzYWdlIDogJ1VuZXhwZWN0ZWQgaW52YXJpYW50IHRyaWdnZXJlZC4nLFxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/jsutils/invariant.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/graphql/jsutils/isObjectLike.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/graphql/jsutils/isObjectLike.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectLike: () => (/* binding */ isObjectLike)\n/* harmony export */ });\n/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nfunction isObjectLike(value) {\n  return typeof value == 'object' && value !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2lzT2JqZWN0TGlrZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGdyYXBocWxcXGpzdXRpbHNcXGlzT2JqZWN0TGlrZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXR1cm4gdHJ1ZSBpZiBgdmFsdWVgIGlzIG9iamVjdC1saWtlLiBBIHZhbHVlIGlzIG9iamVjdC1saWtlIGlmIGl0J3Mgbm90XG4gKiBgbnVsbGAgYW5kIGhhcyBhIGB0eXBlb2ZgIHJlc3VsdCBvZiBcIm9iamVjdFwiLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNPYmplY3RMaWtlKHZhbHVlKSB7XG4gIHJldHVybiB0eXBlb2YgdmFsdWUgPT0gJ29iamVjdCcgJiYgdmFsdWUgIT09IG51bGw7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/jsutils/isObjectLike.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/graphql/language/location.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/language/location.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocation: () => (/* binding */ getLocation)\n/* harmony export */ });\n/* harmony import */ var _jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/invariant.mjs */ \"(ssr)/./node_modules/graphql/jsutils/invariant.mjs\");\n\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nfunction getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || (0,_jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(false);\n\n    if (match.index >= position) {\n      break;\n    }\n\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n\n  return {\n    line,\n    column: position + 1 - lastLineStart,\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9sYW5ndWFnZS9sb2NhdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7O0FBRUE7QUFDQSx1Q0FBdUMsaUVBQVM7O0FBRWhEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxncmFwaHFsXFxsYW5ndWFnZVxcbG9jYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludmFyaWFudCB9IGZyb20gJy4uL2pzdXRpbHMvaW52YXJpYW50Lm1qcyc7XG5jb25zdCBMaW5lUmVnRXhwID0gL1xcclxcbnxbXFxuXFxyXS9nO1xuLyoqXG4gKiBSZXByZXNlbnRzIGEgbG9jYXRpb24gaW4gYSBTb3VyY2UuXG4gKi9cblxuLyoqXG4gKiBUYWtlcyBhIFNvdXJjZSBhbmQgYSBVVEYtOCBjaGFyYWN0ZXIgb2Zmc2V0LCBhbmQgcmV0dXJucyB0aGUgY29ycmVzcG9uZGluZ1xuICogbGluZSBhbmQgY29sdW1uIGFzIGEgU291cmNlTG9jYXRpb24uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRMb2NhdGlvbihzb3VyY2UsIHBvc2l0aW9uKSB7XG4gIGxldCBsYXN0TGluZVN0YXJ0ID0gMDtcbiAgbGV0IGxpbmUgPSAxO1xuXG4gIGZvciAoY29uc3QgbWF0Y2ggb2Ygc291cmNlLmJvZHkubWF0Y2hBbGwoTGluZVJlZ0V4cCkpIHtcbiAgICB0eXBlb2YgbWF0Y2guaW5kZXggPT09ICdudW1iZXInIHx8IGludmFyaWFudChmYWxzZSk7XG5cbiAgICBpZiAobWF0Y2guaW5kZXggPj0gcG9zaXRpb24pIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIGxhc3RMaW5lU3RhcnQgPSBtYXRjaC5pbmRleCArIG1hdGNoWzBdLmxlbmd0aDtcbiAgICBsaW5lICs9IDE7XG4gIH1cblxuICByZXR1cm4ge1xuICAgIGxpbmUsXG4gICAgY29sdW1uOiBwb3NpdGlvbiArIDEgLSBsYXN0TGluZVN0YXJ0LFxuICB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/language/location.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/graphql/language/printLocation.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/graphql/language/printLocation.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   printLocation: () => (/* binding */ printLocation),\n/* harmony export */   printSourceLocation: () => (/* binding */ printSourceLocation)\n/* harmony export */ });\n/* harmony import */ var _location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./location.mjs */ \"(ssr)/./node_modules/graphql/language/location.mjs\");\n\n\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\nfunction printLocation(location) {\n  return printSourceLocation(\n    location.source,\n    (0,_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(location.source, location.start),\n  );\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nfunction printSourceLocation(source, sourceLocation) {\n  const firstLineColumnOffset = source.locationOffset.column - 1;\n  const body = ''.padStart(firstLineColumnOffset) + source.body;\n  const lineIndex = sourceLocation.line - 1;\n  const lineOffset = source.locationOffset.line - 1;\n  const lineNum = sourceLocation.line + lineOffset;\n  const columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  const columnNum = sourceLocation.column + columnOffset;\n  const locationStr = `${source.name}:${lineNum}:${columnNum}\\n`;\n  const lines = body.split(/\\r\\n|[\\n\\r]/g);\n  const locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    const subLineIndex = Math.floor(columnNum / 80);\n    const subLineColumnNum = columnNum % 80;\n    const subLines = [];\n\n    for (let i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n\n    return (\n      locationStr +\n      printPrefixedLines([\n        [`${lineNum} |`, subLines[0]],\n        ...subLines.slice(1, subLineIndex + 1).map((subLine) => ['|', subLine]),\n        ['|', '^'.padStart(subLineColumnNum)],\n        ['|', subLines[subLineIndex + 1]],\n      ])\n    );\n  }\n\n  return (\n    locationStr +\n    printPrefixedLines([\n      // Lines specified like this: [\"prefix\", \"string\"],\n      [`${lineNum - 1} |`, lines[lineIndex - 1]],\n      [`${lineNum} |`, locationLine],\n      ['|', '^'.padStart(columnNum)],\n      [`${lineNum + 1} |`, lines[lineIndex + 1]],\n    ])\n  );\n}\n\nfunction printPrefixedLines(lines) {\n  const existingLines = lines.filter(([_, line]) => line !== undefined);\n  const padLen = Math.max(...existingLines.map(([prefix]) => prefix.length));\n  return existingLines\n    .map(([prefix, line]) => prefix.padStart(padLen) + (line ? ' ' + line : ''))\n    .join('\\n');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/language/printLocation.mjs\n");

/***/ })

};
;