/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@anthropic-ai";
exports.ids = ["vendor-chunks/@anthropic-ai"];
exports.modules = {

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js":
/*!****************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MultipartBody = void 0;\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nclass MultipartBody {\n    constructor(body) {\n        this.body = body;\n    }\n    get [Symbol.toStringTag]() {\n        return 'MultipartBody';\n    }\n}\nexports.MultipartBody = MultipartBody;\n//# sourceMappingURL=MultipartBody.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvX3NoaW1zL011bHRpcGFydEJvZHkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGFudGhyb3BpYy1haVxcc2RrXFxfc2hpbXNcXE11bHRpcGFydEJvZHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLk11bHRpcGFydEJvZHkgPSB2b2lkIDA7XG4vKipcbiAqIERpc2NsYWltZXI6IG1vZHVsZXMgaW4gX3NoaW1zIGFyZW4ndCBpbnRlbmRlZCB0byBiZSBpbXBvcnRlZCBieSBTREsgdXNlcnMuXG4gKi9cbmNsYXNzIE11bHRpcGFydEJvZHkge1xuICAgIGNvbnN0cnVjdG9yKGJvZHkpIHtcbiAgICAgICAgdGhpcy5ib2R5ID0gYm9keTtcbiAgICB9XG4gICAgZ2V0IFtTeW1ib2wudG9TdHJpbmdUYWddKCkge1xuICAgICAgICByZXR1cm4gJ011bHRpcGFydEJvZHknO1xuICAgIH1cbn1cbmV4cG9ydHMuTXVsdGlwYXJ0Qm9keSA9IE11bHRpcGFydEJvZHk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1NdWx0aXBhcnRCb2R5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.js":
/*!********************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\n__exportStar(__webpack_require__(/*! ../node-runtime.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_shims/node-runtime.js\"), exports);\n//# sourceMappingURL=runtime-node.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvX3NoaW1zL2F1dG8vcnVudGltZS1ub2RlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG9DQUFvQztBQUNuRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEO0FBQ0E7QUFDQTtBQUNBLGFBQWEsbUJBQU8sQ0FBQyx5RkFBb0I7QUFDekMiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGFudGhyb3BpYy1haVxcc2RrXFxfc2hpbXNcXGF1dG9cXHJ1bnRpbWUtbm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIHZhciBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihtLCBrKTtcbiAgICBpZiAoIWRlc2MgfHwgKFwiZ2V0XCIgaW4gZGVzYyA/ICFtLl9fZXNNb2R1bGUgOiBkZXNjLndyaXRhYmxlIHx8IGRlc2MuY29uZmlndXJhYmxlKSkge1xuICAgICAgZGVzYyA9IHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfTtcbiAgICB9XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCBkZXNjKTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4uL25vZGUtcnVudGltZS5qc1wiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ydW50aW1lLW5vZGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/_shims/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/_shims/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nconst shims = __webpack_require__(/*! ./registry */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_shims/registry.js\");\nconst auto = __webpack_require__(/*! @anthropic-ai/sdk/_shims/auto/runtime */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.js\");\nif (!shims.kind) shims.setShims(auto.getRuntime(), { auto: true });\nfor (const property of Object.keys(shims)) {\n  Object.defineProperty(exports, property, {\n    get() {\n      return shims[property];\n    },\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvX3NoaW1zL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsbUJBQU8sQ0FBQyw2RUFBWTtBQUNsQyxhQUFhLG1CQUFPLENBQUMsaUhBQXVDO0FBQzVELHFEQUFxRCxZQUFZO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYW50aHJvcGljLWFpXFxzZGtcXF9zaGltc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5jb25zdCBzaGltcyA9IHJlcXVpcmUoJy4vcmVnaXN0cnknKTtcbmNvbnN0IGF1dG8gPSByZXF1aXJlKCdAYW50aHJvcGljLWFpL3Nkay9fc2hpbXMvYXV0by9ydW50aW1lJyk7XG5pZiAoIXNoaW1zLmtpbmQpIHNoaW1zLnNldFNoaW1zKGF1dG8uZ2V0UnVudGltZSgpLCB7IGF1dG86IHRydWUgfSk7XG5mb3IgKGNvbnN0IHByb3BlcnR5IG9mIE9iamVjdC5rZXlzKHNoaW1zKSkge1xuICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgcHJvcGVydHksIHtcbiAgICBnZXQoKSB7XG4gICAgICByZXR1cm4gc2hpbXNbcHJvcGVydHldO1xuICAgIH0sXG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/_shims/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/_shims/node-runtime.js":
/*!***************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/_shims/node-runtime.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getRuntime = void 0;\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nconst nf = __importStar(__webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/node-fetch/lib/index.mjs\"));\nconst fd = __importStar(__webpack_require__(/*! formdata-node */ \"(rsc)/./node_modules/formdata-node/lib/cjs/index.js\"));\nconst agentkeepalive_1 = __importDefault(__webpack_require__(/*! agentkeepalive */ \"(rsc)/./node_modules/agentkeepalive/index.js\"));\nconst abort_controller_1 = __webpack_require__(/*! abort-controller */ \"(rsc)/./node_modules/abort-controller/dist/abort-controller.js\");\nconst node_fs_1 = __webpack_require__(/*! node:fs */ \"node:fs\");\nconst form_data_encoder_1 = __webpack_require__(/*! form-data-encoder */ \"(rsc)/./node_modules/form-data-encoder/lib/cjs/index.js\");\nconst node_stream_1 = __webpack_require__(/*! node:stream */ \"node:stream\");\nconst MultipartBody_1 = __webpack_require__(/*! ./MultipartBody.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js\");\nconst web_1 = __webpack_require__(/*! node:stream/web */ \"node:stream/web\");\nlet fileFromPathWarned = false;\nasync function fileFromPath(path, ...args) {\n    // this import fails in environments that don't handle export maps correctly, like old versions of Jest\n    const { fileFromPath: _fileFromPath } = await Promise.resolve().then(() => __importStar(__webpack_require__(/*! formdata-node/file-from-path */ \"(rsc)/./node_modules/formdata-node/lib/cjs/fileFromPath.js\")));\n    if (!fileFromPathWarned) {\n        console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(path)}) instead`);\n        fileFromPathWarned = true;\n    }\n    // @ts-ignore\n    return await _fileFromPath(path, ...args);\n}\nconst defaultHttpAgent = new agentkeepalive_1.default({ keepAlive: true, timeout: 5 * 60 * 1000 });\nconst defaultHttpsAgent = new agentkeepalive_1.default.HttpsAgent({ keepAlive: true, timeout: 5 * 60 * 1000 });\nasync function getMultipartRequestOptions(form, opts) {\n    const encoder = new form_data_encoder_1.FormDataEncoder(form);\n    const readable = node_stream_1.Readable.from(encoder);\n    const body = new MultipartBody_1.MultipartBody(readable);\n    const headers = {\n        ...opts.headers,\n        ...encoder.headers,\n        'Content-Length': encoder.contentLength,\n    };\n    return { ...opts, body: body, headers };\n}\nfunction getRuntime() {\n    // Polyfill global object if needed.\n    if (typeof AbortController === 'undefined') {\n        // @ts-expect-error (the types are subtly different, but compatible in practice)\n        globalThis.AbortController = abort_controller_1.AbortController;\n    }\n    return {\n        kind: 'node',\n        fetch: nf.default,\n        Request: nf.Request,\n        Response: nf.Response,\n        Headers: nf.Headers,\n        FormData: fd.FormData,\n        Blob: fd.Blob,\n        File: fd.File,\n        ReadableStream: web_1.ReadableStream,\n        getMultipartRequestOptions,\n        getDefaultAgent: (url) => (url.startsWith('https') ? defaultHttpsAgent : defaultHttpAgent),\n        fileFromPath,\n        isFsReadStream: (value) => value instanceof node_fs_1.ReadStream,\n    };\n}\nexports.getRuntime = getRuntime;\n//# sourceMappingURL=node-runtime.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/_shims/node-runtime.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/_shims/registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/_shims/registry.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setShims = exports.isFsReadStream = exports.fileFromPath = exports.getDefaultAgent = exports.getMultipartRequestOptions = exports.ReadableStream = exports.File = exports.Blob = exports.FormData = exports.Headers = exports.Response = exports.Request = exports.fetch = exports.kind = exports.auto = void 0;\nexports.auto = false;\nexports.kind = undefined;\nexports.fetch = undefined;\nexports.Request = undefined;\nexports.Response = undefined;\nexports.Headers = undefined;\nexports.FormData = undefined;\nexports.Blob = undefined;\nexports.File = undefined;\nexports.ReadableStream = undefined;\nexports.getMultipartRequestOptions = undefined;\nexports.getDefaultAgent = undefined;\nexports.fileFromPath = undefined;\nexports.isFsReadStream = undefined;\nfunction setShims(shims, options = { auto: false }) {\n    if (exports.auto) {\n        throw new Error(`you must \\`import '@anthropic-ai/sdk/shims/${shims.kind}'\\` before importing anything else from @anthropic-ai/sdk`);\n    }\n    if (exports.kind) {\n        throw new Error(`can't \\`import '@anthropic-ai/sdk/shims/${shims.kind}'\\` after \\`import '@anthropic-ai/sdk/shims/${exports.kind}'\\``);\n    }\n    exports.auto = options.auto;\n    exports.kind = shims.kind;\n    exports.fetch = shims.fetch;\n    exports.Request = shims.Request;\n    exports.Response = shims.Response;\n    exports.Headers = shims.Headers;\n    exports.FormData = shims.FormData;\n    exports.Blob = shims.Blob;\n    exports.File = shims.File;\n    exports.ReadableStream = shims.ReadableStream;\n    exports.getMultipartRequestOptions = shims.getMultipartRequestOptions;\n    exports.getDefaultAgent = shims.getDefaultAgent;\n    exports.fileFromPath = shims.fileFromPath;\n    exports.isFsReadStream = shims.isFsReadStream;\n}\nexports.setShims = setShims;\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/_shims/registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.partialParse = void 0;\nconst tokenize = (input) => {\n    let current = 0;\n    let tokens = [];\n    while (current < input.length) {\n        let char = input[current];\n        if (char === '\\\\') {\n            current++;\n            continue;\n        }\n        if (char === '{') {\n            tokens.push({\n                type: 'brace',\n                value: '{',\n            });\n            current++;\n            continue;\n        }\n        if (char === '}') {\n            tokens.push({\n                type: 'brace',\n                value: '}',\n            });\n            current++;\n            continue;\n        }\n        if (char === '[') {\n            tokens.push({\n                type: 'paren',\n                value: '[',\n            });\n            current++;\n            continue;\n        }\n        if (char === ']') {\n            tokens.push({\n                type: 'paren',\n                value: ']',\n            });\n            current++;\n            continue;\n        }\n        if (char === ':') {\n            tokens.push({\n                type: 'separator',\n                value: ':',\n            });\n            current++;\n            continue;\n        }\n        if (char === ',') {\n            tokens.push({\n                type: 'delimiter',\n                value: ',',\n            });\n            current++;\n            continue;\n        }\n        if (char === '\"') {\n            let value = '';\n            let danglingQuote = false;\n            char = input[++current];\n            while (char !== '\"') {\n                if (current === input.length) {\n                    danglingQuote = true;\n                    break;\n                }\n                if (char === '\\\\') {\n                    current++;\n                    if (current === input.length) {\n                        danglingQuote = true;\n                        break;\n                    }\n                    value += char + input[current];\n                    char = input[++current];\n                }\n                else {\n                    value += char;\n                    char = input[++current];\n                }\n            }\n            char = input[++current];\n            if (!danglingQuote) {\n                tokens.push({\n                    type: 'string',\n                    value,\n                });\n            }\n            continue;\n        }\n        let WHITESPACE = /\\s/;\n        if (char && WHITESPACE.test(char)) {\n            current++;\n            continue;\n        }\n        let NUMBERS = /[0-9]/;\n        if ((char && NUMBERS.test(char)) || char === '-' || char === '.') {\n            let value = '';\n            if (char === '-') {\n                value += char;\n                char = input[++current];\n            }\n            while ((char && NUMBERS.test(char)) || char === '.') {\n                value += char;\n                char = input[++current];\n            }\n            tokens.push({\n                type: 'number',\n                value,\n            });\n            continue;\n        }\n        let LETTERS = /[a-z]/i;\n        if (char && LETTERS.test(char)) {\n            let value = '';\n            while (char && LETTERS.test(char)) {\n                if (current === input.length) {\n                    break;\n                }\n                value += char;\n                char = input[++current];\n            }\n            if (value == 'true' || value == 'false' || value === 'null') {\n                tokens.push({\n                    type: 'name',\n                    value,\n                });\n            }\n            else {\n                // unknown token, e.g. `nul` which isn't quite `null`\n                current++;\n                continue;\n            }\n            continue;\n        }\n        current++;\n    }\n    return tokens;\n}, strip = (tokens) => {\n    if (tokens.length === 0) {\n        return tokens;\n    }\n    let lastToken = tokens[tokens.length - 1];\n    switch (lastToken.type) {\n        case 'separator':\n            tokens = tokens.slice(0, tokens.length - 1);\n            return strip(tokens);\n            break;\n        case 'number':\n            let lastCharacterOfLastToken = lastToken.value[lastToken.value.length - 1];\n            if (lastCharacterOfLastToken === '.' || lastCharacterOfLastToken === '-') {\n                tokens = tokens.slice(0, tokens.length - 1);\n                return strip(tokens);\n            }\n        case 'string':\n            let tokenBeforeTheLastToken = tokens[tokens.length - 2];\n            if (tokenBeforeTheLastToken?.type === 'delimiter') {\n                tokens = tokens.slice(0, tokens.length - 1);\n                return strip(tokens);\n            }\n            else if (tokenBeforeTheLastToken?.type === 'brace' && tokenBeforeTheLastToken.value === '{') {\n                tokens = tokens.slice(0, tokens.length - 1);\n                return strip(tokens);\n            }\n            break;\n        case 'delimiter':\n            tokens = tokens.slice(0, tokens.length - 1);\n            return strip(tokens);\n            break;\n    }\n    return tokens;\n}, unstrip = (tokens) => {\n    let tail = [];\n    tokens.map((token) => {\n        if (token.type === 'brace') {\n            if (token.value === '{') {\n                tail.push('}');\n            }\n            else {\n                tail.splice(tail.lastIndexOf('}'), 1);\n            }\n        }\n        if (token.type === 'paren') {\n            if (token.value === '[') {\n                tail.push(']');\n            }\n            else {\n                tail.splice(tail.lastIndexOf(']'), 1);\n            }\n        }\n    });\n    if (tail.length > 0) {\n        tail.reverse().map((item) => {\n            if (item === '}') {\n                tokens.push({\n                    type: 'brace',\n                    value: '}',\n                });\n            }\n            else if (item === ']') {\n                tokens.push({\n                    type: 'paren',\n                    value: ']',\n                });\n            }\n        });\n    }\n    return tokens;\n}, generate = (tokens) => {\n    let output = '';\n    tokens.map((token) => {\n        switch (token.type) {\n            case 'string':\n                output += '\"' + token.value + '\"';\n                break;\n            default:\n                output += token.value;\n                break;\n        }\n    });\n    return output;\n}, partialParse = (input) => JSON.parse(generate(unstrip(strip(tokenize(input)))));\nexports.partialParse = partialParse;\n//# sourceMappingURL=parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/core.js":
/*!************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/core.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _AbstractPage_client;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObj = exports.toBase64 = exports.getRequiredHeader = exports.isHeadersProtocol = exports.isRunningInBrowser = exports.debug = exports.hasOwn = exports.isEmptyObj = exports.maybeCoerceBoolean = exports.maybeCoerceFloat = exports.maybeCoerceInteger = exports.coerceBoolean = exports.coerceFloat = exports.coerceInteger = exports.readEnv = exports.ensurePresent = exports.castToError = exports.sleep = exports.safeJSON = exports.isRequestOptions = exports.createResponseHeaders = exports.PagePromise = exports.AbstractPage = exports.APIClient = exports.APIPromise = exports.createForm = exports.multipartFormRequestOptions = exports.maybeMultipartFormRequestOptions = void 0;\nconst version_1 = __webpack_require__(/*! ./version.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/version.js\");\nconst streaming_1 = __webpack_require__(/*! ./streaming.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/streaming.js\");\nconst error_1 = __webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/error.js\");\nconst index_1 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_shims/index.js\");\nconst uploads_1 = __webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/uploads.js\");\nvar uploads_2 = __webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/uploads.js\");\nObject.defineProperty(exports, \"maybeMultipartFormRequestOptions\", ({ enumerable: true, get: function () { return uploads_2.maybeMultipartFormRequestOptions; } }));\nObject.defineProperty(exports, \"multipartFormRequestOptions\", ({ enumerable: true, get: function () { return uploads_2.multipartFormRequestOptions; } }));\nObject.defineProperty(exports, \"createForm\", ({ enumerable: true, get: function () { return uploads_2.createForm; } }));\nasync function defaultParseResponse(props) {\n    const { response } = props;\n    if (props.options.stream) {\n        debug('response', response.status, response.url, response.headers, response.body);\n        // Note: there is an invariant here that isn't represented in the type system\n        // that if you set `stream: true` the response type must also be `Stream<T>`\n        if (props.options.__streamClass) {\n            return props.options.__streamClass.fromSSEResponse(response, props.controller);\n        }\n        return streaming_1.Stream.fromSSEResponse(response, props.controller);\n    }\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n        return null;\n    }\n    if (props.options.__binaryResponse) {\n        return response;\n    }\n    const contentType = response.headers.get('content-type');\n    const isJSON = contentType?.includes('application/json') || contentType?.includes('application/vnd.api+json');\n    if (isJSON) {\n        const json = await response.json();\n        debug('response', response.status, response.url, response.headers, json);\n        return json;\n    }\n    const text = await response.text();\n    debug('response', response.status, response.url, response.headers, text);\n    // TODO handle blob, arraybuffer, other content types, etc.\n    return text;\n}\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nclass APIPromise extends Promise {\n    constructor(responsePromise, parseResponse = defaultParseResponse) {\n        super((resolve) => {\n            // this is maybe a bit weird but this has to be a no-op to not implicitly\n            // parse the response body; instead .then, .catch, .finally are overridden\n            // to parse the response\n            resolve(null);\n        });\n        this.responsePromise = responsePromise;\n        this.parseResponse = parseResponse;\n    }\n    _thenUnwrap(transform) {\n        return new APIPromise(this.responsePromise, async (props) => transform(await this.parseResponse(props)));\n    }\n    /**\n     * Gets the raw `Response` instance instead of parsing the response\n     * data.\n     *\n     * If you want to parse the response body but still get the `Response`\n     * instance, you can use {@link withResponse()}.\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from '@anthropic-ai/sdk'`:\n     * - `import '@anthropic-ai/sdk/shims/node'` (if you're running on Node)\n     * - `import '@anthropic-ai/sdk/shims/web'` (otherwise)\n     */\n    asResponse() {\n        return this.responsePromise.then((p) => p.response);\n    }\n    /**\n     * Gets the parsed response data and the raw `Response` instance.\n     *\n     * If you just want to get the raw `Response` instance without parsing it,\n     * you can use {@link asResponse()}.\n     *\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from '@anthropic-ai/sdk'`:\n     * - `import '@anthropic-ai/sdk/shims/node'` (if you're running on Node)\n     * - `import '@anthropic-ai/sdk/shims/web'` (otherwise)\n     */\n    async withResponse() {\n        const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n        return { data, response };\n    }\n    parse() {\n        if (!this.parsedPromise) {\n            this.parsedPromise = this.responsePromise.then(this.parseResponse);\n        }\n        return this.parsedPromise;\n    }\n    then(onfulfilled, onrejected) {\n        return this.parse().then(onfulfilled, onrejected);\n    }\n    catch(onrejected) {\n        return this.parse().catch(onrejected);\n    }\n    finally(onfinally) {\n        return this.parse().finally(onfinally);\n    }\n}\nexports.APIPromise = APIPromise;\nclass APIClient {\n    constructor({ baseURL, maxRetries = 2, timeout = 600000, // 10 minutes\n    httpAgent, fetch: overridenFetch, }) {\n        this.baseURL = baseURL;\n        this.maxRetries = validatePositiveInteger('maxRetries', maxRetries);\n        this.timeout = validatePositiveInteger('timeout', timeout);\n        this.httpAgent = httpAgent;\n        this.fetch = overridenFetch ?? index_1.fetch;\n    }\n    authHeaders(opts) {\n        return {};\n    }\n    /**\n     * Override this to add your own default headers, for example:\n     *\n     *  {\n     *    ...super.defaultHeaders(),\n     *    Authorization: 'Bearer 123',\n     *  }\n     */\n    defaultHeaders(opts) {\n        return {\n            Accept: 'application/json',\n            'Content-Type': 'application/json',\n            'User-Agent': this.getUserAgent(),\n            ...getPlatformHeaders(),\n            ...this.authHeaders(opts),\n        };\n    }\n    /**\n     * Override this to add your own headers validation:\n     */\n    validateHeaders(headers, customHeaders) { }\n    defaultIdempotencyKey() {\n        return `stainless-node-retry-${uuid4()}`;\n    }\n    get(path, opts) {\n        return this.methodRequest('get', path, opts);\n    }\n    post(path, opts) {\n        return this.methodRequest('post', path, opts);\n    }\n    patch(path, opts) {\n        return this.methodRequest('patch', path, opts);\n    }\n    put(path, opts) {\n        return this.methodRequest('put', path, opts);\n    }\n    delete(path, opts) {\n        return this.methodRequest('delete', path, opts);\n    }\n    methodRequest(method, path, opts) {\n        return this.request(Promise.resolve(opts).then(async (opts) => {\n            const body = opts && (0, uploads_1.isBlobLike)(opts?.body) ? new DataView(await opts.body.arrayBuffer())\n                : opts?.body instanceof DataView ? opts.body\n                    : opts?.body instanceof ArrayBuffer ? new DataView(opts.body)\n                        : opts && ArrayBuffer.isView(opts?.body) ? new DataView(opts.body.buffer)\n                            : opts?.body;\n            return { method, path, ...opts, body };\n        }));\n    }\n    getAPIList(path, Page, opts) {\n        return this.requestAPIList(Page, { method: 'get', path, ...opts });\n    }\n    calculateContentLength(body) {\n        if (typeof body === 'string') {\n            if (typeof Buffer !== 'undefined') {\n                return Buffer.byteLength(body, 'utf8').toString();\n            }\n            if (typeof TextEncoder !== 'undefined') {\n                const encoder = new TextEncoder();\n                const encoded = encoder.encode(body);\n                return encoded.length.toString();\n            }\n        }\n        else if (ArrayBuffer.isView(body)) {\n            return body.byteLength.toString();\n        }\n        return null;\n    }\n    buildRequest(options) {\n        const { method, path, query, headers: headers = {} } = options;\n        const body = ArrayBuffer.isView(options.body) || (options.__binaryRequest && typeof options.body === 'string') ?\n            options.body\n            : (0, uploads_1.isMultipartBody)(options.body) ? options.body.body\n                : options.body ? JSON.stringify(options.body, null, 2)\n                    : null;\n        const contentLength = this.calculateContentLength(body);\n        const url = this.buildURL(path, query);\n        if ('timeout' in options)\n            validatePositiveInteger('timeout', options.timeout);\n        const timeout = options.timeout ?? this.timeout;\n        const httpAgent = options.httpAgent ?? this.httpAgent ?? (0, index_1.getDefaultAgent)(url);\n        const minAgentTimeout = timeout + 1000;\n        if (typeof httpAgent?.options?.timeout === 'number' &&\n            minAgentTimeout > (httpAgent.options.timeout ?? 0)) {\n            // Allow any given request to bump our agent active socket timeout.\n            // This may seem strange, but leaking active sockets should be rare and not particularly problematic,\n            // and without mutating agent we would need to create more of them.\n            // This tradeoff optimizes for performance.\n            httpAgent.options.timeout = minAgentTimeout;\n        }\n        if (this.idempotencyHeader && method !== 'get') {\n            if (!options.idempotencyKey)\n                options.idempotencyKey = this.defaultIdempotencyKey();\n            headers[this.idempotencyHeader] = options.idempotencyKey;\n        }\n        const reqHeaders = this.buildHeaders({ options, headers, contentLength });\n        const req = {\n            method,\n            ...(body && { body: body }),\n            headers: reqHeaders,\n            ...(httpAgent && { agent: httpAgent }),\n            // @ts-ignore node-fetch uses a custom AbortSignal type that is\n            // not compatible with standard web types\n            signal: options.signal ?? null,\n        };\n        return { req, url, timeout };\n    }\n    buildHeaders({ options, headers, contentLength, }) {\n        const reqHeaders = {};\n        if (contentLength) {\n            reqHeaders['content-length'] = contentLength;\n        }\n        const defaultHeaders = this.defaultHeaders(options);\n        applyHeadersMut(reqHeaders, defaultHeaders);\n        applyHeadersMut(reqHeaders, headers);\n        // let builtin fetch set the Content-Type for multipart bodies\n        if ((0, uploads_1.isMultipartBody)(options.body) && index_1.kind !== 'node') {\n            delete reqHeaders['content-type'];\n        }\n        this.validateHeaders(reqHeaders, headers);\n        return reqHeaders;\n    }\n    /**\n     * Used as a callback for mutating the given `FinalRequestOptions` object.\n     */\n    async prepareOptions(options) { }\n    /**\n     * Used as a callback for mutating the given `RequestInit` object.\n     *\n     * This is useful for cases where you want to add certain headers based off of\n     * the request properties, e.g. `method` or `url`.\n     */\n    async prepareRequest(request, { url, options }) { }\n    parseHeaders(headers) {\n        return (!headers ? {}\n            : Symbol.iterator in headers ?\n                Object.fromEntries(Array.from(headers).map((header) => [...header]))\n                : { ...headers });\n    }\n    makeStatusError(status, error, message, headers) {\n        return error_1.APIError.generate(status, error, message, headers);\n    }\n    request(options, remainingRetries = null) {\n        return new APIPromise(this.makeRequest(options, remainingRetries));\n    }\n    async makeRequest(optionsInput, retriesRemaining) {\n        const options = await optionsInput;\n        if (retriesRemaining == null) {\n            retriesRemaining = options.maxRetries ?? this.maxRetries;\n        }\n        await this.prepareOptions(options);\n        const { req, url, timeout } = this.buildRequest(options);\n        await this.prepareRequest(req, { url, options });\n        debug('request', url, options, req.headers);\n        if (options.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        const controller = new AbortController();\n        const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(exports.castToError);\n        if (response instanceof Error) {\n            if (options.signal?.aborted) {\n                throw new error_1.APIUserAbortError();\n            }\n            if (retriesRemaining) {\n                return this.retryRequest(options, retriesRemaining);\n            }\n            if (response.name === 'AbortError') {\n                throw new error_1.APIConnectionTimeoutError();\n            }\n            throw new error_1.APIConnectionError({ cause: response });\n        }\n        const responseHeaders = (0, exports.createResponseHeaders)(response.headers);\n        if (!response.ok) {\n            if (retriesRemaining && this.shouldRetry(response)) {\n                const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n                debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders);\n                return this.retryRequest(options, retriesRemaining, responseHeaders);\n            }\n            const errText = await response.text().catch((e) => (0, exports.castToError)(e).message);\n            const errJSON = (0, exports.safeJSON)(errText);\n            const errMessage = errJSON ? undefined : errText;\n            const retryMessage = retriesRemaining ? `(error; no more retries left)` : `(error; not retryable)`;\n            debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders, errMessage);\n            const err = this.makeStatusError(response.status, errJSON, errMessage, responseHeaders);\n            throw err;\n        }\n        return { response, options, controller };\n    }\n    requestAPIList(Page, options) {\n        const request = this.makeRequest(options, null);\n        return new PagePromise(this, request, Page);\n    }\n    buildURL(path, query) {\n        const url = isAbsoluteURL(path) ?\n            new URL(path)\n            : new URL(this.baseURL + (this.baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n        const defaultQuery = this.defaultQuery();\n        if (!isEmptyObj(defaultQuery)) {\n            query = { ...defaultQuery, ...query };\n        }\n        if (typeof query === 'object' && query && !Array.isArray(query)) {\n            url.search = this.stringifyQuery(query);\n        }\n        return url.toString();\n    }\n    stringifyQuery(query) {\n        return Object.entries(query)\n            .filter(([_, value]) => typeof value !== 'undefined')\n            .map(([key, value]) => {\n            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n            }\n            if (value === null) {\n                return `${encodeURIComponent(key)}=`;\n            }\n            throw new error_1.AnthropicError(`Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);\n        })\n            .join('&');\n    }\n    async fetchWithTimeout(url, init, ms, controller) {\n        const { signal, ...options } = init || {};\n        if (signal)\n            signal.addEventListener('abort', () => controller.abort());\n        const timeout = setTimeout(() => controller.abort(), ms);\n        return (this.getRequestClient()\n            // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n            .fetch.call(undefined, url, { signal: controller.signal, ...options })\n            .finally(() => {\n            clearTimeout(timeout);\n        }));\n    }\n    getRequestClient() {\n        return { fetch: this.fetch };\n    }\n    shouldRetry(response) {\n        // Note this is not a standard header.\n        const shouldRetryHeader = response.headers.get('x-should-retry');\n        // If the server explicitly says whether or not to retry, obey.\n        if (shouldRetryHeader === 'true')\n            return true;\n        if (shouldRetryHeader === 'false')\n            return false;\n        // Retry on request timeouts.\n        if (response.status === 408)\n            return true;\n        // Retry on lock timeouts.\n        if (response.status === 409)\n            return true;\n        // Retry on rate limits.\n        if (response.status === 429)\n            return true;\n        // Retry internal errors.\n        if (response.status >= 500)\n            return true;\n        return false;\n    }\n    async retryRequest(options, retriesRemaining, responseHeaders) {\n        let timeoutMillis;\n        // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n        const retryAfterMillisHeader = responseHeaders?.['retry-after-ms'];\n        if (retryAfterMillisHeader) {\n            const timeoutMs = parseFloat(retryAfterMillisHeader);\n            if (!Number.isNaN(timeoutMs)) {\n                timeoutMillis = timeoutMs;\n            }\n        }\n        // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n        const retryAfterHeader = responseHeaders?.['retry-after'];\n        if (retryAfterHeader && !timeoutMillis) {\n            const timeoutSeconds = parseFloat(retryAfterHeader);\n            if (!Number.isNaN(timeoutSeconds)) {\n                timeoutMillis = timeoutSeconds * 1000;\n            }\n            else {\n                timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n            }\n        }\n        // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n        // just do what it says, but otherwise calculate a default\n        if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n            const maxRetries = options.maxRetries ?? this.maxRetries;\n            timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n        }\n        await (0, exports.sleep)(timeoutMillis);\n        return this.makeRequest(options, retriesRemaining - 1);\n    }\n    calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries) {\n        const initialRetryDelay = 0.5;\n        const maxRetryDelay = 8.0;\n        const numRetries = maxRetries - retriesRemaining;\n        // Apply exponential backoff, but not more than the max.\n        const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n        // Apply some jitter, take up to at most 25 percent of the retry time.\n        const jitter = 1 - Math.random() * 0.25;\n        return sleepSeconds * jitter * 1000;\n    }\n    getUserAgent() {\n        return `${this.constructor.name}/JS ${version_1.VERSION}`;\n    }\n}\nexports.APIClient = APIClient;\nclass AbstractPage {\n    constructor(client, response, body, options) {\n        _AbstractPage_client.set(this, void 0);\n        __classPrivateFieldSet(this, _AbstractPage_client, client, \"f\");\n        this.options = options;\n        this.response = response;\n        this.body = body;\n    }\n    hasNextPage() {\n        const items = this.getPaginatedItems();\n        if (!items.length)\n            return false;\n        return this.nextPageInfo() != null;\n    }\n    async getNextPage() {\n        const nextInfo = this.nextPageInfo();\n        if (!nextInfo) {\n            throw new error_1.AnthropicError('No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.');\n        }\n        const nextOptions = { ...this.options };\n        if ('params' in nextInfo && typeof nextOptions.query === 'object') {\n            nextOptions.query = { ...nextOptions.query, ...nextInfo.params };\n        }\n        else if ('url' in nextInfo) {\n            const params = [...Object.entries(nextOptions.query || {}), ...nextInfo.url.searchParams.entries()];\n            for (const [key, value] of params) {\n                nextInfo.url.searchParams.set(key, value);\n            }\n            nextOptions.query = undefined;\n            nextOptions.path = nextInfo.url.toString();\n        }\n        return await __classPrivateFieldGet(this, _AbstractPage_client, \"f\").requestAPIList(this.constructor, nextOptions);\n    }\n    async *iterPages() {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let page = this;\n        yield page;\n        while (page.hasNextPage()) {\n            page = await page.getNextPage();\n            yield page;\n        }\n    }\n    async *[(_AbstractPage_client = new WeakMap(), Symbol.asyncIterator)]() {\n        for await (const page of this.iterPages()) {\n            for (const item of page.getPaginatedItems()) {\n                yield item;\n            }\n        }\n    }\n}\nexports.AbstractPage = AbstractPage;\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */\nclass PagePromise extends APIPromise {\n    constructor(client, request, Page) {\n        super(request, async (props) => new Page(client, props.response, await defaultParseResponse(props), props.options));\n    }\n    /**\n     * Allow auto-paginating iteration on an unawaited list call, eg:\n     *\n     *    for await (const item of client.items.list()) {\n     *      console.log(item)\n     *    }\n     */\n    async *[Symbol.asyncIterator]() {\n        const page = await this;\n        for await (const item of page) {\n            yield item;\n        }\n    }\n}\nexports.PagePromise = PagePromise;\nconst createResponseHeaders = (headers) => {\n    return new Proxy(Object.fromEntries(\n    // @ts-ignore\n    headers.entries()), {\n        get(target, name) {\n            const key = name.toString();\n            return target[key.toLowerCase()] || target[key];\n        },\n    });\n};\nexports.createResponseHeaders = createResponseHeaders;\n// This is required so that we can determine if a given object matches the RequestOptions\n// type at runtime. While this requires duplication, it is enforced by the TypeScript\n// compiler such that any missing / extraneous keys will cause an error.\nconst requestOptionsKeys = {\n    method: true,\n    path: true,\n    query: true,\n    body: true,\n    headers: true,\n    maxRetries: true,\n    stream: true,\n    timeout: true,\n    httpAgent: true,\n    signal: true,\n    idempotencyKey: true,\n    __binaryRequest: true,\n    __binaryResponse: true,\n    __streamClass: true,\n};\nconst isRequestOptions = (obj) => {\n    return (typeof obj === 'object' &&\n        obj !== null &&\n        !isEmptyObj(obj) &&\n        Object.keys(obj).every((k) => hasOwn(requestOptionsKeys, k)));\n};\nexports.isRequestOptions = isRequestOptions;\nconst getPlatformProperties = () => {\n    if (typeof Deno !== 'undefined' && Deno.build != null) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': normalizePlatform(Deno.build.os),\n            'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n            'X-Stainless-Runtime': 'deno',\n            'X-Stainless-Runtime-Version': typeof Deno.version === 'string' ? Deno.version : Deno.version?.deno ?? 'unknown',\n        };\n    }\n    if (typeof EdgeRuntime !== 'undefined') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': `other:${EdgeRuntime}`,\n            'X-Stainless-Runtime': 'edge',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    // Check if Node.js\n    if (Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': normalizePlatform(process.platform),\n            'X-Stainless-Arch': normalizeArch(process.arch),\n            'X-Stainless-Runtime': 'node',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    const browserInfo = getBrowserInfo();\n    if (browserInfo) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': 'unknown',\n            'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n            'X-Stainless-Runtime-Version': browserInfo.version,\n        };\n    }\n    // TODO add support for Cloudflare workers, etc.\n    return {\n        'X-Stainless-Lang': 'js',\n        'X-Stainless-Package-Version': version_1.VERSION,\n        'X-Stainless-OS': 'Unknown',\n        'X-Stainless-Arch': 'unknown',\n        'X-Stainless-Runtime': 'unknown',\n        'X-Stainless-Runtime-Version': 'unknown',\n    };\n};\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo() {\n    if (typeof navigator === 'undefined' || !navigator) {\n        return null;\n    }\n    // NOTE: The order matters here!\n    const browserPatterns = [\n        { key: 'edge', pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'chrome', pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'firefox', pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'safari', pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n    ];\n    // Find the FIRST matching browser\n    for (const { key, pattern } of browserPatterns) {\n        const match = pattern.exec(navigator.userAgent);\n        if (match) {\n            const major = match[1] || 0;\n            const minor = match[2] || 0;\n            const patch = match[3] || 0;\n            return { browser: key, version: `${major}.${minor}.${patch}` };\n        }\n    }\n    return null;\n}\nconst normalizeArch = (arch) => {\n    // Node docs:\n    // - https://nodejs.org/api/process.html#processarch\n    // Deno docs:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    if (arch === 'x32')\n        return 'x32';\n    if (arch === 'x86_64' || arch === 'x64')\n        return 'x64';\n    if (arch === 'arm')\n        return 'arm';\n    if (arch === 'aarch64' || arch === 'arm64')\n        return 'arm64';\n    if (arch)\n        return `other:${arch}`;\n    return 'unknown';\n};\nconst normalizePlatform = (platform) => {\n    // Node platforms:\n    // - https://nodejs.org/api/process.html#processplatform\n    // Deno platforms:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    // - https://github.com/denoland/deno/issues/14799\n    platform = platform.toLowerCase();\n    // NOTE: this iOS check is untested and may not work\n    // Node does not work natively on IOS, there is a fork at\n    // https://github.com/nodejs-mobile/nodejs-mobile\n    // however it is unknown at the time of writing how to detect if it is running\n    if (platform.includes('ios'))\n        return 'iOS';\n    if (platform === 'android')\n        return 'Android';\n    if (platform === 'darwin')\n        return 'MacOS';\n    if (platform === 'win32')\n        return 'Windows';\n    if (platform === 'freebsd')\n        return 'FreeBSD';\n    if (platform === 'openbsd')\n        return 'OpenBSD';\n    if (platform === 'linux')\n        return 'Linux';\n    if (platform)\n        return `Other:${platform}`;\n    return 'Unknown';\n};\nlet _platformHeaders;\nconst getPlatformHeaders = () => {\n    return (_platformHeaders ?? (_platformHeaders = getPlatformProperties()));\n};\nconst safeJSON = (text) => {\n    try {\n        return JSON.parse(text);\n    }\n    catch (err) {\n        return undefined;\n    }\n};\nexports.safeJSON = safeJSON;\n// https://stackoverflow.com/a/19709846\nconst startsWithSchemeRegexp = new RegExp('^(?:[a-z]+:)?//', 'i');\nconst isAbsoluteURL = (url) => {\n    return startsWithSchemeRegexp.test(url);\n};\nconst sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\nexports.sleep = sleep;\nconst validatePositiveInteger = (name, n) => {\n    if (typeof n !== 'number' || !Number.isInteger(n)) {\n        throw new error_1.AnthropicError(`${name} must be an integer`);\n    }\n    if (n < 0) {\n        throw new error_1.AnthropicError(`${name} must be a positive integer`);\n    }\n    return n;\n};\nconst castToError = (err) => {\n    if (err instanceof Error)\n        return err;\n    return new Error(err);\n};\nexports.castToError = castToError;\nconst ensurePresent = (value) => {\n    if (value == null)\n        throw new error_1.AnthropicError(`Expected a value to be given but received ${value} instead.`);\n    return value;\n};\nexports.ensurePresent = ensurePresent;\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nconst readEnv = (env) => {\n    if (typeof process !== 'undefined') {\n        return process.env?.[env]?.trim() ?? undefined;\n    }\n    if (typeof Deno !== 'undefined') {\n        return Deno.env?.get?.(env)?.trim();\n    }\n    return undefined;\n};\nexports.readEnv = readEnv;\nconst coerceInteger = (value) => {\n    if (typeof value === 'number')\n        return Math.round(value);\n    if (typeof value === 'string')\n        return parseInt(value, 10);\n    throw new error_1.AnthropicError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nexports.coerceInteger = coerceInteger;\nconst coerceFloat = (value) => {\n    if (typeof value === 'number')\n        return value;\n    if (typeof value === 'string')\n        return parseFloat(value);\n    throw new error_1.AnthropicError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nexports.coerceFloat = coerceFloat;\nconst coerceBoolean = (value) => {\n    if (typeof value === 'boolean')\n        return value;\n    if (typeof value === 'string')\n        return value === 'true';\n    return Boolean(value);\n};\nexports.coerceBoolean = coerceBoolean;\nconst maybeCoerceInteger = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceInteger)(value);\n};\nexports.maybeCoerceInteger = maybeCoerceInteger;\nconst maybeCoerceFloat = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceFloat)(value);\n};\nexports.maybeCoerceFloat = maybeCoerceFloat;\nconst maybeCoerceBoolean = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceBoolean)(value);\n};\nexports.maybeCoerceBoolean = maybeCoerceBoolean;\n// https://stackoverflow.com/a/34491287\nfunction isEmptyObj(obj) {\n    if (!obj)\n        return true;\n    for (const _k in obj)\n        return false;\n    return true;\n}\nexports.isEmptyObj = isEmptyObj;\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nfunction hasOwn(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\nexports.hasOwn = hasOwn;\n/**\n * Copies headers from \"newHeaders\" onto \"targetHeaders\",\n * using lower-case for all properties,\n * ignoring any keys with undefined values,\n * and deleting any keys with null values.\n */\nfunction applyHeadersMut(targetHeaders, newHeaders) {\n    for (const k in newHeaders) {\n        if (!hasOwn(newHeaders, k))\n            continue;\n        const lowerKey = k.toLowerCase();\n        if (!lowerKey)\n            continue;\n        const val = newHeaders[k];\n        if (val === null) {\n            delete targetHeaders[lowerKey];\n        }\n        else if (val !== undefined) {\n            targetHeaders[lowerKey] = val;\n        }\n    }\n}\nfunction debug(action, ...args) {\n    if (typeof process !== 'undefined' && process?.env?.['DEBUG'] === 'true') {\n        console.log(`Anthropic:DEBUG:${action}`, ...args);\n    }\n}\nexports.debug = debug;\n/**\n * https://stackoverflow.com/a/2117523\n */\nconst uuid4 = () => {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16) | 0;\n        const v = c === 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n};\nconst isRunningInBrowser = () => {\n    return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n        // @ts-ignore\n        typeof window.document !== 'undefined' &&\n        // @ts-ignore\n        typeof navigator !== 'undefined');\n};\nexports.isRunningInBrowser = isRunningInBrowser;\nconst isHeadersProtocol = (headers) => {\n    return typeof headers?.get === 'function';\n};\nexports.isHeadersProtocol = isHeadersProtocol;\nconst getRequiredHeader = (headers, header) => {\n    const lowerCasedHeader = header.toLowerCase();\n    if ((0, exports.isHeadersProtocol)(headers)) {\n        // to deal with the case where the header looks like Stainless-Event-Id\n        const intercapsHeader = header[0]?.toUpperCase() +\n            header.substring(1).replace(/([^\\w])(\\w)/g, (_m, g1, g2) => g1 + g2.toUpperCase());\n        for (const key of [header, lowerCasedHeader, header.toUpperCase(), intercapsHeader]) {\n            const value = headers.get(key);\n            if (value) {\n                return value;\n            }\n        }\n    }\n    for (const [key, value] of Object.entries(headers)) {\n        if (key.toLowerCase() === lowerCasedHeader) {\n            if (Array.isArray(value)) {\n                if (value.length <= 1)\n                    return value[0];\n                console.warn(`Received ${value.length} entries for the ${header} header, using the first entry.`);\n                return value[0];\n            }\n            return value;\n        }\n    }\n    throw new Error(`Could not find ${header} header`);\n};\nexports.getRequiredHeader = getRequiredHeader;\n/**\n * Encodes a string to Base64 format.\n */\nconst toBase64 = (str) => {\n    if (!str)\n        return '';\n    if (typeof Buffer !== 'undefined') {\n        return Buffer.from(str).toString('base64');\n    }\n    if (typeof btoa !== 'undefined') {\n        return btoa(str);\n    }\n    throw new error_1.AnthropicError('Cannot generate b64 string; Expected `Buffer` or `btoa` to be defined');\n};\nexports.toBase64 = toBase64;\nfunction isObj(obj) {\n    return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\nexports.isObj = isObj;\n//# sourceMappingURL=core.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/error.js":
/*!*************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/error.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InternalServerError = exports.RateLimitError = exports.UnprocessableEntityError = exports.ConflictError = exports.NotFoundError = exports.PermissionDeniedError = exports.AuthenticationError = exports.BadRequestError = exports.APIConnectionTimeoutError = exports.APIConnectionError = exports.APIUserAbortError = exports.APIError = exports.AnthropicError = void 0;\nconst core_1 = __webpack_require__(/*! ./core.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/core.js\");\nclass AnthropicError extends Error {\n}\nexports.AnthropicError = AnthropicError;\nclass APIError extends AnthropicError {\n    constructor(status, error, message, headers) {\n        super(`${APIError.makeMessage(status, error, message)}`);\n        this.status = status;\n        this.headers = headers;\n        this.request_id = headers?.['request-id'];\n        this.error = error;\n    }\n    static makeMessage(status, error, message) {\n        const msg = error?.message ?\n            typeof error.message === 'string' ?\n                error.message\n                : JSON.stringify(error.message)\n            : error ? JSON.stringify(error)\n                : message;\n        if (status && msg) {\n            return `${status} ${msg}`;\n        }\n        if (status) {\n            return `${status} status code (no body)`;\n        }\n        if (msg) {\n            return msg;\n        }\n        return '(no status code or body)';\n    }\n    static generate(status, errorResponse, message, headers) {\n        if (!status) {\n            return new APIConnectionError({ message, cause: (0, core_1.castToError)(errorResponse) });\n        }\n        const error = errorResponse;\n        if (status === 400) {\n            return new BadRequestError(status, error, message, headers);\n        }\n        if (status === 401) {\n            return new AuthenticationError(status, error, message, headers);\n        }\n        if (status === 403) {\n            return new PermissionDeniedError(status, error, message, headers);\n        }\n        if (status === 404) {\n            return new NotFoundError(status, error, message, headers);\n        }\n        if (status === 409) {\n            return new ConflictError(status, error, message, headers);\n        }\n        if (status === 422) {\n            return new UnprocessableEntityError(status, error, message, headers);\n        }\n        if (status === 429) {\n            return new RateLimitError(status, error, message, headers);\n        }\n        if (status >= 500) {\n            return new InternalServerError(status, error, message, headers);\n        }\n        return new APIError(status, error, message, headers);\n    }\n}\nexports.APIError = APIError;\nclass APIUserAbortError extends APIError {\n    constructor({ message } = {}) {\n        super(undefined, undefined, message || 'Request was aborted.', undefined);\n        this.status = undefined;\n    }\n}\nexports.APIUserAbortError = APIUserAbortError;\nclass APIConnectionError extends APIError {\n    constructor({ message, cause }) {\n        super(undefined, undefined, message || 'Connection error.', undefined);\n        this.status = undefined;\n        // in some environments the 'cause' property is already declared\n        // @ts-ignore\n        if (cause)\n            this.cause = cause;\n    }\n}\nexports.APIConnectionError = APIConnectionError;\nclass APIConnectionTimeoutError extends APIConnectionError {\n    constructor({ message } = {}) {\n        super({ message: message ?? 'Request timed out.' });\n    }\n}\nexports.APIConnectionTimeoutError = APIConnectionTimeoutError;\nclass BadRequestError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 400;\n    }\n}\nexports.BadRequestError = BadRequestError;\nclass AuthenticationError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 401;\n    }\n}\nexports.AuthenticationError = AuthenticationError;\nclass PermissionDeniedError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 403;\n    }\n}\nexports.PermissionDeniedError = PermissionDeniedError;\nclass NotFoundError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 404;\n    }\n}\nexports.NotFoundError = NotFoundError;\nclass ConflictError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 409;\n    }\n}\nexports.ConflictError = ConflictError;\nclass UnprocessableEntityError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 422;\n    }\n}\nexports.UnprocessableEntityError = UnprocessableEntityError;\nclass RateLimitError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 429;\n    }\n}\nexports.RateLimitError = RateLimitError;\nclass InternalServerError extends APIError {\n}\nexports.InternalServerError = InternalServerError;\n//# sourceMappingURL=error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/index.js ***!
  \*************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fileFromPath = exports.toFile = exports.UnprocessableEntityError = exports.PermissionDeniedError = exports.InternalServerError = exports.AuthenticationError = exports.BadRequestError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.APIUserAbortError = exports.APIConnectionTimeoutError = exports.APIConnectionError = exports.APIError = exports.AnthropicError = exports.AI_PROMPT = exports.HUMAN_PROMPT = exports.Anthropic = void 0;\nconst Errors = __importStar(__webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/error.js\"));\nconst Uploads = __importStar(__webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/uploads.js\"));\nconst Core = __importStar(__webpack_require__(/*! ./core.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/core.js\"));\nconst API = __importStar(__webpack_require__(/*! ./resources/index.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resources/index.js\"));\n/**\n * API Client for interfacing with the Anthropic API.\n */\nclass Anthropic extends Core.APIClient {\n    /**\n     * API Client for interfacing with the Anthropic API.\n     *\n     * @param {string | null | undefined} [opts.apiKey=process.env['ANTHROPIC_API_KEY'] ?? null]\n     * @param {string | null | undefined} [opts.authToken=process.env['ANTHROPIC_AUTH_TOKEN'] ?? null]\n     * @param {string} [opts.baseURL=process.env['ANTHROPIC_BASE_URL'] ?? https://api.anthropic.com] - Override the default base URL for the API.\n     * @param {number} [opts.timeout=10 minutes] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.\n     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.\n     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.\n     * @param {boolean} [opts.dangerouslyAllowBrowser=false] - By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.\n     */\n    constructor({ baseURL = Core.readEnv('ANTHROPIC_BASE_URL'), apiKey = Core.readEnv('ANTHROPIC_API_KEY') ?? null, authToken = Core.readEnv('ANTHROPIC_AUTH_TOKEN') ?? null, ...opts } = {}) {\n        const options = {\n            apiKey,\n            authToken,\n            ...opts,\n            baseURL: baseURL || `https://api.anthropic.com`,\n        };\n        if (!options.dangerouslyAllowBrowser && Core.isRunningInBrowser()) {\n            throw new Errors.AnthropicError(\"It looks like you're running in a browser-like environment.\\n\\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\\nIf you understand the risks and have appropriate mitigations in place,\\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\\n\\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\\n\\nTODO: link!\\n\");\n        }\n        super({\n            baseURL: options.baseURL,\n            timeout: options.timeout ?? 600000 /* 10 minutes */,\n            httpAgent: options.httpAgent,\n            maxRetries: options.maxRetries,\n            fetch: options.fetch,\n        });\n        this.completions = new API.Completions(this);\n        this.messages = new API.Messages(this);\n        this.beta = new API.Beta(this);\n        this._options = options;\n        this.apiKey = apiKey;\n        this.authToken = authToken;\n    }\n    defaultQuery() {\n        return this._options.defaultQuery;\n    }\n    defaultHeaders(opts) {\n        return {\n            ...super.defaultHeaders(opts),\n            ...(this._options.dangerouslyAllowBrowser ?\n                { 'anthropic-dangerous-direct-browser-access': 'true' }\n                : undefined),\n            'anthropic-version': '2023-06-01',\n            ...this._options.defaultHeaders,\n        };\n    }\n    validateHeaders(headers, customHeaders) {\n        if (this.apiKey && headers['x-api-key']) {\n            return;\n        }\n        if (customHeaders['x-api-key'] === null) {\n            return;\n        }\n        if (this.authToken && headers['authorization']) {\n            return;\n        }\n        if (customHeaders['authorization'] === null) {\n            return;\n        }\n        throw new Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted');\n    }\n    authHeaders(opts) {\n        const apiKeyAuth = this.apiKeyAuth(opts);\n        const bearerAuth = this.bearerAuth(opts);\n        if (apiKeyAuth != null && !Core.isEmptyObj(apiKeyAuth)) {\n            return apiKeyAuth;\n        }\n        if (bearerAuth != null && !Core.isEmptyObj(bearerAuth)) {\n            return bearerAuth;\n        }\n        return {};\n    }\n    apiKeyAuth(opts) {\n        if (this.apiKey == null) {\n            return {};\n        }\n        return { 'X-Api-Key': this.apiKey };\n    }\n    bearerAuth(opts) {\n        if (this.authToken == null) {\n            return {};\n        }\n        return { Authorization: `Bearer ${this.authToken}` };\n    }\n}\nexports.Anthropic = Anthropic;\n_a = Anthropic;\nAnthropic.Anthropic = _a;\nAnthropic.HUMAN_PROMPT = '\\n\\nHuman:';\nAnthropic.AI_PROMPT = '\\n\\nAssistant:';\nAnthropic.DEFAULT_TIMEOUT = 600000; // 10 minutes\nAnthropic.AnthropicError = Errors.AnthropicError;\nAnthropic.APIError = Errors.APIError;\nAnthropic.APIConnectionError = Errors.APIConnectionError;\nAnthropic.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;\nAnthropic.APIUserAbortError = Errors.APIUserAbortError;\nAnthropic.NotFoundError = Errors.NotFoundError;\nAnthropic.ConflictError = Errors.ConflictError;\nAnthropic.RateLimitError = Errors.RateLimitError;\nAnthropic.BadRequestError = Errors.BadRequestError;\nAnthropic.AuthenticationError = Errors.AuthenticationError;\nAnthropic.InternalServerError = Errors.InternalServerError;\nAnthropic.PermissionDeniedError = Errors.PermissionDeniedError;\nAnthropic.UnprocessableEntityError = Errors.UnprocessableEntityError;\nAnthropic.toFile = Uploads.toFile;\nAnthropic.fileFromPath = Uploads.fileFromPath;\nexports.HUMAN_PROMPT = Anthropic.HUMAN_PROMPT, exports.AI_PROMPT = Anthropic.AI_PROMPT;\nexports.AnthropicError = Errors.AnthropicError, exports.APIError = Errors.APIError, exports.APIConnectionError = Errors.APIConnectionError, exports.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError, exports.APIUserAbortError = Errors.APIUserAbortError, exports.NotFoundError = Errors.NotFoundError, exports.ConflictError = Errors.ConflictError, exports.RateLimitError = Errors.RateLimitError, exports.BadRequestError = Errors.BadRequestError, exports.AuthenticationError = Errors.AuthenticationError, exports.InternalServerError = Errors.InternalServerError, exports.PermissionDeniedError = Errors.PermissionDeniedError, exports.UnprocessableEntityError = Errors.UnprocessableEntityError;\nexports.toFile = Uploads.toFile;\nexports.fileFromPath = Uploads.fileFromPath;\n(function (Anthropic) {\n    Anthropic.Completions = API.Completions;\n    Anthropic.Messages = API.Messages;\n    Anthropic.Beta = API.Beta;\n})(Anthropic = exports.Anthropic || (exports.Anthropic = {}));\nexports = module.exports = Anthropic;\nexports[\"default\"] = Anthropic;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/lib/MessageStream.js":
/*!*************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/lib/MessageStream.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _MessageStream_instances, _MessageStream_currentMessageSnapshot, _MessageStream_connectedPromise, _MessageStream_resolveConnectedPromise, _MessageStream_rejectConnectedPromise, _MessageStream_endPromise, _MessageStream_resolveEndPromise, _MessageStream_rejectEndPromise, _MessageStream_listeners, _MessageStream_ended, _MessageStream_errored, _MessageStream_aborted, _MessageStream_catchingPromiseCreated, _MessageStream_getFinalMessage, _MessageStream_getFinalText, _MessageStream_handleError, _MessageStream_beginRequest, _MessageStream_addStreamEvent, _MessageStream_endRequest, _MessageStream_accumulateMessage;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MessageStream = void 0;\nconst error_1 = __webpack_require__(/*! @anthropic-ai/sdk/error */ \"(rsc)/./node_modules/@anthropic-ai/sdk/error.js\");\nconst streaming_1 = __webpack_require__(/*! @anthropic-ai/sdk/streaming */ \"(rsc)/./node_modules/@anthropic-ai/sdk/streaming.js\");\nconst parser_1 = __webpack_require__(/*! ../_vendor/partial-json-parser/parser.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js\");\nconst JSON_BUF_PROPERTY = '__json_buf';\nclass MessageStream {\n    constructor() {\n        _MessageStream_instances.add(this);\n        this.messages = [];\n        this.receivedMessages = [];\n        _MessageStream_currentMessageSnapshot.set(this, void 0);\n        this.controller = new AbortController();\n        _MessageStream_connectedPromise.set(this, void 0);\n        _MessageStream_resolveConnectedPromise.set(this, () => { });\n        _MessageStream_rejectConnectedPromise.set(this, () => { });\n        _MessageStream_endPromise.set(this, void 0);\n        _MessageStream_resolveEndPromise.set(this, () => { });\n        _MessageStream_rejectEndPromise.set(this, () => { });\n        _MessageStream_listeners.set(this, {});\n        _MessageStream_ended.set(this, false);\n        _MessageStream_errored.set(this, false);\n        _MessageStream_aborted.set(this, false);\n        _MessageStream_catchingPromiseCreated.set(this, false);\n        _MessageStream_handleError.set(this, (error) => {\n            __classPrivateFieldSet(this, _MessageStream_errored, true, \"f\");\n            if (error instanceof Error && error.name === 'AbortError') {\n                error = new error_1.APIUserAbortError();\n            }\n            if (error instanceof error_1.APIUserAbortError) {\n                __classPrivateFieldSet(this, _MessageStream_aborted, true, \"f\");\n                return this._emit('abort', error);\n            }\n            if (error instanceof error_1.AnthropicError) {\n                return this._emit('error', error);\n            }\n            if (error instanceof Error) {\n                const anthropicError = new error_1.AnthropicError(error.message);\n                // @ts-ignore\n                anthropicError.cause = error;\n                return this._emit('error', anthropicError);\n            }\n            return this._emit('error', new error_1.AnthropicError(String(error)));\n        });\n        __classPrivateFieldSet(this, _MessageStream_connectedPromise, new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _MessageStream_resolveConnectedPromise, resolve, \"f\");\n            __classPrivateFieldSet(this, _MessageStream_rejectConnectedPromise, reject, \"f\");\n        }), \"f\");\n        __classPrivateFieldSet(this, _MessageStream_endPromise, new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _MessageStream_resolveEndPromise, resolve, \"f\");\n            __classPrivateFieldSet(this, _MessageStream_rejectEndPromise, reject, \"f\");\n        }), \"f\");\n        // Don't let these promises cause unhandled rejection errors.\n        // we will manually cause an unhandled rejection error later\n        // if the user hasn't registered any error listener or called\n        // any promise-returning method.\n        __classPrivateFieldGet(this, _MessageStream_connectedPromise, \"f\").catch(() => { });\n        __classPrivateFieldGet(this, _MessageStream_endPromise, \"f\").catch(() => { });\n    }\n    /**\n     * Intended for use on the frontend, consuming a stream produced with\n     * `.toReadableStream()` on the backend.\n     *\n     * Note that messages sent to the model do not appear in `.on('message')`\n     * in this context.\n     */\n    static fromReadableStream(stream) {\n        const runner = new MessageStream();\n        runner._run(() => runner._fromReadableStream(stream));\n        return runner;\n    }\n    static createMessage(messages, params, options) {\n        const runner = new MessageStream();\n        for (const message of params.messages) {\n            runner._addMessageParam(message);\n        }\n        runner._run(() => runner._createMessage(messages, { ...params, stream: true }, { ...options, headers: { ...options?.headers, 'X-Stainless-Helper-Method': 'stream' } }));\n        return runner;\n    }\n    _run(executor) {\n        executor().then(() => {\n            this._emitFinal();\n            this._emit('end');\n        }, __classPrivateFieldGet(this, _MessageStream_handleError, \"f\"));\n    }\n    _addMessageParam(message) {\n        this.messages.push(message);\n    }\n    _addMessage(message, emit = true) {\n        this.receivedMessages.push(message);\n        if (emit) {\n            this._emit('message', message);\n        }\n    }\n    async _createMessage(messages, params, options) {\n        const signal = options?.signal;\n        if (signal) {\n            if (signal.aborted)\n                this.controller.abort();\n            signal.addEventListener('abort', () => this.controller.abort());\n        }\n        __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_beginRequest).call(this);\n        const stream = await messages.create({ ...params, stream: true }, { ...options, signal: this.controller.signal });\n        this._connected();\n        for await (const event of stream) {\n            __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_addStreamEvent).call(this, event);\n        }\n        if (stream.controller.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_endRequest).call(this);\n    }\n    _connected() {\n        if (this.ended)\n            return;\n        __classPrivateFieldGet(this, _MessageStream_resolveConnectedPromise, \"f\").call(this);\n        this._emit('connect');\n    }\n    get ended() {\n        return __classPrivateFieldGet(this, _MessageStream_ended, \"f\");\n    }\n    get errored() {\n        return __classPrivateFieldGet(this, _MessageStream_errored, \"f\");\n    }\n    get aborted() {\n        return __classPrivateFieldGet(this, _MessageStream_aborted, \"f\");\n    }\n    abort() {\n        this.controller.abort();\n    }\n    /**\n     * Adds the listener function to the end of the listeners array for the event.\n     * No checks are made to see if the listener has already been added. Multiple calls passing\n     * the same combination of event and listener will result in the listener being added, and\n     * called, multiple times.\n     * @returns this MessageStream, so that calls can be chained\n     */\n    on(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] || (__classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] = []);\n        listeners.push({ listener });\n        return this;\n    }\n    /**\n     * Removes the specified listener from the listener array for the event.\n     * off() will remove, at most, one instance of a listener from the listener array. If any single\n     * listener has been added multiple times to the listener array for the specified event, then\n     * off() must be called multiple times to remove each instance.\n     * @returns this MessageStream, so that calls can be chained\n     */\n    off(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event];\n        if (!listeners)\n            return this;\n        const index = listeners.findIndex((l) => l.listener === listener);\n        if (index >= 0)\n            listeners.splice(index, 1);\n        return this;\n    }\n    /**\n     * Adds a one-time listener function for the event. The next time the event is triggered,\n     * this listener is removed and then invoked.\n     * @returns this MessageStream, so that calls can be chained\n     */\n    once(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] || (__classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] = []);\n        listeners.push({ listener, once: true });\n        return this;\n    }\n    /**\n     * This is similar to `.once()`, but returns a Promise that resolves the next time\n     * the event is triggered, instead of calling a listener callback.\n     * @returns a Promise that resolves the next time given event is triggered,\n     * or rejects if an error is emitted.  (If you request the 'error' event,\n     * returns a promise that resolves with the error).\n     *\n     * Example:\n     *\n     *   const message = await stream.emitted('message') // rejects if the stream errors\n     */\n    emitted(event) {\n        return new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _MessageStream_catchingPromiseCreated, true, \"f\");\n            if (event !== 'error')\n                this.once('error', reject);\n            this.once(event, resolve);\n        });\n    }\n    async done() {\n        __classPrivateFieldSet(this, _MessageStream_catchingPromiseCreated, true, \"f\");\n        await __classPrivateFieldGet(this, _MessageStream_endPromise, \"f\");\n    }\n    get currentMessage() {\n        return __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, \"f\");\n    }\n    /**\n     * @returns a promise that resolves with the the final assistant Message response,\n     * or rejects if an error occurred or the stream ended prematurely without producing a Message.\n     */\n    async finalMessage() {\n        await this.done();\n        return __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_getFinalMessage).call(this);\n    }\n    /**\n     * @returns a promise that resolves with the the final assistant Message's text response, concatenated\n     * together if there are more than one text blocks.\n     * Rejects if an error occurred or the stream ended prematurely without producing a Message.\n     */\n    async finalText() {\n        await this.done();\n        return __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_getFinalText).call(this);\n    }\n    _emit(event, ...args) {\n        // make sure we don't emit any MessageStreamEvents after end\n        if (__classPrivateFieldGet(this, _MessageStream_ended, \"f\"))\n            return;\n        if (event === 'end') {\n            __classPrivateFieldSet(this, _MessageStream_ended, true, \"f\");\n            __classPrivateFieldGet(this, _MessageStream_resolveEndPromise, \"f\").call(this);\n        }\n        const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event];\n        if (listeners) {\n            __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] = listeners.filter((l) => !l.once);\n            listeners.forEach(({ listener }) => listener(...args));\n        }\n        if (event === 'abort') {\n            const error = args[0];\n            if (!__classPrivateFieldGet(this, _MessageStream_catchingPromiseCreated, \"f\") && !listeners?.length) {\n                Promise.reject(error);\n            }\n            __classPrivateFieldGet(this, _MessageStream_rejectConnectedPromise, \"f\").call(this, error);\n            __classPrivateFieldGet(this, _MessageStream_rejectEndPromise, \"f\").call(this, error);\n            this._emit('end');\n            return;\n        }\n        if (event === 'error') {\n            // NOTE: _emit('error', error) should only be called from #handleError().\n            const error = args[0];\n            if (!__classPrivateFieldGet(this, _MessageStream_catchingPromiseCreated, \"f\") && !listeners?.length) {\n                // Trigger an unhandled rejection if the user hasn't registered any error handlers.\n                // If you are seeing stack traces here, make sure to handle errors via either:\n                // - runner.on('error', () => ...)\n                // - await runner.done()\n                // - await runner.final...()\n                // - etc.\n                Promise.reject(error);\n            }\n            __classPrivateFieldGet(this, _MessageStream_rejectConnectedPromise, \"f\").call(this, error);\n            __classPrivateFieldGet(this, _MessageStream_rejectEndPromise, \"f\").call(this, error);\n            this._emit('end');\n        }\n    }\n    _emitFinal() {\n        const finalMessage = this.receivedMessages.at(-1);\n        if (finalMessage) {\n            this._emit('finalMessage', __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_getFinalMessage).call(this));\n        }\n    }\n    async _fromReadableStream(readableStream, options) {\n        const signal = options?.signal;\n        if (signal) {\n            if (signal.aborted)\n                this.controller.abort();\n            signal.addEventListener('abort', () => this.controller.abort());\n        }\n        __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_beginRequest).call(this);\n        this._connected();\n        const stream = streaming_1.Stream.fromReadableStream(readableStream, this.controller);\n        for await (const event of stream) {\n            __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_addStreamEvent).call(this, event);\n        }\n        if (stream.controller.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_endRequest).call(this);\n    }\n    [(_MessageStream_currentMessageSnapshot = new WeakMap(), _MessageStream_connectedPromise = new WeakMap(), _MessageStream_resolveConnectedPromise = new WeakMap(), _MessageStream_rejectConnectedPromise = new WeakMap(), _MessageStream_endPromise = new WeakMap(), _MessageStream_resolveEndPromise = new WeakMap(), _MessageStream_rejectEndPromise = new WeakMap(), _MessageStream_listeners = new WeakMap(), _MessageStream_ended = new WeakMap(), _MessageStream_errored = new WeakMap(), _MessageStream_aborted = new WeakMap(), _MessageStream_catchingPromiseCreated = new WeakMap(), _MessageStream_handleError = new WeakMap(), _MessageStream_instances = new WeakSet(), _MessageStream_getFinalMessage = function _MessageStream_getFinalMessage() {\n        if (this.receivedMessages.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a Message with role=assistant');\n        }\n        return this.receivedMessages.at(-1);\n    }, _MessageStream_getFinalText = function _MessageStream_getFinalText() {\n        if (this.receivedMessages.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a Message with role=assistant');\n        }\n        const textBlocks = this.receivedMessages\n            .at(-1)\n            .content.filter((block) => block.type === 'text')\n            .map((block) => block.text);\n        if (textBlocks.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a content block with type=text');\n        }\n        return textBlocks.join(' ');\n    }, _MessageStream_beginRequest = function _MessageStream_beginRequest() {\n        if (this.ended)\n            return;\n        __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, undefined, \"f\");\n    }, _MessageStream_addStreamEvent = function _MessageStream_addStreamEvent(event) {\n        if (this.ended)\n            return;\n        const messageSnapshot = __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_accumulateMessage).call(this, event);\n        this._emit('streamEvent', event, messageSnapshot);\n        switch (event.type) {\n            case 'content_block_delta': {\n                const content = messageSnapshot.content.at(-1);\n                if (event.delta.type === 'text_delta' && content.type === 'text') {\n                    this._emit('text', event.delta.text, content.text || '');\n                }\n                else if (event.delta.type === 'input_json_delta' && content.type === 'tool_use') {\n                    if (content.input) {\n                        this._emit('inputJson', event.delta.partial_json, content.input);\n                    }\n                }\n                break;\n            }\n            case 'message_stop': {\n                this._addMessageParam(messageSnapshot);\n                this._addMessage(messageSnapshot, true);\n                break;\n            }\n            case 'content_block_stop': {\n                this._emit('contentBlock', messageSnapshot.content.at(-1));\n                break;\n            }\n            case 'message_start': {\n                __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, messageSnapshot, \"f\");\n                break;\n            }\n            case 'content_block_start':\n            case 'message_delta':\n                break;\n        }\n    }, _MessageStream_endRequest = function _MessageStream_endRequest() {\n        if (this.ended) {\n            throw new error_1.AnthropicError(`stream has ended, this shouldn't happen`);\n        }\n        const snapshot = __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, \"f\");\n        if (!snapshot) {\n            throw new error_1.AnthropicError(`request ended without sending any chunks`);\n        }\n        __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, undefined, \"f\");\n        return snapshot;\n    }, _MessageStream_accumulateMessage = function _MessageStream_accumulateMessage(event) {\n        let snapshot = __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, \"f\");\n        if (event.type === 'message_start') {\n            if (snapshot) {\n                throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before receiving \"message_stop\"`);\n            }\n            return event.message;\n        }\n        if (!snapshot) {\n            throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before \"message_start\"`);\n        }\n        switch (event.type) {\n            case 'message_stop':\n                return snapshot;\n            case 'message_delta':\n                snapshot.stop_reason = event.delta.stop_reason;\n                snapshot.stop_sequence = event.delta.stop_sequence;\n                snapshot.usage.output_tokens = event.usage.output_tokens;\n                return snapshot;\n            case 'content_block_start':\n                snapshot.content.push(event.content_block);\n                return snapshot;\n            case 'content_block_delta': {\n                const snapshotContent = snapshot.content.at(event.index);\n                if (snapshotContent?.type === 'text' && event.delta.type === 'text_delta') {\n                    snapshotContent.text += event.delta.text;\n                }\n                else if (snapshotContent?.type === 'tool_use' && event.delta.type === 'input_json_delta') {\n                    // we need to keep track of the raw JSON string as well so that we can\n                    // re-parse it for each delta, for now we just store it as an untyped\n                    // non-enumerable property on the snapshot\n                    let jsonBuf = snapshotContent[JSON_BUF_PROPERTY] || '';\n                    jsonBuf += event.delta.partial_json;\n                    Object.defineProperty(snapshotContent, JSON_BUF_PROPERTY, {\n                        value: jsonBuf,\n                        enumerable: false,\n                        writable: true,\n                    });\n                    if (jsonBuf) {\n                        snapshotContent.input = (0, parser_1.partialParse)(jsonBuf);\n                    }\n                }\n                return snapshot;\n            }\n            case 'content_block_stop':\n                return snapshot;\n        }\n    }, Symbol.asyncIterator)]() {\n        const pushQueue = [];\n        const readQueue = [];\n        let done = false;\n        this.on('streamEvent', (event) => {\n            const reader = readQueue.shift();\n            if (reader) {\n                reader.resolve(event);\n            }\n            else {\n                pushQueue.push(event);\n            }\n        });\n        this.on('end', () => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.resolve(undefined);\n            }\n            readQueue.length = 0;\n        });\n        this.on('abort', (err) => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.reject(err);\n            }\n            readQueue.length = 0;\n        });\n        this.on('error', (err) => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.reject(err);\n            }\n            readQueue.length = 0;\n        });\n        return {\n            next: async () => {\n                if (!pushQueue.length) {\n                    if (done) {\n                        return { value: undefined, done: true };\n                    }\n                    return new Promise((resolve, reject) => readQueue.push({ resolve, reject })).then((chunk) => (chunk ? { value: chunk, done: false } : { value: undefined, done: true }));\n                }\n                const chunk = pushQueue.shift();\n                return { value: chunk, done: false };\n            },\n            return: async () => {\n                this.abort();\n                return { value: undefined, done: true };\n            },\n        };\n    }\n    toReadableStream() {\n        const stream = new streaming_1.Stream(this[Symbol.asyncIterator].bind(this), this.controller);\n        return stream.toReadableStream();\n    }\n}\nexports.MessageStream = MessageStream;\n//# sourceMappingURL=MessageStream.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/lib/MessageStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/lib/PromptCachingBetaMessageStream.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/lib/PromptCachingBetaMessageStream.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _PromptCachingBetaMessageStream_instances, _PromptCachingBetaMessageStream_currentMessageSnapshot, _PromptCachingBetaMessageStream_connectedPromise, _PromptCachingBetaMessageStream_resolveConnectedPromise, _PromptCachingBetaMessageStream_rejectConnectedPromise, _PromptCachingBetaMessageStream_endPromise, _PromptCachingBetaMessageStream_resolveEndPromise, _PromptCachingBetaMessageStream_rejectEndPromise, _PromptCachingBetaMessageStream_listeners, _PromptCachingBetaMessageStream_ended, _PromptCachingBetaMessageStream_errored, _PromptCachingBetaMessageStream_aborted, _PromptCachingBetaMessageStream_catchingPromiseCreated, _PromptCachingBetaMessageStream_getFinalMessage, _PromptCachingBetaMessageStream_getFinalText, _PromptCachingBetaMessageStream_handleError, _PromptCachingBetaMessageStream_beginRequest, _PromptCachingBetaMessageStream_addStreamEvent, _PromptCachingBetaMessageStream_endRequest, _PromptCachingBetaMessageStream_accumulateMessage;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PromptCachingBetaMessageStream = void 0;\nconst error_1 = __webpack_require__(/*! @anthropic-ai/sdk/error */ \"(rsc)/./node_modules/@anthropic-ai/sdk/error.js\");\nconst streaming_1 = __webpack_require__(/*! @anthropic-ai/sdk/streaming */ \"(rsc)/./node_modules/@anthropic-ai/sdk/streaming.js\");\nconst parser_1 = __webpack_require__(/*! ../_vendor/partial-json-parser/parser.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js\");\nconst JSON_BUF_PROPERTY = '__json_buf';\nclass PromptCachingBetaMessageStream {\n    constructor() {\n        _PromptCachingBetaMessageStream_instances.add(this);\n        this.messages = [];\n        this.receivedMessages = [];\n        _PromptCachingBetaMessageStream_currentMessageSnapshot.set(this, void 0);\n        this.controller = new AbortController();\n        _PromptCachingBetaMessageStream_connectedPromise.set(this, void 0);\n        _PromptCachingBetaMessageStream_resolveConnectedPromise.set(this, () => { });\n        _PromptCachingBetaMessageStream_rejectConnectedPromise.set(this, () => { });\n        _PromptCachingBetaMessageStream_endPromise.set(this, void 0);\n        _PromptCachingBetaMessageStream_resolveEndPromise.set(this, () => { });\n        _PromptCachingBetaMessageStream_rejectEndPromise.set(this, () => { });\n        _PromptCachingBetaMessageStream_listeners.set(this, {});\n        _PromptCachingBetaMessageStream_ended.set(this, false);\n        _PromptCachingBetaMessageStream_errored.set(this, false);\n        _PromptCachingBetaMessageStream_aborted.set(this, false);\n        _PromptCachingBetaMessageStream_catchingPromiseCreated.set(this, false);\n        _PromptCachingBetaMessageStream_handleError.set(this, (error) => {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_errored, true, \"f\");\n            if (error instanceof Error && error.name === 'AbortError') {\n                error = new error_1.APIUserAbortError();\n            }\n            if (error instanceof error_1.APIUserAbortError) {\n                __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_aborted, true, \"f\");\n                return this._emit('abort', error);\n            }\n            if (error instanceof error_1.AnthropicError) {\n                return this._emit('error', error);\n            }\n            if (error instanceof Error) {\n                const anthropicError = new error_1.AnthropicError(error.message);\n                // @ts-ignore\n                anthropicError.cause = error;\n                return this._emit('error', anthropicError);\n            }\n            return this._emit('error', new error_1.AnthropicError(String(error)));\n        });\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_connectedPromise, new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_resolveConnectedPromise, resolve, \"f\");\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, reject, \"f\");\n        }), \"f\");\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_endPromise, new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_resolveEndPromise, resolve, \"f\");\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_rejectEndPromise, reject, \"f\");\n        }), \"f\");\n        // Don't let these promises cause unhandled rejection errors.\n        // we will manually cause an unhandled rejection error later\n        // if the user hasn't registered any error listener or called\n        // any promise-returning method.\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_connectedPromise, \"f\").catch(() => { });\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_endPromise, \"f\").catch(() => { });\n    }\n    /**\n     * Intended for use on the frontend, consuming a stream produced with\n     * `.toReadableStream()` on the backend.\n     *\n     * Note that messages sent to the model do not appear in `.on('message')`\n     * in this context.\n     */\n    static fromReadableStream(stream) {\n        const runner = new PromptCachingBetaMessageStream();\n        runner._run(() => runner._fromReadableStream(stream));\n        return runner;\n    }\n    static createMessage(messages, params, options) {\n        const runner = new PromptCachingBetaMessageStream();\n        for (const message of params.messages) {\n            runner._addPromptCachingBetaMessageParam(message);\n        }\n        runner._run(() => runner._createPromptCachingBetaMessage(messages, { ...params, stream: true }, { ...options, headers: { ...options?.headers, 'X-Stainless-Helper-Method': 'stream' } }));\n        return runner;\n    }\n    _run(executor) {\n        executor().then(() => {\n            this._emitFinal();\n            this._emit('end');\n        }, __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_handleError, \"f\"));\n    }\n    _addPromptCachingBetaMessageParam(message) {\n        this.messages.push(message);\n    }\n    _addPromptCachingBetaMessage(message, emit = true) {\n        this.receivedMessages.push(message);\n        if (emit) {\n            this._emit('message', message);\n        }\n    }\n    async _createPromptCachingBetaMessage(messages, params, options) {\n        const signal = options?.signal;\n        if (signal) {\n            if (signal.aborted)\n                this.controller.abort();\n            signal.addEventListener('abort', () => this.controller.abort());\n        }\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_beginRequest).call(this);\n        const stream = await messages.create({ ...params, stream: true }, { ...options, signal: this.controller.signal });\n        this._connected();\n        for await (const event of stream) {\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_addStreamEvent).call(this, event);\n        }\n        if (stream.controller.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_endRequest).call(this);\n    }\n    _connected() {\n        if (this.ended)\n            return;\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_resolveConnectedPromise, \"f\").call(this);\n        this._emit('connect');\n    }\n    get ended() {\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_ended, \"f\");\n    }\n    get errored() {\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_errored, \"f\");\n    }\n    get aborted() {\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_aborted, \"f\");\n    }\n    abort() {\n        this.controller.abort();\n    }\n    /**\n     * Adds the listener function to the end of the listeners array for the event.\n     * No checks are made to see if the listener has already been added. Multiple calls passing\n     * the same combination of event and listener will result in the listener being added, and\n     * called, multiple times.\n     * @returns this PromptCachingBetaMessageStream, so that calls can be chained\n     */\n    on(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] || (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] = []);\n        listeners.push({ listener });\n        return this;\n    }\n    /**\n     * Removes the specified listener from the listener array for the event.\n     * off() will remove, at most, one instance of a listener from the listener array. If any single\n     * listener has been added multiple times to the listener array for the specified event, then\n     * off() must be called multiple times to remove each instance.\n     * @returns this PromptCachingBetaMessageStream, so that calls can be chained\n     */\n    off(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event];\n        if (!listeners)\n            return this;\n        const index = listeners.findIndex((l) => l.listener === listener);\n        if (index >= 0)\n            listeners.splice(index, 1);\n        return this;\n    }\n    /**\n     * Adds a one-time listener function for the event. The next time the event is triggered,\n     * this listener is removed and then invoked.\n     * @returns this PromptCachingBetaMessageStream, so that calls can be chained\n     */\n    once(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] || (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] = []);\n        listeners.push({ listener, once: true });\n        return this;\n    }\n    /**\n     * This is similar to `.once()`, but returns a Promise that resolves the next time\n     * the event is triggered, instead of calling a listener callback.\n     * @returns a Promise that resolves the next time given event is triggered,\n     * or rejects if an error is emitted.  (If you request the 'error' event,\n     * returns a promise that resolves with the error).\n     *\n     * Example:\n     *\n     *   const message = await stream.emitted('message') // rejects if the stream errors\n     */\n    emitted(event) {\n        return new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, true, \"f\");\n            if (event !== 'error')\n                this.once('error', reject);\n            this.once(event, resolve);\n        });\n    }\n    async done() {\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, true, \"f\");\n        await __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_endPromise, \"f\");\n    }\n    get currentMessage() {\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, \"f\");\n    }\n    /**\n     * @returns a promise that resolves with the the final assistant PromptCachingBetaMessage response,\n     * or rejects if an error occurred or the stream ended prematurely without producing a PromptCachingBetaMessage.\n     */\n    async finalMessage() {\n        await this.done();\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_getFinalMessage).call(this);\n    }\n    /**\n     * @returns a promise that resolves with the the final assistant PromptCachingBetaMessage's text response, concatenated\n     * together if there are more than one text blocks.\n     * Rejects if an error occurred or the stream ended prematurely without producing a PromptCachingBetaMessage.\n     */\n    async finalText() {\n        await this.done();\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_getFinalText).call(this);\n    }\n    _emit(event, ...args) {\n        // make sure we don't emit any PromptCachingBetaMessageStreamEvents after end\n        if (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_ended, \"f\"))\n            return;\n        if (event === 'end') {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_ended, true, \"f\");\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_resolveEndPromise, \"f\").call(this);\n        }\n        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event];\n        if (listeners) {\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] = listeners.filter((l) => !l.once);\n            listeners.forEach(({ listener }) => listener(...args));\n        }\n        if (event === 'abort') {\n            const error = args[0];\n            if (!__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, \"f\") && !listeners?.length) {\n                Promise.reject(error);\n            }\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, \"f\").call(this, error);\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectEndPromise, \"f\").call(this, error);\n            this._emit('end');\n            return;\n        }\n        if (event === 'error') {\n            // NOTE: _emit('error', error) should only be called from #handleError().\n            const error = args[0];\n            if (!__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, \"f\") && !listeners?.length) {\n                // Trigger an unhandled rejection if the user hasn't registered any error handlers.\n                // If you are seeing stack traces here, make sure to handle errors via either:\n                // - runner.on('error', () => ...)\n                // - await runner.done()\n                // - await runner.final...()\n                // - etc.\n                Promise.reject(error);\n            }\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, \"f\").call(this, error);\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectEndPromise, \"f\").call(this, error);\n            this._emit('end');\n        }\n    }\n    _emitFinal() {\n        const finalPromptCachingBetaMessage = this.receivedMessages.at(-1);\n        if (finalPromptCachingBetaMessage) {\n            this._emit('finalPromptCachingBetaMessage', __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_getFinalMessage).call(this));\n        }\n    }\n    async _fromReadableStream(readableStream, options) {\n        const signal = options?.signal;\n        if (signal) {\n            if (signal.aborted)\n                this.controller.abort();\n            signal.addEventListener('abort', () => this.controller.abort());\n        }\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_beginRequest).call(this);\n        this._connected();\n        const stream = streaming_1.Stream.fromReadableStream(readableStream, this.controller);\n        for await (const event of stream) {\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_addStreamEvent).call(this, event);\n        }\n        if (stream.controller.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_endRequest).call(this);\n    }\n    [(_PromptCachingBetaMessageStream_currentMessageSnapshot = new WeakMap(), _PromptCachingBetaMessageStream_connectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_resolveConnectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_rejectConnectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_endPromise = new WeakMap(), _PromptCachingBetaMessageStream_resolveEndPromise = new WeakMap(), _PromptCachingBetaMessageStream_rejectEndPromise = new WeakMap(), _PromptCachingBetaMessageStream_listeners = new WeakMap(), _PromptCachingBetaMessageStream_ended = new WeakMap(), _PromptCachingBetaMessageStream_errored = new WeakMap(), _PromptCachingBetaMessageStream_aborted = new WeakMap(), _PromptCachingBetaMessageStream_catchingPromiseCreated = new WeakMap(), _PromptCachingBetaMessageStream_handleError = new WeakMap(), _PromptCachingBetaMessageStream_instances = new WeakSet(), _PromptCachingBetaMessageStream_getFinalMessage = function _PromptCachingBetaMessageStream_getFinalMessage() {\n        if (this.receivedMessages.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a PromptCachingBetaMessage with role=assistant');\n        }\n        return this.receivedMessages.at(-1);\n    }, _PromptCachingBetaMessageStream_getFinalText = function _PromptCachingBetaMessageStream_getFinalText() {\n        if (this.receivedMessages.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a PromptCachingBetaMessage with role=assistant');\n        }\n        const textBlocks = this.receivedMessages\n            .at(-1)\n            .content.filter((block) => block.type === 'text')\n            .map((block) => block.text);\n        if (textBlocks.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a content block with type=text');\n        }\n        return textBlocks.join(' ');\n    }, _PromptCachingBetaMessageStream_beginRequest = function _PromptCachingBetaMessageStream_beginRequest() {\n        if (this.ended)\n            return;\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, undefined, \"f\");\n    }, _PromptCachingBetaMessageStream_addStreamEvent = function _PromptCachingBetaMessageStream_addStreamEvent(event) {\n        if (this.ended)\n            return;\n        const messageSnapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_accumulateMessage).call(this, event);\n        this._emit('streamEvent', event, messageSnapshot);\n        switch (event.type) {\n            case 'content_block_delta': {\n                const content = messageSnapshot.content.at(-1);\n                if (event.delta.type === 'text_delta' && content.type === 'text') {\n                    this._emit('text', event.delta.text, content.text || '');\n                }\n                else if (event.delta.type === 'input_json_delta' && content.type === 'tool_use') {\n                    if (content.input) {\n                        this._emit('inputJson', event.delta.partial_json, content.input);\n                    }\n                }\n                break;\n            }\n            case 'message_stop': {\n                this._addPromptCachingBetaMessageParam(messageSnapshot);\n                this._addPromptCachingBetaMessage(messageSnapshot, true);\n                break;\n            }\n            case 'content_block_stop': {\n                this._emit('contentBlock', messageSnapshot.content.at(-1));\n                break;\n            }\n            case 'message_start': {\n                __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, messageSnapshot, \"f\");\n                break;\n            }\n            case 'content_block_start':\n            case 'message_delta':\n                break;\n        }\n    }, _PromptCachingBetaMessageStream_endRequest = function _PromptCachingBetaMessageStream_endRequest() {\n        if (this.ended) {\n            throw new error_1.AnthropicError(`stream has ended, this shouldn't happen`);\n        }\n        const snapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, \"f\");\n        if (!snapshot) {\n            throw new error_1.AnthropicError(`request ended without sending any chunks`);\n        }\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, undefined, \"f\");\n        return snapshot;\n    }, _PromptCachingBetaMessageStream_accumulateMessage = function _PromptCachingBetaMessageStream_accumulateMessage(event) {\n        let snapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, \"f\");\n        if (event.type === 'message_start') {\n            if (snapshot) {\n                throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before receiving \"message_stop\"`);\n            }\n            return event.message;\n        }\n        if (!snapshot) {\n            throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before \"message_start\"`);\n        }\n        switch (event.type) {\n            case 'message_stop':\n                return snapshot;\n            case 'message_delta':\n                snapshot.stop_reason = event.delta.stop_reason;\n                snapshot.stop_sequence = event.delta.stop_sequence;\n                snapshot.usage.output_tokens = event.usage.output_tokens;\n                return snapshot;\n            case 'content_block_start':\n                snapshot.content.push(event.content_block);\n                return snapshot;\n            case 'content_block_delta': {\n                const snapshotContent = snapshot.content.at(event.index);\n                if (snapshotContent?.type === 'text' && event.delta.type === 'text_delta') {\n                    snapshotContent.text += event.delta.text;\n                }\n                else if (snapshotContent?.type === 'tool_use' && event.delta.type === 'input_json_delta') {\n                    // we need to keep track of the raw JSON string as well so that we can\n                    // re-parse it for each delta, for now we just store it as an untyped\n                    // non-enumerable property on the snapshot\n                    let jsonBuf = snapshotContent[JSON_BUF_PROPERTY] || '';\n                    jsonBuf += event.delta.partial_json;\n                    Object.defineProperty(snapshotContent, JSON_BUF_PROPERTY, {\n                        value: jsonBuf,\n                        enumerable: false,\n                        writable: true,\n                    });\n                    if (jsonBuf) {\n                        snapshotContent.input = (0, parser_1.partialParse)(jsonBuf);\n                    }\n                }\n                return snapshot;\n            }\n            case 'content_block_stop':\n                return snapshot;\n        }\n    }, Symbol.asyncIterator)]() {\n        const pushQueue = [];\n        const readQueue = [];\n        let done = false;\n        this.on('streamEvent', (event) => {\n            const reader = readQueue.shift();\n            if (reader) {\n                reader.resolve(event);\n            }\n            else {\n                pushQueue.push(event);\n            }\n        });\n        this.on('end', () => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.resolve(undefined);\n            }\n            readQueue.length = 0;\n        });\n        this.on('abort', (err) => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.reject(err);\n            }\n            readQueue.length = 0;\n        });\n        this.on('error', (err) => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.reject(err);\n            }\n            readQueue.length = 0;\n        });\n        return {\n            next: async () => {\n                if (!pushQueue.length) {\n                    if (done) {\n                        return { value: undefined, done: true };\n                    }\n                    return new Promise((resolve, reject) => readQueue.push({ resolve, reject })).then((chunk) => (chunk ? { value: chunk, done: false } : { value: undefined, done: true }));\n                }\n                const chunk = pushQueue.shift();\n                return { value: chunk, done: false };\n            },\n            return: async () => {\n                this.abort();\n                return { value: undefined, done: true };\n            },\n        };\n    }\n    toReadableStream() {\n        const stream = new streaming_1.Stream(this[Symbol.asyncIterator].bind(this), this.controller);\n        return stream.toReadableStream();\n    }\n}\nexports.PromptCachingBetaMessageStream = PromptCachingBetaMessageStream;\n//# sourceMappingURL=PromptCachingBetaMessageStream.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/lib/PromptCachingBetaMessageStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/resource.js":
/*!****************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/resource.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.APIResource = void 0;\nclass APIResource {\n    constructor(client) {\n        this._client = client;\n    }\n}\nexports.APIResource = APIResource;\n//# sourceMappingURL=resource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvcmVzb3VyY2UuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQiIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYW50aHJvcGljLWFpXFxzZGtcXHJlc291cmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQVBJUmVzb3VyY2UgPSB2b2lkIDA7XG5jbGFzcyBBUElSZXNvdXJjZSB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuX2NsaWVudCA9IGNsaWVudDtcbiAgICB9XG59XG5leHBvcnRzLkFQSVJlc291cmNlID0gQVBJUmVzb3VyY2U7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXNvdXJjZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/resource.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/beta.js":
/*!***************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/resources/beta/beta.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Beta = void 0;\nconst resource_1 = __webpack_require__(/*! ../../resource.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resource.js\");\nconst PromptCachingAPI = __importStar(__webpack_require__(/*! ./prompt-caching/prompt-caching.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/prompt-caching.js\"));\nclass Beta extends resource_1.APIResource {\n    constructor() {\n        super(...arguments);\n        this.promptCaching = new PromptCachingAPI.PromptCaching(this._client);\n    }\n}\nexports.Beta = Beta;\n(function (Beta) {\n    Beta.PromptCaching = PromptCachingAPI.PromptCaching;\n})(Beta = exports.Beta || (exports.Beta = {}));\n//# sourceMappingURL=beta.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/beta.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/messages.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/messages.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Messages = void 0;\nconst resource_1 = __webpack_require__(/*! ../../../resource.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resource.js\");\nconst PromptCachingBetaMessageStream_1 = __webpack_require__(/*! ../../../lib/PromptCachingBetaMessageStream.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/lib/PromptCachingBetaMessageStream.js\");\nclass Messages extends resource_1.APIResource {\n    create(body, options) {\n        return this._client.post('/v1/messages?beta=prompt_caching', {\n            body,\n            timeout: this._client._options.timeout ?? 600000,\n            ...options,\n            headers: { 'anthropic-beta': 'prompt-caching-2024-07-31', ...options?.headers },\n            stream: body.stream ?? false,\n        });\n    }\n    /**\n     * Create a Message stream\n     */\n    stream(body, options) {\n        return PromptCachingBetaMessageStream_1.PromptCachingBetaMessageStream.createMessage(this, body, options);\n    }\n}\nexports.Messages = Messages;\n(function (Messages) {\n})(Messages = exports.Messages || (exports.Messages = {}));\n//# sourceMappingURL=messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/messages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/prompt-caching.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/prompt-caching.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PromptCaching = void 0;\nconst resource_1 = __webpack_require__(/*! ../../../resource.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resource.js\");\nconst MessagesAPI = __importStar(__webpack_require__(/*! ./messages.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/messages.js\"));\nclass PromptCaching extends resource_1.APIResource {\n    constructor() {\n        super(...arguments);\n        this.messages = new MessagesAPI.Messages(this._client);\n    }\n}\nexports.PromptCaching = PromptCaching;\n(function (PromptCaching) {\n    PromptCaching.Messages = MessagesAPI.Messages;\n})(PromptCaching = exports.PromptCaching || (exports.PromptCaching = {}));\n//# sourceMappingURL=prompt-caching.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/prompt-caching.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/resources/completions.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/resources/completions.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Completions = void 0;\nconst resource_1 = __webpack_require__(/*! ../resource.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resource.js\");\nclass Completions extends resource_1.APIResource {\n    create(body, options) {\n        return this._client.post('/v1/complete', {\n            body,\n            timeout: this._client._options.timeout ?? 600000,\n            ...options,\n            stream: body.stream ?? false,\n        });\n    }\n}\nexports.Completions = Completions;\n(function (Completions) {\n})(Completions = exports.Completions || (exports.Completions = {}));\n//# sourceMappingURL=completions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvcmVzb3VyY2VzL2NvbXBsZXRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CO0FBQ25CLG1CQUFtQixtQkFBTyxDQUFDLDBFQUFnQjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0EsQ0FBQyx3Q0FBd0MsbUJBQW1CLEtBQUs7QUFDakUiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGFudGhyb3BpYy1haVxcc2RrXFxyZXNvdXJjZXNcXGNvbXBsZXRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQ29tcGxldGlvbnMgPSB2b2lkIDA7XG5jb25zdCByZXNvdXJjZV8xID0gcmVxdWlyZShcIi4uL3Jlc291cmNlLmpzXCIpO1xuY2xhc3MgQ29tcGxldGlvbnMgZXh0ZW5kcyByZXNvdXJjZV8xLkFQSVJlc291cmNlIHtcbiAgICBjcmVhdGUoYm9keSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy92MS9jb21wbGV0ZScsIHtcbiAgICAgICAgICAgIGJvZHksXG4gICAgICAgICAgICB0aW1lb3V0OiB0aGlzLl9jbGllbnQuX29wdGlvbnMudGltZW91dCA/PyA2MDAwMDAsXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgc3RyZWFtOiBib2R5LnN0cmVhbSA/PyBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0cy5Db21wbGV0aW9ucyA9IENvbXBsZXRpb25zO1xuKGZ1bmN0aW9uIChDb21wbGV0aW9ucykge1xufSkoQ29tcGxldGlvbnMgPSBleHBvcnRzLkNvbXBsZXRpb25zIHx8IChleHBvcnRzLkNvbXBsZXRpb25zID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbXBsZXRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/resources/completions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/resources/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/resources/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Messages = exports.Completions = exports.Beta = void 0;\nvar beta_1 = __webpack_require__(/*! ./beta/beta.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resources/beta/beta.js\");\nObject.defineProperty(exports, \"Beta\", ({ enumerable: true, get: function () { return beta_1.Beta; } }));\nvar completions_1 = __webpack_require__(/*! ./completions.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resources/completions.js\");\nObject.defineProperty(exports, \"Completions\", ({ enumerable: true, get: function () { return completions_1.Completions; } }));\nvar messages_1 = __webpack_require__(/*! ./messages.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resources/messages.js\");\nObject.defineProperty(exports, \"Messages\", ({ enumerable: true, get: function () { return messages_1.Messages; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvcmVzb3VyY2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0JBQWdCLEdBQUcsbUJBQW1CLEdBQUcsWUFBWTtBQUNyRCxhQUFhLG1CQUFPLENBQUMscUZBQWdCO0FBQ3JDLHdDQUF1QyxFQUFFLHFDQUFxQyx1QkFBdUIsRUFBQztBQUN0RyxvQkFBb0IsbUJBQU8sQ0FBQyx5RkFBa0I7QUFDOUMsK0NBQThDLEVBQUUscUNBQXFDLHFDQUFxQyxFQUFDO0FBQzNILGlCQUFpQixtQkFBTyxDQUFDLG1GQUFlO0FBQ3hDLDRDQUEyQyxFQUFFLHFDQUFxQywrQkFBK0IsRUFBQztBQUNsSCIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYW50aHJvcGljLWFpXFxzZGtcXHJlc291cmNlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5NZXNzYWdlcyA9IGV4cG9ydHMuQ29tcGxldGlvbnMgPSBleHBvcnRzLkJldGEgPSB2b2lkIDA7XG52YXIgYmV0YV8xID0gcmVxdWlyZShcIi4vYmV0YS9iZXRhLmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiQmV0YVwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gYmV0YV8xLkJldGE7IH0gfSk7XG52YXIgY29tcGxldGlvbnNfMSA9IHJlcXVpcmUoXCIuL2NvbXBsZXRpb25zLmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiQ29tcGxldGlvbnNcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIGNvbXBsZXRpb25zXzEuQ29tcGxldGlvbnM7IH0gfSk7XG52YXIgbWVzc2FnZXNfMSA9IHJlcXVpcmUoXCIuL21lc3NhZ2VzLmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiTWVzc2FnZXNcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIG1lc3NhZ2VzXzEuTWVzc2FnZXM7IH0gfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/resources/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/resources/messages.js":
/*!**************************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/resources/messages.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Messages = exports.MessageStream = void 0;\nconst resource_1 = __webpack_require__(/*! ../resource.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/resource.js\");\nconst MessageStream_1 = __webpack_require__(/*! ../lib/MessageStream.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/lib/MessageStream.js\");\nvar MessageStream_2 = __webpack_require__(/*! ../lib/MessageStream.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/lib/MessageStream.js\");\nObject.defineProperty(exports, \"MessageStream\", ({ enumerable: true, get: function () { return MessageStream_2.MessageStream; } }));\nclass Messages extends resource_1.APIResource {\n    create(body, options) {\n        if (body.model in DEPRECATED_MODELS) {\n            console.warn(`The model '${body.model}' is deprecated and will reach end-of-life on ${DEPRECATED_MODELS[body.model]}\\nPlease migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);\n        }\n        return this._client.post('/v1/messages', {\n            body,\n            timeout: this._client._options.timeout ?? 600000,\n            ...options,\n            stream: body.stream ?? false,\n        });\n    }\n    /**\n     * Create a Message stream\n     */\n    stream(body, options) {\n        return MessageStream_1.MessageStream.createMessage(this, body, options);\n    }\n}\nexports.Messages = Messages;\nconst DEPRECATED_MODELS = {\n    'claude-1.3': 'November 6th, 2024',\n    'claude-1.3-100k': 'November 6th, 2024',\n    'claude-instant-1.1': 'November 6th, 2024',\n    'claude-instant-1.1-100k': 'November 6th, 2024',\n    'claude-instant-1.2': 'November 6th, 2024',\n};\n(function (Messages) {\n})(Messages = exports.Messages || (exports.Messages = {}));\n//# sourceMappingURL=messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvcmVzb3VyY2VzL21lc3NhZ2VzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0JBQWdCLEdBQUcscUJBQXFCO0FBQ3hDLG1CQUFtQixtQkFBTyxDQUFDLDBFQUFnQjtBQUMzQyx3QkFBd0IsbUJBQU8sQ0FBQyw0RkFBeUI7QUFDekQsc0JBQXNCLG1CQUFPLENBQUMsNEZBQXlCO0FBQ3ZELGlEQUFnRCxFQUFFLHFDQUFxQyx5Q0FBeUMsRUFBQztBQUNqSTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsV0FBVyxnREFBZ0QsOEJBQThCO0FBQ2hJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGtDQUFrQyxnQkFBZ0IsS0FBSztBQUN4RCIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAYW50aHJvcGljLWFpXFxzZGtcXHJlc291cmNlc1xcbWVzc2FnZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5NZXNzYWdlcyA9IGV4cG9ydHMuTWVzc2FnZVN0cmVhbSA9IHZvaWQgMDtcbmNvbnN0IHJlc291cmNlXzEgPSByZXF1aXJlKFwiLi4vcmVzb3VyY2UuanNcIik7XG5jb25zdCBNZXNzYWdlU3RyZWFtXzEgPSByZXF1aXJlKFwiLi4vbGliL01lc3NhZ2VTdHJlYW0uanNcIik7XG52YXIgTWVzc2FnZVN0cmVhbV8yID0gcmVxdWlyZShcIi4uL2xpYi9NZXNzYWdlU3RyZWFtLmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiTWVzc2FnZVN0cmVhbVwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gTWVzc2FnZVN0cmVhbV8yLk1lc3NhZ2VTdHJlYW07IH0gfSk7XG5jbGFzcyBNZXNzYWdlcyBleHRlbmRzIHJlc291cmNlXzEuQVBJUmVzb3VyY2Uge1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIGlmIChib2R5Lm1vZGVsIGluIERFUFJFQ0FURURfTU9ERUxTKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oYFRoZSBtb2RlbCAnJHtib2R5Lm1vZGVsfScgaXMgZGVwcmVjYXRlZCBhbmQgd2lsbCByZWFjaCBlbmQtb2YtbGlmZSBvbiAke0RFUFJFQ0FURURfTU9ERUxTW2JvZHkubW9kZWxdfVxcblBsZWFzZSBtaWdyYXRlIHRvIGEgbmV3ZXIgbW9kZWwuIFZpc2l0IGh0dHBzOi8vZG9jcy5hbnRocm9waWMuY29tL2VuL2RvY3MvcmVzb3VyY2VzL21vZGVsLWRlcHJlY2F0aW9ucyBmb3IgbW9yZSBpbmZvcm1hdGlvbi5gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy92MS9tZXNzYWdlcycsIHtcbiAgICAgICAgICAgIGJvZHksXG4gICAgICAgICAgICB0aW1lb3V0OiB0aGlzLl9jbGllbnQuX29wdGlvbnMudGltZW91dCA/PyA2MDAwMDAsXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgc3RyZWFtOiBib2R5LnN0cmVhbSA/PyBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhIE1lc3NhZ2Ugc3RyZWFtXG4gICAgICovXG4gICAgc3RyZWFtKGJvZHksIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIE1lc3NhZ2VTdHJlYW1fMS5NZXNzYWdlU3RyZWFtLmNyZWF0ZU1lc3NhZ2UodGhpcywgYm9keSwgb3B0aW9ucyk7XG4gICAgfVxufVxuZXhwb3J0cy5NZXNzYWdlcyA9IE1lc3NhZ2VzO1xuY29uc3QgREVQUkVDQVRFRF9NT0RFTFMgPSB7XG4gICAgJ2NsYXVkZS0xLjMnOiAnTm92ZW1iZXIgNnRoLCAyMDI0JyxcbiAgICAnY2xhdWRlLTEuMy0xMDBrJzogJ05vdmVtYmVyIDZ0aCwgMjAyNCcsXG4gICAgJ2NsYXVkZS1pbnN0YW50LTEuMSc6ICdOb3ZlbWJlciA2dGgsIDIwMjQnLFxuICAgICdjbGF1ZGUtaW5zdGFudC0xLjEtMTAwayc6ICdOb3ZlbWJlciA2dGgsIDIwMjQnLFxuICAgICdjbGF1ZGUtaW5zdGFudC0xLjInOiAnTm92ZW1iZXIgNnRoLCAyMDI0Jyxcbn07XG4oZnVuY3Rpb24gKE1lc3NhZ2VzKSB7XG59KShNZXNzYWdlcyA9IGV4cG9ydHMuTWVzc2FnZXMgfHwgKGV4cG9ydHMuTWVzc2FnZXMgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWVzc2FnZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/resources/messages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/streaming.js":
/*!*****************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/streaming.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.readableStreamAsyncIterable = exports._decodeChunks = exports._iterSSEMessages = exports.Stream = void 0;\nconst index_1 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_shims/index.js\");\nconst error_1 = __webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/error.js\");\nconst core_1 = __webpack_require__(/*! @anthropic-ai/sdk/core */ \"(rsc)/./node_modules/@anthropic-ai/sdk/core.js\");\nconst error_2 = __webpack_require__(/*! @anthropic-ai/sdk/error */ \"(rsc)/./node_modules/@anthropic-ai/sdk/error.js\");\nclass Stream {\n    constructor(iterator, controller) {\n        this.iterator = iterator;\n        this.controller = controller;\n    }\n    static fromSSEResponse(response, controller) {\n        let consumed = false;\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const sse of _iterSSEMessages(response, controller)) {\n                    if (sse.event === 'completion') {\n                        try {\n                            yield JSON.parse(sse.data);\n                        }\n                        catch (e) {\n                            console.error(`Could not parse message into JSON:`, sse.data);\n                            console.error(`From chunk:`, sse.raw);\n                            throw e;\n                        }\n                    }\n                    if (sse.event === 'message_start' ||\n                        sse.event === 'message_delta' ||\n                        sse.event === 'message_stop' ||\n                        sse.event === 'content_block_start' ||\n                        sse.event === 'content_block_delta' ||\n                        sse.event === 'content_block_stop') {\n                        try {\n                            yield JSON.parse(sse.data);\n                        }\n                        catch (e) {\n                            console.error(`Could not parse message into JSON:`, sse.data);\n                            console.error(`From chunk:`, sse.raw);\n                            throw e;\n                        }\n                    }\n                    if (sse.event === 'ping') {\n                        continue;\n                    }\n                    if (sse.event === 'error') {\n                        throw error_2.APIError.generate(undefined, `SSE Error: ${sse.data}`, sse.data, (0, core_1.createResponseHeaders)(response.headers));\n                    }\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    /**\n     * Generates a Stream from a newline-separated ReadableStream\n     * where each item is a JSON value.\n     */\n    static fromReadableStream(readableStream, controller) {\n        let consumed = false;\n        async function* iterLines() {\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(readableStream);\n            for await (const chunk of iter) {\n                for (const line of lineDecoder.decode(chunk)) {\n                    yield line;\n                }\n            }\n            for (const line of lineDecoder.flush()) {\n                yield line;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const line of iterLines()) {\n                    if (done)\n                        continue;\n                    if (line)\n                        yield JSON.parse(line);\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    [Symbol.asyncIterator]() {\n        return this.iterator();\n    }\n    /**\n     * Splits the stream into two streams which can be\n     * independently read from at different speeds.\n     */\n    tee() {\n        const left = [];\n        const right = [];\n        const iterator = this.iterator();\n        const teeIterator = (queue) => {\n            return {\n                next: () => {\n                    if (queue.length === 0) {\n                        const result = iterator.next();\n                        left.push(result);\n                        right.push(result);\n                    }\n                    return queue.shift();\n                },\n            };\n        };\n        return [\n            new Stream(() => teeIterator(left), this.controller),\n            new Stream(() => teeIterator(right), this.controller),\n        ];\n    }\n    /**\n     * Converts this stream to a newline-separated ReadableStream of\n     * JSON stringified values in the stream\n     * which can be turned back into a Stream with `Stream.fromReadableStream()`.\n     */\n    toReadableStream() {\n        const self = this;\n        let iter;\n        const encoder = new TextEncoder();\n        return new index_1.ReadableStream({\n            async start() {\n                iter = self[Symbol.asyncIterator]();\n            },\n            async pull(ctrl) {\n                try {\n                    const { value, done } = await iter.next();\n                    if (done)\n                        return ctrl.close();\n                    const bytes = encoder.encode(JSON.stringify(value) + '\\n');\n                    ctrl.enqueue(bytes);\n                }\n                catch (err) {\n                    ctrl.error(err);\n                }\n            },\n            async cancel() {\n                await iter.return?.();\n            },\n        });\n    }\n}\nexports.Stream = Stream;\nasync function* _iterSSEMessages(response, controller) {\n    if (!response.body) {\n        controller.abort();\n        throw new error_1.AnthropicError(`Attempted to iterate over a response with no body`);\n    }\n    const sseDecoder = new SSEDecoder();\n    const lineDecoder = new LineDecoder();\n    const iter = readableStreamAsyncIterable(response.body);\n    for await (const sseChunk of iterSSEChunks(iter)) {\n        for (const line of lineDecoder.decode(sseChunk)) {\n            const sse = sseDecoder.decode(line);\n            if (sse)\n                yield sse;\n        }\n    }\n    for (const line of lineDecoder.flush()) {\n        const sse = sseDecoder.decode(line);\n        if (sse)\n            yield sse;\n    }\n}\nexports._iterSSEMessages = _iterSSEMessages;\n/**\n * Given an async iterable iterator, iterates over it and yields full\n * SSE chunks, i.e. yields when a double new-line is encountered.\n */\nasync function* iterSSEChunks(iterator) {\n    let data = new Uint8Array();\n    for await (const chunk of iterator) {\n        if (chunk == null) {\n            continue;\n        }\n        const binaryChunk = chunk instanceof ArrayBuffer ? new Uint8Array(chunk)\n            : typeof chunk === 'string' ? new TextEncoder().encode(chunk)\n                : chunk;\n        let newData = new Uint8Array(data.length + binaryChunk.length);\n        newData.set(data);\n        newData.set(binaryChunk, data.length);\n        data = newData;\n        let patternIndex;\n        while ((patternIndex = findDoubleNewlineIndex(data)) !== -1) {\n            yield data.slice(0, patternIndex);\n            data = data.slice(patternIndex);\n        }\n    }\n    if (data.length > 0) {\n        yield data;\n    }\n}\nfunction findDoubleNewlineIndex(buffer) {\n    // This function searches the buffer for the end patterns (\\r\\r, \\n\\n, \\r\\n\\r\\n)\n    // and returns the index right after the first occurrence of any pattern,\n    // or -1 if none of the patterns are found.\n    const newline = 0x0a; // \\n\n    const carriage = 0x0d; // \\r\n    for (let i = 0; i < buffer.length - 2; i++) {\n        if (buffer[i] === newline && buffer[i + 1] === newline) {\n            // \\n\\n\n            return i + 2;\n        }\n        if (buffer[i] === carriage && buffer[i + 1] === carriage) {\n            // \\r\\r\n            return i + 2;\n        }\n        if (buffer[i] === carriage &&\n            buffer[i + 1] === newline &&\n            i + 3 < buffer.length &&\n            buffer[i + 2] === carriage &&\n            buffer[i + 3] === newline) {\n            // \\r\\n\\r\\n\n            return i + 4;\n        }\n    }\n    return -1;\n}\nclass SSEDecoder {\n    constructor() {\n        this.event = null;\n        this.data = [];\n        this.chunks = [];\n    }\n    decode(line) {\n        if (line.endsWith('\\r')) {\n            line = line.substring(0, line.length - 1);\n        }\n        if (!line) {\n            // empty line and we didn't previously encounter any messages\n            if (!this.event && !this.data.length)\n                return null;\n            const sse = {\n                event: this.event,\n                data: this.data.join('\\n'),\n                raw: this.chunks,\n            };\n            this.event = null;\n            this.data = [];\n            this.chunks = [];\n            return sse;\n        }\n        this.chunks.push(line);\n        if (line.startsWith(':')) {\n            return null;\n        }\n        let [fieldname, _, value] = partition(line, ':');\n        if (value.startsWith(' ')) {\n            value = value.substring(1);\n        }\n        if (fieldname === 'event') {\n            this.event = value;\n        }\n        else if (fieldname === 'data') {\n            this.data.push(value);\n        }\n        return null;\n    }\n}\n/**\n * A re-implementation of httpx's `LineDecoder` in Python that handles incrementally\n * reading lines from text.\n *\n * https://github.com/encode/httpx/blob/920333ea98118e9cf617f246905d7b202510941c/httpx/_decoders.py#L258\n */\nclass LineDecoder {\n    constructor() {\n        this.buffer = [];\n        this.trailingCR = false;\n    }\n    decode(chunk) {\n        let text = this.decodeText(chunk);\n        if (this.trailingCR) {\n            text = '\\r' + text;\n            this.trailingCR = false;\n        }\n        if (text.endsWith('\\r')) {\n            this.trailingCR = true;\n            text = text.slice(0, -1);\n        }\n        if (!text) {\n            return [];\n        }\n        const trailingNewline = LineDecoder.NEWLINE_CHARS.has(text[text.length - 1] || '');\n        let lines = text.split(LineDecoder.NEWLINE_REGEXP);\n        // if there is a trailing new line then the last entry will be an empty\n        // string which we don't care about\n        if (trailingNewline) {\n            lines.pop();\n        }\n        if (lines.length === 1 && !trailingNewline) {\n            this.buffer.push(lines[0]);\n            return [];\n        }\n        if (this.buffer.length > 0) {\n            lines = [this.buffer.join('') + lines[0], ...lines.slice(1)];\n            this.buffer = [];\n        }\n        if (!trailingNewline) {\n            this.buffer = [lines.pop() || ''];\n        }\n        return lines;\n    }\n    decodeText(bytes) {\n        if (bytes == null)\n            return '';\n        if (typeof bytes === 'string')\n            return bytes;\n        // Node:\n        if (typeof Buffer !== 'undefined') {\n            if (bytes instanceof Buffer) {\n                return bytes.toString();\n            }\n            if (bytes instanceof Uint8Array) {\n                return Buffer.from(bytes).toString();\n            }\n            throw new error_1.AnthropicError(`Unexpected: received non-Uint8Array (${bytes.constructor.name}) stream chunk in an environment with a global \"Buffer\" defined, which this library assumes to be Node. Please report this error.`);\n        }\n        // Browser\n        if (typeof TextDecoder !== 'undefined') {\n            if (bytes instanceof Uint8Array || bytes instanceof ArrayBuffer) {\n                this.textDecoder ?? (this.textDecoder = new TextDecoder('utf8'));\n                return this.textDecoder.decode(bytes);\n            }\n            throw new error_1.AnthropicError(`Unexpected: received non-Uint8Array/ArrayBuffer (${bytes.constructor.name}) in a web platform. Please report this error.`);\n        }\n        throw new error_1.AnthropicError(`Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.`);\n    }\n    flush() {\n        if (!this.buffer.length && !this.trailingCR) {\n            return [];\n        }\n        const lines = [this.buffer.join('')];\n        this.buffer = [];\n        this.trailingCR = false;\n        return lines;\n    }\n}\n// prettier-ignore\nLineDecoder.NEWLINE_CHARS = new Set(['\\n', '\\r']);\nLineDecoder.NEWLINE_REGEXP = /\\r\\n|[\\n\\r]/g;\n/** This is an internal helper function that's just used for testing */\nfunction _decodeChunks(chunks) {\n    const decoder = new LineDecoder();\n    const lines = [];\n    for (const chunk of chunks) {\n        lines.push(...decoder.decode(chunk));\n    }\n    return lines;\n}\nexports._decodeChunks = _decodeChunks;\nfunction partition(str, delimiter) {\n    const index = str.indexOf(delimiter);\n    if (index !== -1) {\n        return [str.substring(0, index), delimiter, str.substring(index + delimiter.length)];\n    }\n    return [str, '', ''];\n}\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nfunction readableStreamAsyncIterable(stream) {\n    if (stream[Symbol.asyncIterator])\n        return stream;\n    const reader = stream.getReader();\n    return {\n        async next() {\n            try {\n                const result = await reader.read();\n                if (result?.done)\n                    reader.releaseLock(); // release lock when stream becomes closed\n                return result;\n            }\n            catch (e) {\n                reader.releaseLock(); // release lock when stream becomes errored\n                throw e;\n            }\n        },\n        async return() {\n            const cancelPromise = reader.cancel();\n            reader.releaseLock();\n            await cancelPromise;\n            return { done: true, value: undefined };\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n    };\n}\nexports.readableStreamAsyncIterable = readableStreamAsyncIterable;\n//# sourceMappingURL=streaming.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/streaming.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/uploads.js":
/*!***************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/uploads.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createForm = exports.multipartFormRequestOptions = exports.maybeMultipartFormRequestOptions = exports.isMultipartBody = exports.toFile = exports.isUploadable = exports.isBlobLike = exports.isFileLike = exports.isResponseLike = exports.fileFromPath = void 0;\nconst index_1 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_shims/index.js\");\nvar index_2 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/@anthropic-ai/sdk/_shims/index.js\");\nObject.defineProperty(exports, \"fileFromPath\", ({ enumerable: true, get: function () { return index_2.fileFromPath; } }));\nconst isResponseLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.url === 'string' &&\n    typeof value.blob === 'function';\nexports.isResponseLike = isResponseLike;\nconst isFileLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.name === 'string' &&\n    typeof value.lastModified === 'number' &&\n    (0, exports.isBlobLike)(value);\nexports.isFileLike = isFileLike;\n/**\n * The BlobLike type omits arrayBuffer() because @types/node-fetch@^2.6.4 lacks it; but this check\n * adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isBlobLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.size === 'number' &&\n    typeof value.type === 'string' &&\n    typeof value.text === 'function' &&\n    typeof value.slice === 'function' &&\n    typeof value.arrayBuffer === 'function';\nexports.isBlobLike = isBlobLike;\nconst isUploadable = (value) => {\n    return (0, exports.isFileLike)(value) || (0, exports.isResponseLike)(value) || (0, index_1.isFsReadStream)(value);\n};\nexports.isUploadable = isUploadable;\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nasync function toFile(value, name, options) {\n    // If it's a promise, resolve it.\n    value = await value;\n    // If we've been given a `File` we don't need to do anything\n    if ((0, exports.isFileLike)(value)) {\n        return value;\n    }\n    if ((0, exports.isResponseLike)(value)) {\n        const blob = await value.blob();\n        name || (name = new URL(value.url).pathname.split(/[\\\\/]/).pop() ?? 'unknown_file');\n        // we need to convert the `Blob` into an array buffer because the `Blob` class\n        // that `node-fetch` defines is incompatible with the web standard which results\n        // in `new File` interpreting it as a string instead of binary data.\n        const data = (0, exports.isBlobLike)(blob) ? [(await blob.arrayBuffer())] : [blob];\n        return new index_1.File(data, name, options);\n    }\n    const bits = await getBytes(value);\n    name || (name = getName(value) ?? 'unknown_file');\n    if (!options?.type) {\n        const type = bits[0]?.type;\n        if (typeof type === 'string') {\n            options = { ...options, type };\n        }\n    }\n    return new index_1.File(bits, name, options);\n}\nexports.toFile = toFile;\nasync function getBytes(value) {\n    let parts = [];\n    if (typeof value === 'string' ||\n        ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n        value instanceof ArrayBuffer) {\n        parts.push(value);\n    }\n    else if ((0, exports.isBlobLike)(value)) {\n        parts.push(await value.arrayBuffer());\n    }\n    else if (isAsyncIterableIterator(value) // includes Readable, ReadableStream, etc.\n    ) {\n        for await (const chunk of value) {\n            parts.push(chunk); // TODO, consider validating?\n        }\n    }\n    else {\n        throw new Error(`Unexpected data type: ${typeof value}; constructor: ${value?.constructor\n            ?.name}; props: ${propsForError(value)}`);\n    }\n    return parts;\n}\nfunction propsForError(value) {\n    const props = Object.getOwnPropertyNames(value);\n    return `[${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\nfunction getName(value) {\n    return (getStringFromMaybeBuffer(value.name) ||\n        getStringFromMaybeBuffer(value.filename) ||\n        // For fs.ReadStream\n        getStringFromMaybeBuffer(value.path)?.split(/[\\\\/]/).pop());\n}\nconst getStringFromMaybeBuffer = (x) => {\n    if (typeof x === 'string')\n        return x;\n    if (typeof Buffer !== 'undefined' && x instanceof Buffer)\n        return String(x);\n    return undefined;\n};\nconst isAsyncIterableIterator = (value) => value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\nconst isMultipartBody = (body) => body && typeof body === 'object' && body.body && body[Symbol.toStringTag] === 'MultipartBody';\nexports.isMultipartBody = isMultipartBody;\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nconst maybeMultipartFormRequestOptions = async (opts) => {\n    if (!hasUploadableValue(opts.body))\n        return opts;\n    const form = await (0, exports.createForm)(opts.body);\n    return (0, index_1.getMultipartRequestOptions)(form, opts);\n};\nexports.maybeMultipartFormRequestOptions = maybeMultipartFormRequestOptions;\nconst multipartFormRequestOptions = async (opts) => {\n    const form = await (0, exports.createForm)(opts.body);\n    return (0, index_1.getMultipartRequestOptions)(form, opts);\n};\nexports.multipartFormRequestOptions = multipartFormRequestOptions;\nconst createForm = async (body) => {\n    const form = new index_1.FormData();\n    await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n    return form;\n};\nexports.createForm = createForm;\nconst hasUploadableValue = (value) => {\n    if ((0, exports.isUploadable)(value))\n        return true;\n    if (Array.isArray(value))\n        return value.some(hasUploadableValue);\n    if (value && typeof value === 'object') {\n        for (const k in value) {\n            if (hasUploadableValue(value[k]))\n                return true;\n        }\n    }\n    return false;\n};\nconst addFormValue = async (form, key, value) => {\n    if (value === undefined)\n        return;\n    if (value == null) {\n        throw new TypeError(`Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`);\n    }\n    // TODO: make nested formats configurable\n    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n        form.append(key, String(value));\n    }\n    else if ((0, exports.isUploadable)(value)) {\n        const file = await toFile(value);\n        form.append(key, file);\n    }\n    else if (Array.isArray(value)) {\n        await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n    }\n    else if (typeof value === 'object') {\n        await Promise.all(Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)));\n    }\n    else {\n        throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`);\n    }\n};\n//# sourceMappingURL=uploads.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/uploads.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@anthropic-ai/sdk/version.js":
/*!***************************************************!*\
  !*** ./node_modules/@anthropic-ai/sdk/version.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.VERSION = void 0;\nexports.VERSION = '0.27.3'; // x-release-please-version\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxlQUFlO0FBQ2YsZUFBZSxhQUFhO0FBQzVCIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBhbnRocm9waWMtYWlcXHNka1xcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuVkVSU0lPTiA9IHZvaWQgMDtcbmV4cG9ydHMuVkVSU0lPTiA9ICcwLjI3LjMnOyAvLyB4LXJlbGVhc2UtcGxlYXNlLXZlcnNpb25cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@anthropic-ai/sdk/version.js\n");

/***/ })

};
;