'use client';

import { useEffect, useRef, useState } from 'react';
import { FaChartBar } from 'react-icons/fa';

export default function ChartsPage() {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const [data, setData] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const REMOTE_ACTION_URL = 'https://stage.ionm.copilot.zinniax.com';
  // const REMOTE_ACTION_URL = 'https://localhost:8001';
  // Store chart instance in a ref (not on the canvas element)
  const chartInstanceRef = useRef<any>(null);
  const link = REMOTE_ACTION_URL + '/api/user-token-usage';  
  
  useEffect(() => {
    fetch(link)
      .then(res => {
        if (!res.ok) throw new Error('Failed to fetch token usage');
        return res.json();
      })
      .then(data => {
        setData(data);
        setLoading(false);
        setError(null);
      })
      .catch(err => {
        setError(err.message);
        setLoading(false);
      });

    // Cleanup chart on unmount
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
      }
    };
  }, [link]);

  // Separate effect for chart creation after data is loaded
  useEffect(() => {
    if (!loading && !error && Object.keys(data).length > 0 && chartRef.current) {
      const Chart = require('chart.js/auto');
      
      // Destroy previous chart instance if exists
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
      }
      
      // Dynamically set canvas height based on number of users (e.g., 60px per user)
      const canvas = chartRef.current;
      if (canvas) {
        canvas.height = Math.max(400, Object.keys(data).length * 60);
      }
      
      chartInstanceRef.current = new Chart(chartRef.current, {
        type: 'bar',
        data: {
          labels: Object.keys(data),
          datasets: [{
            label: 'Tokens Used',
            data: Object.values(data),
            backgroundColor: [
              'rgba(56,189,248,0.7)',
              'rgba(251,113,133,0.7)',
              'rgba(132,204,22,0.7)',
              'rgba(168,85,247,0.7)',
              'rgba(253,224,71,0.7)',
              'rgba(244,63,94,0.7)'
            ],
            borderRadius: 8,
            borderSkipped: false,
          }]
        },
        options: {
          indexAxis: 'y', // horizontal bars!
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: '#e5e7eb' },
              ticks: { color: '#334155', font: { size: 18, weight: 'bold' } }
            },
            x: {
              grid: { color: '#e5e7eb' },
              ticks: { color: '#334155', font: { size: 16 } }
            }
          }
        }
      });
    }
  }, [data, loading, error]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
      <div className="flex items-center px-8 py-6 bg-white shadow-lg sticky top-0 z-10">
        <div className="bg-gradient-to-tr from-blue-500 to-indigo-400 p-3 rounded-full shadow-lg mr-4">
          <FaChartBar className="text-white text-2xl" />
        </div>
        <h2 className="text-3xl font-extrabold text-gray-800 tracking-tight">User Token Usage Dashboard</h2>
      </div>
      
      <div className="flex-1 flex flex-col items-center justify-start py-8">
        {loading && <p className="text-lg text-gray-500">Loading...</p>}
        {error && <p className="text-red-600">Error: {error}</p>}
        {!loading && !error && Object.keys(data).length === 0 && (
          <p className="text-gray-500">No token usage data available.</p>
        )}
        
        {/* The chart is now outside any fixed-height box, so the page scrolls */}
        {!loading && !error && Object.keys(data).length > 0 && (
          <div className="w-full flex flex-col items-center">
            <canvas
              ref={chartRef}
              width={1200}
              style={{
                maxWidth: '95vw',
                background: 'linear-gradient(to top right, #e0f2fe, #ede9fe)',
                borderRadius: '1.5rem',
                boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
                margin: '2rem 0',
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}