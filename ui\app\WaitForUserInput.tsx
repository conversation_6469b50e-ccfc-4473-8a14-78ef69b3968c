"use client";

import { useCoAgent, useCopilotAction } from "@copilotkit/react-core";
import { CopilotChat  } from "@copilotkit/react-ui";

export function WaitForUserInput() {
  useCopilotAction({
    name: "AskHuman",
    available: "remote",
    parameters: [
      {
        name: "question",
      },
    ],
    handler: async ({ question }) => {
      return window.prompt(question);
    },
  });
  const { state : agentState } = useCoAgent({
    name: "scheduling_agent",
    configurable: {
      thread_id: localStorage.getItem("session_id"),
    },
    
  })
  console.log('AGENT_STATE', agentState);
  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <div className="text-2xl">LangGraph Wait For User Input Example</div>
      <div className="text-xs">
        (https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/wait-user-input/#agent)
      </div>
      <div>
        Use the search tool to ask the user where they are, then look up the
        case there
      </div>

      <CopilotChat >
        </CopilotChat> 
    </div>
  );
}
