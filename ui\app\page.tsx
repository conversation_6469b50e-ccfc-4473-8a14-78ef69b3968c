// page.tsx

"use client";

import { useEffect, useState, useRef } from "react";
import { CopilotKit } from "@copilotkit/react-core";
import "@copilotkit/react-ui/styles.css";
import { CopilotChat } from "@copilotkit/react-ui";
import ChatArea from "@/components/ChatArea";
import { v4 as uuidv4 } from "uuid";
import { decodeJwt } from './helper';
import { TextMessage, Role } from "@copilotkit/runtime-client-gql";
import { useCopilotChat } from "@copilotkit/react-core";

const prompts = [
  "Cancel the case for <PERSON><PERSON>",
  "Reschedule case 106258",
  "Schedule a case for <PERSON><PERSON><PERSON>",
  "Reopen case 106188",
  "<PERSON><PERSON> has a spine surgery rescheduled"
];

function PromptButtons({ onUserInteracted, animateOut, onAnimationEnd }: { onUserInteracted: () => void, animateOut: boolean, onAnimationEnd: () => void }) {
  const { appendMessage } = useCopilotChat();
  function handlePromptClick(prompt: string) {
    appendMessage(new TextMessage({ content: prompt, role: Role.User }));
    onUserInteracted();
  }

const buttonClass = "rounded-full px-4 py-3 text-blue-700 shadow-sm border-[1.5px] border-blue-300 transition-all duration-200 max-w-xs";

  return (
    <div
      className={`flex flex-col gap-3 items-center transition-opacity duration-500 ${animateOut ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
      onTransitionEnd={onAnimationEnd}
    >
      <div className="flex flex-wrap gap-3 justify-center">
        {prompts.map((prompt, idx) => (
          <button
            key={idx}
            className={buttonClass}
            onClick={() => handlePromptClick(prompt)}
          >
            {prompt}
          </button>
        ))}
      </div>
    </div>
  );
}

function Greeting({ animateOut, onAnimationEnd }: { animateOut: boolean, onAnimationEnd: () => void }) {
  return (
    <div
      className={`flex flex-col items-center text-center transition-opacity duration-500 ${animateOut ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
      onTransitionEnd={onAnimationEnd}
    >
      <h2 className="text-4xl  font-bold text-blue-600 text-center">Hello there 👋</h2>
      <h3 className="text-xl text-gray-700 mt-2 mb-3">How can I assist you today ?</h3>
      {/* <p className="text-sm text-gray-500">You can pick a prompt or ask your own question below.</p> */}
    </div>
  );
}


export default function Home() {
  const [loading, setLoading] = useState(true);
  const [username, setUsername] = useState<string | null>(null);
  const [userInteracted, setUserInteracted] = useState(false);
  const [hideSuggestions, setHideSuggestions] = useState(false);
  const [suggestionAnimationDone, setSuggestionAnimationDone] = useState(false);
  const copilotChatRef = useRef<any>(null);

  // Listen for manual chat input to trigger suggestion removal
  const { appendMessage } = useCopilotChat();
  useEffect(() => {
    // Patch CopilotChat input to detect manual user message
    const handler = (e: any) => {
      if (!userInteracted && e?.detail?.role === 'user') {
        setUserInteracted(true);
      }
    };
    window.addEventListener('copilotkit-user-message', handler);
    return () => window.removeEventListener('copilotkit-user-message', handler);
  }, [userInteracted]);

  useEffect(() => {
  const observer = new MutationObserver(() => {
    const inputEl = document.querySelector<HTMLTextAreaElement>('.copilotKitInputContainer textarea');
    if (inputEl) {
      const handleInput = () => {
        if (!userInteracted) {
          setUserInteracted(true);
          const customEvent = new CustomEvent("copilotkit-user-message", {
            detail: { role: 'user' }
          });
          window.dispatchEvent(customEvent);
        }
      };

      inputEl.addEventListener('keydown', handleInput);

      observer.disconnect(); // stop observing once found
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  return () => observer.disconnect();
}, [userInteracted]);


  



  useEffect(() => {
    const REMOTE_ACTION_URL = 'https://stage.ionm.copilot.zinniax.com'
    // const REMOTE_ACTION_URL = 'http://localhost:8001'
    console.log("NEXT_PUBLIC_REMOTE_ACTION_URL", REMOTE_ACTION_URL);
    // --- Session ID Management ---
    let sessionId = localStorage.getItem("session_id");
    if (!sessionId) {
      sessionId = uuidv4();
      localStorage.setItem("session_id", sessionId);
      document.cookie = `session_id=${sessionId}; path=/;`;
    }
    const params = new URLSearchParams(window.location.search);
    window.history.replaceState({}, document.title, "/");
    const token = params.get("token");
    if (!token) {
      window.location.href = "https://stage.ionm.zinniax.com/";
      setLoading(false);
      return;
    }
    if (token) {
      const result = decodeJwt(token);
      if (result) {
        const email = result.payload['sub'];
        if (email) {
          let username = email.split('@')[0];
          let words = username.replace(/\./g, ' ').split(' ');
          username = words.map((word: string) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ');
          localStorage.setItem("copilotkit_username", username);
          setUsername(username);
        }
      }
    }
    if (token) {
      localStorage.setItem("copilotkit_token", token);
      const link = REMOTE_ACTION_URL + '/api/store-token';
      fetch(link, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ token, sessionId }),
        credentials: "include",
      })
        .then(() => setLoading(false));
    }
    const storedUsername = localStorage.getItem("copilotkit_username");
    if (storedUsername) {
      setUsername(storedUsername);
    }
  }, []);

  // When user interacts, start fade-out
  useEffect(() => {
    if (userInteracted) setHideSuggestions(true);
  }, [userInteracted]);

  useEffect(() => {
    const interval = setInterval(() => {
      const footer = document.querySelector(".copilotKitInputContainer .poweredBy");
      const container = document.querySelector(".copilotKitInputContainer");

      if (footer && container) {
        // Prevent adding multiple overlays
        if (!container.querySelector(".poweredByCover")) {
          const overlay = document.createElement("div");
          overlay.className = "poweredByCover";
          Object.assign(overlay.style, {
            position: "absolute",
            top: "0",
            left: "0",
            right: "0",
            bottom: "0",
            background: "white", // match page bg
            zIndex: "2",
          });

          const wrapper = document.createElement("div");
          wrapper.style.position = "relative";
          wrapper.appendChild(overlay);
          footer.parentElement?.insertBefore(wrapper, footer);
          wrapper.appendChild(footer);
        }

        clearInterval(interval); // once done, stop checking
      }
    }, 300); // Wait for CopilotChat to fully mount

    return () => clearInterval(interval);
  }, []);

  // When fade-out animation ends, unmount suggestions
  function handleSuggestionAnimationEnd() {
    if (hideSuggestions) setSuggestionAnimationDone(true);
  }

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-10 w-10 border-t-4 border-blue-500 border-solid"></div>
        <p className="mt-4 text-gray-600 text-sm">Verifying session, please wait...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen w-screen bg-white">
      {/* Fixed Logo */}
      <img src="/logo.png" alt="ZinniaX Logo" className="fixed top-4 left-4 h-10 w-auto " />
      {/* Unified header area */}
      <div className="flex flex-col items-center pt-8 pb-2 px-4">
        <div className="flex flex-col items-center mb-2">
          <h1 className="text-3xl font-bold text-blue-600 text-center">ZinniaX Copilot</h1>
        </div>
        <span className="text-md text-gray-500 mb-2 text-center">Your Everyday Copilot</span>
        {/* Greeting and Suggestions, animated */}
        {/* Greeting and Suggestions, centered and unified */}
        {!suggestionAnimationDone && (
          <div
            className="w-full flex flex-col items-center justify-center gap-4 px-4"
            style={{ paddingTop: '150px' }}
          >
            <Greeting
              animateOut={hideSuggestions}
              onAnimationEnd={handleSuggestionAnimationEnd}
            />
            <PromptButtons
              onUserInteracted={() => setUserInteracted(true)}
              animateOut={hideSuggestions}
              onAnimationEnd={handleSuggestionAnimationEnd}
            />
          </div>
        )}

      </div>
      {/* Chat Area - centered and constrained to 60% width */}
      <main className="flex-1 flex flex-col min-h-0 overflow-hidden items-center">
        <div className="flex flex-col flex-1 min-h-0 overflow-hidden w-full max-w-4xl" style={{ width: '60vw', minWidth: 320 }}>
          <CopilotChat
            labels={{
              title: "ZinniaX Copilot",
              initial: "",
              placeholder: "Ask Zinniax Copilot...."
            }}
            className="flex flex-col flex-1 min-h-0 overflow-hidden copilot-chat-with-suggestions"
          />
          <ChatArea />
        </div>
      </main>
    </div>
  );
}
