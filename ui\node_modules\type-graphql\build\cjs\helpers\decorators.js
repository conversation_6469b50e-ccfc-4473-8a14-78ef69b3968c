"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getArrayFromOverloadedRest = exports.getNameDecoratorParams = exports.getTypeDecoratorParams = void 0;
function getTypeDecoratorParams(returnTypeFuncOrOptions, maybeOptions) {
    if (typeof returnTypeFuncOrOptions === "function") {
        return {
            returnTypeFunc: returnTypeFuncOrOptions,
            options: maybeOptions || {},
        };
    }
    return {
        options: returnTypeFuncOrOptions || {},
    };
}
exports.getTypeDecoratorParams = getTypeDecoratorParams;
function getNameDecoratorParams(nameOrOptions, maybeOptions) {
    if (typeof nameOrOptions === "string") {
        return {
            name: nameOrOptions,
            options: maybeOptions || {},
        };
    }
    return {
        options: nameOrOptions || {},
    };
}
exports.getNameDecoratorParams = getNameDecoratorParams;
function getArrayFromOverloadedRest(overloadedArray) {
    let items;
    if (Array.isArray(overloadedArray[0])) {
        items = overloadedArray[0];
    }
    else {
        items = overloadedArray;
    }
    return items;
}
exports.getArrayFromOverloadedRest = getArrayFromOverloadedRest;
