/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/graphql-yoga";
exports.ids = ["vendor-chunks/graphql-yoga"];
exports.modules = {

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/error.js":
/*!************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isGraphQLError = isGraphQLError;\nexports.isOriginalGraphQLError = isOriginalGraphQLError;\nexports.isAbortError = isAbortError;\nexports.handleError = handleError;\nexports.getResponseInitByRespectingErrors = getResponseInitByRespectingErrors;\nexports.areGraphQLErrors = areGraphQLErrors;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nfunction isAggregateError(obj) {\n    return obj != null && typeof obj === 'object' && 'errors' in obj;\n}\nfunction hasToString(obj) {\n    return obj != null && typeof obj.toString === 'function';\n}\nfunction isGraphQLError(val) {\n    return val instanceof graphql_1.GraphQLError;\n}\nfunction isOriginalGraphQLError(val) {\n    if (val instanceof graphql_1.GraphQLError) {\n        if (val.originalError != null) {\n            return isOriginalGraphQLError(val.originalError);\n        }\n        return true;\n    }\n    return false;\n}\nfunction isAbortError(error) {\n    return (typeof error === 'object' &&\n        error?.constructor?.name === 'DOMException' &&\n        (error.name === 'AbortError' ||\n            error.name === 'TimeoutError'));\n}\nfunction handleError(error, maskedErrorsOpts, logger) {\n    const errors = new Set();\n    if (isAggregateError(error)) {\n        for (const singleError of error.errors) {\n            const handledErrors = handleError(singleError, maskedErrorsOpts, logger);\n            for (const handledError of handledErrors) {\n                errors.add(handledError);\n            }\n        }\n    }\n    else if (isAbortError(error)) {\n        logger.debug('Request aborted');\n    }\n    else if (maskedErrorsOpts) {\n        const maskedError = maskedErrorsOpts.maskError(error, maskedErrorsOpts.errorMessage, maskedErrorsOpts.isDev);\n        if (maskedError !== error) {\n            logger.error(error);\n        }\n        errors.add(isGraphQLError(maskedError)\n            ? maskedError\n            : (0, utils_1.createGraphQLError)(maskedError.message, {\n                originalError: maskedError,\n            }));\n    }\n    else if (isGraphQLError(error)) {\n        errors.add(error);\n    }\n    else if (error instanceof Error) {\n        errors.add((0, utils_1.createGraphQLError)(error.message, {\n            originalError: error,\n        }));\n    }\n    else if (typeof error === 'string') {\n        errors.add((0, utils_1.createGraphQLError)(error, {\n            extensions: {\n                code: 'INTERNAL_SERVER_ERROR',\n                unexpected: true,\n            },\n        }));\n    }\n    else if (hasToString(error)) {\n        errors.add((0, utils_1.createGraphQLError)(error.toString(), {\n            extensions: {\n                code: 'INTERNAL_SERVER_ERROR',\n                unexpected: true,\n            },\n        }));\n    }\n    else {\n        logger.error(error);\n        errors.add((0, utils_1.createGraphQLError)('Unexpected error.', {\n            extensions: {\n                http: {\n                    unexpected: true,\n                },\n            },\n        }));\n    }\n    return Array.from(errors);\n}\nfunction getResponseInitByRespectingErrors(result, headers = {}, isApplicationJson = false) {\n    let status;\n    let unexpectedErrorExists = false;\n    if ('extensions' in result && result.extensions?.http) {\n        if (result.extensions.http.headers) {\n            Object.assign(headers, result.extensions.http.headers);\n        }\n        if (result.extensions.http.status) {\n            status = result.extensions.http.status;\n        }\n    }\n    if ('errors' in result && result.errors?.length) {\n        for (const error of result.errors) {\n            if (error.extensions?.['http']) {\n                if (error.extensions['http'].headers) {\n                    Object.assign(headers, error.extensions['http'].headers);\n                }\n                if (isApplicationJson && error.extensions['http'].spec) {\n                    continue;\n                }\n                if (error.extensions['http'].status &&\n                    (!status || error.extensions['http'].status > status)) {\n                    status = error.extensions['http'].status;\n                }\n            }\n            else if (!isOriginalGraphQLError(error) || error.extensions?.['unexpected']) {\n                unexpectedErrorExists = true;\n            }\n        }\n    }\n    else {\n        status ||= 200;\n    }\n    if (!status) {\n        if (unexpectedErrorExists && !('data' in result)) {\n            status = 500;\n        }\n        else {\n            status = 200;\n        }\n    }\n    return {\n        status,\n        headers,\n    };\n}\nfunction areGraphQLErrors(obj) {\n    return (Array.isArray(obj) &&\n        obj.length > 0 &&\n        // if one item in the array is a GraphQLError, we're good\n        obj.some(isGraphQLError));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/graphiql-html.js":
/*!********************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/graphiql-html.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports[\"default\"] = \"<!doctype html><html lang=en><head><meta charset=utf-8><title>__TITLE__</title><link rel=icon href=https://raw.githubusercontent.com/graphql-hive/graphql-yoga/main/website/public/favicon.ico><link crossorigin rel=stylesheet href=https://unpkg.com/@graphql-yoga/graphiql@4.3.5/dist/graphiql.css></head><body id=body class=no-focus-outline><noscript>You need to enable JavaScript to run this app.</noscript><div id=root></div><script type=module>import{renderYogaGraphiQL}from\\\"https://unpkg.com/@graphql-yoga/graphiql@4.3.5/dist/yoga-graphiql.es.js\\\";renderYogaGraphiQL(root,__OPTS__)</script></body></html>\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9ncmFwaGlxbC1odG1sLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGtCQUFlLHVjQUF1YyxtQkFBbUIsZ0ZBQWdGIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGdyYXBocWwteW9nYVxcY2pzXFxncmFwaGlxbC1odG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5kZWZhdWx0ID0gXCI8IWRvY3R5cGUgaHRtbD48aHRtbCBsYW5nPWVuPjxoZWFkPjxtZXRhIGNoYXJzZXQ9dXRmLTg+PHRpdGxlPl9fVElUTEVfXzwvdGl0bGU+PGxpbmsgcmVsPWljb24gaHJlZj1odHRwczovL3Jhdy5naXRodWJ1c2VyY29udGVudC5jb20vZ3JhcGhxbC1oaXZlL2dyYXBocWwteW9nYS9tYWluL3dlYnNpdGUvcHVibGljL2Zhdmljb24uaWNvPjxsaW5rIGNyb3Nzb3JpZ2luIHJlbD1zdHlsZXNoZWV0IGhyZWY9aHR0cHM6Ly91bnBrZy5jb20vQGdyYXBocWwteW9nYS9ncmFwaGlxbEA0LjMuNS9kaXN0L2dyYXBoaXFsLmNzcz48L2hlYWQ+PGJvZHkgaWQ9Ym9keSBjbGFzcz1uby1mb2N1cy1vdXRsaW5lPjxub3NjcmlwdD5Zb3UgbmVlZCB0byBlbmFibGUgSmF2YVNjcmlwdCB0byBydW4gdGhpcyBhcHAuPC9ub3NjcmlwdD48ZGl2IGlkPXJvb3Q+PC9kaXY+PHNjcmlwdCB0eXBlPW1vZHVsZT5pbXBvcnR7cmVuZGVyWW9nYUdyYXBoaVFMfWZyb21cXFwiaHR0cHM6Ly91bnBrZy5jb20vQGdyYXBocWwteW9nYS9ncmFwaGlxbEA0LjMuNS9kaXN0L3lvZ2EtZ3JhcGhpcWwuZXMuanNcXFwiO3JlbmRlcllvZ2FHcmFwaGlRTChyb290LF9fT1BUU19fKTwvc2NyaXB0PjwvYm9keT48L2h0bWw+XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/graphiql-html.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/index.js":
/*!************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DisposableSymbols = exports.useExecutionCancellation = exports.processRegularResult = exports.getSSEProcessor = exports.mapMaybePromise = exports.isPromise = exports.createGraphQLError = exports.composeInstrumentation = exports.chain = exports.getInstrumentationAndPlugin = exports.usePayloadFormatter = exports.useLogger = exports.useExtendContext = exports.useErrorHandler = exports.useEnvelop = exports.mapAsyncIterator = exports.makeSubscribe = exports.makeExecute = exports.isIntrospectionOperationString = exports.isAsyncIterable = exports.handleStreamOrSingleExecutionResult = exports.finalAsyncIterator = exports.errorAsyncIterator = exports.envelop = exports.mergeSchemas = exports.createLRUCache = exports._createLRUCache = exports.maskError = exports.useSchema = exports.useReadinessCheck = exports.shouldRenderGraphiQL = exports.renderGraphiQL = void 0;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\ntslib_1.__exportStar(__webpack_require__(/*! @graphql-yoga/logger */ \"(rsc)/./node_modules/@graphql-yoga/logger/cjs/index.js\"), exports);\nvar use_graphiql_js_1 = __webpack_require__(/*! ./plugins/use-graphiql.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-graphiql.js\");\nObject.defineProperty(exports, \"renderGraphiQL\", ({ enumerable: true, get: function () { return use_graphiql_js_1.renderGraphiQL; } }));\nObject.defineProperty(exports, \"shouldRenderGraphiQL\", ({ enumerable: true, get: function () { return use_graphiql_js_1.shouldRenderGraphiQL; } }));\nvar use_readiness_check_js_1 = __webpack_require__(/*! ./plugins/use-readiness-check.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-readiness-check.js\");\nObject.defineProperty(exports, \"useReadinessCheck\", ({ enumerable: true, get: function () { return use_readiness_check_js_1.useReadinessCheck; } }));\nvar use_schema_js_1 = __webpack_require__(/*! ./plugins/use-schema.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-schema.js\");\nObject.defineProperty(exports, \"useSchema\", ({ enumerable: true, get: function () { return use_schema_js_1.useSchema; } }));\ntslib_1.__exportStar(__webpack_require__(/*! ./schema.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/schema.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./server.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/server.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./subscription.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/subscription.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/types.js\"), exports);\nvar mask_error_js_1 = __webpack_require__(/*! ./utils/mask-error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/utils/mask-error.js\");\nObject.defineProperty(exports, \"maskError\", ({ enumerable: true, get: function () { return mask_error_js_1.maskError; } }));\nvar create_lru_cache_js_1 = __webpack_require__(/*! ./utils/create-lru-cache.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/utils/create-lru-cache.js\");\nObject.defineProperty(exports, \"_createLRUCache\", ({ enumerable: true, get: function () { return create_lru_cache_js_1._createLRUCache; } }));\nObject.defineProperty(exports, \"createLRUCache\", ({ enumerable: true, get: function () { return create_lru_cache_js_1.createLRUCache; } }));\nvar schema_1 = __webpack_require__(/*! @graphql-tools/schema */ \"(rsc)/./node_modules/@graphql-tools/schema/cjs/index.js\");\nObject.defineProperty(exports, \"mergeSchemas\", ({ enumerable: true, get: function () { return schema_1.mergeSchemas; } }));\nvar core_1 = __webpack_require__(/*! @envelop/core */ \"(rsc)/./node_modules/@envelop/core/cjs/index.js\");\n// useful for anyone creating a new envelop instance\nObject.defineProperty(exports, \"envelop\", ({ enumerable: true, get: function () { return core_1.envelop; } }));\nObject.defineProperty(exports, \"errorAsyncIterator\", ({ enumerable: true, get: function () { return core_1.errorAsyncIterator; } }));\nObject.defineProperty(exports, \"finalAsyncIterator\", ({ enumerable: true, get: function () { return core_1.finalAsyncIterator; } }));\nObject.defineProperty(exports, \"handleStreamOrSingleExecutionResult\", ({ enumerable: true, get: function () { return core_1.handleStreamOrSingleExecutionResult; } }));\nObject.defineProperty(exports, \"isAsyncIterable\", ({ enumerable: true, get: function () { return core_1.isAsyncIterable; } }));\n// useful helpers\nObject.defineProperty(exports, \"isIntrospectionOperationString\", ({ enumerable: true, get: function () { return core_1.isIntrospectionOperationString; } }));\nObject.defineProperty(exports, \"makeExecute\", ({ enumerable: true, get: function () { return core_1.makeExecute; } }));\nObject.defineProperty(exports, \"makeSubscribe\", ({ enumerable: true, get: function () { return core_1.makeSubscribe; } }));\nObject.defineProperty(exports, \"mapAsyncIterator\", ({ enumerable: true, get: function () { return core_1.mapAsyncIterator; } }));\n// Default plugins\nObject.defineProperty(exports, \"useEnvelop\", ({ enumerable: true, get: function () { return core_1.useEnvelop; } }));\nObject.defineProperty(exports, \"useErrorHandler\", ({ enumerable: true, get: function () { return core_1.useErrorHandler; } }));\nObject.defineProperty(exports, \"useExtendContext\", ({ enumerable: true, get: function () { return core_1.useExtendContext; } }));\nObject.defineProperty(exports, \"useLogger\", ({ enumerable: true, get: function () { return core_1.useLogger; } }));\nObject.defineProperty(exports, \"usePayloadFormatter\", ({ enumerable: true, get: function () { return core_1.usePayloadFormatter; } }));\nvar instrumentation_1 = __webpack_require__(/*! @envelop/instrumentation */ \"(rsc)/./node_modules/@envelop/instrumentation/cjs/index.js\");\nObject.defineProperty(exports, \"getInstrumentationAndPlugin\", ({ enumerable: true, get: function () { return instrumentation_1.getInstrumentationAndPlugin; } }));\nObject.defineProperty(exports, \"chain\", ({ enumerable: true, get: function () { return instrumentation_1.chain; } }));\nObject.defineProperty(exports, \"composeInstrumentation\", ({ enumerable: true, get: function () { return instrumentation_1.composeInstrumentation; } }));\nvar utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nObject.defineProperty(exports, \"createGraphQLError\", ({ enumerable: true, get: function () { return utils_1.createGraphQLError; } }));\nObject.defineProperty(exports, \"isPromise\", ({ enumerable: true, get: function () { return utils_1.isPromise; } }));\nObject.defineProperty(exports, \"mapMaybePromise\", ({ enumerable: true, get: function () { return utils_1.mapMaybePromise; } }));\nvar sse_js_1 = __webpack_require__(/*! ./plugins/result-processor/sse.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/sse.js\");\nObject.defineProperty(exports, \"getSSEProcessor\", ({ enumerable: true, get: function () { return sse_js_1.getSSEProcessor; } }));\nvar regular_js_1 = __webpack_require__(/*! ./plugins/result-processor/regular.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/regular.js\");\nObject.defineProperty(exports, \"processRegularResult\", ({ enumerable: true, get: function () { return regular_js_1.processRegularResult; } }));\nvar use_execution_cancellation_js_1 = __webpack_require__(/*! ./plugins/use-execution-cancellation.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-execution-cancellation.js\");\nObject.defineProperty(exports, \"useExecutionCancellation\", ({ enumerable: true, get: function () { return use_execution_cancellation_js_1.useExecutionCancellation; } }));\nvar server_1 = __webpack_require__(/*! @whatwg-node/server */ \"(rsc)/./node_modules/@whatwg-node/server/cjs/index.js\");\nObject.defineProperty(exports, \"DisposableSymbols\", ({ enumerable: true, get: function () { return server_1.DisposableSymbols; } }));\ntslib_1.__exportStar(__webpack_require__(/*! @envelop/instrumentation */ \"(rsc)/./node_modules/@envelop/instrumentation/cjs/index.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/landing-page-html.js":
/*!************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/landing-page-html.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports[\"default\"] = \"<!doctype html><html lang=en><head><meta charset=utf-8><title>Welcome to GraphQL Yoga</title><link rel=icon href=https://raw.githubusercontent.com/graphql-hive/graphql-yoga/main/website/public/favicon.ico><style>body,html{padding:0;margin:0;height:100%;font-family:Inter,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Oxygen,Ubuntu,Cantarell,'Fira Sans','Droid Sans','Helvetica Neue',sans-serif;color:#fff;background-color:#000}main>section.hero{display:flex;height:90vh;justify-content:center;align-items:center;flex-direction:column}.logo{display:flex;align-items:center}.buttons{margin-top:24px}h1{font-size:80px}h2{color:#888;max-width:50%;margin-top:0;text-align:center}a{color:#fff;text-decoration:none;margin-left:10px;margin-right:10px;font-weight:700;transition:color .3s ease;padding:4px;overflow:visible}a.graphiql:hover{color:rgba(255,0,255,.7)}a.docs:hover{color:rgba(28,200,238,.7)}a.tutorial:hover{color:rgba(125,85,245,.7)}svg{margin-right:24px}.not-what-your-looking-for{margin-top:5vh}.not-what-your-looking-for>*{margin-left:auto;margin-right:auto}.not-what-your-looking-for>p{text-align:center}.not-what-your-looking-for>h2{color:#464646}.not-what-your-looking-for>p{max-width:600px;line-height:1.3em}.not-what-your-looking-for>pre{max-width:300px}</style></head><body id=body><main><section class=hero><div class=logo><div><svg xmlns=http://www.w3.org/2000/svg viewBox=\\\"-0.41 0.445 472.812 499.811\\\" height=150><defs><linearGradient id=paint0_linear_1677_11483 x1=16 y1=14 x2=87.2132 y2=44.5982 gradientUnits=userSpaceOnUse gradientTransform=\\\"matrix(8.139854, 0, 0, 8.139854, -130.346407, -113.25101)\\\"><stop stop-color=#7433FF /><stop offset=1 stop-color=#FFA3FD /></linearGradient><linearGradient id=paint1_linear_1677_11483 x1=16 y1=14 x2=87.2132 y2=44.5982 gradientUnits=userSpaceOnUse gradientTransform=\\\"matrix(8.139854, 0, 0, 8.139854, -130.346407, -113.25101)\\\"><stop stop-color=#7433FF /><stop offset=1 stop-color=#FFA3FD /></linearGradient><linearGradient id=paint2_linear_1677_11483 x1=16 y1=14 x2=87.2132 y2=44.5982 gradientUnits=userSpaceOnUse gradientTransform=\\\"matrix(8.139854, 0, 0, 8.139854, -130.346407, -113.25101)\\\"><stop stop-color=#7433FF /><stop offset=1 stop-color=#FFA3FD /></linearGradient><linearGradient id=paint3_linear_1677_11483 x1=16 y1=14 x2=87.2132 y2=44.5982 gradientUnits=userSpaceOnUse><stop stop-color=#7433FF /><stop offset=1 stop-color=#FFA3FD /></linearGradient><linearGradient id=paint4_linear_1677_11483 x1=16 y1=14 x2=87.2132 y2=44.5982 gradientUnits=userSpaceOnUse><stop stop-color=#7433FF /><stop offset=1 stop-color=#FFA3FD /></linearGradient><linearGradient id=paint5_linear_1677_11483 x1=16 y1=14 x2=87.2132 y2=44.5982 gradientUnits=userSpaceOnUse><stop stop-color=#7433FF /><stop offset=1 stop-color=#FFA3FD /></linearGradient><filter id=filter0_f_1677_11483 x=23 y=-25 width=100 height=100 filterUnits=userSpaceOnUse color-interpolation-filters=sRGB><feFlood flood-opacity=0 result=BackgroundImageFix /><feBlend mode=normal in=SourceGraphic in2=BackgroundImageFix result=shape /><feGaussianBlur stdDeviation=12 result=effect1_foregroundBlur_1677_11483 /></filter><filter id=filter1_f_1677_11483 x=-24 y=19 width=100 height=100 filterUnits=userSpaceOnUse color-interpolation-filters=sRGB><feFlood flood-opacity=0 result=BackgroundImageFix /><feBlend mode=normal in=SourceGraphic in2=BackgroundImageFix result=shape /><feGaussianBlur stdDeviation=12 result=effect1_foregroundBlur_1677_11483 /></filter><linearGradient id=paint6_linear_1677_11483 x1=30 y1=28 x2=66.1645 y2=44.4363 gradientUnits=userSpaceOnUse gradientTransform=\\\"matrix(8.139854, 0, 0, 8.139854, -130.346407, -113.25101)\\\"><stop stop-color=#7433FF /><stop offset=1 stop-color=#FFA3FD /></linearGradient><filter id=filter2_f_1677_11483 x=-12 y=-44 width=100 height=100 filterUnits=userSpaceOnUse color-interpolation-filters=sRGB><feFlood flood-opacity=0 result=BackgroundImageFix /><feBlend mode=normal in=SourceGraphic in2=BackgroundImageFix result=shape /><feGaussianBlur stdDeviation=12 result=effect1_foregroundBlur_1677_11483 /></filter><filter id=filter3_f_1677_11483 x=13 y=19 width=100 height=100 filterUnits=userSpaceOnUse color-interpolation-filters=sRGB><feFlood flood-opacity=0 result=BackgroundImageFix /><feBlend mode=normal in=SourceGraphic in2=BackgroundImageFix result=shape /><feGaussianBlur stdDeviation=12 result=effect1_foregroundBlur_1677_11483 /></filter></defs><mask id=mask0_1677_11483 style=mask-type:alpha maskUnits=userSpaceOnUse x=16 y=14 width=58 height=62><path d=\\\"M21 25.3501C21.7279 25.3501 22.4195 25.5056 23.0433 25.7853L42.1439 14.8C43.0439 14.3 44.1439 14 45.1439 14C46.2439 14 47.2439 14.3 48.1439 14.8L64.5439 24.3C63.3439 25.1 62.4439 26.3 61.8439 27.7L45.9438 18.5C45.6439 18.3 45.344 18.3 45.0441 18.3C44.7441 18.3 44.4439 18.4 44.1439 18.5L25.8225 29.0251C25.9382 29.4471 26 29.8914 26 30.3501C26 33.1115 23.7614 35.3501 21 35.3501C18.2386 35.3501 16 33.1115 16 30.3501C16 27.5887 18.2386 25.3501 21 25.3501Z\\\" fill=url(#paint3_linear_1677_11483) /><path d=\\\"M67.2438 35.0329C65.3487 34.3219 64 32.4934 64 30.35C64 27.5886 66.2386 25.35 69 25.35C71.7614 25.35 74 27.5886 74 30.35C74 32.1825 73.0142 33.7848 71.5439 34.6554V55.2C71.5439 57.4 70.3439 59.4 68.5439 60.5L52.1439 69.9C52.1439 68.4 51.6438 66.9 50.7438 65.8L66.3439 56.8C66.9439 56.5 67.2438 55.9 67.2438 55.2V35.0329Z\\\" fill=url(#paint4_linear_1677_11483) /><path d=\\\"M49.8439 69.1055C49.9458 69.5034 50 69.9204 50 70.3501C50 73.1115 47.7614 75.3501 45 75.3501C42.5102 75.3501 40.4454 73.5302 40.0633 71.1481L21.8439 60.6C19.9439 59.5 18.8439 57.5 18.8439 55.3V36.8C19.5439 37 20.3439 37.2 21.0439 37.2C21.7439 37.2 22.4439 37.1 23.0439 36.9V55.3C23.0439 56 23.4438 56.6 23.9438 56.9L41.3263 66.9583C42.2398 65.9694 43.5476 65.3501 45 65.3501C47.3291 65.3501 49.2862 66.9426 49.8419 69.0981L49.8436 69.0997L49.8439 69.1055Z\\\" fill=url(#paint5_linear_1677_11483) /></mask><mask id=mask1_1677_11483 style=mask-type:alpha maskUnits=userSpaceOnUse x=30 y=28 width=30 height=30><path fill-rule=evenodd clip-rule=evenodd d=\\\"M49.3945 32.3945C49.3945 34.7088 47.5796 38.5469 45 38.5469C42.4271 38.5469 40.6055 34.7112 40.6055 32.3945C40.6055 29.9714 42.5769 28 45 28C47.4231 28 49.3945 29.9714 49.3945 32.3945ZM35.332 49.0433V48.2148C35.332 42.8117 37.8535 41.0004 39.8796 39.545L39.8801 39.5447C40.3928 39.1767 40.8604 38.8404 41.2488 38.4742C42.3293 39.6642 43.626 40.3047 45 40.3047C46.3752 40.3047 47.6725 39.6642 48.7529 38.4754C49.1408 38.841 49.6078 39.1773 50.1199 39.5447L50.1204 39.545C52.1465 41.0004 54.668 42.8117 54.668 48.2148V49.0433L53.8406 49.092C49.9848 49.3185 46.8646 46.9002 45 43.5777C43.1159 46.935 39.9847 49.318 36.1594 49.092L35.332 49.0433ZM58.1463 51.0747L58.1463 51.0746C57.0179 50.891 50.0128 49.7507 45.0007 55.693C40.0116 49.7553 33.1965 50.8592 31.9095 51.0677L31.9095 51.0677C31.7906 51.087 31.7189 51.0986 31.7002 51.0963C31.7005 51.0969 31.7011 51.1045 31.7023 51.1187C31.726 51.4003 31.9682 54.2745 34.0566 56.2422L30 58H60L55.8956 56.2422C57.8537 54.4764 58.1396 52.2685 58.2508 51.4092V51.4091C58.2697 51.2628 58.2836 51.1556 58.2998 51.0963C58.2881 51.0977 58.2356 51.0892 58.1463 51.0747ZM40.4836 50.104C42.3956 49.3212 43.6746 48.1737 45 46.61C46.332 48.1841 47.6159 49.3259 49.5164 50.104C49.5356 50.1425 49.5557 50.1805 49.5756 50.2182C49.5793 50.2253 49.583 50.2323 49.5867 50.2393C48.0911 50.8127 46.4264 51.825 45.0047 53.1444C43.5906 51.8221 41.9673 50.8196 40.4256 50.2153C40.4455 50.1784 40.4648 50.1415 40.4836 50.104Z\\\" fill=black /></mask><path d=\\\"M 40.59 93.095 C 46.517 93.095 52.14 94.365 57.22 96.635 L 212.7 7.22 C 220.025 3.149 228.978 0.706 237.12 0.706 C 246.073 0.706 254.213 3.149 261.54 7.22 L 395.032 84.547 C 385.264 91.059 377.939 100.827 373.055 112.224 L 243.631 37.338 C 241.19 35.71 238.747 35.71 236.305 35.71 C 233.863 35.71 231.42 36.523 228.978 37.338 L 79.84 123.009 C 80.786 126.443 81.29 130.058 81.29 133.793 C 81.29 156.269 63.065 174.493 40.59 174.493 C 18.116 174.493 -0.109 156.269 -0.109 133.793 C -0.109 111.32 18.116 93.095 40.59 93.095 Z\\\" fill=url(#paint0_linear_1677_11483) /><path d=\\\"M 417.01 171.913 C 401.585 166.126 390.603 151.238 390.603 133.793 C 390.603 111.32 408.83 93.095 431.303 93.095 C 453.777 93.095 472.001 111.32 472.001 133.793 C 472.001 148.706 463.976 161.755 452.011 168.835 L 452.011 336.07 C 452.011 353.977 442.243 370.258 427.591 379.21 L 294.098 455.726 C 294.098 443.516 290.029 431.306 282.703 422.353 L 409.683 349.093 C 414.568 346.651 417.01 341.767 417.01 336.07 L 417.01 171.913 Z\\\" fill=url(#paint1_linear_1677_11483) /><path d=\\\"M 275.376 449.253 C 276.206 452.495 276.646 455.889 276.646 459.389 C 276.646 481.863 258.422 500.087 235.947 500.087 C 215.679 500.087 198.87 485.272 195.761 465.883 L 47.46 380.025 C 31.995 371.071 23.041 354.792 23.041 336.884 L 23.041 186.296 C 28.738 187.923 35.25 189.553 40.948 189.553 C 46.646 189.553 52.345 188.738 57.228 187.111 L 57.228 336.884 C 57.228 342.582 60.485 347.465 64.554 349.908 L 206.042 431.777 C 213.481 423.728 224.127 418.689 235.947 418.689 C 254.905 418.689 270.833 431.656 275.36 449.196 L 275.376 449.214 L 275.376 449.253 Z\\\" fill=url(#paint2_linear_1677_11483) /><g mask=url(#mask0_1677_11483) transform=\\\"matrix(8.139854, 0, 0, 8.139854, -130.346375, -113.251038)\\\"><g filter=url(#filter0_f_1677_11483)><circle cx=73 cy=25 r=26 fill=#ED2E7E /></g><g filter=url(#filter1_f_1677_11483)><circle cx=26 cy=69 r=26 fill=#1CC8EE /></g></g><path fill-rule=evenodd clip-rule=evenodd d=\\\"M 271.713 150.431 C 271.713 169.275 256.948 200.517 235.947 200.517 C 215.003 200.517 200.172 169.292 200.172 150.431 C 200.172 130.708 216.225 114.666 235.947 114.666 C 255.67 114.666 271.713 130.708 271.713 150.431 Z M 157.251 285.952 L 157.251 279.212 C 157.251 235.233 177.771 220.485 194.27 208.641 C 198.447 205.644 202.247 202.901 205.414 199.923 C 214.204 209.608 224.763 214.826 235.947 214.826 C 247.138 214.826 257.697 209.608 266.496 199.931 C 269.653 202.911 273.456 205.644 277.622 208.641 C 294.114 220.485 314.642 235.233 314.642 279.212 L 314.642 285.952 L 307.912 286.351 C 276.525 288.191 251.128 268.509 235.947 241.468 C 220.611 268.795 195.126 288.191 163.981 286.351 L 157.251 285.952 Z M 342.953 302.492 C 333.771 300.994 276.751 291.715 235.955 340.082 C 195.345 291.749 139.865 300.734 129.389 302.436 C 128.428 302.59 127.841 302.688 127.687 302.665 C 127.687 302.673 127.695 302.729 127.702 302.85 C 127.897 305.138 129.867 328.532 146.872 344.55 L 113.849 358.862 L 358.044 358.862 L 324.639 344.55 C 340.576 330.177 342.905 312.202 343.807 305.212 C 343.962 304.022 344.077 303.153 344.206 302.665 C 344.108 302.68 343.686 302.606 342.953 302.492 Z M 199.188 294.59 C 214.751 288.215 225.161 278.879 235.947 266.15 C 246.788 278.96 257.241 288.255 272.707 294.59 C 272.869 294.898 273.031 295.207 273.196 295.518 C 273.219 295.574 273.252 295.631 273.285 295.688 C 261.107 300.361 247.555 308.598 235.989 319.334 C 224.477 308.573 211.258 300.417 198.715 295.493 C 198.87 295.191 199.033 294.891 199.188 294.59 Z\\\" fill=url(#paint6_linear_1677_11483) /><g mask=url(#mask1_1677_11483) transform=\\\"matrix(8.139854, 0, 0, 8.139854, -130.346375, -113.251038)\\\"><g filter=url(#filter2_f_1677_11483)><circle cx=38 cy=6 r=26 fill=#ED2E7E /></g><g filter=url(#filter3_f_1677_11483)><circle cx=63 cy=69 r=26 fill=#1CC8EE /></g></g></svg></div><h1>GraphQL Yoga</h1><p>Version: 5.13.5</p></div><h2>The batteries-included cross-platform GraphQL Server.</h2><div class=buttons><a href=https://www.the-guild.dev/graphql/yoga-server/docs class=docs>Read the Docs</a> <a href=https://www.the-guild.dev/graphql/yoga-server/tutorial/basic class=tutorial>Start the Tutorial </a><a href=__GRAPHIQL_LINK__ class=graphiql>Visit GraphiQL</a></div></section><section class=not-what-your-looking-for><h2>Not the page you are looking for? 👀</h2><p>This page is shown be default whenever a 404 is hit.<br>You can disable this by behavior via the <code>landingPage</code> option.</p><pre>\\n          <code>\\nimport { createYoga } from 'graphql-yoga';\\n\\nconst yoga = createYoga({\\n  landingPage: false\\n})\\n          </code>\\n        </pre><p>If you expected this page to be the GraphQL route, you need to configure Yoga. Currently, the GraphQL route is configured to be on <code>__GRAPHIQL_LINK__</code>.</p><pre>\\n          <code>\\nimport { createYoga } from 'graphql-yoga';\\n\\nconst yoga = createYoga({\\n  graphqlEndpoint: '__REQUEST_PATH__',\\n})\\n          </code>\\n        </pre></section></main></body></html>\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/landing-page-html.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/get.js":
/*!*********************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-parser/get.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isGETRequest = isGETRequest;\nexports.parseGETRequest = parseGETRequest;\nconst fetch_1 = __webpack_require__(/*! @whatwg-node/fetch */ \"(rsc)/./node_modules/@whatwg-node/fetch/dist/node-ponyfill.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/utils.js\");\nfunction isGETRequest(request) {\n    return request.method === 'GET';\n}\nfunction parseGETRequest(request) {\n    const queryString = request.url.substring(request.url.indexOf('?') + 1);\n    const searchParams = new fetch_1.URLSearchParams(queryString);\n    return (0, utils_js_1.handleURLSearchParams)(searchParams);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9wbHVnaW5zL3JlcXVlc3QtcGFyc2VyL2dldC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0I7QUFDcEIsdUJBQXVCO0FBQ3ZCLGdCQUFnQixtQkFBTyxDQUFDLHlGQUFvQjtBQUM1QyxtQkFBbUIsbUJBQU8sQ0FBQyx5RkFBWTtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGdyYXBocWwteW9nYVxcY2pzXFxwbHVnaW5zXFxyZXF1ZXN0LXBhcnNlclxcZ2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5pc0dFVFJlcXVlc3QgPSBpc0dFVFJlcXVlc3Q7XG5leHBvcnRzLnBhcnNlR0VUUmVxdWVzdCA9IHBhcnNlR0VUUmVxdWVzdDtcbmNvbnN0IGZldGNoXzEgPSByZXF1aXJlKFwiQHdoYXR3Zy1ub2RlL2ZldGNoXCIpO1xuY29uc3QgdXRpbHNfanNfMSA9IHJlcXVpcmUoXCIuL3V0aWxzLmpzXCIpO1xuZnVuY3Rpb24gaXNHRVRSZXF1ZXN0KHJlcXVlc3QpIHtcbiAgICByZXR1cm4gcmVxdWVzdC5tZXRob2QgPT09ICdHRVQnO1xufVxuZnVuY3Rpb24gcGFyc2VHRVRSZXF1ZXN0KHJlcXVlc3QpIHtcbiAgICBjb25zdCBxdWVyeVN0cmluZyA9IHJlcXVlc3QudXJsLnN1YnN0cmluZyhyZXF1ZXN0LnVybC5pbmRleE9mKCc/JykgKyAxKTtcbiAgICBjb25zdCBzZWFyY2hQYXJhbXMgPSBuZXcgZmV0Y2hfMS5VUkxTZWFyY2hQYXJhbXMocXVlcnlTdHJpbmcpO1xuICAgIHJldHVybiAoMCwgdXRpbHNfanNfMS5oYW5kbGVVUkxTZWFyY2hQYXJhbXMpKHNlYXJjaFBhcmFtcyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/get.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-form-url-encoded.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-parser/post-form-url-encoded.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isPOSTFormUrlEncodedRequest = isPOSTFormUrlEncodedRequest;\nexports.parsePOSTFormUrlEncodedRequest = parsePOSTFormUrlEncodedRequest;\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/utils.js\");\nfunction isPOSTFormUrlEncodedRequest(request) {\n    return (request.method === 'POST' && (0, utils_js_1.isContentTypeMatch)(request, 'application/x-www-form-urlencoded'));\n}\nfunction parsePOSTFormUrlEncodedRequest(request) {\n    return request.text().then(utils_js_1.parseURLSearchParams);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9wbHVnaW5zL3JlcXVlc3QtcGFyc2VyL3Bvc3QtZm9ybS11cmwtZW5jb2RlZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxtQ0FBbUM7QUFDbkMsc0NBQXNDO0FBQ3RDLG1CQUFtQixtQkFBTyxDQUFDLHlGQUFZO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxncmFwaHFsLXlvZ2FcXGNqc1xccGx1Z2luc1xccmVxdWVzdC1wYXJzZXJcXHBvc3QtZm9ybS11cmwtZW5jb2RlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuaXNQT1NURm9ybVVybEVuY29kZWRSZXF1ZXN0ID0gaXNQT1NURm9ybVVybEVuY29kZWRSZXF1ZXN0O1xuZXhwb3J0cy5wYXJzZVBPU1RGb3JtVXJsRW5jb2RlZFJlcXVlc3QgPSBwYXJzZVBPU1RGb3JtVXJsRW5jb2RlZFJlcXVlc3Q7XG5jb25zdCB1dGlsc19qc18xID0gcmVxdWlyZShcIi4vdXRpbHMuanNcIik7XG5mdW5jdGlvbiBpc1BPU1RGb3JtVXJsRW5jb2RlZFJlcXVlc3QocmVxdWVzdCkge1xuICAgIHJldHVybiAocmVxdWVzdC5tZXRob2QgPT09ICdQT1NUJyAmJiAoMCwgdXRpbHNfanNfMS5pc0NvbnRlbnRUeXBlTWF0Y2gpKHJlcXVlc3QsICdhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWQnKSk7XG59XG5mdW5jdGlvbiBwYXJzZVBPU1RGb3JtVXJsRW5jb2RlZFJlcXVlc3QocmVxdWVzdCkge1xuICAgIHJldHVybiByZXF1ZXN0LnRleHQoKS50aGVuKHV0aWxzX2pzXzEucGFyc2VVUkxTZWFyY2hQYXJhbXMpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-form-url-encoded.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-graphql-string.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-parser/post-graphql-string.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isPOSTGraphQLStringRequest = isPOSTGraphQLStringRequest;\nexports.parsePOSTGraphQLStringRequest = parsePOSTGraphQLStringRequest;\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/utils.js\");\nfunction isPOSTGraphQLStringRequest(request) {\n    return request.method === 'POST' && (0, utils_js_1.isContentTypeMatch)(request, 'application/graphql');\n}\nfunction parsePOSTGraphQLStringRequest(request) {\n    return request.text().then(query => ({ query }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9wbHVnaW5zL3JlcXVlc3QtcGFyc2VyL3Bvc3QtZ3JhcGhxbC1zdHJpbmcuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0NBQWtDO0FBQ2xDLHFDQUFxQztBQUNyQyxtQkFBbUIsbUJBQU8sQ0FBQyx5RkFBWTtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxPQUFPO0FBQ2xEIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGdyYXBocWwteW9nYVxcY2pzXFxwbHVnaW5zXFxyZXF1ZXN0LXBhcnNlclxccG9zdC1ncmFwaHFsLXN0cmluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuaXNQT1NUR3JhcGhRTFN0cmluZ1JlcXVlc3QgPSBpc1BPU1RHcmFwaFFMU3RyaW5nUmVxdWVzdDtcbmV4cG9ydHMucGFyc2VQT1NUR3JhcGhRTFN0cmluZ1JlcXVlc3QgPSBwYXJzZVBPU1RHcmFwaFFMU3RyaW5nUmVxdWVzdDtcbmNvbnN0IHV0aWxzX2pzXzEgPSByZXF1aXJlKFwiLi91dGlscy5qc1wiKTtcbmZ1bmN0aW9uIGlzUE9TVEdyYXBoUUxTdHJpbmdSZXF1ZXN0KHJlcXVlc3QpIHtcbiAgICByZXR1cm4gcmVxdWVzdC5tZXRob2QgPT09ICdQT1NUJyAmJiAoMCwgdXRpbHNfanNfMS5pc0NvbnRlbnRUeXBlTWF0Y2gpKHJlcXVlc3QsICdhcHBsaWNhdGlvbi9ncmFwaHFsJyk7XG59XG5mdW5jdGlvbiBwYXJzZVBPU1RHcmFwaFFMU3RyaW5nUmVxdWVzdChyZXF1ZXN0KSB7XG4gICAgcmV0dXJuIHJlcXVlc3QudGV4dCgpLnRoZW4ocXVlcnkgPT4gKHsgcXVlcnkgfSkpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-graphql-string.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-json.js":
/*!***************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-parser/post-json.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isPOSTJsonRequest = isPOSTJsonRequest;\nexports.parsePOSTJsonRequest = parsePOSTJsonRequest;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/utils.js\");\nfunction isPOSTJsonRequest(request) {\n    return (request.method === 'POST' &&\n        ((0, utils_js_1.isContentTypeMatch)(request, 'application/json') ||\n            (0, utils_js_1.isContentTypeMatch)(request, 'application/graphql+json')));\n}\nfunction parsePOSTJsonRequest(request) {\n    return (0, promise_helpers_1.handleMaybePromise)(() => request.json(), (requestBody) => {\n        if (requestBody == null) {\n            throw (0, utils_1.createGraphQLError)(`POST body is expected to be object but received ${requestBody}`, {\n                extensions: {\n                    http: {\n                        status: 400,\n                    },\n                    code: 'BAD_REQUEST',\n                },\n            });\n        }\n        const requestBodyTypeof = typeof requestBody;\n        if (requestBodyTypeof !== 'object') {\n            throw (0, utils_1.createGraphQLError)(`POST body is expected to be object but received ${requestBodyTypeof}`, {\n                extensions: {\n                    http: {\n                        status: 400,\n                    },\n                    code: 'BAD_REQUEST',\n                },\n            });\n        }\n        return requestBody;\n    }, err => {\n        if (err instanceof graphql_1.GraphQLError) {\n            throw err;\n        }\n        const extensions = {\n            http: {\n                spec: true,\n                status: 400,\n            },\n            code: 'BAD_REQUEST',\n        };\n        if (err instanceof Error) {\n            extensions['originalError'] = {\n                name: err.name,\n                message: err.message,\n            };\n        }\n        throw (0, utils_1.createGraphQLError)('POST body sent invalid JSON.', {\n            extensions,\n        });\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-json.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-multipart.js":
/*!********************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-parser/post-multipart.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isPOSTMultipartRequest = isPOSTMultipartRequest;\nexports.parsePOSTMultipartRequest = parsePOSTMultipartRequest;\nconst dset_1 = __webpack_require__(/*! dset */ \"(rsc)/./node_modules/dset/dist/index.js\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/utils.js\");\nfunction isPOSTMultipartRequest(request) {\n    return request.method === 'POST' && (0, utils_js_1.isContentTypeMatch)(request, 'multipart/form-data');\n}\nfunction parsePOSTMultipartRequest(request) {\n    return (0, promise_helpers_1.handleMaybePromise)(() => request.formData(), (requestBody) => {\n        const operationsStr = requestBody.get('operations');\n        if (!operationsStr) {\n            throw (0, utils_1.createGraphQLError)('Missing multipart form field \"operations\"');\n        }\n        if (typeof operationsStr !== 'string') {\n            throw (0, utils_1.createGraphQLError)('Multipart form field \"operations\" must be a string');\n        }\n        let operations;\n        try {\n            operations = JSON.parse(operationsStr);\n        }\n        catch {\n            throw (0, utils_1.createGraphQLError)('Multipart form field \"operations\" must be a valid JSON string');\n        }\n        const mapStr = requestBody.get('map');\n        if (mapStr != null) {\n            if (typeof mapStr !== 'string') {\n                throw (0, utils_1.createGraphQLError)('Multipart form field \"map\" must be a string');\n            }\n            let map;\n            try {\n                map = JSON.parse(mapStr);\n            }\n            catch {\n                throw (0, utils_1.createGraphQLError)('Multipart form field \"map\" must be a valid JSON string');\n            }\n            for (const fileIndex in map) {\n                const file = requestBody.get(fileIndex);\n                const keys = map[fileIndex];\n                for (const key of keys) {\n                    (0, dset_1.dset)(operations, key, file);\n                }\n            }\n        }\n        return operations;\n    }, e => {\n        if (e instanceof Error && e.message.startsWith('File size limit exceeded: ')) {\n            throw (0, utils_1.createGraphQLError)(e.message, {\n                extensions: {\n                    http: {\n                        status: 413,\n                    },\n                },\n            });\n        }\n        throw e;\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-multipart.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/utils.js":
/*!***********************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-parser/utils.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.handleURLSearchParams = handleURLSearchParams;\nexports.parseURLSearchParams = parseURLSearchParams;\nexports.isContentTypeMatch = isContentTypeMatch;\nconst fetch_1 = __webpack_require__(/*! @whatwg-node/fetch */ \"(rsc)/./node_modules/@whatwg-node/fetch/dist/node-ponyfill.js\");\nfunction handleURLSearchParams(searchParams) {\n    const operationName = searchParams.get('operationName') || undefined;\n    const query = searchParams.get('query') || undefined;\n    const variablesStr = searchParams.get('variables') || undefined;\n    const extensionsStr = searchParams.get('extensions') || undefined;\n    return {\n        operationName,\n        query,\n        variables: variablesStr ? JSON.parse(variablesStr) : undefined,\n        extensions: extensionsStr ? JSON.parse(extensionsStr) : undefined,\n    };\n}\nfunction parseURLSearchParams(requestBody) {\n    const searchParams = new fetch_1.URLSearchParams(requestBody);\n    return handleURLSearchParams(searchParams);\n}\nfunction isContentTypeMatch(request, expectedContentType) {\n    let contentType = request.headers.get('content-type');\n    // a list of content-types is not valid as per HTTP spec, but some clients dont care\n    contentType = contentType?.split(',')[0] || null;\n    return (contentType === expectedContentType || !!contentType?.startsWith(`${expectedContentType};`));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-check-graphql-query-params.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-validation/use-check-graphql-query-params.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assertInvalidParams = assertInvalidParams;\nexports.checkGraphQLQueryParams = checkGraphQLQueryParams;\nexports.isValidGraphQLParams = isValidGraphQLParams;\nexports.useCheckGraphQLQueryParams = useCheckGraphQLQueryParams;\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nconst expectedParameters = new Set(['query', 'variables', 'operationName', 'extensions']);\nfunction assertInvalidParams(params, extraParamNames) {\n    if (params == null || typeof params !== 'object') {\n        throw (0, utils_1.createGraphQLError)('Invalid \"params\" in the request body', {\n            extensions: {\n                http: {\n                    spec: true,\n                    status: 400,\n                },\n                code: 'BAD_REQUEST',\n            },\n        });\n    }\n    for (const paramKey in params) {\n        if (params[paramKey] == null) {\n            continue;\n        }\n        if (!expectedParameters.has(paramKey)) {\n            if (extraParamNames?.includes(paramKey)) {\n                continue;\n            }\n            throw (0, utils_1.createGraphQLError)(`Unexpected parameter \"${paramKey}\" in the request body.`, {\n                extensions: {\n                    http: {\n                        status: 400,\n                    },\n                    code: 'BAD_REQUEST',\n                },\n            });\n        }\n    }\n}\nfunction checkGraphQLQueryParams(params, extraParamNames) {\n    if (!isObject(params)) {\n        throw (0, utils_1.createGraphQLError)(`Expected params to be an object but given ${extendedTypeof(params)}.`, {\n            extensions: {\n                http: {\n                    status: 400,\n                    headers: {\n                        Allow: 'GET, POST',\n                    },\n                },\n                code: 'BAD_REQUEST',\n            },\n        });\n    }\n    assertInvalidParams(params, extraParamNames);\n    if (params['query'] == null) {\n        throw (0, utils_1.createGraphQLError)('Must provide query string.', {\n            extensions: {\n                http: {\n                    spec: true,\n                    status: 400,\n                    headers: {\n                        Allow: 'GET, POST',\n                    },\n                },\n                code: 'BAD_REQUEST',\n            },\n        });\n    }\n    const queryType = extendedTypeof(params['query']);\n    if (queryType !== 'string') {\n        throw (0, utils_1.createGraphQLError)(`Expected \"query\" param to be a string, but given ${queryType}.`, {\n            extensions: {\n                http: {\n                    status: 400,\n                    headers: {\n                        Allow: 'GET, POST',\n                    },\n                },\n                code: 'BAD_REQUEST',\n            },\n        });\n    }\n    const variablesParamType = extendedTypeof(params['variables']);\n    if (!['object', 'null', 'undefined'].includes(variablesParamType)) {\n        throw (0, utils_1.createGraphQLError)(`Expected \"variables\" param to be empty or an object, but given ${variablesParamType}.`, {\n            extensions: {\n                http: {\n                    status: 400,\n                    headers: {\n                        Allow: 'GET, POST',\n                    },\n                },\n                code: 'BAD_REQUEST',\n            },\n        });\n    }\n    const extensionsParamType = extendedTypeof(params['extensions']);\n    if (!['object', 'null', 'undefined'].includes(extensionsParamType)) {\n        throw (0, utils_1.createGraphQLError)(`Expected \"extensions\" param to be empty or an object, but given ${extensionsParamType}.`, {\n            extensions: {\n                http: {\n                    status: 400,\n                    headers: {\n                        Allow: 'GET, POST',\n                    },\n                },\n                code: 'BAD_REQUEST',\n            },\n        });\n    }\n    return params;\n}\nfunction isValidGraphQLParams(params) {\n    try {\n        checkGraphQLQueryParams(params);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nfunction useCheckGraphQLQueryParams(extraParamNames) {\n    return {\n        onParams({ params }) {\n            checkGraphQLQueryParams(params, extraParamNames);\n        },\n    };\n}\nfunction extendedTypeof(val) {\n    if (val === null) {\n        return 'null';\n    }\n    if (Array.isArray(val)) {\n        return 'array';\n    }\n    return typeof val;\n}\nfunction isObject(val) {\n    return extendedTypeof(val) === 'object';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-check-graphql-query-params.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-check-method-for-graphql.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-validation/use-check-method-for-graphql.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isValidMethodForGraphQL = isValidMethodForGraphQL;\nexports.useCheckMethodForGraphQL = useCheckMethodForGraphQL;\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nfunction isValidMethodForGraphQL(method) {\n    return method === 'GET' || method === 'POST';\n}\nfunction useCheckMethodForGraphQL() {\n    return {\n        onRequestParse({ request }) {\n            if (!isValidMethodForGraphQL(request.method)) {\n                throw (0, utils_1.createGraphQLError)('GraphQL only supports GET and POST requests.', {\n                    extensions: {\n                        http: {\n                            status: 405,\n                            headers: {\n                                Allow: 'GET, POST',\n                            },\n                        },\n                        code: 'BAD_REQUEST',\n                    },\n                });\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-check-method-for-graphql.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-http-validation-error.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-validation/use-http-validation-error.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useHTTPValidationError = useHTTPValidationError;\nfunction useHTTPValidationError() {\n    return {\n        onValidate() {\n            return ({ valid, result }) => {\n                if (!valid) {\n                    for (const error of result) {\n                        error.extensions ||= {};\n                        error.extensions.code ||= 'GRAPHQL_VALIDATION_FAILED';\n                        error.extensions.http ||= {};\n                        error.extensions.http.spec =\n                            error.extensions.http.spec == null ? true : error.extensions.http.spec;\n                        error.extensions.http.status ||= 400;\n                    }\n                }\n            };\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9wbHVnaW5zL3JlcXVlc3QtdmFsaWRhdGlvbi91c2UtaHR0cC12YWxpZGF0aW9uLWVycm9yLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsZUFBZTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbC15b2dhXFxjanNcXHBsdWdpbnNcXHJlcXVlc3QtdmFsaWRhdGlvblxcdXNlLWh0dHAtdmFsaWRhdGlvbi1lcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudXNlSFRUUFZhbGlkYXRpb25FcnJvciA9IHVzZUhUVFBWYWxpZGF0aW9uRXJyb3I7XG5mdW5jdGlvbiB1c2VIVFRQVmFsaWRhdGlvbkVycm9yKCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIG9uVmFsaWRhdGUoKSB7XG4gICAgICAgICAgICByZXR1cm4gKHsgdmFsaWQsIHJlc3VsdCB9KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCF2YWxpZCkge1xuICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGVycm9yIG9mIHJlc3VsdCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3IuZXh0ZW5zaW9ucyB8fD0ge307XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvci5leHRlbnNpb25zLmNvZGUgfHw9ICdHUkFQSFFMX1ZBTElEQVRJT05fRkFJTEVEJztcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yLmV4dGVuc2lvbnMuaHR0cCB8fD0ge307XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvci5leHRlbnNpb25zLmh0dHAuc3BlYyA9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3IuZXh0ZW5zaW9ucy5odHRwLnNwZWMgPT0gbnVsbCA/IHRydWUgOiBlcnJvci5leHRlbnNpb25zLmh0dHAuc3BlYztcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yLmV4dGVuc2lvbnMuaHR0cC5zdGF0dXMgfHw9IDQwMDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-http-validation-error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-limit-batching.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-validation/use-limit-batching.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useLimitBatching = useLimitBatching;\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nfunction useLimitBatching(limit) {\n    return {\n        onRequestParse() {\n            return {\n                onRequestParseDone({ requestParserResult }) {\n                    if (Array.isArray(requestParserResult)) {\n                        if (!limit) {\n                            throw (0, utils_1.createGraphQLError)(`Batching is not supported.`, {\n                                extensions: {\n                                    http: {\n                                        status: 400,\n                                    },\n                                    code: 'BAD_REQUEST',\n                                },\n                            });\n                        }\n                        if (requestParserResult.length > limit) {\n                            throw (0, utils_1.createGraphQLError)(`Batching is limited to ${limit} operations per request.`, {\n                                extensions: {\n                                    http: {\n                                        status: 413,\n                                    },\n                                    code: 'BAD_REQUEST',\n                                },\n                            });\n                        }\n                    }\n                },\n            };\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-limit-batching.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-prevent-mutation-via-get.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/request-validation/use-prevent-mutation-via-get.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assertMutationViaGet = assertMutationViaGet;\nexports.usePreventMutationViaGET = usePreventMutationViaGET;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nfunction assertMutationViaGet(method, document, operationName) {\n    const operation = document\n        ? ((0, graphql_1.getOperationAST)(document, operationName) ?? undefined)\n        : undefined;\n    if (!operation) {\n        throw (0, utils_1.createGraphQLError)('Could not determine what operation to execute.', {\n            extensions: {\n                code: 'OPERATION_RESOLUTION_FAILURE',\n                http: {\n                    status: 400,\n                },\n            },\n        });\n    }\n    if (operation.operation === 'mutation' && method === 'GET') {\n        throw (0, utils_1.createGraphQLError)('Can only perform a mutation operation from a POST request.', {\n            extensions: {\n                http: {\n                    status: 405,\n                    headers: {\n                        Allow: 'POST',\n                    },\n                },\n                code: 'BAD_REQUEST',\n            },\n        });\n    }\n}\nfunction usePreventMutationViaGET() {\n    return {\n        onParse() {\n            // We should improve this by getting Yoga stuff from the hook params directly instead of the context\n            return ({ result, context: { request, \n            // the `params` might be missing in cases where the user provided\n            // malformed context to getEnveloped (like `yoga.getEnveloped({})`)\n            params: { operationName } = {}, }, }) => {\n                // Run only if this is a Yoga request\n                // the `request` might be missing when using graphql-ws for example\n                // in which case throwing an error would abruptly close the socket\n                if (!request) {\n                    return;\n                }\n                if (result instanceof Error) {\n                    if (result instanceof graphql_1.GraphQLError) {\n                        // @ts-expect-error - We are modifying the extensions on purpose\n                        const extensions = (result.extensions ||= {});\n                        extensions['code'] ||= 'GRAPHQL_PARSE_FAILED';\n                        const httpExtensions = (extensions['http'] ||= {});\n                        httpExtensions.spec ||= true;\n                        httpExtensions.status ||= 400;\n                    }\n                }\n                else {\n                    assertMutationViaGet(request.method, result, operationName);\n                }\n            };\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-prevent-mutation-via-get.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/accept.js":
/*!**************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/result-processor/accept.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getMediaTypesForRequestInOrder = getMediaTypesForRequestInOrder;\nexports.isMatchingMediaType = isMatchingMediaType;\nfunction getMediaTypesForRequestInOrder(request) {\n    const accepts = (request.headers.get('accept') || '*/*')\n        .replace(/\\s/g, '')\n        .toLowerCase()\n        .split(',');\n    const mediaTypes = [];\n    for (const accept of accepts) {\n        const [mediaType, ...params] = accept.split(';');\n        if (mediaType === undefined)\n            continue; // If true, malformed header.\n        const charset = params?.find(param => param.includes('charset=')) || 'charset=utf-8'; // utf-8 is assumed when not specified;\n        if (charset !== 'charset=utf-8') {\n            // only utf-8 is supported\n            continue;\n        }\n        mediaTypes.push(mediaType);\n    }\n    return mediaTypes.reverse();\n}\nfunction isMatchingMediaType(askedMediaType, processorMediaType) {\n    const [askedPre, askedSuf] = askedMediaType.split('/');\n    const [pre, suf] = processorMediaType.split('/');\n    if ((pre === '*' || pre === askedPre) && (suf === '*' || suf === askedSuf)) {\n        return true;\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/accept.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/multipart.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/result-processor/multipart.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.processMultipartResult = processMultipartResult;\nconst core_1 = __webpack_require__(/*! @envelop/core */ \"(rsc)/./node_modules/@envelop/core/cjs/index.js\");\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst server_1 = __webpack_require__(/*! @whatwg-node/server */ \"(rsc)/./node_modules/@whatwg-node/server/cjs/index.js\");\nconst error_js_1 = __webpack_require__(/*! ../../error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/error.js\");\nconst stringify_js_1 = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/stringify.js\");\nfunction processMultipartResult(result, fetchAPI) {\n    const headersInit = {\n        Connection: 'keep-alive',\n        'Content-Type': 'multipart/mixed; boundary=\"-\"',\n        'Transfer-Encoding': 'chunked',\n    };\n    const responseInit = (0, error_js_1.getResponseInitByRespectingErrors)(result, headersInit);\n    let iterator;\n    const textEncoder = new fetchAPI.TextEncoder();\n    const readableStream = new fetchAPI.ReadableStream({\n        start(controller) {\n            if ((0, core_1.isAsyncIterable)(result)) {\n                iterator = result[Symbol.asyncIterator]();\n            }\n            else {\n                let finished = false;\n                iterator = {\n                    next: () => {\n                        if (finished) {\n                            return (0, server_1.fakePromise)({ done: true, value: null });\n                        }\n                        finished = true;\n                        return (0, server_1.fakePromise)({ done: false, value: result });\n                    },\n                };\n            }\n            controller.enqueue(textEncoder.encode('\\r\\n'));\n            controller.enqueue(textEncoder.encode(`---`));\n        },\n        pull(controller) {\n            return (0, promise_helpers_1.handleMaybePromise)(() => iterator.next(), ({ done, value }) => {\n                if (value != null) {\n                    controller.enqueue(textEncoder.encode('\\r\\n'));\n                    controller.enqueue(textEncoder.encode('Content-Type: application/json; charset=utf-8'));\n                    controller.enqueue(textEncoder.encode('\\r\\n'));\n                    const chunk = (0, stringify_js_1.jsonStringifyResultWithoutInternals)(value);\n                    const encodedChunk = textEncoder.encode(chunk);\n                    controller.enqueue(textEncoder.encode('Content-Length: ' + encodedChunk.byteLength));\n                    controller.enqueue(textEncoder.encode('\\r\\n'));\n                    controller.enqueue(textEncoder.encode('\\r\\n'));\n                    controller.enqueue(encodedChunk);\n                    controller.enqueue(textEncoder.encode('\\r\\n'));\n                    controller.enqueue(textEncoder.encode('---'));\n                }\n                if (done) {\n                    controller.enqueue(textEncoder.encode('--\\r\\n'));\n                    controller.close();\n                }\n            }, err => {\n                controller.error(err);\n            });\n        },\n        cancel(e) {\n            if (iterator.return) {\n                return (0, promise_helpers_1.handleMaybePromise)(() => iterator.return?.(e), () => { });\n            }\n        },\n    });\n    return new fetchAPI.Response(readableStream, responseInit);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/multipart.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/regular.js":
/*!***************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/result-processor/regular.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.processRegularResult = processRegularResult;\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nconst error_js_1 = __webpack_require__(/*! ../../error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/error.js\");\nconst stringify_js_1 = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/stringify.js\");\nfunction processRegularResult(executionResult, fetchAPI, acceptedHeader) {\n    if ((0, utils_1.isAsyncIterable)(executionResult)) {\n        return new fetchAPI.Response(null, {\n            status: 406,\n            statusText: 'Not Acceptable',\n            headers: {\n                accept: 'application/json; charset=utf-8, application/graphql-response+json; charset=utf-8',\n            },\n        });\n    }\n    const headersInit = {\n        'Content-Type': acceptedHeader + '; charset=utf-8',\n    };\n    const responseInit = (0, error_js_1.getResponseInitByRespectingErrors)(executionResult, headersInit, \n    // prefer 200 only if accepting application/json and all errors are exclusively GraphQL errors\n    acceptedHeader === 'application/json' &&\n        !Array.isArray(executionResult) &&\n        (0, error_js_1.areGraphQLErrors)(executionResult.errors) &&\n        executionResult.errors.some(err => !err.extensions?.['originalError'] || (0, error_js_1.isGraphQLError)(err.extensions['originalError'])));\n    const responseBody = (0, stringify_js_1.jsonStringifyResultWithoutInternals)(executionResult);\n    return new fetchAPI.Response(responseBody, responseInit);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/regular.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/sse.js":
/*!***********************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/result-processor/sse.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getSSEProcessor = getSSEProcessor;\nconst core_1 = __webpack_require__(/*! @envelop/core */ \"(rsc)/./node_modules/@envelop/core/cjs/index.js\");\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst server_1 = __webpack_require__(/*! @whatwg-node/server */ \"(rsc)/./node_modules/@whatwg-node/server/cjs/index.js\");\nconst error_js_1 = __webpack_require__(/*! ../../error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/error.js\");\nconst stringify_js_1 = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/stringify.js\");\nfunction getSSEProcessor() {\n    return function processSSEResult(result, fetchAPI) {\n        let pingIntervalMs = 12_000;\n        // for testing the pings, reduce the timeout\n        if (globalThis.process?.env?.['NODE_ENV'] === 'test') {\n            pingIntervalMs = 300;\n        }\n        const headersInit = {\n            'Content-Type': 'text/event-stream',\n            Connection: 'keep-alive',\n            'Cache-Control': 'no-cache',\n            'Content-Encoding': 'none',\n        };\n        const responseInit = (0, error_js_1.getResponseInitByRespectingErrors)(result, headersInit, true);\n        let iterator;\n        let pingInterval;\n        const textEncoder = new fetchAPI.TextEncoder();\n        const readableStream = new fetchAPI.ReadableStream({\n            start(controller) {\n                // always start with a ping because some browsers dont accept a header flush\n                // causing the fetch to stall until something is streamed through the response\n                controller.enqueue(textEncoder.encode(':\\n\\n'));\n                // ping client every 12 seconds to keep the connection alive\n                pingInterval = setInterval(() => {\n                    if (!controller.desiredSize) {\n                        clearInterval(pingInterval);\n                        return;\n                    }\n                    controller.enqueue(textEncoder.encode(':\\n\\n'));\n                }, pingIntervalMs);\n                if ((0, core_1.isAsyncIterable)(result)) {\n                    iterator = result[Symbol.asyncIterator]();\n                }\n                else {\n                    let finished = false;\n                    iterator = {\n                        next: () => {\n                            if (finished) {\n                                return (0, server_1.fakePromise)({ done: true, value: null });\n                            }\n                            finished = true;\n                            return (0, server_1.fakePromise)({ done: false, value: result });\n                        },\n                    };\n                }\n            },\n            pull(controller) {\n                return (0, promise_helpers_1.handleMaybePromise)(() => iterator.next(), result => {\n                    if (result.value != null) {\n                        controller.enqueue(textEncoder.encode(`event: next\\n`));\n                        const chunk = (0, stringify_js_1.jsonStringifyResultWithoutInternals)(result.value);\n                        controller.enqueue(textEncoder.encode(`data: ${chunk}\\n\\n`));\n                    }\n                    if (result.done) {\n                        controller.enqueue(textEncoder.encode(`event: complete\\n`));\n                        controller.enqueue(textEncoder.encode(`data:\\n\\n`));\n                        clearInterval(pingInterval);\n                        controller.close();\n                    }\n                }, err => {\n                    controller.error(err);\n                });\n            },\n            cancel(e) {\n                clearInterval(pingInterval);\n                if (iterator.return) {\n                    return (0, promise_helpers_1.handleMaybePromise)(() => iterator.return?.(e), () => { });\n                }\n            },\n        });\n        return new fetchAPI.Response(readableStream, responseInit);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/sse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/stringify.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/result-processor/stringify.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.jsonStringifyResultWithoutInternals = jsonStringifyResultWithoutInternals;\nexports.omitInternalsFromResultErrors = omitInternalsFromResultErrors;\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nconst error_js_1 = __webpack_require__(/*! ../../error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/error.js\");\n// JSON stringifier that adjusts the result error extensions while serialising\nfunction jsonStringifyResultWithoutInternals(result) {\n    if (Array.isArray(result)) {\n        return `[${result\n            .map(r => {\n            const sanitizedResult = omitInternalsFromResultErrors(r);\n            const stringifier = r.stringify || JSON.stringify;\n            return stringifier(sanitizedResult);\n        })\n            .join(',')}]`;\n    }\n    const sanitizedResult = omitInternalsFromResultErrors(result);\n    const stringifier = result.stringify || JSON.stringify;\n    return stringifier(sanitizedResult);\n}\nfunction omitInternalsFromResultErrors(result) {\n    if (result.errors?.length || result.extensions?.http) {\n        const newResult = { ...result };\n        newResult.errors &&= newResult.errors.map(omitInternalsFromError);\n        if (newResult.extensions) {\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars -- TS should check for unused vars instead\n            const { http, ...extensions } = result.extensions;\n            newResult.extensions = Object.keys(extensions).length ? extensions : undefined;\n        }\n        return newResult;\n    }\n    return result;\n}\nfunction omitInternalsFromError(err) {\n    if ((0, error_js_1.isGraphQLError)(err)) {\n        const serializedError = 'toJSON' in err && typeof err.toJSON === 'function' ? err.toJSON() : Object(err);\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars -- TS should check for unused vars instead\n        const { http, unexpected, ...extensions } = serializedError.extensions || {};\n        return (0, utils_1.createGraphQLError)(err.message, {\n            nodes: err.nodes,\n            source: err.source,\n            positions: err.positions,\n            path: err.path,\n            originalError: omitInternalsFromError(err.originalError || undefined),\n            extensions: Object.keys(extensions).length ? extensions : undefined,\n        });\n    }\n    return err;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/stringify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-execution-cancellation.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-execution-cancellation.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useExecutionCancellation = useExecutionCancellation;\n/**\n * Enables experimental support for request cancelation.\n */\nfunction useExecutionCancellation() {\n    return {\n        onExecute({ args }) {\n            // @ts-expect-error we don't have this typing in envelop\n            args.signal = args.contextValue?.request?.signal ?? undefined;\n        },\n        onSubscribe({ args }) {\n            // @ts-expect-error we don't have this typing in envelop\n            args.signal = args.contextValue?.request?.signal ?? undefined;\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9wbHVnaW5zL3VzZS1leGVjdXRpb24tY2FuY2VsbGF0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLE1BQU07QUFDMUI7QUFDQTtBQUNBLFNBQVM7QUFDVCxzQkFBc0IsTUFBTTtBQUM1QjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbC15b2dhXFxjanNcXHBsdWdpbnNcXHVzZS1leGVjdXRpb24tY2FuY2VsbGF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy51c2VFeGVjdXRpb25DYW5jZWxsYXRpb24gPSB1c2VFeGVjdXRpb25DYW5jZWxsYXRpb247XG4vKipcbiAqIEVuYWJsZXMgZXhwZXJpbWVudGFsIHN1cHBvcnQgZm9yIHJlcXVlc3QgY2FuY2VsYXRpb24uXG4gKi9cbmZ1bmN0aW9uIHVzZUV4ZWN1dGlvbkNhbmNlbGxhdGlvbigpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBvbkV4ZWN1dGUoeyBhcmdzIH0pIHtcbiAgICAgICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3Igd2UgZG9uJ3QgaGF2ZSB0aGlzIHR5cGluZyBpbiBlbnZlbG9wXG4gICAgICAgICAgICBhcmdzLnNpZ25hbCA9IGFyZ3MuY29udGV4dFZhbHVlPy5yZXF1ZXN0Py5zaWduYWwgPz8gdW5kZWZpbmVkO1xuICAgICAgICB9LFxuICAgICAgICBvblN1YnNjcmliZSh7IGFyZ3MgfSkge1xuICAgICAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciB3ZSBkb24ndCBoYXZlIHRoaXMgdHlwaW5nIGluIGVudmVsb3BcbiAgICAgICAgICAgIGFyZ3Muc2lnbmFsID0gYXJncy5jb250ZXh0VmFsdWU/LnJlcXVlc3Q/LnNpZ25hbCA/PyB1bmRlZmluZWQ7XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-execution-cancellation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-graphiql.js":
/*!***************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-graphiql.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.renderGraphiQL = void 0;\nexports.shouldRenderGraphiQL = shouldRenderGraphiQL;\nexports.useGraphiQL = useGraphiQL;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst graphiql_html_js_1 = tslib_1.__importDefault(__webpack_require__(/*! ../graphiql-html.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/graphiql-html.js\"));\nfunction shouldRenderGraphiQL({ headers, method }) {\n    return method === 'GET' && !!headers?.get('accept')?.includes('text/html');\n}\nconst renderGraphiQL = (opts) => graphiql_html_js_1.default\n    .replace('__TITLE__', opts?.title || 'Yoga GraphiQL')\n    .replace('__OPTS__', JSON.stringify(opts ?? {}));\nexports.renderGraphiQL = renderGraphiQL;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useGraphiQL(config) {\n    const logger = config.logger ?? console;\n    let graphiqlOptionsFactory;\n    if (typeof config?.options === 'function') {\n        graphiqlOptionsFactory = config?.options;\n    }\n    else if (typeof config?.options === 'object') {\n        graphiqlOptionsFactory = () => config?.options;\n    }\n    else if (config?.options === false) {\n        graphiqlOptionsFactory = () => false;\n    }\n    else {\n        graphiqlOptionsFactory = () => ({});\n    }\n    const renderer = config?.render ?? exports.renderGraphiQL;\n    let urlPattern;\n    const getUrlPattern = ({ URLPattern }) => {\n        urlPattern ||= new URLPattern({\n            pathname: config.graphqlEndpoint,\n        });\n        return urlPattern;\n    };\n    return {\n        onRequest({ request, serverContext, fetchAPI, endResponse, url }) {\n            if (shouldRenderGraphiQL(request) &&\n                (request.url.endsWith(config.graphqlEndpoint) ||\n                    request.url.endsWith(`${config.graphqlEndpoint}/`) ||\n                    url.pathname === config.graphqlEndpoint ||\n                    url.pathname === `${config.graphqlEndpoint}/` ||\n                    getUrlPattern(fetchAPI).test(url))) {\n                logger.debug(`Rendering GraphiQL`);\n                return (0, promise_helpers_1.handleMaybePromise)(() => graphiqlOptionsFactory(request, serverContext), graphiqlOptions => {\n                    if (graphiqlOptions) {\n                        return (0, promise_helpers_1.handleMaybePromise)(() => renderer({\n                            ...(graphiqlOptions === true ? {} : graphiqlOptions),\n                        }), graphiqlBody => {\n                            const response = new fetchAPI.Response(graphiqlBody, {\n                                headers: {\n                                    'Content-Type': 'text/html',\n                                },\n                                status: 200,\n                            });\n                            endResponse(response);\n                        });\n                    }\n                });\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-graphiql.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-health-check.js":
/*!*******************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-health-check.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useHealthCheck = useHealthCheck;\nfunction useHealthCheck({ id = Date.now().toString(), logger = console, endpoint = '/health', } = {}) {\n    return {\n        onRequest({ endResponse, fetchAPI, request }) {\n            if (request.url.endsWith(endpoint)) {\n                logger.debug('Responding Health Check');\n                const response = new fetchAPI.Response(null, {\n                    status: 200,\n                    headers: {\n                        'x-yoga-id': id,\n                    },\n                });\n                endResponse(response);\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9wbHVnaW5zL3VzZS1oZWFsdGgtY2hlY2suanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsc0JBQXNCO0FBQ3RCLDBCQUEwQixzRUFBc0UsSUFBSTtBQUNwRztBQUNBLG9CQUFvQixnQ0FBZ0M7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbC15b2dhXFxjanNcXHBsdWdpbnNcXHVzZS1oZWFsdGgtY2hlY2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVzZUhlYWx0aENoZWNrID0gdXNlSGVhbHRoQ2hlY2s7XG5mdW5jdGlvbiB1c2VIZWFsdGhDaGVjayh7IGlkID0gRGF0ZS5ub3coKS50b1N0cmluZygpLCBsb2dnZXIgPSBjb25zb2xlLCBlbmRwb2ludCA9ICcvaGVhbHRoJywgfSA9IHt9KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgb25SZXF1ZXN0KHsgZW5kUmVzcG9uc2UsIGZldGNoQVBJLCByZXF1ZXN0IH0pIHtcbiAgICAgICAgICAgIGlmIChyZXF1ZXN0LnVybC5lbmRzV2l0aChlbmRwb2ludCkpIHtcbiAgICAgICAgICAgICAgICBsb2dnZXIuZGVidWcoJ1Jlc3BvbmRpbmcgSGVhbHRoIENoZWNrJyk7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBuZXcgZmV0Y2hBUEkuUmVzcG9uc2UobnVsbCwge1xuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDIwMCxcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgICAgICAgJ3gteW9nYS1pZCc6IGlkLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGVuZFJlc3BvbnNlKHJlc3BvbnNlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-health-check.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-parser-and-validation-cache.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-parser-and-validation-cache.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useParserAndValidationCache = useParserAndValidationCache;\nconst create_lru_cache_js_1 = __webpack_require__(/*! ../utils/create-lru-cache.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/utils/create-lru-cache.js\");\nfunction useParserAndValidationCache({ documentCache = (0, create_lru_cache_js_1._createLRUCache)(), errorCache = (0, create_lru_cache_js_1._createLRUCache)(), validationCache = true, }) {\n    const validationCacheByRules = (0, create_lru_cache_js_1._createLRUCache)();\n    return {\n        onParse({ params, setParsedDocument }) {\n            const strDocument = params.source.toString();\n            const document = documentCache.get(strDocument);\n            if (document) {\n                setParsedDocument(document);\n                return;\n            }\n            const parserError = errorCache.get(strDocument);\n            if (parserError) {\n                throw parserError;\n            }\n            return ({ result }) => {\n                if (result != null) {\n                    if (result instanceof Error) {\n                        errorCache.set(strDocument, result);\n                    }\n                    else {\n                        documentCache.set(strDocument, result);\n                    }\n                }\n            };\n        },\n        onValidate({ params: { schema, documentAST, rules }, setResult,\n        // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n         }) {\n            /** No schema no cache */\n            if (schema == null) {\n                return;\n            }\n            if (validationCache !== false) {\n                const rulesKey = rules?.map((rule) => rule.name).join(',') || '';\n                let validationCacheBySchema = validationCacheByRules.get(rulesKey);\n                if (!validationCacheBySchema) {\n                    validationCacheBySchema = new WeakMap();\n                    validationCacheByRules.set(rulesKey, validationCacheBySchema);\n                }\n                let validationCacheByDocument = validationCacheBySchema.get(schema);\n                if (!validationCacheByDocument) {\n                    validationCacheByDocument = new WeakMap();\n                    validationCacheBySchema.set(schema, validationCacheByDocument);\n                }\n                const cachedResult = validationCacheByDocument.get(documentAST);\n                if (cachedResult) {\n                    setResult(cachedResult);\n                    return;\n                }\n                return ({ result }) => {\n                    if (result != null) {\n                        validationCacheByDocument?.set(documentAST, result);\n                    }\n                };\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-parser-and-validation-cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-readiness-check.js":
/*!**********************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-readiness-check.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useReadinessCheck = useReadinessCheck;\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\n/**\n * Adds a readiness check for Yoga by simply implementing the `check` option.\n */\nfunction useReadinessCheck({ endpoint = '/ready', check, }) {\n    let urlPattern;\n    return {\n        onYogaInit({ yoga }) {\n            urlPattern = new yoga.fetchAPI.URLPattern({ pathname: endpoint });\n        },\n        onRequest({ request, endResponse, fetchAPI, url }) {\n            if (request.url.endsWith(endpoint) || url.pathname === endpoint || urlPattern.test(url)) {\n                return (0, promise_helpers_1.handleMaybePromise)(() => check({ request, fetchAPI }), readyOrResponse => {\n                    let response;\n                    if (typeof readyOrResponse === 'object') {\n                        response = readyOrResponse;\n                    }\n                    else {\n                        response = new fetchAPI.Response(null, {\n                            status: readyOrResponse === false ? 503 : 200,\n                        });\n                    }\n                    endResponse(response);\n                }, err => {\n                    const isError = err instanceof Error;\n                    const response = new fetchAPI.Response(isError ? err.message : null, {\n                        status: 503,\n                        headers: isError ? { 'content-type': 'text/plain; charset=utf-8' } : {},\n                    });\n                    endResponse(response);\n                });\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-readiness-check.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-request-parser.js":
/*!*********************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-request-parser.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useRequestParser = useRequestParser;\nconst DEFAULT_MATCHER = () => true;\nfunction useRequestParser(options) {\n    const matchFn = options.match || DEFAULT_MATCHER;\n    return {\n        onRequestParse({ request, setRequestParser }) {\n            if (matchFn(request)) {\n                setRequestParser(options.parse);\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9wbHVnaW5zL3VzZS1yZXF1ZXN0LXBhcnNlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsMkJBQTJCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGdyYXBocWwteW9nYVxcY2pzXFxwbHVnaW5zXFx1c2UtcmVxdWVzdC1wYXJzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVzZVJlcXVlc3RQYXJzZXIgPSB1c2VSZXF1ZXN0UGFyc2VyO1xuY29uc3QgREVGQVVMVF9NQVRDSEVSID0gKCkgPT4gdHJ1ZTtcbmZ1bmN0aW9uIHVzZVJlcXVlc3RQYXJzZXIob3B0aW9ucykge1xuICAgIGNvbnN0IG1hdGNoRm4gPSBvcHRpb25zLm1hdGNoIHx8IERFRkFVTFRfTUFUQ0hFUjtcbiAgICByZXR1cm4ge1xuICAgICAgICBvblJlcXVlc3RQYXJzZSh7IHJlcXVlc3QsIHNldFJlcXVlc3RQYXJzZXIgfSkge1xuICAgICAgICAgICAgaWYgKG1hdGNoRm4ocmVxdWVzdCkpIHtcbiAgICAgICAgICAgICAgICBzZXRSZXF1ZXN0UGFyc2VyKG9wdGlvbnMucGFyc2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgIH07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-request-parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-result-processor.js":
/*!***********************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-result-processor.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useResultProcessors = useResultProcessors;\nconst core_1 = __webpack_require__(/*! @envelop/core */ \"(rsc)/./node_modules/@envelop/core/cjs/index.js\");\nconst accept_js_1 = __webpack_require__(/*! ./result-processor/accept.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/accept.js\");\nconst multipart_js_1 = __webpack_require__(/*! ./result-processor/multipart.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/multipart.js\");\nconst regular_js_1 = __webpack_require__(/*! ./result-processor/regular.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/regular.js\");\nconst sse_js_1 = __webpack_require__(/*! ./result-processor/sse.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/result-processor/sse.js\");\nconst multipart = {\n    mediaTypes: ['multipart/mixed'],\n    asyncIterables: true,\n    processResult: multipart_js_1.processMultipartResult,\n};\nfunction getSSEProcessorConfig() {\n    return {\n        mediaTypes: ['text/event-stream'],\n        asyncIterables: true,\n        processResult: (0, sse_js_1.getSSEProcessor)(),\n    };\n}\nconst regular = {\n    mediaTypes: ['application/graphql-response+json', 'application/json'],\n    asyncIterables: false,\n    processResult: regular_js_1.processRegularResult,\n};\nfunction useResultProcessors() {\n    const isSubscriptionRequestMap = new WeakMap();\n    const sse = getSSEProcessorConfig();\n    const defaultList = [sse, multipart, regular];\n    const subscriptionList = [sse, regular];\n    return {\n        onSubscribe({ args: { contextValue } }) {\n            if (contextValue.request) {\n                isSubscriptionRequestMap.set(contextValue.request, true);\n            }\n        },\n        onResultProcess({ request, result, acceptableMediaTypes, setResultProcessor }) {\n            const isSubscriptionRequest = isSubscriptionRequestMap.get(request);\n            const processorConfigList = isSubscriptionRequest ? subscriptionList : defaultList;\n            const requestMediaTypes = (0, accept_js_1.getMediaTypesForRequestInOrder)(request);\n            const isAsyncIterableResult = (0, core_1.isAsyncIterable)(result);\n            for (const resultProcessorConfig of processorConfigList) {\n                for (const requestMediaType of requestMediaTypes) {\n                    if (isAsyncIterableResult && !resultProcessorConfig.asyncIterables) {\n                        continue;\n                    }\n                    for (const processorMediaType of resultProcessorConfig.mediaTypes) {\n                        acceptableMediaTypes.push(processorMediaType);\n                        if ((0, accept_js_1.isMatchingMediaType)(processorMediaType, requestMediaType)) {\n                            setResultProcessor(resultProcessorConfig.processResult, processorMediaType);\n                        }\n                    }\n                }\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9wbHVnaW5zL3VzZS1yZXN1bHQtcHJvY2Vzc29yLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDJCQUEyQjtBQUMzQixlQUFlLG1CQUFPLENBQUMsc0VBQWU7QUFDdEMsb0JBQW9CLG1CQUFPLENBQUMsOEdBQThCO0FBQzFELHVCQUF1QixtQkFBTyxDQUFDLG9IQUFpQztBQUNoRSxxQkFBcUIsbUJBQU8sQ0FBQyxnSEFBK0I7QUFDNUQsaUJBQWlCLG1CQUFPLENBQUMsd0dBQTJCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsUUFBUSxnQkFBZ0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDBCQUEwQiwyREFBMkQ7QUFDckY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGdyYXBocWwteW9nYVxcY2pzXFxwbHVnaW5zXFx1c2UtcmVzdWx0LXByb2Nlc3Nvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudXNlUmVzdWx0UHJvY2Vzc29ycyA9IHVzZVJlc3VsdFByb2Nlc3NvcnM7XG5jb25zdCBjb3JlXzEgPSByZXF1aXJlKFwiQGVudmVsb3AvY29yZVwiKTtcbmNvbnN0IGFjY2VwdF9qc18xID0gcmVxdWlyZShcIi4vcmVzdWx0LXByb2Nlc3Nvci9hY2NlcHQuanNcIik7XG5jb25zdCBtdWx0aXBhcnRfanNfMSA9IHJlcXVpcmUoXCIuL3Jlc3VsdC1wcm9jZXNzb3IvbXVsdGlwYXJ0LmpzXCIpO1xuY29uc3QgcmVndWxhcl9qc18xID0gcmVxdWlyZShcIi4vcmVzdWx0LXByb2Nlc3Nvci9yZWd1bGFyLmpzXCIpO1xuY29uc3Qgc3NlX2pzXzEgPSByZXF1aXJlKFwiLi9yZXN1bHQtcHJvY2Vzc29yL3NzZS5qc1wiKTtcbmNvbnN0IG11bHRpcGFydCA9IHtcbiAgICBtZWRpYVR5cGVzOiBbJ211bHRpcGFydC9taXhlZCddLFxuICAgIGFzeW5jSXRlcmFibGVzOiB0cnVlLFxuICAgIHByb2Nlc3NSZXN1bHQ6IG11bHRpcGFydF9qc18xLnByb2Nlc3NNdWx0aXBhcnRSZXN1bHQsXG59O1xuZnVuY3Rpb24gZ2V0U1NFUHJvY2Vzc29yQ29uZmlnKCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIG1lZGlhVHlwZXM6IFsndGV4dC9ldmVudC1zdHJlYW0nXSxcbiAgICAgICAgYXN5bmNJdGVyYWJsZXM6IHRydWUsXG4gICAgICAgIHByb2Nlc3NSZXN1bHQ6ICgwLCBzc2VfanNfMS5nZXRTU0VQcm9jZXNzb3IpKCksXG4gICAgfTtcbn1cbmNvbnN0IHJlZ3VsYXIgPSB7XG4gICAgbWVkaWFUeXBlczogWydhcHBsaWNhdGlvbi9ncmFwaHFsLXJlc3BvbnNlK2pzb24nLCAnYXBwbGljYXRpb24vanNvbiddLFxuICAgIGFzeW5jSXRlcmFibGVzOiBmYWxzZSxcbiAgICBwcm9jZXNzUmVzdWx0OiByZWd1bGFyX2pzXzEucHJvY2Vzc1JlZ3VsYXJSZXN1bHQsXG59O1xuZnVuY3Rpb24gdXNlUmVzdWx0UHJvY2Vzc29ycygpIHtcbiAgICBjb25zdCBpc1N1YnNjcmlwdGlvblJlcXVlc3RNYXAgPSBuZXcgV2Vha01hcCgpO1xuICAgIGNvbnN0IHNzZSA9IGdldFNTRVByb2Nlc3NvckNvbmZpZygpO1xuICAgIGNvbnN0IGRlZmF1bHRMaXN0ID0gW3NzZSwgbXVsdGlwYXJ0LCByZWd1bGFyXTtcbiAgICBjb25zdCBzdWJzY3JpcHRpb25MaXN0ID0gW3NzZSwgcmVndWxhcl07XG4gICAgcmV0dXJuIHtcbiAgICAgICAgb25TdWJzY3JpYmUoeyBhcmdzOiB7IGNvbnRleHRWYWx1ZSB9IH0pIHtcbiAgICAgICAgICAgIGlmIChjb250ZXh0VmFsdWUucmVxdWVzdCkge1xuICAgICAgICAgICAgICAgIGlzU3Vic2NyaXB0aW9uUmVxdWVzdE1hcC5zZXQoY29udGV4dFZhbHVlLnJlcXVlc3QsIHRydWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBvblJlc3VsdFByb2Nlc3MoeyByZXF1ZXN0LCByZXN1bHQsIGFjY2VwdGFibGVNZWRpYVR5cGVzLCBzZXRSZXN1bHRQcm9jZXNzb3IgfSkge1xuICAgICAgICAgICAgY29uc3QgaXNTdWJzY3JpcHRpb25SZXF1ZXN0ID0gaXNTdWJzY3JpcHRpb25SZXF1ZXN0TWFwLmdldChyZXF1ZXN0KTtcbiAgICAgICAgICAgIGNvbnN0IHByb2Nlc3NvckNvbmZpZ0xpc3QgPSBpc1N1YnNjcmlwdGlvblJlcXVlc3QgPyBzdWJzY3JpcHRpb25MaXN0IDogZGVmYXVsdExpc3Q7XG4gICAgICAgICAgICBjb25zdCByZXF1ZXN0TWVkaWFUeXBlcyA9ICgwLCBhY2NlcHRfanNfMS5nZXRNZWRpYVR5cGVzRm9yUmVxdWVzdEluT3JkZXIpKHJlcXVlc3QpO1xuICAgICAgICAgICAgY29uc3QgaXNBc3luY0l0ZXJhYmxlUmVzdWx0ID0gKDAsIGNvcmVfMS5pc0FzeW5jSXRlcmFibGUpKHJlc3VsdCk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IHJlc3VsdFByb2Nlc3NvckNvbmZpZyBvZiBwcm9jZXNzb3JDb25maWdMaXN0KSB7XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCByZXF1ZXN0TWVkaWFUeXBlIG9mIHJlcXVlc3RNZWRpYVR5cGVzKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc0FzeW5jSXRlcmFibGVSZXN1bHQgJiYgIXJlc3VsdFByb2Nlc3NvckNvbmZpZy5hc3luY0l0ZXJhYmxlcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBwcm9jZXNzb3JNZWRpYVR5cGUgb2YgcmVzdWx0UHJvY2Vzc29yQ29uZmlnLm1lZGlhVHlwZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdGFibGVNZWRpYVR5cGVzLnB1c2gocHJvY2Vzc29yTWVkaWFUeXBlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICgoMCwgYWNjZXB0X2pzXzEuaXNNYXRjaGluZ01lZGlhVHlwZSkocHJvY2Vzc29yTWVkaWFUeXBlLCByZXF1ZXN0TWVkaWFUeXBlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFJlc3VsdFByb2Nlc3NvcihyZXN1bHRQcm9jZXNzb3JDb25maWcucHJvY2Vzc1Jlc3VsdCwgcHJvY2Vzc29yTWVkaWFUeXBlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-result-processor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-schema.js":
/*!*************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-schema.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useSchema = void 0;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst useSchema = (schemaDef) => {\n    if (schemaDef == null) {\n        return {};\n    }\n    if ((0, graphql_1.isSchema)(schemaDef)) {\n        return {\n            onPluginInit({ setSchema }) {\n                setSchema(schemaDef);\n            },\n        };\n    }\n    if ('then' in schemaDef) {\n        let schema;\n        return {\n            onRequestParse() {\n                return {\n                    onRequestParseDone() {\n                        if (!schema) {\n                            return (0, promise_helpers_1.handleMaybePromise)(() => schemaDef, schemaDef => {\n                                schema = schemaDef;\n                            });\n                        }\n                    },\n                };\n            },\n            onEnveloped({ setSchema }) {\n                if (!schema) {\n                    throw new Error(`You provide a promise of a schema but it hasn't been resolved yet. Make sure you use this plugin with GraphQL Yoga.`);\n                }\n                setSchema(schema);\n            },\n        };\n    }\n    const schemaByRequest = new WeakMap();\n    return {\n        onRequestParse({ request, serverContext }) {\n            return {\n                onRequestParseDone() {\n                    return (0, promise_helpers_1.handleMaybePromise)(() => schemaDef({\n                        ...serverContext,\n                        request,\n                    }), schemaDef => {\n                        schemaByRequest.set(request, schemaDef);\n                    });\n                },\n            };\n        },\n        onEnveloped({ setSchema, context }) {\n            if (context?.request == null) {\n                throw new Error('Request object is not available in the context. Make sure you use this plugin with GraphQL Yoga.');\n            }\n            const schema = schemaByRequest.get(context.request);\n            if (schema == null) {\n                throw new Error(`No schema found for this request. Make sure you use this plugin with GraphQL Yoga.`);\n            }\n            setSchema(schema);\n        },\n    };\n};\nexports.useSchema = useSchema;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-schema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-unhandled-route.js":
/*!**********************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/plugins/use-unhandled-route.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.defaultRenderLandingPage = void 0;\nexports.useUnhandledRoute = useUnhandledRoute;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nconst landing_page_html_js_1 = tslib_1.__importDefault(__webpack_require__(/*! ../landing-page-html.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/landing-page-html.js\"));\nconst defaultRenderLandingPage = function defaultRenderLandingPage(opts) {\n    return new opts.fetchAPI.Response(landing_page_html_js_1.default\n        .replace(/__GRAPHIQL_LINK__/g, opts.graphqlEndpoint)\n        .replace(/__REQUEST_PATH__/g, opts.url.pathname), {\n        status: 200,\n        statusText: 'OK',\n        headers: {\n            'Content-Type': 'text/html',\n        },\n    });\n};\nexports.defaultRenderLandingPage = defaultRenderLandingPage;\nfunction useUnhandledRoute(args) {\n    let urlPattern;\n    function getUrlPattern({ URLPattern }) {\n        urlPattern ||= new URLPattern({\n            pathname: args.graphqlEndpoint,\n        });\n        return urlPattern;\n    }\n    const landingPageRenderer = args.landingPageRenderer || exports.defaultRenderLandingPage;\n    return {\n        onRequest({ request, fetchAPI, endResponse, url }) {\n            if (!request.url.endsWith(args.graphqlEndpoint) &&\n                !request.url.endsWith(`${args.graphqlEndpoint}/`) &&\n                url.pathname !== args.graphqlEndpoint &&\n                url.pathname !== `${args.graphqlEndpoint}/` &&\n                !getUrlPattern(fetchAPI).test(url)) {\n                if (args.showLandingPage === true &&\n                    request.method === 'GET' &&\n                    !!request.headers?.get('accept')?.includes('text/html')) {\n                    const landingPage$ = landingPageRenderer({\n                        request,\n                        fetchAPI,\n                        url,\n                        graphqlEndpoint: args.graphqlEndpoint,\n                        get urlPattern() {\n                            return getUrlPattern(fetchAPI);\n                        },\n                    });\n                    if ((0, utils_1.isPromise)(landingPage$)) {\n                        return landingPage$.then(endResponse);\n                    }\n                    endResponse(landingPage$);\n                    return;\n                }\n                endResponse(new fetchAPI.Response('', {\n                    status: 404,\n                    statusText: 'Not Found',\n                }));\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-unhandled-route.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/process-request.js":
/*!**********************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/process-request.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.processResult = processResult;\nexports.processRequest = processRequest;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nfunction processResult({ request, result, fetchAPI, onResultProcessHooks, serverContext, }) {\n    let resultProcessor;\n    const acceptableMediaTypes = [];\n    let acceptedMediaType = '*/*';\n    return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(onResultProcessHooks, onResultProcessHook => onResultProcessHook({\n        request,\n        acceptableMediaTypes,\n        result,\n        setResult(newResult) {\n            result = newResult;\n        },\n        resultProcessor,\n        setResultProcessor(newResultProcessor, newAcceptedMimeType) {\n            resultProcessor = newResultProcessor;\n            acceptedMediaType = newAcceptedMimeType;\n        },\n        serverContext,\n    })), () => {\n        // If no result processor found for this result, return an error\n        if (!resultProcessor) {\n            return new fetchAPI.Response(null, {\n                status: 406,\n                statusText: 'Not Acceptable',\n                headers: {\n                    accept: acceptableMediaTypes.join('; charset=utf-8, '),\n                },\n            });\n        }\n        return resultProcessor(result, fetchAPI, acceptedMediaType);\n    });\n}\nfunction processRequest({ params, enveloped, }) {\n    // Parse GraphQLParams\n    const document = enveloped.parse(params.query);\n    // Validate parsed Document Node\n    const errors = enveloped.validate(enveloped.schema, document);\n    if (errors.length > 0) {\n        return { errors };\n    }\n    // Build the context for the execution\n    return (0, promise_helpers_1.handleMaybePromise)(() => enveloped.contextFactory(), contextValue => {\n        const executionArgs = {\n            schema: enveloped.schema,\n            document,\n            contextValue,\n            variableValues: params.variables,\n            operationName: params.operationName,\n        };\n        // Get the actual operation\n        const operation = (0, graphql_1.getOperationAST)(document, params.operationName);\n        // Choose the right executor\n        const executeFn = operation?.operation === 'subscription' ? enveloped.subscribe : enveloped.execute;\n        // Get the result to be processed\n        return executeFn(executionArgs);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/process-request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/schema.js":
/*!*************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/schema.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createSchema = createSchema;\nconst schema_1 = __webpack_require__(/*! @graphql-tools/schema */ \"(rsc)/./node_modules/@graphql-tools/schema/cjs/index.js\");\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\nfunction createSchema(opts) {\n    return (0, schema_1.makeExecutableSchema)(opts);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9zY2hlbWEuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCLGlCQUFpQixtQkFBTyxDQUFDLHNGQUF1QjtBQUNoRDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxncmFwaHFsLXlvZ2FcXGNqc1xcc2NoZW1hLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jcmVhdGVTY2hlbWEgPSBjcmVhdGVTY2hlbWE7XG5jb25zdCBzY2hlbWFfMSA9IHJlcXVpcmUoXCJAZ3JhcGhxbC10b29scy9zY2hlbWFcIik7XG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWVtcHR5LW9iamVjdC10eXBlXG5mdW5jdGlvbiBjcmVhdGVTY2hlbWEob3B0cykge1xuICAgIHJldHVybiAoMCwgc2NoZW1hXzEubWFrZUV4ZWN1dGFibGVTY2hlbWEpKG9wdHMpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/schema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/server.js":
/*!*************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/server.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.YogaServer = void 0;\nexports.createYoga = createYoga;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/index.mjs\");\nconst core_1 = __webpack_require__(/*! @envelop/core */ \"(rsc)/./node_modules/@envelop/core/cjs/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @envelop/instrumentation */ \"(rsc)/./node_modules/@envelop/instrumentation/cjs/index.js\");\nconst executor_1 = __webpack_require__(/*! @graphql-tools/executor */ \"(rsc)/./node_modules/@graphql-tools/executor/cjs/index.js\");\nconst logger_1 = __webpack_require__(/*! @graphql-yoga/logger */ \"(rsc)/./node_modules/@graphql-yoga/logger/cjs/index.js\");\nconst defaultFetchAPI = tslib_1.__importStar(__webpack_require__(/*! @whatwg-node/fetch */ \"(rsc)/./node_modules/@whatwg-node/fetch/dist/node-ponyfill.js\"));\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst server_1 = __webpack_require__(/*! @whatwg-node/server */ \"(rsc)/./node_modules/@whatwg-node/server/cjs/index.js\");\nconst error_js_1 = __webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/error.js\");\nconst get_js_1 = __webpack_require__(/*! ./plugins/request-parser/get.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/get.js\");\nconst post_form_url_encoded_js_1 = __webpack_require__(/*! ./plugins/request-parser/post-form-url-encoded.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-form-url-encoded.js\");\nconst post_graphql_string_js_1 = __webpack_require__(/*! ./plugins/request-parser/post-graphql-string.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-graphql-string.js\");\nconst post_json_js_1 = __webpack_require__(/*! ./plugins/request-parser/post-json.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-json.js\");\nconst post_multipart_js_1 = __webpack_require__(/*! ./plugins/request-parser/post-multipart.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-parser/post-multipart.js\");\nconst use_check_graphql_query_params_js_1 = __webpack_require__(/*! ./plugins/request-validation/use-check-graphql-query-params.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-check-graphql-query-params.js\");\nconst use_check_method_for_graphql_js_1 = __webpack_require__(/*! ./plugins/request-validation/use-check-method-for-graphql.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-check-method-for-graphql.js\");\nconst use_http_validation_error_js_1 = __webpack_require__(/*! ./plugins/request-validation/use-http-validation-error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-http-validation-error.js\");\nconst use_limit_batching_js_1 = __webpack_require__(/*! ./plugins/request-validation/use-limit-batching.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-limit-batching.js\");\nconst use_prevent_mutation_via_get_js_1 = __webpack_require__(/*! ./plugins/request-validation/use-prevent-mutation-via-get.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/request-validation/use-prevent-mutation-via-get.js\");\nconst use_graphiql_js_1 = __webpack_require__(/*! ./plugins/use-graphiql.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-graphiql.js\");\nconst use_health_check_js_1 = __webpack_require__(/*! ./plugins/use-health-check.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-health-check.js\");\nconst use_parser_and_validation_cache_js_1 = __webpack_require__(/*! ./plugins/use-parser-and-validation-cache.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-parser-and-validation-cache.js\");\nconst use_request_parser_js_1 = __webpack_require__(/*! ./plugins/use-request-parser.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-request-parser.js\");\nconst use_result_processor_js_1 = __webpack_require__(/*! ./plugins/use-result-processor.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-result-processor.js\");\nconst use_schema_js_1 = __webpack_require__(/*! ./plugins/use-schema.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-schema.js\");\nconst use_unhandled_route_js_1 = __webpack_require__(/*! ./plugins/use-unhandled-route.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/plugins/use-unhandled-route.js\");\nconst process_request_js_1 = __webpack_require__(/*! ./process-request.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/process-request.js\");\nconst mask_error_js_1 = __webpack_require__(/*! ./utils/mask-error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/utils/mask-error.js\");\n/**\n * Base class that can be extended to create a GraphQL server with any HTTP server framework.\n * @internal\n */\nclass YogaServer {\n    /**\n     * Instance of envelop\n     */\n    getEnveloped;\n    logger;\n    graphqlEndpoint;\n    fetchAPI;\n    plugins;\n    instrumentation;\n    onRequestParseHooks;\n    onParamsHooks;\n    onExecutionResultHooks;\n    onResultProcessHooks;\n    maskedErrorsOpts;\n    id;\n    version = '5.13.5';\n    constructor(options) {\n        this.id = options?.id ?? 'yoga';\n        this.fetchAPI = {\n            ...defaultFetchAPI,\n        };\n        if (options?.fetchAPI) {\n            for (const key in options.fetchAPI) {\n                if (options.fetchAPI[key]) {\n                    this.fetchAPI[key] = options.fetchAPI[key];\n                }\n            }\n        }\n        const logger = options?.logging == null ? true : options.logging;\n        this.logger =\n            typeof logger === 'boolean'\n                ? logger === true\n                    ? (0, logger_1.createLogger)()\n                    : (0, logger_1.createLogger)('silent')\n                : typeof logger === 'string'\n                    ? (0, logger_1.createLogger)(logger)\n                    : logger;\n        const maskErrorFn = (typeof options?.maskedErrors === 'object' && options.maskedErrors.maskError) || mask_error_js_1.maskError;\n        const maskedErrorSet = new WeakSet();\n        this.maskedErrorsOpts =\n            options?.maskedErrors === false\n                ? null\n                : {\n                    errorMessage: 'Unexpected error.',\n                    ...(typeof options?.maskedErrors === 'object' ? options.maskedErrors : {}),\n                    maskError: (error, message) => {\n                        if (maskedErrorSet.has(error)) {\n                            return error;\n                        }\n                        const newError = maskErrorFn(error, message, this.maskedErrorsOpts?.isDev);\n                        if (newError !== error) {\n                            this.logger.error(error);\n                        }\n                        maskedErrorSet.add(newError);\n                        return newError;\n                    },\n                };\n        const maskedErrors = this.maskedErrorsOpts == null ? null : this.maskedErrorsOpts;\n        let batchingLimit = 0;\n        if (options?.batching) {\n            if (typeof options.batching === 'boolean') {\n                batchingLimit = 10;\n            }\n            else {\n                batchingLimit = options.batching.limit ?? 10;\n            }\n        }\n        this.graphqlEndpoint = options?.graphqlEndpoint || '/graphql';\n        const graphqlEndpoint = this.graphqlEndpoint;\n        this.plugins = [\n            (0, core_1.useEngine)({\n                parse: graphql_1.parse,\n                validate: graphql_1.validate,\n                execute: executor_1.normalizedExecutor,\n                subscribe: executor_1.normalizedExecutor,\n                specifiedRules: graphql_1.specifiedRules,\n            }),\n            // Use the schema provided by the user\n            !!options?.schema && (0, use_schema_js_1.useSchema)(options.schema),\n            options?.context != null &&\n                (0, core_1.useExtendContext)(initialContext => {\n                    if (options?.context) {\n                        if (typeof options.context === 'function') {\n                            return options.context(initialContext);\n                        }\n                        return options.context;\n                    }\n                    return {};\n                }),\n            // Middlewares before processing the incoming HTTP request\n            (0, use_health_check_js_1.useHealthCheck)({\n                id: this.id,\n                logger: this.logger,\n                endpoint: options?.healthCheckEndpoint,\n            }),\n            options?.cors !== false && (0, server_1.useCORS)(options?.cors),\n            options?.graphiql !== false &&\n                (0, use_graphiql_js_1.useGraphiQL)({\n                    graphqlEndpoint,\n                    options: options?.graphiql,\n                    render: options?.renderGraphiQL,\n                    logger: this.logger,\n                }),\n            // Middlewares before the GraphQL execution\n            (0, use_request_parser_js_1.useRequestParser)({\n                match: get_js_1.isGETRequest,\n                parse: get_js_1.parseGETRequest,\n            }),\n            (0, use_request_parser_js_1.useRequestParser)({\n                match: post_json_js_1.isPOSTJsonRequest,\n                parse: post_json_js_1.parsePOSTJsonRequest,\n            }),\n            options?.multipart !== false &&\n                (0, use_request_parser_js_1.useRequestParser)({\n                    match: post_multipart_js_1.isPOSTMultipartRequest,\n                    parse: post_multipart_js_1.parsePOSTMultipartRequest,\n                }),\n            (0, use_request_parser_js_1.useRequestParser)({\n                match: post_graphql_string_js_1.isPOSTGraphQLStringRequest,\n                parse: post_graphql_string_js_1.parsePOSTGraphQLStringRequest,\n            }),\n            (0, use_request_parser_js_1.useRequestParser)({\n                match: post_form_url_encoded_js_1.isPOSTFormUrlEncodedRequest,\n                parse: post_form_url_encoded_js_1.parsePOSTFormUrlEncodedRequest,\n            }),\n            // Middlewares after the GraphQL execution\n            (0, use_result_processor_js_1.useResultProcessors)(),\n            ...(options?.plugins ?? []),\n            // To make sure those are called at the end\n            {\n                onPluginInit({ addPlugin }) {\n                    if (options?.parserAndValidationCache !== false) {\n                        addPlugin(\n                        // @ts-expect-error Add plugins has context but this hook doesn't care\n                        (0, use_parser_and_validation_cache_js_1.useParserAndValidationCache)(!options?.parserAndValidationCache || options?.parserAndValidationCache === true\n                            ? {}\n                            : options?.parserAndValidationCache));\n                    }\n                    // @ts-expect-error Add plugins has context but this hook doesn't care\n                    addPlugin((0, use_limit_batching_js_1.useLimitBatching)(batchingLimit));\n                    // @ts-expect-error Add plugins has context but this hook doesn't care\n                    addPlugin((0, use_check_graphql_query_params_js_1.useCheckGraphQLQueryParams)(options?.extraParamNames));\n                    const showLandingPage = !!(options?.landingPage ?? true);\n                    addPlugin(\n                    // @ts-expect-error Add plugins has context but this hook doesn't care\n                    (0, use_unhandled_route_js_1.useUnhandledRoute)({\n                        graphqlEndpoint,\n                        showLandingPage,\n                        landingPageRenderer: typeof options?.landingPage === 'function' ? options.landingPage : undefined,\n                    }));\n                    // We check the method after user-land plugins because the plugin might support more methods (like graphql-sse).\n                    // @ts-expect-error Add plugins has context but this hook doesn't care\n                    addPlugin((0, use_check_method_for_graphql_js_1.useCheckMethodForGraphQL)());\n                    // We make sure that the user doesn't send a mutation with GET\n                    // @ts-expect-error Add plugins has context but this hook doesn't care\n                    addPlugin((0, use_prevent_mutation_via_get_js_1.usePreventMutationViaGET)());\n                    if (maskedErrors) {\n                        // Make sure we always throw AbortError instead of masking it!\n                        addPlugin({\n                            onSubscribe() {\n                                return {\n                                    onSubscribeError({ error }) {\n                                        if ((0, error_js_1.isAbortError)(error)) {\n                                            throw error;\n                                        }\n                                    },\n                                };\n                            },\n                        });\n                        addPlugin((0, core_1.useMaskedErrors)(maskedErrors));\n                    }\n                    addPlugin(\n                    // We handle validation errors at the end\n                    (0, use_http_validation_error_js_1.useHTTPValidationError)());\n                },\n            },\n        ];\n        this.getEnveloped = (0, core_1.envelop)({\n            plugins: this.plugins,\n        });\n        this.plugins = this.getEnveloped._plugins;\n        this.onRequestParseHooks = [];\n        this.onParamsHooks = [];\n        this.onExecutionResultHooks = [];\n        this.onResultProcessHooks = [];\n        for (const plugin of this.plugins) {\n            if (plugin) {\n                if (plugin.onYogaInit) {\n                    plugin.onYogaInit({\n                        yoga: this,\n                    });\n                }\n                if (plugin.onRequestParse) {\n                    this.onRequestParseHooks.push(plugin.onRequestParse);\n                }\n                if (plugin.onParams) {\n                    this.onParamsHooks.push(plugin.onParams);\n                }\n                if (plugin.onExecutionResult) {\n                    this.onExecutionResultHooks.push(plugin.onExecutionResult);\n                }\n                if (plugin.onResultProcess) {\n                    this.onResultProcessHooks.push(plugin.onResultProcess);\n                }\n                if (plugin.instrumentation) {\n                    this.instrumentation = this.instrumentation\n                        ? (0, instrumentation_1.chain)(this.instrumentation, plugin.instrumentation)\n                        : plugin.instrumentation;\n                }\n            }\n        }\n    }\n    handleParams = ({ request, context, params }) => {\n        const additionalContext = context['request'] === request\n            ? {\n                params,\n            }\n            : {\n                request,\n                params,\n            };\n        Object.assign(context, additionalContext);\n        const enveloped = this.getEnveloped(context);\n        this.logger.debug(`Processing GraphQL Parameters`);\n        return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.handleMaybePromise)(() => (0, process_request_js_1.processRequest)({ params, enveloped }), result => {\n            this.logger.debug(`Processing GraphQL Parameters done.`);\n            return result;\n        }, error => {\n            const errors = (0, error_js_1.handleError)(error, this.maskedErrorsOpts, this.logger);\n            return {\n                errors,\n            };\n        }), result => {\n            if ((0, core_1.isAsyncIterable)(result)) {\n                result = (0, promise_helpers_1.mapAsyncIterator)(result, v => v, (error) => {\n                    if (error.name === 'AbortError') {\n                        this.logger.debug(`Request aborted`);\n                        throw error;\n                    }\n                    const errors = (0, error_js_1.handleError)(error, this.maskedErrorsOpts, this.logger);\n                    return {\n                        errors,\n                    };\n                });\n            }\n            return result;\n        });\n    };\n    getResultForParams = ({ params, request, }, context) => {\n        let result;\n        let paramsHandler = this.handleParams;\n        return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(this.onParamsHooks, onParamsHook => onParamsHook({\n            params,\n            request,\n            setParams(newParams) {\n                params = newParams;\n            },\n            paramsHandler,\n            setParamsHandler(newHandler) {\n                paramsHandler = newHandler;\n            },\n            setResult(newResult) {\n                result = newResult;\n            },\n            fetchAPI: this.fetchAPI,\n            context,\n        })), () => (0, promise_helpers_1.handleMaybePromise)(() => result ||\n            paramsHandler({\n                request,\n                params,\n                context: context,\n            }), result => (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(this.onExecutionResultHooks, onExecutionResult => onExecutionResult({\n            result,\n            setResult(newResult) {\n                result = newResult;\n            },\n            request,\n            context: context,\n        })), () => result)));\n    };\n    parseRequest = (request, serverContext) => {\n        let url = new Proxy({}, {\n            get: (_target, prop, _receiver) => {\n                url = new this.fetchAPI.URL(request.url, 'http://localhost');\n                return Reflect.get(url, prop, url);\n            },\n        });\n        let requestParser;\n        const onRequestParseDoneList = [];\n        return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(this.onRequestParseHooks, onRequestParse => (0, promise_helpers_1.handleMaybePromise)(() => onRequestParse({\n            request,\n            url,\n            requestParser,\n            serverContext,\n            setRequestParser(parser) {\n                requestParser = parser;\n            },\n        }), requestParseHookResult => requestParseHookResult?.onRequestParseDone), onRequestParseDoneList), () => {\n            this.logger.debug(`Parsing request to extract GraphQL parameters`);\n            if (!requestParser) {\n                return {\n                    response: new this.fetchAPI.Response(null, {\n                        status: 415,\n                        statusText: 'Unsupported Media Type',\n                    }),\n                };\n            }\n            return (0, promise_helpers_1.handleMaybePromise)(() => requestParser(request), requestParserResult => {\n                return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsyncVoid)(onRequestParseDoneList, onRequestParseDone => onRequestParseDone({\n                    requestParserResult,\n                    setRequestParserResult(newParams) {\n                        requestParserResult = newParams;\n                    },\n                })), () => ({\n                    requestParserResult,\n                }));\n            });\n        });\n    };\n    handle = (request, serverContext) => {\n        const instrumented = this.instrumentation && (0, instrumentation_1.getInstrumented)({ request });\n        const parseRequest = this.instrumentation?.requestParse\n            ? instrumented.asyncFn(this.instrumentation?.requestParse, this.parseRequest)\n            : this.parseRequest;\n        return (0, promise_helpers_1.unfakePromise)((0, promise_helpers_1.fakePromise)()\n            .then(() => parseRequest(request, serverContext))\n            .then(({ response, requestParserResult }) => {\n            if (response) {\n                return response;\n            }\n            const getResultForParams = this.instrumentation?.operation\n                ? (payload, context) => {\n                    const instrumented = (0, instrumentation_1.getInstrumented)({ context, request: payload.request });\n                    const tracedHandler = instrumented.asyncFn(this.instrumentation?.operation, this.getResultForParams);\n                    return tracedHandler(payload, context);\n                }\n                : this.getResultForParams;\n            return (0, promise_helpers_1.handleMaybePromise)(() => (Array.isArray(requestParserResult)\n                ? Promise.all(requestParserResult.map(params => getResultForParams({\n                    params,\n                    request,\n                }, Object.create(serverContext))))\n                : getResultForParams({\n                    params: requestParserResult,\n                    request,\n                }, serverContext)), result => {\n                const tracedProcessResult = this.instrumentation?.resultProcess\n                    ? instrumented.asyncFn(this.instrumentation.resultProcess, (process_request_js_1.processResult))\n                    : process_request_js_1.processResult;\n                return tracedProcessResult({\n                    request,\n                    result,\n                    fetchAPI: this.fetchAPI,\n                    onResultProcessHooks: this.onResultProcessHooks,\n                    serverContext,\n                });\n            });\n        })\n            .catch(error => {\n            const errors = (0, error_js_1.handleError)(error, this.maskedErrorsOpts, this.logger);\n            const result = {\n                errors,\n            };\n            return (0, process_request_js_1.processResult)({\n                request,\n                result,\n                fetchAPI: this.fetchAPI,\n                onResultProcessHooks: this.onResultProcessHooks,\n                serverContext,\n            });\n        }));\n    };\n}\nexports.YogaServer = YogaServer;\nfunction createYoga(options) {\n    const server = new YogaServer(options);\n    return (0, server_1.createServerAdapter)(server, {\n        fetchAPI: server.fetchAPI,\n        plugins: server['plugins'],\n        disposeOnProcessTerminate: options.disposeOnProcessTerminate,\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/server.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/subscription.js":
/*!*******************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/subscription.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\ntslib_1.__exportStar(__webpack_require__(/*! @graphql-yoga/subscription */ \"(rsc)/./node_modules/@graphql-yoga/subscription/cjs/index.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy9zdWJzY3JpcHRpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0JBQWdCLG1CQUFPLENBQUMsdURBQU87QUFDL0IscUJBQXFCLG1CQUFPLENBQUMsZ0dBQTRCIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGdyYXBocWwteW9nYVxcY2pzXFxzdWJzY3JpcHRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5jb25zdCB0c2xpYl8xID0gcmVxdWlyZShcInRzbGliXCIpO1xudHNsaWJfMS5fX2V4cG9ydFN0YXIocmVxdWlyZShcIkBncmFwaHFsLXlvZ2Evc3Vic2NyaXB0aW9uXCIpLCBleHBvcnRzKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/subscription.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/types.js":
/*!************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/types.js ***!
  \************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/utils/create-lru-cache.js":
/*!*****************************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/utils/create-lru-cache.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createLRUCache = void 0;\nexports._createLRUCache = _createLRUCache;\n/* eslint-disable @typescript-eslint/no-empty-object-type */\nconst lru_cache_1 = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/graphql-yoga/node_modules/lru-cache/dist/commonjs/index.js\");\n/**\n * @deprecated In the next major, `createLRUCache` will be renamed to `_createLRUCache`,\n * and the current `createLRUCache` will be removed.\n */\nexports.createLRUCache = _createLRUCache;\nconst DEFAULT_MAX = 1024;\nconst DEFAULT_TTL = 3_600_000;\n/**\n * @internal This is an internal utility, and you should use it with your own risk.\n * This utility can have breaking changes in the future.\n */\nfunction _createLRUCache({ max = DEFAULT_MAX, ttl = DEFAULT_TTL, } = {}) {\n    return new lru_cache_1.LRUCache({ max, ttl });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC15b2dhL2Nqcy91dGlscy9jcmVhdGUtbHJ1LWNhY2hlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHNCQUFzQjtBQUN0Qix1QkFBdUI7QUFDdkI7QUFDQSxvQkFBb0IsbUJBQU8sQ0FBQyxrR0FBVztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsd0NBQXdDLElBQUk7QUFDdkUsc0NBQXNDLFVBQVU7QUFDaEQiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbC15b2dhXFxjanNcXHV0aWxzXFxjcmVhdGUtbHJ1LWNhY2hlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jcmVhdGVMUlVDYWNoZSA9IHZvaWQgMDtcbmV4cG9ydHMuX2NyZWF0ZUxSVUNhY2hlID0gX2NyZWF0ZUxSVUNhY2hlO1xuLyogZXNsaW50LWRpc2FibGUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWVtcHR5LW9iamVjdC10eXBlICovXG5jb25zdCBscnVfY2FjaGVfMSA9IHJlcXVpcmUoXCJscnUtY2FjaGVcIik7XG4vKipcbiAqIEBkZXByZWNhdGVkIEluIHRoZSBuZXh0IG1ham9yLCBgY3JlYXRlTFJVQ2FjaGVgIHdpbGwgYmUgcmVuYW1lZCB0byBgX2NyZWF0ZUxSVUNhY2hlYCxcbiAqIGFuZCB0aGUgY3VycmVudCBgY3JlYXRlTFJVQ2FjaGVgIHdpbGwgYmUgcmVtb3ZlZC5cbiAqL1xuZXhwb3J0cy5jcmVhdGVMUlVDYWNoZSA9IF9jcmVhdGVMUlVDYWNoZTtcbmNvbnN0IERFRkFVTFRfTUFYID0gMTAyNDtcbmNvbnN0IERFRkFVTFRfVFRMID0gM182MDBfMDAwO1xuLyoqXG4gKiBAaW50ZXJuYWwgVGhpcyBpcyBhbiBpbnRlcm5hbCB1dGlsaXR5LCBhbmQgeW91IHNob3VsZCB1c2UgaXQgd2l0aCB5b3VyIG93biByaXNrLlxuICogVGhpcyB1dGlsaXR5IGNhbiBoYXZlIGJyZWFraW5nIGNoYW5nZXMgaW4gdGhlIGZ1dHVyZS5cbiAqL1xuZnVuY3Rpb24gX2NyZWF0ZUxSVUNhY2hlKHsgbWF4ID0gREVGQVVMVF9NQVgsIHR0bCA9IERFRkFVTFRfVFRMLCB9ID0ge30pIHtcbiAgICByZXR1cm4gbmV3IGxydV9jYWNoZV8xLkxSVUNhY2hlKHsgbWF4LCB0dGwgfSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/utils/create-lru-cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/cjs/utils/mask-error.js":
/*!***********************************************************!*\
  !*** ./node_modules/graphql-yoga/cjs/utils/mask-error.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.maskError = void 0;\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/@graphql-tools/utils/cjs/index.js\");\nconst error_js_1 = __webpack_require__(/*! ../error.js */ \"(rsc)/./node_modules/graphql-yoga/cjs/error.js\");\nfunction serializeError(error) {\n    if ((0, error_js_1.isGraphQLError)(error)) {\n        return error.toJSON();\n    }\n    if (error instanceof Error) {\n        return {\n            message: error.message,\n            stack: error.stack,\n            cause: error.cause,\n        };\n    }\n    return error;\n}\nconst maskError = (error, message, isDev = globalThis.process?.env?.['NODE_ENV'] === 'development') => {\n    if ((0, error_js_1.isOriginalGraphQLError)(error)) {\n        return error;\n    }\n    const errorExtensions = {\n        code: 'INTERNAL_SERVER_ERROR',\n        unexpected: true,\n    };\n    const errorOptions = {\n        extensions: errorExtensions,\n    };\n    if ((0, error_js_1.isGraphQLError)(error)) {\n        errorOptions.nodes = error.nodes;\n        errorOptions.source = error.source;\n        errorOptions.positions = error.positions;\n        errorOptions.path = error.path;\n        if (isDev && error.originalError) {\n            errorExtensions['originalError'] = serializeError(error.originalError);\n        }\n        if (error.extensions?.['http']) {\n            errorExtensions['http'] = error.extensions['http'];\n        }\n    }\n    else if (isDev) {\n        errorExtensions['originalError'] = serializeError(error);\n    }\n    return (0, utils_1.createGraphQLError)(message, errorOptions);\n};\nexports.maskError = maskError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/cjs/utils/mask-error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-yoga/node_modules/lru-cache/dist/commonjs/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/graphql-yoga/node_modules/lru-cache/dist/commonjs/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n/**\n * @module LRUCache\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LRUCache = void 0;\nconst perf = typeof performance === 'object' &&\n    performance &&\n    typeof performance.now === 'function'\n    ? performance\n    : Date;\nconst warned = new Set();\n/* c8 ignore start */\nconst PROCESS = (typeof process === 'object' && !!process ? process : {});\n/* c8 ignore start */\nconst emitWarning = (msg, type, code, fn) => {\n    typeof PROCESS.emitWarning === 'function'\n        ? PROCESS.emitWarning(msg, type, code, fn)\n        : console.error(`[${code}] ${type}: ${msg}`);\n};\nlet AC = globalThis.AbortController;\nlet AS = globalThis.AbortSignal;\n/* c8 ignore start */\nif (typeof AC === 'undefined') {\n    //@ts-ignore\n    AS = class AbortSignal {\n        onabort;\n        _onabort = [];\n        reason;\n        aborted = false;\n        addEventListener(_, fn) {\n            this._onabort.push(fn);\n        }\n    };\n    //@ts-ignore\n    AC = class AbortController {\n        constructor() {\n            warnACPolyfill();\n        }\n        signal = new AS();\n        abort(reason) {\n            if (this.signal.aborted)\n                return;\n            //@ts-ignore\n            this.signal.reason = reason;\n            //@ts-ignore\n            this.signal.aborted = true;\n            //@ts-ignore\n            for (const fn of this.signal._onabort) {\n                fn(reason);\n            }\n            this.signal.onabort?.(reason);\n        }\n    };\n    let printACPolyfillWarning = PROCESS.env?.LRU_CACHE_IGNORE_AC_WARNING !== '1';\n    const warnACPolyfill = () => {\n        if (!printACPolyfillWarning)\n            return;\n        printACPolyfillWarning = false;\n        emitWarning('AbortController is not defined. If using lru-cache in ' +\n            'node 14, load an AbortController polyfill from the ' +\n            '`node-abort-controller` package. A minimal polyfill is ' +\n            'provided for use by LRUCache.fetch(), but it should not be ' +\n            'relied upon in other contexts (eg, passing it to other APIs that ' +\n            'use AbortController/AbortSignal might have undesirable effects). ' +\n            'You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.', 'NO_ABORT_CONTROLLER', 'ENOTSUP', warnACPolyfill);\n    };\n}\n/* c8 ignore stop */\nconst shouldWarn = (code) => !warned.has(code);\nconst TYPE = Symbol('type');\nconst isPosInt = (n) => n && n === Math.floor(n) && n > 0 && isFinite(n);\n/* c8 ignore start */\n// This is a little bit ridiculous, tbh.\n// The maximum array length is 2^32-1 or thereabouts on most JS impls.\n// And well before that point, you're caching the entire world, I mean,\n// that's ~32GB of just integers for the next/prev links, plus whatever\n// else to hold that many keys and values.  Just filling the memory with\n// zeroes at init time is brutal when you get that big.\n// But why not be complete?\n// Maybe in the future, these limits will have expanded.\nconst getUintArray = (max) => !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n        ? Uint8Array\n        : max <= Math.pow(2, 16)\n            ? Uint16Array\n            : max <= Math.pow(2, 32)\n                ? Uint32Array\n                : max <= Number.MAX_SAFE_INTEGER\n                    ? ZeroArray\n                    : null;\n/* c8 ignore stop */\nclass ZeroArray extends Array {\n    constructor(size) {\n        super(size);\n        this.fill(0);\n    }\n}\nclass Stack {\n    heap;\n    length;\n    // private constructor\n    static #constructing = false;\n    static create(max) {\n        const HeapCls = getUintArray(max);\n        if (!HeapCls)\n            return [];\n        Stack.#constructing = true;\n        const s = new Stack(max, HeapCls);\n        Stack.#constructing = false;\n        return s;\n    }\n    constructor(max, HeapCls) {\n        /* c8 ignore start */\n        if (!Stack.#constructing) {\n            throw new TypeError('instantiate Stack using Stack.create(n)');\n        }\n        /* c8 ignore stop */\n        this.heap = new HeapCls(max);\n        this.length = 0;\n    }\n    push(n) {\n        this.heap[this.length++] = n;\n    }\n    pop() {\n        return this.heap[--this.length];\n    }\n}\n/**\n * Default export, the thing you're using this module to get.\n *\n * The `K` and `V` types define the key and value types, respectively. The\n * optional `FC` type defines the type of the `context` object passed to\n * `cache.fetch()` and `cache.memo()`.\n *\n * Keys and values **must not** be `null` or `undefined`.\n *\n * All properties from the options object (with the exception of `max`,\n * `maxSize`, `fetchMethod`, `memoMethod`, `dispose` and `disposeAfter`) are\n * added as normal public members. (The listed options are read-only getters.)\n *\n * Changing any of these will alter the defaults for subsequent method calls.\n */\nclass LRUCache {\n    // options that cannot be changed without disaster\n    #max;\n    #maxSize;\n    #dispose;\n    #disposeAfter;\n    #fetchMethod;\n    #memoMethod;\n    /**\n     * {@link LRUCache.OptionsBase.ttl}\n     */\n    ttl;\n    /**\n     * {@link LRUCache.OptionsBase.ttlResolution}\n     */\n    ttlResolution;\n    /**\n     * {@link LRUCache.OptionsBase.ttlAutopurge}\n     */\n    ttlAutopurge;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnGet}\n     */\n    updateAgeOnGet;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnHas}\n     */\n    updateAgeOnHas;\n    /**\n     * {@link LRUCache.OptionsBase.allowStale}\n     */\n    allowStale;\n    /**\n     * {@link LRUCache.OptionsBase.noDisposeOnSet}\n     */\n    noDisposeOnSet;\n    /**\n     * {@link LRUCache.OptionsBase.noUpdateTTL}\n     */\n    noUpdateTTL;\n    /**\n     * {@link LRUCache.OptionsBase.maxEntrySize}\n     */\n    maxEntrySize;\n    /**\n     * {@link LRUCache.OptionsBase.sizeCalculation}\n     */\n    sizeCalculation;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnFetchRejection}\n     */\n    noDeleteOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnStaleGet}\n     */\n    noDeleteOnStaleGet;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort}\n     */\n    allowStaleOnFetchAbort;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchRejection}\n     */\n    allowStaleOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.ignoreFetchAbort}\n     */\n    ignoreFetchAbort;\n    // computed properties\n    #size;\n    #calculatedSize;\n    #keyMap;\n    #keyList;\n    #valList;\n    #next;\n    #prev;\n    #head;\n    #tail;\n    #free;\n    #disposed;\n    #sizes;\n    #starts;\n    #ttls;\n    #hasDispose;\n    #hasFetchMethod;\n    #hasDisposeAfter;\n    /**\n     * Do not call this method unless you need to inspect the\n     * inner workings of the cache.  If anything returned by this\n     * object is modified in any way, strange breakage may occur.\n     *\n     * These fields are private for a reason!\n     *\n     * @internal\n     */\n    static unsafeExposeInternals(c) {\n        return {\n            // properties\n            starts: c.#starts,\n            ttls: c.#ttls,\n            sizes: c.#sizes,\n            keyMap: c.#keyMap,\n            keyList: c.#keyList,\n            valList: c.#valList,\n            next: c.#next,\n            prev: c.#prev,\n            get head() {\n                return c.#head;\n            },\n            get tail() {\n                return c.#tail;\n            },\n            free: c.#free,\n            // methods\n            isBackgroundFetch: (p) => c.#isBackgroundFetch(p),\n            backgroundFetch: (k, index, options, context) => c.#backgroundFetch(k, index, options, context),\n            moveToTail: (index) => c.#moveToTail(index),\n            indexes: (options) => c.#indexes(options),\n            rindexes: (options) => c.#rindexes(options),\n            isStale: (index) => c.#isStale(index),\n        };\n    }\n    // Protected read-only members\n    /**\n     * {@link LRUCache.OptionsBase.max} (read-only)\n     */\n    get max() {\n        return this.#max;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.maxSize} (read-only)\n     */\n    get maxSize() {\n        return this.#maxSize;\n    }\n    /**\n     * The total computed size of items in the cache (read-only)\n     */\n    get calculatedSize() {\n        return this.#calculatedSize;\n    }\n    /**\n     * The number of items stored in the cache (read-only)\n     */\n    get size() {\n        return this.#size;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.fetchMethod} (read-only)\n     */\n    get fetchMethod() {\n        return this.#fetchMethod;\n    }\n    get memoMethod() {\n        return this.#memoMethod;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.dispose} (read-only)\n     */\n    get dispose() {\n        return this.#dispose;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.disposeAfter} (read-only)\n     */\n    get disposeAfter() {\n        return this.#disposeAfter;\n    }\n    constructor(options) {\n        const { max = 0, ttl, ttlResolution = 1, ttlAutopurge, updateAgeOnGet, updateAgeOnHas, allowStale, dispose, disposeAfter, noDisposeOnSet, noUpdateTTL, maxSize = 0, maxEntrySize = 0, sizeCalculation, fetchMethod, memoMethod, noDeleteOnFetchRejection, noDeleteOnStaleGet, allowStaleOnFetchRejection, allowStaleOnFetchAbort, ignoreFetchAbort, } = options;\n        if (max !== 0 && !isPosInt(max)) {\n            throw new TypeError('max option must be a nonnegative integer');\n        }\n        const UintArray = max ? getUintArray(max) : Array;\n        if (!UintArray) {\n            throw new Error('invalid max value: ' + max);\n        }\n        this.#max = max;\n        this.#maxSize = maxSize;\n        this.maxEntrySize = maxEntrySize || this.#maxSize;\n        this.sizeCalculation = sizeCalculation;\n        if (this.sizeCalculation) {\n            if (!this.#maxSize && !this.maxEntrySize) {\n                throw new TypeError('cannot set sizeCalculation without setting maxSize or maxEntrySize');\n            }\n            if (typeof this.sizeCalculation !== 'function') {\n                throw new TypeError('sizeCalculation set to non-function');\n            }\n        }\n        if (memoMethod !== undefined &&\n            typeof memoMethod !== 'function') {\n            throw new TypeError('memoMethod must be a function if defined');\n        }\n        this.#memoMethod = memoMethod;\n        if (fetchMethod !== undefined &&\n            typeof fetchMethod !== 'function') {\n            throw new TypeError('fetchMethod must be a function if specified');\n        }\n        this.#fetchMethod = fetchMethod;\n        this.#hasFetchMethod = !!fetchMethod;\n        this.#keyMap = new Map();\n        this.#keyList = new Array(max).fill(undefined);\n        this.#valList = new Array(max).fill(undefined);\n        this.#next = new UintArray(max);\n        this.#prev = new UintArray(max);\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free = Stack.create(max);\n        this.#size = 0;\n        this.#calculatedSize = 0;\n        if (typeof dispose === 'function') {\n            this.#dispose = dispose;\n        }\n        if (typeof disposeAfter === 'function') {\n            this.#disposeAfter = disposeAfter;\n            this.#disposed = [];\n        }\n        else {\n            this.#disposeAfter = undefined;\n            this.#disposed = undefined;\n        }\n        this.#hasDispose = !!this.#dispose;\n        this.#hasDisposeAfter = !!this.#disposeAfter;\n        this.noDisposeOnSet = !!noDisposeOnSet;\n        this.noUpdateTTL = !!noUpdateTTL;\n        this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection;\n        this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection;\n        this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort;\n        this.ignoreFetchAbort = !!ignoreFetchAbort;\n        // NB: maxEntrySize is set to maxSize if it's set\n        if (this.maxEntrySize !== 0) {\n            if (this.#maxSize !== 0) {\n                if (!isPosInt(this.#maxSize)) {\n                    throw new TypeError('maxSize must be a positive integer if specified');\n                }\n            }\n            if (!isPosInt(this.maxEntrySize)) {\n                throw new TypeError('maxEntrySize must be a positive integer if specified');\n            }\n            this.#initializeSizeTracking();\n        }\n        this.allowStale = !!allowStale;\n        this.noDeleteOnStaleGet = !!noDeleteOnStaleGet;\n        this.updateAgeOnGet = !!updateAgeOnGet;\n        this.updateAgeOnHas = !!updateAgeOnHas;\n        this.ttlResolution =\n            isPosInt(ttlResolution) || ttlResolution === 0\n                ? ttlResolution\n                : 1;\n        this.ttlAutopurge = !!ttlAutopurge;\n        this.ttl = ttl || 0;\n        if (this.ttl) {\n            if (!isPosInt(this.ttl)) {\n                throw new TypeError('ttl must be a positive integer if specified');\n            }\n            this.#initializeTTLTracking();\n        }\n        // do not allow completely unbounded caches\n        if (this.#max === 0 && this.ttl === 0 && this.#maxSize === 0) {\n            throw new TypeError('At least one of max, maxSize, or ttl is required');\n        }\n        if (!this.ttlAutopurge && !this.#max && !this.#maxSize) {\n            const code = 'LRU_CACHE_UNBOUNDED';\n            if (shouldWarn(code)) {\n                warned.add(code);\n                const msg = 'TTL caching without ttlAutopurge, max, or maxSize can ' +\n                    'result in unbounded memory consumption.';\n                emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache);\n            }\n        }\n    }\n    /**\n     * Return the number of ms left in the item's TTL. If item is not in cache,\n     * returns `0`. Returns `Infinity` if item is in cache without a defined TTL.\n     */\n    getRemainingTTL(key) {\n        return this.#keyMap.has(key) ? Infinity : 0;\n    }\n    #initializeTTLTracking() {\n        const ttls = new ZeroArray(this.#max);\n        const starts = new ZeroArray(this.#max);\n        this.#ttls = ttls;\n        this.#starts = starts;\n        this.#setItemTTL = (index, ttl, start = perf.now()) => {\n            starts[index] = ttl !== 0 ? start : 0;\n            ttls[index] = ttl;\n            if (ttl !== 0 && this.ttlAutopurge) {\n                const t = setTimeout(() => {\n                    if (this.#isStale(index)) {\n                        this.#delete(this.#keyList[index], 'expire');\n                    }\n                }, ttl + 1);\n                // unref() not supported on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n        };\n        this.#updateItemAge = index => {\n            starts[index] = ttls[index] !== 0 ? perf.now() : 0;\n        };\n        this.#statusTTL = (status, index) => {\n            if (ttls[index]) {\n                const ttl = ttls[index];\n                const start = starts[index];\n                /* c8 ignore next */\n                if (!ttl || !start)\n                    return;\n                status.ttl = ttl;\n                status.start = start;\n                status.now = cachedNow || getNow();\n                const age = status.now - start;\n                status.remainingTTL = ttl - age;\n            }\n        };\n        // debounce calls to perf.now() to 1s so we're not hitting\n        // that costly call repeatedly.\n        let cachedNow = 0;\n        const getNow = () => {\n            const n = perf.now();\n            if (this.ttlResolution > 0) {\n                cachedNow = n;\n                const t = setTimeout(() => (cachedNow = 0), this.ttlResolution);\n                // not available on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n            return n;\n        };\n        this.getRemainingTTL = key => {\n            const index = this.#keyMap.get(key);\n            if (index === undefined) {\n                return 0;\n            }\n            const ttl = ttls[index];\n            const start = starts[index];\n            if (!ttl || !start) {\n                return Infinity;\n            }\n            const age = (cachedNow || getNow()) - start;\n            return ttl - age;\n        };\n        this.#isStale = index => {\n            const s = starts[index];\n            const t = ttls[index];\n            return !!t && !!s && (cachedNow || getNow()) - s > t;\n        };\n    }\n    // conditionally set private methods related to TTL\n    #updateItemAge = () => { };\n    #statusTTL = () => { };\n    #setItemTTL = () => { };\n    /* c8 ignore stop */\n    #isStale = () => false;\n    #initializeSizeTracking() {\n        const sizes = new ZeroArray(this.#max);\n        this.#calculatedSize = 0;\n        this.#sizes = sizes;\n        this.#removeItemSize = index => {\n            this.#calculatedSize -= sizes[index];\n            sizes[index] = 0;\n        };\n        this.#requireSize = (k, v, size, sizeCalculation) => {\n            // provisionally accept background fetches.\n            // actual value size will be checked when they return.\n            if (this.#isBackgroundFetch(v)) {\n                return 0;\n            }\n            if (!isPosInt(size)) {\n                if (sizeCalculation) {\n                    if (typeof sizeCalculation !== 'function') {\n                        throw new TypeError('sizeCalculation must be a function');\n                    }\n                    size = sizeCalculation(v, k);\n                    if (!isPosInt(size)) {\n                        throw new TypeError('sizeCalculation return invalid (expect positive integer)');\n                    }\n                }\n                else {\n                    throw new TypeError('invalid size value (must be positive integer). ' +\n                        'When maxSize or maxEntrySize is used, sizeCalculation ' +\n                        'or size must be set.');\n                }\n            }\n            return size;\n        };\n        this.#addItemSize = (index, size, status) => {\n            sizes[index] = size;\n            if (this.#maxSize) {\n                const maxSize = this.#maxSize - sizes[index];\n                while (this.#calculatedSize > maxSize) {\n                    this.#evict(true);\n                }\n            }\n            this.#calculatedSize += sizes[index];\n            if (status) {\n                status.entrySize = size;\n                status.totalCalculatedSize = this.#calculatedSize;\n            }\n        };\n    }\n    #removeItemSize = _i => { };\n    #addItemSize = (_i, _s, _st) => { };\n    #requireSize = (_k, _v, size, sizeCalculation) => {\n        if (size || sizeCalculation) {\n            throw new TypeError('cannot set size without setting maxSize or maxEntrySize on cache');\n        }\n        return 0;\n    };\n    *#indexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#tail; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#head) {\n                    break;\n                }\n                else {\n                    i = this.#prev[i];\n                }\n            }\n        }\n    }\n    *#rindexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#head; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#tail) {\n                    break;\n                }\n                else {\n                    i = this.#next[i];\n                }\n            }\n        }\n    }\n    #isValidIndex(index) {\n        return (index !== undefined &&\n            this.#keyMap.get(this.#keyList[index]) === index);\n    }\n    /**\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from most recently used to least recently used.\n     */\n    *entries() {\n        for (const i of this.#indexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.entries}\n     *\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from least recently used to most recently used.\n     */\n    *rentries() {\n        for (const i of this.#rindexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the keys in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *keys() {\n        for (const i of this.#indexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.keys}\n     *\n     * Return a generator yielding the keys in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rkeys() {\n        for (const i of this.#rindexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the values in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *values() {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.values}\n     *\n     * Return a generator yielding the values in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rvalues() {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Iterating over the cache itself yields the same results as\n     * {@link LRUCache.entries}\n     */\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    /**\n     * A String value that is used in the creation of the default string\n     * description of an object. Called by the built-in method\n     * `Object.prototype.toString`.\n     */\n    [Symbol.toStringTag] = 'LRUCache';\n    /**\n     * Find a value for which the supplied fn method returns a truthy value,\n     * similar to `Array.find()`. fn is called as `fn(value, key, cache)`.\n     */\n    find(fn, getOptions = {}) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            if (fn(value, this.#keyList[i], this)) {\n                return this.get(this.#keyList[i], getOptions);\n            }\n        }\n    }\n    /**\n     * Call the supplied function on each item in the cache, in order from most\n     * recently used to least recently used.\n     *\n     * `fn` is called as `fn(value, key, cache)`.\n     *\n     * If `thisp` is provided, function will be called in the `this`-context of\n     * the provided object, or the cache if no `thisp` object is provided.\n     *\n     * Does not update age or recenty of use, or iterate over stale values.\n     */\n    forEach(fn, thisp = this) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * The same as {@link LRUCache.forEach} but items are iterated over in\n     * reverse order.  (ie, less recently used items are iterated over first.)\n     */\n    rforEach(fn, thisp = this) {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * Delete any stale entries. Returns true if anything was removed,\n     * false otherwise.\n     */\n    purgeStale() {\n        let deleted = false;\n        for (const i of this.#rindexes({ allowStale: true })) {\n            if (this.#isStale(i)) {\n                this.#delete(this.#keyList[i], 'expire');\n                deleted = true;\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Get the extended info about a given entry, to get its value, size, and\n     * TTL info simultaneously. Returns `undefined` if the key is not present.\n     *\n     * Unlike {@link LRUCache#dump}, which is designed to be portable and survive\n     * serialization, the `start` value is always the current timestamp, and the\n     * `ttl` is a calculated remaining time to live (negative if expired).\n     *\n     * Always returns stale values, if their info is found in the cache, so be\n     * sure to check for expirations (ie, a negative {@link LRUCache.Entry#ttl})\n     * if relevant.\n     */\n    info(key) {\n        const i = this.#keyMap.get(key);\n        if (i === undefined)\n            return undefined;\n        const v = this.#valList[i];\n        const value = this.#isBackgroundFetch(v)\n            ? v.__staleWhileFetching\n            : v;\n        if (value === undefined)\n            return undefined;\n        const entry = { value };\n        if (this.#ttls && this.#starts) {\n            const ttl = this.#ttls[i];\n            const start = this.#starts[i];\n            if (ttl && start) {\n                const remain = ttl - (perf.now() - start);\n                entry.ttl = remain;\n                entry.start = Date.now();\n            }\n        }\n        if (this.#sizes) {\n            entry.size = this.#sizes[i];\n        }\n        return entry;\n    }\n    /**\n     * Return an array of [key, {@link LRUCache.Entry}] tuples which can be\n     * passed to {@link LRLUCache#load}.\n     *\n     * The `start` fields are calculated relative to a portable `Date.now()`\n     * timestamp, even if `performance.now()` is available.\n     *\n     * Stale entries are always included in the `dump`, even if\n     * {@link LRUCache.OptionsBase.allowStale} is false.\n     *\n     * Note: this returns an actual array, not a generator, so it can be more\n     * easily passed around.\n     */\n    dump() {\n        const arr = [];\n        for (const i of this.#indexes({ allowStale: true })) {\n            const key = this.#keyList[i];\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined || key === undefined)\n                continue;\n            const entry = { value };\n            if (this.#ttls && this.#starts) {\n                entry.ttl = this.#ttls[i];\n                // always dump the start relative to a portable timestamp\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = perf.now() - this.#starts[i];\n                entry.start = Math.floor(Date.now() - age);\n            }\n            if (this.#sizes) {\n                entry.size = this.#sizes[i];\n            }\n            arr.unshift([key, entry]);\n        }\n        return arr;\n    }\n    /**\n     * Reset the cache and load in the items in entries in the order listed.\n     *\n     * The shape of the resulting cache may be different if the same options are\n     * not used in both caches.\n     *\n     * The `start` fields are assumed to be calculated relative to a portable\n     * `Date.now()` timestamp, even if `performance.now()` is available.\n     */\n    load(arr) {\n        this.clear();\n        for (const [key, entry] of arr) {\n            if (entry.start) {\n                // entry.start is a portable timestamp, but we may be using\n                // node's performance.now(), so calculate the offset, so that\n                // we get the intended remaining TTL, no matter how long it's\n                // been on ice.\n                //\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = Date.now() - entry.start;\n                entry.start = perf.now() - age;\n            }\n            this.set(key, entry.value, entry);\n        }\n    }\n    /**\n     * Add a value to the cache.\n     *\n     * Note: if `undefined` is specified as a value, this is an alias for\n     * {@link LRUCache#delete}\n     *\n     * Fields on the {@link LRUCache.SetOptions} options param will override\n     * their corresponding values in the constructor options for the scope\n     * of this single `set()` operation.\n     *\n     * If `start` is provided, then that will set the effective start\n     * time for the TTL calculation. Note that this must be a previous\n     * value of `performance.now()` if supported, or a previous value of\n     * `Date.now()` if not.\n     *\n     * Options object may also include `size`, which will prevent\n     * calling the `sizeCalculation` function and just use the specified\n     * number if it is a positive integer, and `noDisposeOnSet` which\n     * will prevent calling a `dispose` function in the case of\n     * overwrites.\n     *\n     * If the `size` (or return value of `sizeCalculation`) for a given\n     * entry is greater than `maxEntrySize`, then the item will not be\n     * added to the cache.\n     *\n     * Will update the recency of the entry.\n     *\n     * If the value is `undefined`, then this is an alias for\n     * `cache.delete(key)`. `undefined` is never stored in the cache.\n     */\n    set(k, v, setOptions = {}) {\n        if (v === undefined) {\n            this.delete(k);\n            return this;\n        }\n        const { ttl = this.ttl, start, noDisposeOnSet = this.noDisposeOnSet, sizeCalculation = this.sizeCalculation, status, } = setOptions;\n        let { noUpdateTTL = this.noUpdateTTL } = setOptions;\n        const size = this.#requireSize(k, v, setOptions.size || 0, sizeCalculation);\n        // if the item doesn't fit, don't do anything\n        // NB: maxEntrySize set to maxSize by default\n        if (this.maxEntrySize && size > this.maxEntrySize) {\n            if (status) {\n                status.set = 'miss';\n                status.maxEntrySizeExceeded = true;\n            }\n            // have to delete, in case something is there already.\n            this.#delete(k, 'set');\n            return this;\n        }\n        let index = this.#size === 0 ? undefined : this.#keyMap.get(k);\n        if (index === undefined) {\n            // addition\n            index = (this.#size === 0\n                ? this.#tail\n                : this.#free.length !== 0\n                    ? this.#free.pop()\n                    : this.#size === this.#max\n                        ? this.#evict(false)\n                        : this.#size);\n            this.#keyList[index] = k;\n            this.#valList[index] = v;\n            this.#keyMap.set(k, index);\n            this.#next[this.#tail] = index;\n            this.#prev[index] = this.#tail;\n            this.#tail = index;\n            this.#size++;\n            this.#addItemSize(index, size, status);\n            if (status)\n                status.set = 'add';\n            noUpdateTTL = false;\n        }\n        else {\n            // update\n            this.#moveToTail(index);\n            const oldVal = this.#valList[index];\n            if (v !== oldVal) {\n                if (this.#hasFetchMethod && this.#isBackgroundFetch(oldVal)) {\n                    oldVal.__abortController.abort(new Error('replaced'));\n                    const { __staleWhileFetching: s } = oldVal;\n                    if (s !== undefined && !noDisposeOnSet) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(s, k, 'set');\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([s, k, 'set']);\n                        }\n                    }\n                }\n                else if (!noDisposeOnSet) {\n                    if (this.#hasDispose) {\n                        this.#dispose?.(oldVal, k, 'set');\n                    }\n                    if (this.#hasDisposeAfter) {\n                        this.#disposed?.push([oldVal, k, 'set']);\n                    }\n                }\n                this.#removeItemSize(index);\n                this.#addItemSize(index, size, status);\n                this.#valList[index] = v;\n                if (status) {\n                    status.set = 'replace';\n                    const oldValue = oldVal && this.#isBackgroundFetch(oldVal)\n                        ? oldVal.__staleWhileFetching\n                        : oldVal;\n                    if (oldValue !== undefined)\n                        status.oldValue = oldValue;\n                }\n            }\n            else if (status) {\n                status.set = 'update';\n            }\n        }\n        if (ttl !== 0 && !this.#ttls) {\n            this.#initializeTTLTracking();\n        }\n        if (this.#ttls) {\n            if (!noUpdateTTL) {\n                this.#setItemTTL(index, ttl, start);\n            }\n            if (status)\n                this.#statusTTL(status, index);\n        }\n        if (!noDisposeOnSet && this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return this;\n    }\n    /**\n     * Evict the least recently used item, returning its value or\n     * `undefined` if cache is empty.\n     */\n    pop() {\n        try {\n            while (this.#size) {\n                const val = this.#valList[this.#head];\n                this.#evict(true);\n                if (this.#isBackgroundFetch(val)) {\n                    if (val.__staleWhileFetching) {\n                        return val.__staleWhileFetching;\n                    }\n                }\n                else if (val !== undefined) {\n                    return val;\n                }\n            }\n        }\n        finally {\n            if (this.#hasDisposeAfter && this.#disposed) {\n                const dt = this.#disposed;\n                let task;\n                while ((task = dt?.shift())) {\n                    this.#disposeAfter?.(...task);\n                }\n            }\n        }\n    }\n    #evict(free) {\n        const head = this.#head;\n        const k = this.#keyList[head];\n        const v = this.#valList[head];\n        if (this.#hasFetchMethod && this.#isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('evicted'));\n        }\n        else if (this.#hasDispose || this.#hasDisposeAfter) {\n            if (this.#hasDispose) {\n                this.#dispose?.(v, k, 'evict');\n            }\n            if (this.#hasDisposeAfter) {\n                this.#disposed?.push([v, k, 'evict']);\n            }\n        }\n        this.#removeItemSize(head);\n        // if we aren't about to use the index, then null these out\n        if (free) {\n            this.#keyList[head] = undefined;\n            this.#valList[head] = undefined;\n            this.#free.push(head);\n        }\n        if (this.#size === 1) {\n            this.#head = this.#tail = 0;\n            this.#free.length = 0;\n        }\n        else {\n            this.#head = this.#next[head];\n        }\n        this.#keyMap.delete(k);\n        this.#size--;\n        return head;\n    }\n    /**\n     * Check if a key is in the cache, without updating the recency of use.\n     * Will return false if the item is stale, even though it is technically\n     * in the cache.\n     *\n     * Check if a key is in the cache, without updating the recency of\n     * use. Age is updated if {@link LRUCache.OptionsBase.updateAgeOnHas} is set\n     * to `true` in either the options or the constructor.\n     *\n     * Will return `false` if the item is stale, even though it is technically in\n     * the cache. The difference can be determined (if it matters) by using a\n     * `status` argument, and inspecting the `has` field.\n     *\n     * Will not update item age unless\n     * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.\n     */\n    has(k, hasOptions = {}) {\n        const { updateAgeOnHas = this.updateAgeOnHas, status } = hasOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v) &&\n                v.__staleWhileFetching === undefined) {\n                return false;\n            }\n            if (!this.#isStale(index)) {\n                if (updateAgeOnHas) {\n                    this.#updateItemAge(index);\n                }\n                if (status) {\n                    status.has = 'hit';\n                    this.#statusTTL(status, index);\n                }\n                return true;\n            }\n            else if (status) {\n                status.has = 'stale';\n                this.#statusTTL(status, index);\n            }\n        }\n        else if (status) {\n            status.has = 'miss';\n        }\n        return false;\n    }\n    /**\n     * Like {@link LRUCache#get} but doesn't update recency or delete stale\n     * items.\n     *\n     * Returns `undefined` if the item is stale, unless\n     * {@link LRUCache.OptionsBase.allowStale} is set.\n     */\n    peek(k, peekOptions = {}) {\n        const { allowStale = this.allowStale } = peekOptions;\n        const index = this.#keyMap.get(k);\n        if (index === undefined ||\n            (!allowStale && this.#isStale(index))) {\n            return;\n        }\n        const v = this.#valList[index];\n        // either stale and allowed, or forcing a refresh of non-stale value\n        return this.#isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n    }\n    #backgroundFetch(k, index, options, context) {\n        const v = index === undefined ? undefined : this.#valList[index];\n        if (this.#isBackgroundFetch(v)) {\n            return v;\n        }\n        const ac = new AC();\n        const { signal } = options;\n        // when/if our AC signals, then stop listening to theirs.\n        signal?.addEventListener('abort', () => ac.abort(signal.reason), {\n            signal: ac.signal,\n        });\n        const fetchOpts = {\n            signal: ac.signal,\n            options,\n            context,\n        };\n        const cb = (v, updateCache = false) => {\n            const { aborted } = ac.signal;\n            const ignoreAbort = options.ignoreFetchAbort && v !== undefined;\n            if (options.status) {\n                if (aborted && !updateCache) {\n                    options.status.fetchAborted = true;\n                    options.status.fetchError = ac.signal.reason;\n                    if (ignoreAbort)\n                        options.status.fetchAbortIgnored = true;\n                }\n                else {\n                    options.status.fetchResolved = true;\n                }\n            }\n            if (aborted && !ignoreAbort && !updateCache) {\n                return fetchFail(ac.signal.reason);\n            }\n            // either we didn't abort, and are still here, or we did, and ignored\n            const bf = p;\n            if (this.#valList[index] === p) {\n                if (v === undefined) {\n                    if (bf.__staleWhileFetching) {\n                        this.#valList[index] = bf.__staleWhileFetching;\n                    }\n                    else {\n                        this.#delete(k, 'fetch');\n                    }\n                }\n                else {\n                    if (options.status)\n                        options.status.fetchUpdated = true;\n                    this.set(k, v, fetchOpts.options);\n                }\n            }\n            return v;\n        };\n        const eb = (er) => {\n            if (options.status) {\n                options.status.fetchRejected = true;\n                options.status.fetchError = er;\n            }\n            return fetchFail(er);\n        };\n        const fetchFail = (er) => {\n            const { aborted } = ac.signal;\n            const allowStaleAborted = aborted && options.allowStaleOnFetchAbort;\n            const allowStale = allowStaleAborted || options.allowStaleOnFetchRejection;\n            const noDelete = allowStale || options.noDeleteOnFetchRejection;\n            const bf = p;\n            if (this.#valList[index] === p) {\n                // if we allow stale on fetch rejections, then we need to ensure that\n                // the stale value is not removed from the cache when the fetch fails.\n                const del = !noDelete || bf.__staleWhileFetching === undefined;\n                if (del) {\n                    this.#delete(k, 'fetch');\n                }\n                else if (!allowStaleAborted) {\n                    // still replace the *promise* with the stale value,\n                    // since we are done with the promise at this point.\n                    // leave it untouched if we're still waiting for an\n                    // aborted background fetch that hasn't yet returned.\n                    this.#valList[index] = bf.__staleWhileFetching;\n                }\n            }\n            if (allowStale) {\n                if (options.status && bf.__staleWhileFetching !== undefined) {\n                    options.status.returnedStale = true;\n                }\n                return bf.__staleWhileFetching;\n            }\n            else if (bf.__returned === bf) {\n                throw er;\n            }\n        };\n        const pcall = (res, rej) => {\n            const fmp = this.#fetchMethod?.(k, v, fetchOpts);\n            if (fmp && fmp instanceof Promise) {\n                fmp.then(v => res(v === undefined ? undefined : v), rej);\n            }\n            // ignored, we go until we finish, regardless.\n            // defer check until we are actually aborting,\n            // so fetchMethod can override.\n            ac.signal.addEventListener('abort', () => {\n                if (!options.ignoreFetchAbort ||\n                    options.allowStaleOnFetchAbort) {\n                    res(undefined);\n                    // when it eventually resolves, update the cache.\n                    if (options.allowStaleOnFetchAbort) {\n                        res = v => cb(v, true);\n                    }\n                }\n            });\n        };\n        if (options.status)\n            options.status.fetchDispatched = true;\n        const p = new Promise(pcall).then(cb, eb);\n        const bf = Object.assign(p, {\n            __abortController: ac,\n            __staleWhileFetching: v,\n            __returned: undefined,\n        });\n        if (index === undefined) {\n            // internal, don't expose status.\n            this.set(k, bf, { ...fetchOpts.options, status: undefined });\n            index = this.#keyMap.get(k);\n        }\n        else {\n            this.#valList[index] = bf;\n        }\n        return bf;\n    }\n    #isBackgroundFetch(p) {\n        if (!this.#hasFetchMethod)\n            return false;\n        const b = p;\n        return (!!b &&\n            b instanceof Promise &&\n            b.hasOwnProperty('__staleWhileFetching') &&\n            b.__abortController instanceof AC);\n    }\n    async fetch(k, fetchOptions = {}) {\n        const { \n        // get options\n        allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, \n        // set options\n        ttl = this.ttl, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, \n        // fetch exclusive options\n        noDeleteOnFetchRejection = this.noDeleteOnFetchRejection, allowStaleOnFetchRejection = this.allowStaleOnFetchRejection, ignoreFetchAbort = this.ignoreFetchAbort, allowStaleOnFetchAbort = this.allowStaleOnFetchAbort, context, forceRefresh = false, status, signal, } = fetchOptions;\n        if (!this.#hasFetchMethod) {\n            if (status)\n                status.fetch = 'get';\n            return this.get(k, {\n                allowStale,\n                updateAgeOnGet,\n                noDeleteOnStaleGet,\n                status,\n            });\n        }\n        const options = {\n            allowStale,\n            updateAgeOnGet,\n            noDeleteOnStaleGet,\n            ttl,\n            noDisposeOnSet,\n            size,\n            sizeCalculation,\n            noUpdateTTL,\n            noDeleteOnFetchRejection,\n            allowStaleOnFetchRejection,\n            allowStaleOnFetchAbort,\n            ignoreFetchAbort,\n            status,\n            signal,\n        };\n        let index = this.#keyMap.get(k);\n        if (index === undefined) {\n            if (status)\n                status.fetch = 'miss';\n            const p = this.#backgroundFetch(k, index, options, context);\n            return (p.__returned = p);\n        }\n        else {\n            // in cache, maybe already fetching\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                const stale = allowStale && v.__staleWhileFetching !== undefined;\n                if (status) {\n                    status.fetch = 'inflight';\n                    if (stale)\n                        status.returnedStale = true;\n                }\n                return stale ? v.__staleWhileFetching : (v.__returned = v);\n            }\n            // if we force a refresh, that means do NOT serve the cached value,\n            // unless we are already in the process of refreshing the cache.\n            const isStale = this.#isStale(index);\n            if (!forceRefresh && !isStale) {\n                if (status)\n                    status.fetch = 'hit';\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                if (status)\n                    this.#statusTTL(status, index);\n                return v;\n            }\n            // ok, it is stale or a forced refresh, and not already fetching.\n            // refresh the cache.\n            const p = this.#backgroundFetch(k, index, options, context);\n            const hasStale = p.__staleWhileFetching !== undefined;\n            const staleVal = hasStale && allowStale;\n            if (status) {\n                status.fetch = isStale ? 'stale' : 'refresh';\n                if (staleVal && isStale)\n                    status.returnedStale = true;\n            }\n            return staleVal ? p.__staleWhileFetching : (p.__returned = p);\n        }\n    }\n    async forceFetch(k, fetchOptions = {}) {\n        const v = await this.fetch(k, fetchOptions);\n        if (v === undefined)\n            throw new Error('fetch() returned undefined');\n        return v;\n    }\n    memo(k, memoOptions = {}) {\n        const memoMethod = this.#memoMethod;\n        if (!memoMethod) {\n            throw new Error('no memoMethod provided to constructor');\n        }\n        const { context, forceRefresh, ...options } = memoOptions;\n        const v = this.get(k, options);\n        if (!forceRefresh && v !== undefined)\n            return v;\n        const vv = memoMethod(k, v, {\n            options,\n            context,\n        });\n        this.set(k, vv, options);\n        return vv;\n    }\n    /**\n     * Return a value from the cache. Will update the recency of the cache\n     * entry found.\n     *\n     * If the key is not found, get() will return `undefined`.\n     */\n    get(k, getOptions = {}) {\n        const { allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, status, } = getOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const value = this.#valList[index];\n            const fetching = this.#isBackgroundFetch(value);\n            if (status)\n                this.#statusTTL(status, index);\n            if (this.#isStale(index)) {\n                if (status)\n                    status.get = 'stale';\n                // delete only if not an in-flight background fetch\n                if (!fetching) {\n                    if (!noDeleteOnStaleGet) {\n                        this.#delete(k, 'expire');\n                    }\n                    if (status && allowStale)\n                        status.returnedStale = true;\n                    return allowStale ? value : undefined;\n                }\n                else {\n                    if (status &&\n                        allowStale &&\n                        value.__staleWhileFetching !== undefined) {\n                        status.returnedStale = true;\n                    }\n                    return allowStale ? value.__staleWhileFetching : undefined;\n                }\n            }\n            else {\n                if (status)\n                    status.get = 'hit';\n                // if we're currently fetching it, we don't actually have it yet\n                // it's not stale, which means this isn't a staleWhileRefetching.\n                // If it's not stale, and fetching, AND has a __staleWhileFetching\n                // value, then that means the user fetched with {forceRefresh:true},\n                // so it's safe to return that value.\n                if (fetching) {\n                    return value.__staleWhileFetching;\n                }\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                return value;\n            }\n        }\n        else if (status) {\n            status.get = 'miss';\n        }\n    }\n    #connect(p, n) {\n        this.#prev[n] = p;\n        this.#next[p] = n;\n    }\n    #moveToTail(index) {\n        // if tail already, nothing to do\n        // if head, move head to next[index]\n        // else\n        //   move next[prev[index]] to next[index] (head has no prev)\n        //   move prev[next[index]] to prev[index]\n        // prev[index] = tail\n        // next[tail] = index\n        // tail = index\n        if (index !== this.#tail) {\n            if (index === this.#head) {\n                this.#head = this.#next[index];\n            }\n            else {\n                this.#connect(this.#prev[index], this.#next[index]);\n            }\n            this.#connect(this.#tail, index);\n            this.#tail = index;\n        }\n    }\n    /**\n     * Deletes a key out of the cache.\n     *\n     * Returns true if the key was deleted, false otherwise.\n     */\n    delete(k) {\n        return this.#delete(k, 'delete');\n    }\n    #delete(k, reason) {\n        let deleted = false;\n        if (this.#size !== 0) {\n            const index = this.#keyMap.get(k);\n            if (index !== undefined) {\n                deleted = true;\n                if (this.#size === 1) {\n                    this.#clear(reason);\n                }\n                else {\n                    this.#removeItemSize(index);\n                    const v = this.#valList[index];\n                    if (this.#isBackgroundFetch(v)) {\n                        v.__abortController.abort(new Error('deleted'));\n                    }\n                    else if (this.#hasDispose || this.#hasDisposeAfter) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(v, k, reason);\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([v, k, reason]);\n                        }\n                    }\n                    this.#keyMap.delete(k);\n                    this.#keyList[index] = undefined;\n                    this.#valList[index] = undefined;\n                    if (index === this.#tail) {\n                        this.#tail = this.#prev[index];\n                    }\n                    else if (index === this.#head) {\n                        this.#head = this.#next[index];\n                    }\n                    else {\n                        const pi = this.#prev[index];\n                        this.#next[pi] = this.#next[index];\n                        const ni = this.#next[index];\n                        this.#prev[ni] = this.#prev[index];\n                    }\n                    this.#size--;\n                    this.#free.push(index);\n                }\n            }\n        }\n        if (this.#hasDisposeAfter && this.#disposed?.length) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Clear the cache entirely, throwing away all values.\n     */\n    clear() {\n        return this.#clear('delete');\n    }\n    #clear(reason) {\n        for (const index of this.#rindexes({ allowStale: true })) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                v.__abortController.abort(new Error('deleted'));\n            }\n            else {\n                const k = this.#keyList[index];\n                if (this.#hasDispose) {\n                    this.#dispose?.(v, k, reason);\n                }\n                if (this.#hasDisposeAfter) {\n                    this.#disposed?.push([v, k, reason]);\n                }\n            }\n        }\n        this.#keyMap.clear();\n        this.#valList.fill(undefined);\n        this.#keyList.fill(undefined);\n        if (this.#ttls && this.#starts) {\n            this.#ttls.fill(0);\n            this.#starts.fill(0);\n        }\n        if (this.#sizes) {\n            this.#sizes.fill(0);\n        }\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free.length = 0;\n        this.#calculatedSize = 0;\n        this.#size = 0;\n        if (this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n    }\n}\nexports.LRUCache = LRUCache;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-yoga/node_modules/lru-cache/dist/commonjs/index.js\n");

/***/ })

};
;