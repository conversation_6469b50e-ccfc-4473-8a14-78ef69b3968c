"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@segment";
exports.ids = ["vendor-chunks/@segment"];
exports.modules = {

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/analytics/dispatch.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/analytics/dispatch.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dispatch: () => (/* binding */ dispatch),\n/* harmony export */   getDelay: () => (/* binding */ getDelay)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _callback__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../callback */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/callback/index.js\");\n\n\n/* The amount of time in ms to wait before invoking the callback. */\nvar getDelay = function (startTimeInEpochMS, timeoutInMS) {\n    var elapsedTime = Date.now() - startTimeInEpochMS;\n    // increasing the timeout increases the delay by almost the same amount -- this is weird legacy behavior.\n    return Math.max((timeoutInMS !== null && timeoutInMS !== void 0 ? timeoutInMS : 300) - elapsedTime, 0);\n};\n/**\n * Push an event into the dispatch queue and invoke any callbacks.\n *\n * @param event - Segment event to enqueue.\n * @param queue - Queue to dispatch against.\n * @param emitter - This is typically an instance of \"Analytics\" -- used for metrics / progress information.\n * @param options\n */\nfunction dispatch(ctx, queue, emitter, options) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var startTime, dispatched;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    emitter.emit('dispatch_start', ctx);\n                    startTime = Date.now();\n                    if (!queue.isEmpty()) return [3 /*break*/, 2];\n                    return [4 /*yield*/, queue.dispatchSingle(ctx)];\n                case 1:\n                    dispatched = _a.sent();\n                    return [3 /*break*/, 4];\n                case 2: return [4 /*yield*/, queue.dispatch(ctx)];\n                case 3:\n                    dispatched = _a.sent();\n                    _a.label = 4;\n                case 4:\n                    if (!(options === null || options === void 0 ? void 0 : options.callback)) return [3 /*break*/, 6];\n                    return [4 /*yield*/, (0,_callback__WEBPACK_IMPORTED_MODULE_1__.invokeCallback)(dispatched, options.callback, getDelay(startTime, options.timeout))];\n                case 5:\n                    dispatched = _a.sent();\n                    _a.label = 6;\n                case 6:\n                    if (options === null || options === void 0 ? void 0 : options.debug) {\n                        dispatched.flush();\n                    }\n                    return [2 /*return*/, dispatched];\n            }\n        });\n    });\n}\n//# sourceMappingURL=dispatch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/analytics/dispatch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/callback/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/callback/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invokeCallback: () => (/* binding */ invokeCallback),\n/* harmony export */   pTimeout: () => (/* binding */ pTimeout),\n/* harmony export */   sleep: () => (/* binding */ sleep)\n/* harmony export */ });\nfunction pTimeout(promise, timeout) {\n    return new Promise(function (resolve, reject) {\n        var timeoutId = setTimeout(function () {\n            reject(Error('Promise timed out'));\n        }, timeout);\n        promise\n            .then(function (val) {\n            clearTimeout(timeoutId);\n            return resolve(val);\n        })\n            .catch(reject);\n    });\n}\nfunction sleep(timeoutInMs) {\n    return new Promise(function (resolve) { return setTimeout(resolve, timeoutInMs); });\n}\n/**\n * @param ctx\n * @param callback - the function to invoke\n * @param delay - aka \"timeout\". The amount of time in ms to wait before invoking the callback.\n */\nfunction invokeCallback(ctx, callback, delay) {\n    var cb = function () {\n        try {\n            return Promise.resolve(callback(ctx));\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    };\n    return (sleep(delay)\n        // pTimeout ensures that the callback can't cause the context to hang\n        .then(function () { return pTimeout(cb(), 1000); })\n        .catch(function (err) {\n        ctx === null || ctx === void 0 ? void 0 : ctx.log('warn', 'Callback Error', { error: err });\n        ctx === null || ctx === void 0 ? void 0 : ctx.stats.increment('callback_error');\n    })\n        .then(function () { return ctx; }));\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/callback/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/context/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/context/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextCancelation: () => (/* binding */ ContextCancelation),\n/* harmony export */   CoreContext: () => (/* binding */ CoreContext)\n/* harmony export */ });\n/* harmony import */ var _lukeed_uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lukeed/uuid */ \"(rsc)/./node_modules/@lukeed/uuid/dist/index.mjs\");\n/* harmony import */ var dset__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dset */ \"(rsc)/./node_modules/dset/dist/index.mjs\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logger */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/logger/index.js\");\n/* harmony import */ var _stats__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../stats */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/stats/index.js\");\n\n\n\n\nvar ContextCancelation = /** @class */ (function () {\n    function ContextCancelation(options) {\n        var _a, _b, _c;\n        this.retry = (_a = options.retry) !== null && _a !== void 0 ? _a : true;\n        this.type = (_b = options.type) !== null && _b !== void 0 ? _b : 'plugin Error';\n        this.reason = (_c = options.reason) !== null && _c !== void 0 ? _c : '';\n    }\n    return ContextCancelation;\n}());\n\nvar CoreContext = /** @class */ (function () {\n    function CoreContext(event, id, stats, logger) {\n        if (id === void 0) { id = (0,_lukeed_uuid__WEBPACK_IMPORTED_MODULE_0__.v4)(); }\n        if (stats === void 0) { stats = new _stats__WEBPACK_IMPORTED_MODULE_2__.NullStats(); }\n        if (logger === void 0) { logger = new _logger__WEBPACK_IMPORTED_MODULE_3__.CoreLogger(); }\n        this.attempts = 0;\n        this.event = event;\n        this._id = id;\n        this.logger = logger;\n        this.stats = stats;\n    }\n    CoreContext.system = function () {\n        // This should be overridden by the subclass to return an instance of the subclass.\n    };\n    CoreContext.prototype.isSame = function (other) {\n        return other.id === this.id;\n    };\n    CoreContext.prototype.cancel = function (error) {\n        if (error) {\n            throw error;\n        }\n        throw new ContextCancelation({ reason: 'Context Cancel' });\n    };\n    CoreContext.prototype.log = function (level, message, extras) {\n        this.logger.log(level, message, extras);\n    };\n    Object.defineProperty(CoreContext.prototype, \"id\", {\n        get: function () {\n            return this._id;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CoreContext.prototype.updateEvent = function (path, val) {\n        var _a;\n        // Don't allow integrations that are set to false to be overwritten with integration settings.\n        if (path.split('.')[0] === 'integrations') {\n            var integrationName = path.split('.')[1];\n            if (((_a = this.event.integrations) === null || _a === void 0 ? void 0 : _a[integrationName]) === false) {\n                return this.event;\n            }\n        }\n        (0,dset__WEBPACK_IMPORTED_MODULE_1__.dset)(this.event, path, val);\n        return this.event;\n    };\n    CoreContext.prototype.failedDelivery = function () {\n        return this._failedDelivery;\n    };\n    CoreContext.prototype.setFailedDelivery = function (options) {\n        this._failedDelivery = options;\n    };\n    CoreContext.prototype.logs = function () {\n        return this.logger.logs;\n    };\n    CoreContext.prototype.flush = function () {\n        this.logger.flush();\n        this.stats.flush();\n    };\n    CoreContext.prototype.toJSON = function () {\n        return {\n            id: this._id,\n            event: this.event,\n            logs: this.logger.logs,\n            metrics: this.stats.metrics,\n        };\n    };\n    return CoreContext;\n}());\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/context/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/events/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/events/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreEventFactory: () => (/* binding */ CoreEventFactory)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var dset__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dset */ \"(rsc)/./node_modules/dset/dist/index.mjs\");\n/* harmony import */ var _utils_pick__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/pick */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/pick.js\");\n/* harmony import */ var _validation_assertions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../validation/assertions */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/assertions.js\");\n\n\n\n\n\n/**\n * Internal settings object that is used internally by the factory\n */\nvar InternalEventFactorySettings = /** @class */ (function () {\n    function InternalEventFactorySettings(settings) {\n        var _a, _b;\n        this.settings = settings;\n        this.createMessageId = settings.createMessageId;\n        this.onEventMethodCall = (_a = settings.onEventMethodCall) !== null && _a !== void 0 ? _a : (function () { });\n        this.onFinishedEvent = (_b = settings.onFinishedEvent) !== null && _b !== void 0 ? _b : (function () { });\n    }\n    return InternalEventFactorySettings;\n}());\nvar CoreEventFactory = /** @class */ (function () {\n    function CoreEventFactory(settings) {\n        this.settings = new InternalEventFactorySettings(settings);\n    }\n    CoreEventFactory.prototype.track = function (event, properties, options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'track', options: options });\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), { event: event, type: 'track', properties: properties !== null && properties !== void 0 ? properties : {}, options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options), integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations) }));\n    };\n    CoreEventFactory.prototype.page = function (category, page, properties, options, globalIntegrations) {\n        var _a;\n        this.settings.onEventMethodCall({ type: 'page', options: options });\n        var event = {\n            type: 'page',\n            properties: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, properties),\n            options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options),\n            integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations),\n        };\n        if (category !== null) {\n            event.category = category;\n            event.properties = (_a = event.properties) !== null && _a !== void 0 ? _a : {};\n            event.properties.category = category;\n        }\n        if (page !== null) {\n            event.name = page;\n        }\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), event));\n    };\n    CoreEventFactory.prototype.screen = function (category, screen, properties, options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'screen', options: options });\n        var event = {\n            type: 'screen',\n            properties: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, properties),\n            options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options),\n            integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations),\n        };\n        if (category !== null) {\n            event.category = category;\n        }\n        if (screen !== null) {\n            event.name = screen;\n        }\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), event));\n    };\n    CoreEventFactory.prototype.identify = function (userId, traits, options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'identify', options: options });\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), { type: 'identify', userId: userId, traits: traits !== null && traits !== void 0 ? traits : {}, options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options), integrations: globalIntegrations }));\n    };\n    CoreEventFactory.prototype.group = function (groupId, traits, options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'group', options: options });\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), { type: 'group', traits: traits !== null && traits !== void 0 ? traits : {}, options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options), integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations), //\n            groupId: groupId }));\n    };\n    CoreEventFactory.prototype.alias = function (to, from, // TODO: can we make this undefined?\n    options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'alias', options: options });\n        var base = {\n            userId: to,\n            type: 'alias',\n            options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options),\n            integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations),\n        };\n        if (from !== null) {\n            base.previousId = from;\n        }\n        if (to === undefined) {\n            return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, base), this.baseEvent()));\n        }\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), base));\n    };\n    CoreEventFactory.prototype.baseEvent = function () {\n        return {\n            integrations: {},\n            options: {},\n        };\n    };\n    /**\n     * Builds the context part of an event based on \"foreign\" keys that\n     * are provided in the `Options` parameter for an Event\n     */\n    CoreEventFactory.prototype.context = function (options) {\n        var _a;\n        /**\n         * If the event options are known keys from this list, we move them to the top level of the event.\n         * Any other options are moved to context.\n         */\n        var eventOverrideKeys = [\n            'userId',\n            'anonymousId',\n            'timestamp',\n            'messageId',\n        ];\n        delete options['integrations'];\n        var providedOptionsKeys = Object.keys(options);\n        var context = (_a = options.context) !== null && _a !== void 0 ? _a : {};\n        var eventOverrides = {};\n        providedOptionsKeys.forEach(function (key) {\n            if (key === 'context') {\n                return;\n            }\n            if (eventOverrideKeys.includes(key)) {\n                (0,dset__WEBPACK_IMPORTED_MODULE_0__.dset)(eventOverrides, key, options[key]);\n            }\n            else {\n                (0,dset__WEBPACK_IMPORTED_MODULE_0__.dset)(context, key, options[key]);\n            }\n        });\n        return [context, eventOverrides];\n    };\n    CoreEventFactory.prototype.normalize = function (event) {\n        var _a, _b;\n        var integrationBooleans = Object.keys((_a = event.integrations) !== null && _a !== void 0 ? _a : {}).reduce(function (integrationNames, name) {\n            var _a;\n            var _b;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, integrationNames), (_a = {}, _a[name] = Boolean((_b = event.integrations) === null || _b === void 0 ? void 0 : _b[name]), _a));\n        }, {});\n        // filter out any undefined options\n        event.options = (0,_utils_pick__WEBPACK_IMPORTED_MODULE_2__.pickBy)(event.options || {}, function (_, value) {\n            return value !== undefined;\n        });\n        // This is pretty trippy, but here's what's going on:\n        // - a) We don't pass initial integration options as part of the event, only if they're true or false\n        // - b) We do accept per integration overrides (like integrations.Amplitude.sessionId) at the event level\n        // Hence the need to convert base integration options to booleans, but maintain per event integration overrides\n        var allIntegrations = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, integrationBooleans), (_b = event.options) === null || _b === void 0 ? void 0 : _b.integrations);\n        var _c = event.options\n            ? this.context(event.options)\n            : [], context = _c[0], overrides = _c[1];\n        var options = event.options, rest = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__rest)(event, [\"options\"]);\n        var evt = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({ timestamp: new Date() }, rest), { context: context, integrations: allIntegrations }), overrides), { messageId: options.messageId || this.settings.createMessageId() });\n        this.settings.onFinishedEvent(evt);\n        (0,_validation_assertions__WEBPACK_IMPORTED_MODULE_3__.validateEvent)(evt);\n        return evt;\n    };\n    return CoreEventFactory;\n}());\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/events/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/logger/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/logger/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreLogger: () => (/* binding */ CoreLogger)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar CoreLogger = /** @class */ (function () {\n    function CoreLogger() {\n        this._logs = [];\n    }\n    CoreLogger.prototype.log = function (level, message, extras) {\n        var time = new Date();\n        this._logs.push({\n            level: level,\n            message: message,\n            time: time,\n            extras: extras,\n        });\n    };\n    Object.defineProperty(CoreLogger.prototype, \"logs\", {\n        get: function () {\n            return this._logs;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CoreLogger.prototype.flush = function () {\n        if (this.logs.length > 1) {\n            var formatted = this._logs.reduce(function (logs, log) {\n                var _a;\n                var _b, _c;\n                var line = (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, log), { json: JSON.stringify(log.extras, null, ' '), extras: log.extras });\n                delete line['time'];\n                var key = (_c = (_b = log.time) === null || _b === void 0 ? void 0 : _b.toISOString()) !== null && _c !== void 0 ? _c : '';\n                if (logs[key]) {\n                    key = \"\".concat(key, \"-\").concat(Math.random());\n                }\n                return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, logs), (_a = {}, _a[key] = line, _a));\n            }, {});\n            // ie doesn't like console.table\n            if (console.table) {\n                console.table(formatted);\n            }\n            else {\n                console.log(formatted);\n            }\n        }\n        else {\n            this.logs.forEach(function (logEntry) {\n                var level = logEntry.level, message = logEntry.message, extras = logEntry.extras;\n                if (level === 'info' || level === 'debug') {\n                    console.log(message, extras !== null && extras !== void 0 ? extras : '');\n                }\n                else {\n                    console[level](message, extras !== null && extras !== void 0 ? extras : '');\n                }\n            });\n        }\n        this._logs = [];\n    };\n    return CoreLogger;\n}());\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/logger/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backoff: () => (/* binding */ backoff)\n/* harmony export */ });\nfunction backoff(params) {\n    var random = Math.random() + 1;\n    var _a = params.minTimeout, minTimeout = _a === void 0 ? 500 : _a, _b = params.factor, factor = _b === void 0 ? 2 : _b, attempt = params.attempt, _c = params.maxTimeout, maxTimeout = _c === void 0 ? Infinity : _c;\n    return Math.min(random * minTimeout * Math.pow(factor, attempt), maxTimeout);\n}\n//# sourceMappingURL=backoff.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vcHJpb3JpdHktcXVldWUvYmFja29mZi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBzZWdtZW50XFxhbmFseXRpY3MtY29yZVxcZGlzdFxcZXNtXFxwcmlvcml0eS1xdWV1ZVxcYmFja29mZi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gYmFja29mZihwYXJhbXMpIHtcbiAgICB2YXIgcmFuZG9tID0gTWF0aC5yYW5kb20oKSArIDE7XG4gICAgdmFyIF9hID0gcGFyYW1zLm1pblRpbWVvdXQsIG1pblRpbWVvdXQgPSBfYSA9PT0gdm9pZCAwID8gNTAwIDogX2EsIF9iID0gcGFyYW1zLmZhY3RvciwgZmFjdG9yID0gX2IgPT09IHZvaWQgMCA/IDIgOiBfYiwgYXR0ZW1wdCA9IHBhcmFtcy5hdHRlbXB0LCBfYyA9IHBhcmFtcy5tYXhUaW1lb3V0LCBtYXhUaW1lb3V0ID0gX2MgPT09IHZvaWQgMCA/IEluZmluaXR5IDogX2M7XG4gICAgcmV0dXJuIE1hdGgubWluKHJhbmRvbSAqIG1pblRpbWVvdXQgKiBNYXRoLnBvdyhmYWN0b3IsIGF0dGVtcHQpLCBtYXhUaW1lb3V0KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJhY2tvZmYuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ON_REMOVE_FROM_FUTURE: () => (/* binding */ ON_REMOVE_FROM_FUTURE),\n/* harmony export */   PriorityQueue: () => (/* binding */ PriorityQueue)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n/* harmony import */ var _backoff__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./backoff */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js\");\n\n\n\n/**\n * @internal\n */\nvar ON_REMOVE_FROM_FUTURE = 'onRemoveFromFuture';\nvar PriorityQueue = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(PriorityQueue, _super);\n    function PriorityQueue(maxAttempts, queue, seen) {\n        var _this = _super.call(this) || this;\n        _this.future = [];\n        _this.maxAttempts = maxAttempts;\n        _this.queue = queue;\n        _this.seen = seen !== null && seen !== void 0 ? seen : {};\n        return _this;\n    }\n    PriorityQueue.prototype.push = function () {\n        var _this = this;\n        var items = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            items[_i] = arguments[_i];\n        }\n        var accepted = items.map(function (operation) {\n            var attempts = _this.updateAttempts(operation);\n            if (attempts > _this.maxAttempts || _this.includes(operation)) {\n                return false;\n            }\n            _this.queue.push(operation);\n            return true;\n        });\n        this.queue = this.queue.sort(function (a, b) { return _this.getAttempts(a) - _this.getAttempts(b); });\n        return accepted;\n    };\n    PriorityQueue.prototype.pushWithBackoff = function (item, minTimeout) {\n        var _this = this;\n        if (minTimeout === void 0) { minTimeout = 0; }\n        // One immediate retry unless we have a minimum timeout (e.g. for rate limiting)\n        if (minTimeout == 0 && this.getAttempts(item) === 0) {\n            return this.push(item)[0];\n        }\n        var attempt = this.updateAttempts(item);\n        if (attempt > this.maxAttempts || this.includes(item)) {\n            return false;\n        }\n        var timeout = (0,_backoff__WEBPACK_IMPORTED_MODULE_1__.backoff)({ attempt: attempt - 1 });\n        if (minTimeout > 0 && timeout < minTimeout) {\n            timeout = minTimeout;\n        }\n        setTimeout(function () {\n            _this.queue.push(item);\n            // remove from future list\n            _this.future = _this.future.filter(function (f) { return f.id !== item.id; });\n            // Lets listeners know that a 'future' message is now available in the queue\n            _this.emit(ON_REMOVE_FROM_FUTURE);\n        }, timeout);\n        this.future.push(item);\n        return true;\n    };\n    PriorityQueue.prototype.getAttempts = function (item) {\n        var _a;\n        return (_a = this.seen[item.id]) !== null && _a !== void 0 ? _a : 0;\n    };\n    PriorityQueue.prototype.updateAttempts = function (item) {\n        this.seen[item.id] = this.getAttempts(item) + 1;\n        return this.getAttempts(item);\n    };\n    PriorityQueue.prototype.includes = function (item) {\n        return (this.queue.includes(item) ||\n            this.future.includes(item) ||\n            Boolean(this.queue.find(function (i) { return i.id === item.id; })) ||\n            Boolean(this.future.find(function (i) { return i.id === item.id; })));\n    };\n    PriorityQueue.prototype.pop = function () {\n        return this.queue.shift();\n    };\n    Object.defineProperty(PriorityQueue.prototype, \"length\", {\n        get: function () {\n            return this.queue.length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(PriorityQueue.prototype, \"todo\", {\n        get: function () {\n            return this.queue.length + this.future.length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return PriorityQueue;\n}(_segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_2__.Emitter));\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/queue/delivery.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/queue/delivery.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attempt: () => (/* binding */ attempt),\n/* harmony export */   ensure: () => (/* binding */ ensure)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/context/index.js\");\n\n\nfunction tryAsync(fn) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var err_1;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    _a.trys.push([0, 2, , 3]);\n                    return [4 /*yield*/, fn()];\n                case 1: return [2 /*return*/, _a.sent()];\n                case 2:\n                    err_1 = _a.sent();\n                    return [2 /*return*/, Promise.reject(err_1)];\n                case 3: return [2 /*return*/];\n            }\n        });\n    });\n}\nfunction attempt(ctx, plugin) {\n    ctx.log('debug', 'plugin', { plugin: plugin.name });\n    var start = new Date().getTime();\n    var hook = plugin[ctx.event.type];\n    if (hook === undefined) {\n        return Promise.resolve(ctx);\n    }\n    var newCtx = tryAsync(function () { return hook.apply(plugin, [ctx]); })\n        .then(function (ctx) {\n        var done = new Date().getTime() - start;\n        ctx.stats.gauge('plugin_time', done, [\"plugin:\".concat(plugin.name)]);\n        return ctx;\n    })\n        .catch(function (err) {\n        if (err instanceof _context__WEBPACK_IMPORTED_MODULE_1__.ContextCancelation &&\n            err.type === 'middleware_cancellation') {\n            throw err;\n        }\n        if (err instanceof _context__WEBPACK_IMPORTED_MODULE_1__.ContextCancelation) {\n            ctx.log('warn', err.type, {\n                plugin: plugin.name,\n                error: err,\n            });\n            return err;\n        }\n        ctx.log('error', 'plugin Error', {\n            plugin: plugin.name,\n            error: err,\n        });\n        ctx.stats.increment('plugin_error', 1, [\"plugin:\".concat(plugin.name)]);\n        return err;\n    });\n    return newCtx;\n}\nfunction ensure(ctx, plugin) {\n    return attempt(ctx, plugin).then(function (newContext) {\n        if (newContext instanceof _context__WEBPACK_IMPORTED_MODULE_1__.CoreContext) {\n            return newContext;\n        }\n        ctx.log('debug', 'Context canceled');\n        ctx.stats.increment('context_canceled');\n        ctx.cancel(newContext);\n    });\n}\n//# sourceMappingURL=delivery.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/queue/delivery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/queue/event-queue.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/queue/event-queue.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreEventQueue: () => (/* binding */ CoreEventQueue)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _utils_group_by__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/group-by */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/group-by.js\");\n/* harmony import */ var _priority_queue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../priority-queue */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/context/index.js\");\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n/* harmony import */ var _task_task_group__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../task/task-group */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/task/task-group.js\");\n/* harmony import */ var _delivery__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./delivery */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/queue/delivery.js\");\n\n\n\n\n\n\n\nvar CoreEventQueue = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(CoreEventQueue, _super);\n    function CoreEventQueue(priorityQueue) {\n        var _this = _super.call(this) || this;\n        /**\n         * All event deliveries get suspended until all the tasks in this task group are complete.\n         * For example: a middleware that augments the event object should be loaded safely as a\n         * critical task, this way, event queue will wait for it to be ready before sending events.\n         *\n         * This applies to all the events already in the queue, and the upcoming ones\n         */\n        _this.criticalTasks = (0,_task_task_group__WEBPACK_IMPORTED_MODULE_1__.createTaskGroup)();\n        _this.plugins = [];\n        _this.failedInitializations = [];\n        _this.flushing = false;\n        _this.queue = priorityQueue;\n        _this.queue.on(_priority_queue__WEBPACK_IMPORTED_MODULE_2__.ON_REMOVE_FROM_FUTURE, function () {\n            _this.scheduleFlush(0);\n        });\n        return _this;\n    }\n    CoreEventQueue.prototype.register = function (ctx, plugin, instance) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var handleLoadError, err_1;\n            var _this = this;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.plugins.push(plugin);\n                        handleLoadError = function (err) {\n                            _this.failedInitializations.push(plugin.name);\n                            _this.emit('initialization_failure', plugin);\n                            console.warn(plugin.name, err);\n                            ctx.log('warn', 'Failed to load destination', {\n                                plugin: plugin.name,\n                                error: err,\n                            });\n                            // Filter out the failed plugin by excluding it from the list\n                            _this.plugins = _this.plugins.filter(function (p) { return p !== plugin; });\n                        };\n                        if (!(plugin.type === 'destination' && plugin.name !== 'Segment.io')) return [3 /*break*/, 1];\n                        plugin.load(ctx, instance).catch(handleLoadError);\n                        return [3 /*break*/, 4];\n                    case 1:\n                        _a.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, plugin.load(ctx, instance)];\n                    case 2:\n                        _a.sent();\n                        return [3 /*break*/, 4];\n                    case 3:\n                        err_1 = _a.sent();\n                        handleLoadError(err_1);\n                        return [3 /*break*/, 4];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    CoreEventQueue.prototype.deregister = function (ctx, plugin, instance) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var e_1;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        _a.trys.push([0, 3, , 4]);\n                        if (!plugin.unload) return [3 /*break*/, 2];\n                        return [4 /*yield*/, Promise.resolve(plugin.unload(ctx, instance))];\n                    case 1:\n                        _a.sent();\n                        _a.label = 2;\n                    case 2:\n                        this.plugins = this.plugins.filter(function (p) { return p.name !== plugin.name; });\n                        return [3 /*break*/, 4];\n                    case 3:\n                        e_1 = _a.sent();\n                        ctx.log('warn', 'Failed to unload destination', {\n                            plugin: plugin.name,\n                            error: e_1,\n                        });\n                        return [3 /*break*/, 4];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    CoreEventQueue.prototype.dispatch = function (ctx) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var willDeliver;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                ctx.log('debug', 'Dispatching');\n                ctx.stats.increment('message_dispatched');\n                this.queue.push(ctx);\n                willDeliver = this.subscribeToDelivery(ctx);\n                this.scheduleFlush(0);\n                return [2 /*return*/, willDeliver];\n            });\n        });\n    };\n    CoreEventQueue.prototype.subscribeToDelivery = function (ctx) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var _this = this;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                return [2 /*return*/, new Promise(function (resolve) {\n                        var onDeliver = function (flushed, delivered) {\n                            if (flushed.isSame(ctx)) {\n                                _this.off('flush', onDeliver);\n                                if (delivered) {\n                                    resolve(flushed);\n                                }\n                                else {\n                                    resolve(flushed);\n                                }\n                            }\n                        };\n                        _this.on('flush', onDeliver);\n                    })];\n            });\n        });\n    };\n    CoreEventQueue.prototype.dispatchSingle = function (ctx) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var _this = this;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                ctx.log('debug', 'Dispatching');\n                ctx.stats.increment('message_dispatched');\n                this.queue.updateAttempts(ctx);\n                ctx.attempts = 1;\n                return [2 /*return*/, this.deliver(ctx).catch(function (err) {\n                        var accepted = _this.enqueuRetry(err, ctx);\n                        if (!accepted) {\n                            ctx.setFailedDelivery({ reason: err });\n                            return ctx;\n                        }\n                        return _this.subscribeToDelivery(ctx);\n                    })];\n            });\n        });\n    };\n    CoreEventQueue.prototype.isEmpty = function () {\n        return this.queue.length === 0;\n    };\n    CoreEventQueue.prototype.scheduleFlush = function (timeout) {\n        var _this = this;\n        if (timeout === void 0) { timeout = 500; }\n        if (this.flushing) {\n            return;\n        }\n        this.flushing = true;\n        setTimeout(function () {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            _this.flush().then(function () {\n                setTimeout(function () {\n                    _this.flushing = false;\n                    if (_this.queue.length) {\n                        _this.scheduleFlush(0);\n                    }\n                }, 0);\n            });\n        }, timeout);\n    };\n    CoreEventQueue.prototype.deliver = function (ctx) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var start, done, err_2, error;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.criticalTasks.done()];\n                    case 1:\n                        _a.sent();\n                        start = Date.now();\n                        _a.label = 2;\n                    case 2:\n                        _a.trys.push([2, 4, , 5]);\n                        return [4 /*yield*/, this.flushOne(ctx)];\n                    case 3:\n                        ctx = _a.sent();\n                        done = Date.now() - start;\n                        this.emit('delivery_success', ctx);\n                        ctx.stats.gauge('delivered', done);\n                        ctx.log('debug', 'Delivered', ctx.event);\n                        return [2 /*return*/, ctx];\n                    case 4:\n                        err_2 = _a.sent();\n                        error = err_2;\n                        ctx.log('error', 'Failed to deliver', error);\n                        this.emit('delivery_failure', ctx, error);\n                        ctx.stats.increment('delivery_failed');\n                        throw err_2;\n                    case 5: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    CoreEventQueue.prototype.enqueuRetry = function (err, ctx) {\n        var retriable = !(err instanceof _context__WEBPACK_IMPORTED_MODULE_3__.ContextCancelation) || err.retry;\n        if (!retriable) {\n            return false;\n        }\n        return this.queue.pushWithBackoff(ctx);\n    };\n    CoreEventQueue.prototype.flush = function () {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var ctx, err_3, accepted;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (this.queue.length === 0) {\n                            return [2 /*return*/, []];\n                        }\n                        ctx = this.queue.pop();\n                        if (!ctx) {\n                            return [2 /*return*/, []];\n                        }\n                        ctx.attempts = this.queue.getAttempts(ctx);\n                        _a.label = 1;\n                    case 1:\n                        _a.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this.deliver(ctx)];\n                    case 2:\n                        ctx = _a.sent();\n                        this.emit('flush', ctx, true);\n                        return [3 /*break*/, 4];\n                    case 3:\n                        err_3 = _a.sent();\n                        accepted = this.enqueuRetry(err_3, ctx);\n                        if (!accepted) {\n                            ctx.setFailedDelivery({ reason: err_3 });\n                            this.emit('flush', ctx, false);\n                        }\n                        return [2 /*return*/, []];\n                    case 4: return [2 /*return*/, [ctx]];\n                }\n            });\n        });\n    };\n    CoreEventQueue.prototype.isReady = function () {\n        // return this.plugins.every((p) => p.isLoaded())\n        // should we wait for every plugin to load?\n        return true;\n    };\n    CoreEventQueue.prototype.availableExtensions = function (denyList) {\n        var available = this.plugins.filter(function (p) {\n            var _a, _b, _c;\n            // Only filter out destination plugins or the Segment.io plugin\n            if (p.type !== 'destination' && p.name !== 'Segment.io') {\n                return true;\n            }\n            var alternativeNameMatch = undefined;\n            (_a = p.alternativeNames) === null || _a === void 0 ? void 0 : _a.forEach(function (name) {\n                if (denyList[name] !== undefined) {\n                    alternativeNameMatch = denyList[name];\n                }\n            });\n            // Explicit integration option takes precedence, `All: false` does not apply to Segment.io\n            return ((_c = (_b = denyList[p.name]) !== null && _b !== void 0 ? _b : alternativeNameMatch) !== null && _c !== void 0 ? _c : (p.name === 'Segment.io' ? true : denyList.All) !== false);\n        });\n        var _a = (0,_utils_group_by__WEBPACK_IMPORTED_MODULE_4__.groupBy)(available, 'type'), _b = _a.before, before = _b === void 0 ? [] : _b, _c = _a.enrichment, enrichment = _c === void 0 ? [] : _c, _d = _a.destination, destination = _d === void 0 ? [] : _d, _e = _a.after, after = _e === void 0 ? [] : _e;\n        return {\n            before: before,\n            enrichment: enrichment,\n            destinations: destination,\n            after: after,\n        };\n    };\n    CoreEventQueue.prototype.flushOne = function (ctx) {\n        var _a, _b;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var _c, before, enrichment, _i, before_1, beforeWare, temp, _d, enrichment_1, enrichmentWare, temp, _e, destinations, after, afterCalls;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_f) {\n                switch (_f.label) {\n                    case 0:\n                        if (!this.isReady()) {\n                            throw new Error('Not ready');\n                        }\n                        if (ctx.attempts > 1) {\n                            this.emit('delivery_retry', ctx);\n                        }\n                        _c = this.availableExtensions((_a = ctx.event.integrations) !== null && _a !== void 0 ? _a : {}), before = _c.before, enrichment = _c.enrichment;\n                        _i = 0, before_1 = before;\n                        _f.label = 1;\n                    case 1:\n                        if (!(_i < before_1.length)) return [3 /*break*/, 4];\n                        beforeWare = before_1[_i];\n                        return [4 /*yield*/, (0,_delivery__WEBPACK_IMPORTED_MODULE_5__.ensure)(ctx, beforeWare)];\n                    case 2:\n                        temp = _f.sent();\n                        if (temp instanceof _context__WEBPACK_IMPORTED_MODULE_3__.CoreContext) {\n                            ctx = temp;\n                        }\n                        this.emit('message_enriched', ctx, beforeWare);\n                        _f.label = 3;\n                    case 3:\n                        _i++;\n                        return [3 /*break*/, 1];\n                    case 4:\n                        _d = 0, enrichment_1 = enrichment;\n                        _f.label = 5;\n                    case 5:\n                        if (!(_d < enrichment_1.length)) return [3 /*break*/, 8];\n                        enrichmentWare = enrichment_1[_d];\n                        return [4 /*yield*/, (0,_delivery__WEBPACK_IMPORTED_MODULE_5__.attempt)(ctx, enrichmentWare)];\n                    case 6:\n                        temp = _f.sent();\n                        if (temp instanceof _context__WEBPACK_IMPORTED_MODULE_3__.CoreContext) {\n                            ctx = temp;\n                        }\n                        this.emit('message_enriched', ctx, enrichmentWare);\n                        _f.label = 7;\n                    case 7:\n                        _d++;\n                        return [3 /*break*/, 5];\n                    case 8:\n                        _e = this.availableExtensions((_b = ctx.event.integrations) !== null && _b !== void 0 ? _b : {}), destinations = _e.destinations, after = _e.after;\n                        return [4 /*yield*/, new Promise(function (resolve, reject) {\n                                setTimeout(function () {\n                                    var attempts = destinations.map(function (destination) {\n                                        return (0,_delivery__WEBPACK_IMPORTED_MODULE_5__.attempt)(ctx, destination);\n                                    });\n                                    Promise.all(attempts).then(resolve).catch(reject);\n                                }, 0);\n                            })];\n                    case 9:\n                        _f.sent();\n                        ctx.stats.increment('message_delivered');\n                        this.emit('message_delivered', ctx);\n                        afterCalls = after.map(function (after) { return (0,_delivery__WEBPACK_IMPORTED_MODULE_5__.attempt)(ctx, after); });\n                        return [4 /*yield*/, Promise.all(afterCalls)];\n                    case 10:\n                        _f.sent();\n                        return [2 /*return*/, ctx];\n                }\n            });\n        });\n    };\n    return CoreEventQueue;\n}(_segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_6__.Emitter));\n\n//# sourceMappingURL=event-queue.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/queue/event-queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/stats/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/stats/index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreStats: () => (/* binding */ CoreStats),\n/* harmony export */   NullStats: () => (/* binding */ NullStats)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar compactMetricType = function (type) {\n    var enums = {\n        gauge: 'g',\n        counter: 'c',\n    };\n    return enums[type];\n};\nvar CoreStats = /** @class */ (function () {\n    function CoreStats() {\n        this.metrics = [];\n    }\n    CoreStats.prototype.increment = function (metric, by, tags) {\n        if (by === void 0) { by = 1; }\n        this.metrics.push({\n            metric: metric,\n            value: by,\n            tags: tags !== null && tags !== void 0 ? tags : [],\n            type: 'counter',\n            timestamp: Date.now(),\n        });\n    };\n    CoreStats.prototype.gauge = function (metric, value, tags) {\n        this.metrics.push({\n            metric: metric,\n            value: value,\n            tags: tags !== null && tags !== void 0 ? tags : [],\n            type: 'gauge',\n            timestamp: Date.now(),\n        });\n    };\n    CoreStats.prototype.flush = function () {\n        var formatted = this.metrics.map(function (m) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, m), { tags: m.tags.join(',') })); });\n        // ie doesn't like console.table\n        if (console.table) {\n            console.table(formatted);\n        }\n        else {\n            console.log(formatted);\n        }\n        this.metrics = [];\n    };\n    /**\n     * compact keys for smaller payload\n     */\n    CoreStats.prototype.serialize = function () {\n        return this.metrics.map(function (m) {\n            return {\n                m: m.metric,\n                v: m.value,\n                t: m.tags,\n                k: compactMetricType(m.type),\n                e: m.timestamp,\n            };\n        });\n    };\n    return CoreStats;\n}());\n\nvar NullStats = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(NullStats, _super);\n    function NullStats() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NullStats.prototype.gauge = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    NullStats.prototype.increment = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    NullStats.prototype.flush = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    NullStats.prototype.serialize = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n        return [];\n    };\n    return NullStats;\n}(CoreStats));\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vc3RhdHMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0Esd0RBQXdELFFBQVEsK0NBQVEsQ0FBQywrQ0FBUSxHQUFHLFFBQVEsd0JBQXdCLEtBQUs7QUFDekg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxDQUFDO0FBQ29CO0FBQ3JCO0FBQ0EsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsdUJBQXVCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsdUJBQXVCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsdUJBQXVCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsdUJBQXVCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ29CO0FBQ3JCIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBzZWdtZW50XFxhbmFseXRpY3MtY29yZVxcZGlzdFxcZXNtXFxzdGF0c1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgX19hc3NpZ24sIF9fZXh0ZW5kcyB9IGZyb20gXCJ0c2xpYlwiO1xudmFyIGNvbXBhY3RNZXRyaWNUeXBlID0gZnVuY3Rpb24gKHR5cGUpIHtcbiAgICB2YXIgZW51bXMgPSB7XG4gICAgICAgIGdhdWdlOiAnZycsXG4gICAgICAgIGNvdW50ZXI6ICdjJyxcbiAgICB9O1xuICAgIHJldHVybiBlbnVtc1t0eXBlXTtcbn07XG52YXIgQ29yZVN0YXRzID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKCkge1xuICAgIGZ1bmN0aW9uIENvcmVTdGF0cygpIHtcbiAgICAgICAgdGhpcy5tZXRyaWNzID0gW107XG4gICAgfVxuICAgIENvcmVTdGF0cy5wcm90b3R5cGUuaW5jcmVtZW50ID0gZnVuY3Rpb24gKG1ldHJpYywgYnksIHRhZ3MpIHtcbiAgICAgICAgaWYgKGJ5ID09PSB2b2lkIDApIHsgYnkgPSAxOyB9XG4gICAgICAgIHRoaXMubWV0cmljcy5wdXNoKHtcbiAgICAgICAgICAgIG1ldHJpYzogbWV0cmljLFxuICAgICAgICAgICAgdmFsdWU6IGJ5LFxuICAgICAgICAgICAgdGFnczogdGFncyAhPT0gbnVsbCAmJiB0YWdzICE9PSB2b2lkIDAgPyB0YWdzIDogW10sXG4gICAgICAgICAgICB0eXBlOiAnY291bnRlcicsXG4gICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgQ29yZVN0YXRzLnByb3RvdHlwZS5nYXVnZSA9IGZ1bmN0aW9uIChtZXRyaWMsIHZhbHVlLCB0YWdzKSB7XG4gICAgICAgIHRoaXMubWV0cmljcy5wdXNoKHtcbiAgICAgICAgICAgIG1ldHJpYzogbWV0cmljLFxuICAgICAgICAgICAgdmFsdWU6IHZhbHVlLFxuICAgICAgICAgICAgdGFnczogdGFncyAhPT0gbnVsbCAmJiB0YWdzICE9PSB2b2lkIDAgPyB0YWdzIDogW10sXG4gICAgICAgICAgICB0eXBlOiAnZ2F1Z2UnLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIENvcmVTdGF0cy5wcm90b3R5cGUuZmx1c2ggPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBmb3JtYXR0ZWQgPSB0aGlzLm1ldHJpY3MubWFwKGZ1bmN0aW9uIChtKSB7IHJldHVybiAoX19hc3NpZ24oX19hc3NpZ24oe30sIG0pLCB7IHRhZ3M6IG0udGFncy5qb2luKCcsJykgfSkpOyB9KTtcbiAgICAgICAgLy8gaWUgZG9lc24ndCBsaWtlIGNvbnNvbGUudGFibGVcbiAgICAgICAgaWYgKGNvbnNvbGUudGFibGUpIHtcbiAgICAgICAgICAgIGNvbnNvbGUudGFibGUoZm9ybWF0dGVkKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGZvcm1hdHRlZCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5tZXRyaWNzID0gW107XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBjb21wYWN0IGtleXMgZm9yIHNtYWxsZXIgcGF5bG9hZFxuICAgICAqL1xuICAgIENvcmVTdGF0cy5wcm90b3R5cGUuc2VyaWFsaXplID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5tZXRyaWNzLm1hcChmdW5jdGlvbiAobSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBtOiBtLm1ldHJpYyxcbiAgICAgICAgICAgICAgICB2OiBtLnZhbHVlLFxuICAgICAgICAgICAgICAgIHQ6IG0udGFncyxcbiAgICAgICAgICAgICAgICBrOiBjb21wYWN0TWV0cmljVHlwZShtLnR5cGUpLFxuICAgICAgICAgICAgICAgIGU6IG0udGltZXN0YW1wLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICByZXR1cm4gQ29yZVN0YXRzO1xufSgpKTtcbmV4cG9ydCB7IENvcmVTdGF0cyB9O1xudmFyIE51bGxTdGF0cyA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uIChfc3VwZXIpIHtcbiAgICBfX2V4dGVuZHMoTnVsbFN0YXRzLCBfc3VwZXIpO1xuICAgIGZ1bmN0aW9uIE51bGxTdGF0cygpIHtcbiAgICAgICAgcmV0dXJuIF9zdXBlciAhPT0gbnVsbCAmJiBfc3VwZXIuYXBwbHkodGhpcywgYXJndW1lbnRzKSB8fCB0aGlzO1xuICAgIH1cbiAgICBOdWxsU3RhdHMucHJvdG90eXBlLmdhdWdlID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgX2FyZ3MgPSBbXTtcbiAgICAgICAgZm9yICh2YXIgX2kgPSAwOyBfaSA8IGFyZ3VtZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgICAgIF9hcmdzW19pXSA9IGFyZ3VtZW50c1tfaV07XG4gICAgICAgIH1cbiAgICB9O1xuICAgIE51bGxTdGF0cy5wcm90b3R5cGUuaW5jcmVtZW50ID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgX2FyZ3MgPSBbXTtcbiAgICAgICAgZm9yICh2YXIgX2kgPSAwOyBfaSA8IGFyZ3VtZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgICAgIF9hcmdzW19pXSA9IGFyZ3VtZW50c1tfaV07XG4gICAgICAgIH1cbiAgICB9O1xuICAgIE51bGxTdGF0cy5wcm90b3R5cGUuZmx1c2ggPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBfYXJncyA9IFtdO1xuICAgICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgYXJndW1lbnRzLmxlbmd0aDsgX2krKykge1xuICAgICAgICAgICAgX2FyZ3NbX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgTnVsbFN0YXRzLnByb3RvdHlwZS5zZXJpYWxpemUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBfYXJncyA9IFtdO1xuICAgICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgYXJndW1lbnRzLmxlbmd0aDsgX2krKykge1xuICAgICAgICAgICAgX2FyZ3NbX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gW107XG4gICAgfTtcbiAgICByZXR1cm4gTnVsbFN0YXRzO1xufShDb3JlU3RhdHMpKTtcbmV4cG9ydCB7IE51bGxTdGF0cyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/stats/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/task/task-group.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/task/task-group.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTaskGroup: () => (/* binding */ createTaskGroup)\n/* harmony export */ });\n/* harmony import */ var _utils_is_thenable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-thenable */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/is-thenable.js\");\n\nvar createTaskGroup = function () {\n    var taskCompletionPromise;\n    var resolvePromise;\n    var count = 0;\n    return {\n        done: function () { return taskCompletionPromise; },\n        run: function (op) {\n            var returnValue = op();\n            if ((0,_utils_is_thenable__WEBPACK_IMPORTED_MODULE_0__.isThenable)(returnValue)) {\n                if (++count === 1) {\n                    taskCompletionPromise = new Promise(function (res) { return (resolvePromise = res); });\n                }\n                returnValue.finally(function () { return --count === 0 && resolvePromise(); });\n            }\n            return returnValue;\n        },\n    };\n};\n//# sourceMappingURL=task-group.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdGFzay90YXNrLWdyb3VwLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtEO0FBQzNDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsK0JBQStCO0FBQzNEO0FBQ0E7QUFDQSxnQkFBZ0IsOERBQVU7QUFDMUI7QUFDQSx5RUFBeUUsZ0NBQWdDO0FBQ3pHO0FBQ0Esa0RBQWtELDJDQUEyQztBQUM3RjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAc2VnbWVudFxcYW5hbHl0aWNzLWNvcmVcXGRpc3RcXGVzbVxcdGFza1xcdGFzay1ncm91cC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc1RoZW5hYmxlIH0gZnJvbSAnLi4vdXRpbHMvaXMtdGhlbmFibGUnO1xuZXhwb3J0IHZhciBjcmVhdGVUYXNrR3JvdXAgPSBmdW5jdGlvbiAoKSB7XG4gICAgdmFyIHRhc2tDb21wbGV0aW9uUHJvbWlzZTtcbiAgICB2YXIgcmVzb2x2ZVByb21pc2U7XG4gICAgdmFyIGNvdW50ID0gMDtcbiAgICByZXR1cm4ge1xuICAgICAgICBkb25lOiBmdW5jdGlvbiAoKSB7IHJldHVybiB0YXNrQ29tcGxldGlvblByb21pc2U7IH0sXG4gICAgICAgIHJ1bjogZnVuY3Rpb24gKG9wKSB7XG4gICAgICAgICAgICB2YXIgcmV0dXJuVmFsdWUgPSBvcCgpO1xuICAgICAgICAgICAgaWYgKGlzVGhlbmFibGUocmV0dXJuVmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgaWYgKCsrY291bnQgPT09IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgdGFza0NvbXBsZXRpb25Qcm9taXNlID0gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlcykgeyByZXR1cm4gKHJlc29sdmVQcm9taXNlID0gcmVzKTsgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVyblZhbHVlLmZpbmFsbHkoZnVuY3Rpb24gKCkgeyByZXR1cm4gLS1jb3VudCA9PT0gMCAmJiByZXNvbHZlUHJvbWlzZSgpOyB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiByZXR1cm5WYWx1ZTtcbiAgICAgICAgfSxcbiAgICB9O1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRhc2stZ3JvdXAuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/task/task-group.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/bind-all.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/utils/bind-all.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bindAll: () => (/* binding */ bindAll)\n/* harmony export */ });\nfunction bindAll(obj) {\n    var proto = obj.constructor.prototype;\n    for (var _i = 0, _a = Object.getOwnPropertyNames(proto); _i < _a.length; _i++) {\n        var key = _a[_i];\n        if (key !== 'constructor') {\n            var desc = Object.getOwnPropertyDescriptor(obj.constructor.prototype, key);\n            if (!!desc && typeof desc.value === 'function') {\n                obj[key] = obj[key].bind(obj);\n            }\n        }\n    }\n    return obj;\n}\n//# sourceMappingURL=bind-all.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdXRpbHMvYmluZC1hbGwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQSw2REFBNkQsZ0JBQWdCO0FBQzdFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1jb3JlXFxkaXN0XFxlc21cXHV0aWxzXFxiaW5kLWFsbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gYmluZEFsbChvYmopIHtcbiAgICB2YXIgcHJvdG8gPSBvYmouY29uc3RydWN0b3IucHJvdG90eXBlO1xuICAgIGZvciAodmFyIF9pID0gMCwgX2EgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyhwcm90byk7IF9pIDwgX2EubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIHZhciBrZXkgPSBfYVtfaV07XG4gICAgICAgIGlmIChrZXkgIT09ICdjb25zdHJ1Y3RvcicpIHtcbiAgICAgICAgICAgIHZhciBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihvYmouY29uc3RydWN0b3IucHJvdG90eXBlLCBrZXkpO1xuICAgICAgICAgICAgaWYgKCEhZGVzYyAmJiB0eXBlb2YgZGVzYy52YWx1ZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgIG9ialtrZXldID0gb2JqW2tleV0uYmluZChvYmopO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBvYmo7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1iaW5kLWFsbC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/bind-all.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/group-by.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/utils/group-by.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupBy: () => (/* binding */ groupBy)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n\nfunction groupBy(collection, grouper) {\n    var results = {};\n    collection.forEach(function (item) {\n        var _a;\n        var key = undefined;\n        if (typeof grouper === 'string') {\n            var suggestedKey = item[grouper];\n            key =\n                typeof suggestedKey !== 'string'\n                    ? JSON.stringify(suggestedKey)\n                    : suggestedKey;\n        }\n        else if (grouper instanceof Function) {\n            key = grouper(item);\n        }\n        if (key === undefined) {\n            return;\n        }\n        results[key] = (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__spreadArray)([], ((_a = results[key]) !== null && _a !== void 0 ? _a : []), true), [item], false);\n    });\n    return results;\n}\n//# sourceMappingURL=group-by.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdXRpbHMvZ3JvdXAtYnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0M7QUFDL0I7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLG9EQUFhLENBQUMsb0RBQWE7QUFDbEQsS0FBSztBQUNMO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAc2VnbWVudFxcYW5hbHl0aWNzLWNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXGdyb3VwLWJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IF9fc3ByZWFkQXJyYXkgfSBmcm9tIFwidHNsaWJcIjtcbmV4cG9ydCBmdW5jdGlvbiBncm91cEJ5KGNvbGxlY3Rpb24sIGdyb3VwZXIpIHtcbiAgICB2YXIgcmVzdWx0cyA9IHt9O1xuICAgIGNvbGxlY3Rpb24uZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHZhciBrZXkgPSB1bmRlZmluZWQ7XG4gICAgICAgIGlmICh0eXBlb2YgZ3JvdXBlciA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgIHZhciBzdWdnZXN0ZWRLZXkgPSBpdGVtW2dyb3VwZXJdO1xuICAgICAgICAgICAga2V5ID1cbiAgICAgICAgICAgICAgICB0eXBlb2Ygc3VnZ2VzdGVkS2V5ICE9PSAnc3RyaW5nJ1xuICAgICAgICAgICAgICAgICAgICA/IEpTT04uc3RyaW5naWZ5KHN1Z2dlc3RlZEtleSlcbiAgICAgICAgICAgICAgICAgICAgOiBzdWdnZXN0ZWRLZXk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoZ3JvdXBlciBpbnN0YW5jZW9mIEZ1bmN0aW9uKSB7XG4gICAgICAgICAgICBrZXkgPSBncm91cGVyKGl0ZW0pO1xuICAgICAgICB9XG4gICAgICAgIGlmIChrZXkgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHJlc3VsdHNba2V5XSA9IF9fc3ByZWFkQXJyYXkoX19zcHJlYWRBcnJheShbXSwgKChfYSA9IHJlc3VsdHNba2V5XSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogW10pLCB0cnVlKSwgW2l0ZW1dLCBmYWxzZSk7XG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3VsdHM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ncm91cC1ieS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/group-by.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/is-thenable.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/utils/is-thenable.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isThenable: () => (/* binding */ isThenable)\n/* harmony export */ });\n/**\n *  Check if  thenable\n *  (instanceof Promise doesn't respect realms)\n */\nvar isThenable = function (value) {\n    return typeof value === 'object' &&\n        value !== null &&\n        'then' in value &&\n        typeof value.then === 'function';\n};\n//# sourceMappingURL=is-thenable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdXRpbHMvaXMtdGhlbmFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1jb3JlXFxkaXN0XFxlc21cXHV0aWxzXFxpcy10aGVuYWJsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqICBDaGVjayBpZiAgdGhlbmFibGVcbiAqICAoaW5zdGFuY2VvZiBQcm9taXNlIGRvZXNuJ3QgcmVzcGVjdCByZWFsbXMpXG4gKi9cbmV4cG9ydCB2YXIgaXNUaGVuYWJsZSA9IGZ1bmN0aW9uICh2YWx1ZSkge1xuICAgIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnICYmXG4gICAgICAgIHZhbHVlICE9PSBudWxsICYmXG4gICAgICAgICd0aGVuJyBpbiB2YWx1ZSAmJlxuICAgICAgICB0eXBlb2YgdmFsdWUudGhlbiA9PT0gJ2Z1bmN0aW9uJztcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pcy10aGVuYWJsZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/is-thenable.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/pick.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/utils/pick.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickBy: () => (/* binding */ pickBy)\n/* harmony export */ });\nvar pickBy = function (obj, fn) {\n    return Object.keys(obj)\n        .filter(function (k) { return fn(k, obj[k]); })\n        .reduce(function (acc, key) { return ((acc[key] = obj[key]), acc); }, {});\n};\n//# sourceMappingURL=pick.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdXRpbHMvcGljay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBLCtCQUErQix1QkFBdUI7QUFDdEQsc0NBQXNDLHNDQUFzQyxJQUFJO0FBQ2hGO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1jb3JlXFxkaXN0XFxlc21cXHV0aWxzXFxwaWNrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgcGlja0J5ID0gZnVuY3Rpb24gKG9iaiwgZm4pIHtcbiAgICByZXR1cm4gT2JqZWN0LmtleXMob2JqKVxuICAgICAgICAuZmlsdGVyKGZ1bmN0aW9uIChrKSB7IHJldHVybiBmbihrLCBvYmpba10pOyB9KVxuICAgICAgICAucmVkdWNlKGZ1bmN0aW9uIChhY2MsIGtleSkgeyByZXR1cm4gKChhY2Nba2V5XSA9IG9ialtrZXldKSwgYWNjKTsgfSwge30pO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBpY2suanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/assertions.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/validation/assertions.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertEventExists: () => (/* binding */ assertEventExists),\n/* harmony export */   assertEventType: () => (/* binding */ assertEventType),\n/* harmony export */   assertMessageId: () => (/* binding */ assertMessageId),\n/* harmony export */   assertTrackEventName: () => (/* binding */ assertTrackEventName),\n/* harmony export */   assertTrackEventProperties: () => (/* binding */ assertTrackEventProperties),\n/* harmony export */   assertTraits: () => (/* binding */ assertTraits),\n/* harmony export */   assertUserIdentity: () => (/* binding */ assertUserIdentity),\n/* harmony export */   validateEvent: () => (/* binding */ validateEvent)\n/* harmony export */ });\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/errors.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/helpers.js\");\n\n\nvar stringError = 'is not a string';\nvar objError = 'is not an object';\nvar nilError = 'is nil';\n// user identity check could hypothetically could be used in the browser event factory, but not 100% sure -- so this is node only for now\nfunction assertUserIdentity(event) {\n    var USER_FIELD_NAME = '.userId/anonymousId/previousId/groupId';\n    var getAnyUserId = function (event) { var _a, _b, _c; return (_c = (_b = (_a = event.userId) !== null && _a !== void 0 ? _a : event.anonymousId) !== null && _b !== void 0 ? _b : event.groupId) !== null && _c !== void 0 ? _c : event.previousId; };\n    var id = getAnyUserId(event);\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.exists)(id)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError(USER_FIELD_NAME, nilError);\n    }\n    else if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isString)(id)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError(USER_FIELD_NAME, stringError);\n    }\n}\nfunction assertEventExists(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.exists)(event)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('Event', nilError);\n    }\n    if (typeof event !== 'object') {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('Event', objError);\n    }\n}\nfunction assertEventType(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isString)(event.type)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.type', stringError);\n    }\n}\nfunction assertTrackEventName(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isString)(event.event)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.event', stringError);\n    }\n}\nfunction assertTrackEventProperties(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(event.properties)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.properties', objError);\n    }\n}\nfunction assertTraits(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(event.traits)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.traits', objError);\n    }\n}\nfunction assertMessageId(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isString)(event.messageId)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.messageId', stringError);\n    }\n}\nfunction validateEvent(event) {\n    assertEventExists(event);\n    assertEventType(event);\n    assertMessageId(event);\n    if (event.type === 'track') {\n        assertTrackEventName(event);\n        assertTrackEventProperties(event);\n    }\n    if (['group', 'identify'].includes(event.type)) {\n        assertTraits(event);\n    }\n}\n//# sourceMappingURL=assertions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/assertions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/errors.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/validation/errors.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ValidationError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(ValidationError, _super);\n    function ValidationError(field, message) {\n        var _this = _super.call(this, \"\".concat(field, \" \").concat(message)) || this;\n        _this.field = field;\n        return _this;\n    }\n    return ValidationError;\n}(Error));\n\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdmFsaWRhdGlvbi9lcnJvcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEM7QUFDQSxJQUFJLGdEQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUMwQjtBQUMzQiIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAc2VnbWVudFxcYW5hbHl0aWNzLWNvcmVcXGRpc3RcXGVzbVxcdmFsaWRhdGlvblxcZXJyb3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IF9fZXh0ZW5kcyB9IGZyb20gXCJ0c2xpYlwiO1xudmFyIFZhbGlkYXRpb25FcnJvciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uIChfc3VwZXIpIHtcbiAgICBfX2V4dGVuZHMoVmFsaWRhdGlvbkVycm9yLCBfc3VwZXIpO1xuICAgIGZ1bmN0aW9uIFZhbGlkYXRpb25FcnJvcihmaWVsZCwgbWVzc2FnZSkge1xuICAgICAgICB2YXIgX3RoaXMgPSBfc3VwZXIuY2FsbCh0aGlzLCBcIlwiLmNvbmNhdChmaWVsZCwgXCIgXCIpLmNvbmNhdChtZXNzYWdlKSkgfHwgdGhpcztcbiAgICAgICAgX3RoaXMuZmllbGQgPSBmaWVsZDtcbiAgICAgICAgcmV0dXJuIF90aGlzO1xuICAgIH1cbiAgICByZXR1cm4gVmFsaWRhdGlvbkVycm9yO1xufShFcnJvcikpO1xuZXhwb3J0IHsgVmFsaWRhdGlvbkVycm9yIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lcnJvcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/helpers.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-core/dist/esm/validation/helpers.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exists: () => (/* binding */ exists),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isString: () => (/* binding */ isString)\n/* harmony export */ });\nfunction isString(obj) {\n    return typeof obj === 'string';\n}\nfunction isNumber(obj) {\n    return typeof obj === 'number';\n}\nfunction isFunction(obj) {\n    return typeof obj === 'function';\n}\nfunction exists(val) {\n    return val !== undefined && val !== null;\n}\nfunction isPlainObject(obj) {\n    return (Object.prototype.toString.call(obj).slice(8, -1).toLowerCase() === 'object');\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdmFsaWRhdGlvbi9oZWxwZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1jb3JlXFxkaXN0XFxlc21cXHZhbGlkYXRpb25cXGhlbHBlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGlzU3RyaW5nKG9iaikge1xuICAgIHJldHVybiB0eXBlb2Ygb2JqID09PSAnc3RyaW5nJztcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc051bWJlcihvYmopIHtcbiAgICByZXR1cm4gdHlwZW9mIG9iaiA9PT0gJ251bWJlcic7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNGdW5jdGlvbihvYmopIHtcbiAgICByZXR1cm4gdHlwZW9mIG9iaiA9PT0gJ2Z1bmN0aW9uJztcbn1cbmV4cG9ydCBmdW5jdGlvbiBleGlzdHModmFsKSB7XG4gICAgcmV0dXJuIHZhbCAhPT0gdW5kZWZpbmVkICYmIHZhbCAhPT0gbnVsbDtcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc1BsYWluT2JqZWN0KG9iaikge1xuICAgIHJldHVybiAoT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKG9iaikuc2xpY2UoOCwgLTEpLnRvTG93ZXJDYXNlKCkgPT09ICdvYmplY3QnKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWhlbHBlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/create-deferred/create-deferred.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@segment/analytics-generic-utils/dist/esm/create-deferred/create-deferred.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDeferred: () => (/* binding */ createDeferred)\n/* harmony export */ });\n/**\n * Return a promise that can be externally resolved\n */\nvar createDeferred = function () {\n    var resolve;\n    var reject;\n    var settled = false;\n    var promise = new Promise(function (_resolve, _reject) {\n        resolve = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            settled = true;\n            _resolve.apply(void 0, args);\n        };\n        reject = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            settled = true;\n            _reject.apply(void 0, args);\n        };\n    });\n    return {\n        resolve: resolve,\n        reject: reject,\n        promise: promise,\n        isSettled: function () { return settled; },\n    };\n};\n//# sourceMappingURL=create-deferred.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/create-deferred/create-deferred.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter)\n/* harmony export */ });\n/**\n * Event Emitter that takes the expected contract as a generic\n * @example\n * ```ts\n *  type Contract = {\n *    delivery_success: [DeliverySuccessResponse, Metrics],\n *    delivery_failure: [DeliveryError]\n * }\n *  new Emitter<Contract>()\n *  .on('delivery_success', (res, metrics) => ...)\n *  .on('delivery_failure', (err) => ...)\n * ```\n */\nvar Emitter = /** @class */ (function () {\n    function Emitter(options) {\n        var _a;\n        this.callbacks = {};\n        this.warned = false;\n        this.maxListeners = (_a = options === null || options === void 0 ? void 0 : options.maxListeners) !== null && _a !== void 0 ? _a : 10;\n    }\n    Emitter.prototype.warnIfPossibleMemoryLeak = function (event) {\n        if (this.warned) {\n            return;\n        }\n        if (this.maxListeners &&\n            this.callbacks[event].length > this.maxListeners) {\n            console.warn(\"Event Emitter: Possible memory leak detected; \".concat(String(event), \" has exceeded \").concat(this.maxListeners, \" listeners.\"));\n            this.warned = true;\n        }\n    };\n    Emitter.prototype.on = function (event, callback) {\n        if (!this.callbacks[event]) {\n            this.callbacks[event] = [callback];\n        }\n        else {\n            this.callbacks[event].push(callback);\n            this.warnIfPossibleMemoryLeak(event);\n        }\n        return this;\n    };\n    Emitter.prototype.once = function (event, callback) {\n        var _this = this;\n        var on = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            _this.off(event, on);\n            callback.apply(_this, args);\n        };\n        this.on(event, on);\n        return this;\n    };\n    Emitter.prototype.off = function (event, callback) {\n        var _a;\n        var fns = (_a = this.callbacks[event]) !== null && _a !== void 0 ? _a : [];\n        var without = fns.filter(function (fn) { return fn !== callback; });\n        this.callbacks[event] = without;\n        return this;\n    };\n    Emitter.prototype.emit = function (event) {\n        var _this = this;\n        var _a;\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var callbacks = (_a = this.callbacks[event]) !== null && _a !== void 0 ? _a : [];\n        callbacks.forEach(function (callback) {\n            callback.apply(_this, args);\n        });\n        return this;\n    };\n    return Emitter;\n}());\n\n//# sourceMappingURL=emitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/analytics-node.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/app/analytics-node.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/utils/bind-all.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/callback/index.js\");\n/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./settings */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/settings.js\");\n/* harmony import */ var _generated_version__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../generated/version */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/generated/version.js\");\n/* harmony import */ var _plugins_segmentio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../plugins/segmentio */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/index.js\");\n/* harmony import */ var _event_factory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./event-factory */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/event-factory.js\");\n/* harmony import */ var _dispatch_emit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dispatch-emit */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/dispatch-emit.js\");\n/* harmony import */ var _emitter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./emitter */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/emitter.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./context */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/context.js\");\n/* harmony import */ var _event_queue__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./event-queue */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/event-queue.js\");\n/* harmony import */ var _lib_http_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/http-client */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/http-client.js\");\n\n\n\n\n\n\n\n\n\n\nclass Analytics extends _emitter__WEBPACK_IMPORTED_MODULE_5__.NodeEmitter {\n    _eventFactory;\n    _isClosed = false;\n    _pendingEvents = 0;\n    _closeAndFlushDefaultTimeout;\n    _publisher;\n    _isFlushing = false;\n    _queue;\n    ready;\n    constructor(settings) {\n        super();\n        (0,_settings__WEBPACK_IMPORTED_MODULE_0__.validateSettings)(settings);\n        this._eventFactory = new _event_factory__WEBPACK_IMPORTED_MODULE_3__.NodeEventFactory();\n        this._queue = new _event_queue__WEBPACK_IMPORTED_MODULE_7__.NodeEventQueue();\n        const flushInterval = settings.flushInterval ?? 10000;\n        this._closeAndFlushDefaultTimeout = flushInterval * 1.25; // add arbitrary multiplier in case an event is in a plugin.\n        const { plugin, publisher } = (0,_plugins_segmentio__WEBPACK_IMPORTED_MODULE_2__.createConfiguredNodePlugin)({\n            writeKey: settings.writeKey,\n            host: settings.host,\n            path: settings.path,\n            maxRetries: settings.maxRetries ?? 3,\n            flushAt: settings.flushAt ?? settings.maxEventsInBatch ?? 15,\n            httpRequestTimeout: settings.httpRequestTimeout,\n            disable: settings.disable,\n            flushInterval,\n            httpClient: typeof settings.httpClient === 'function'\n                ? new _lib_http_client__WEBPACK_IMPORTED_MODULE_8__.FetchHTTPClient(settings.httpClient)\n                : settings.httpClient ?? new _lib_http_client__WEBPACK_IMPORTED_MODULE_8__.FetchHTTPClient(),\n            oauthSettings: settings.oauthSettings,\n        }, this);\n        this._publisher = publisher;\n        this.ready = this.register(plugin).then(() => undefined);\n        this.emit('initialize', settings);\n        (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_9__.bindAll)(this);\n    }\n    get VERSION() {\n        return _generated_version__WEBPACK_IMPORTED_MODULE_1__.version;\n    }\n    /**\n     * Call this method to stop collecting new events and flush all existing events.\n     * This method also waits for any event method-specific callbacks to be triggered,\n     * and any of their subsequent promises to be resolved/rejected.\n     */\n    closeAndFlush({ timeout = this._closeAndFlushDefaultTimeout, } = {}) {\n        return this.flush({ timeout, close: true });\n    }\n    /**\n     * Call this method to flush all existing events..\n     * This method also waits for any event method-specific callbacks to be triggered,\n     * and any of their subsequent promises to be resolved/rejected.\n     */\n    async flush({ timeout, close = false, } = {}) {\n        if (this._isFlushing) {\n            // if we're already flushing, then we don't need to do anything\n            console.warn('Overlapping flush calls detected. Please wait for the previous flush to finish before calling .flush again');\n            return;\n        }\n        else {\n            this._isFlushing = true;\n        }\n        if (close) {\n            this._isClosed = true;\n        }\n        this._publisher.flush(this._pendingEvents);\n        const promise = new Promise((resolve) => {\n            if (!this._pendingEvents) {\n                resolve();\n            }\n            else {\n                this.once('drained', () => {\n                    resolve();\n                });\n            }\n        }).finally(() => {\n            this._isFlushing = false;\n        });\n        return timeout ? (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_10__.pTimeout)(promise, timeout).catch(() => undefined) : promise;\n    }\n    _dispatch(segmentEvent, callback) {\n        if (this._isClosed) {\n            this.emit('call_after_close', segmentEvent);\n            return undefined;\n        }\n        this._pendingEvents++;\n        (0,_dispatch_emit__WEBPACK_IMPORTED_MODULE_4__.dispatchAndEmit)(segmentEvent, this._queue, this, callback)\n            .catch((ctx) => ctx)\n            .finally(() => {\n            this._pendingEvents--;\n            if (!this._pendingEvents) {\n                this.emit('drained');\n            }\n        });\n    }\n    /**\n     * Combines two unassociated user identities.\n     * @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#alias\n     */\n    alias({ userId, previousId, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.alias(userId, previousId, {\n            context,\n            integrations,\n            timestamp,\n            messageId,\n        });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Associates an identified user with a collective.\n     *  @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#group\n     */\n    group({ timestamp, groupId, userId, anonymousId, traits = {}, context, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.group(groupId, traits, {\n            context,\n            anonymousId,\n            userId,\n            timestamp,\n            integrations,\n            messageId,\n        });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Includes a unique userId and (maybe anonymousId) and any optional traits you know about them.\n     * @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#identify\n     */\n    identify({ userId, anonymousId, traits = {}, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.identify(userId, traits, {\n            context,\n            anonymousId,\n            userId,\n            timestamp,\n            integrations,\n            messageId,\n        });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * The page method lets you record page views on your website, along with optional extra information about the page being viewed.\n     * @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#page\n     */\n    page({ userId, anonymousId, category, name, properties, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.page(category ?? null, name ?? null, properties, { context, anonymousId, userId, timestamp, integrations, messageId });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Records screen views on your app, along with optional extra information\n     * about the screen viewed by the user.\n     *\n     * TODO: This is not documented on the segment docs ATM (for node).\n     */\n    screen({ userId, anonymousId, category, name, properties, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.screen(category ?? null, name ?? null, properties, { context, anonymousId, userId, timestamp, integrations, messageId });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Records actions your users perform.\n     * @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#track\n     */\n    track({ userId, anonymousId, event, properties, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.track(event, properties, {\n            context,\n            userId,\n            anonymousId,\n            timestamp,\n            integrations,\n            messageId,\n        });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Registers one or more plugins to augment Analytics functionality.\n     * @param plugins\n     */\n    register(...plugins) {\n        return this._queue.criticalTasks.run(async () => {\n            const ctx = _context__WEBPACK_IMPORTED_MODULE_6__.Context.system();\n            const registrations = plugins.map((xt) => this._queue.register(ctx, xt, this));\n            await Promise.all(registrations);\n            this.emit('register', plugins.map((el) => el.name));\n        });\n    }\n    /**\n     * Deregisters one or more plugins based on their names.\n     * @param pluginNames - The names of one or more plugins to deregister.\n     */\n    async deregister(...pluginNames) {\n        const ctx = _context__WEBPACK_IMPORTED_MODULE_6__.Context.system();\n        const deregistrations = pluginNames.map((pl) => {\n            const plugin = this._queue.plugins.find((p) => p.name === pl);\n            if (plugin) {\n                return this._queue.deregister(ctx, plugin, this);\n            }\n            else {\n                ctx.log('warn', `plugin ${pl} not found`);\n            }\n        });\n        await Promise.all(deregistrations);\n        this.emit('deregister', pluginNames);\n    }\n}\n//# sourceMappingURL=analytics-node.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/analytics-node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/context.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/app/context.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/context/index.js\");\n// create a derived class since we may want to add node specific things to Context later\n\n// While this is not a type, it is a definition\nclass Context extends _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__.CoreContext {\n    static system() {\n        return new this({ type: 'track', event: 'system' });\n    }\n}\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vYXBwL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNzRDtBQUN0RDtBQUNPLHNCQUFzQixnRUFBVztBQUN4QztBQUNBLDBCQUEwQixnQ0FBZ0M7QUFDMUQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBzZWdtZW50XFxhbmFseXRpY3Mtbm9kZVxcZGlzdFxcZXNtXFxhcHBcXGNvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gY3JlYXRlIGEgZGVyaXZlZCBjbGFzcyBzaW5jZSB3ZSBtYXkgd2FudCB0byBhZGQgbm9kZSBzcGVjaWZpYyB0aGluZ3MgdG8gQ29udGV4dCBsYXRlclxuaW1wb3J0IHsgQ29yZUNvbnRleHQgfSBmcm9tICdAc2VnbWVudC9hbmFseXRpY3MtY29yZSc7XG4vLyBXaGlsZSB0aGlzIGlzIG5vdCBhIHR5cGUsIGl0IGlzIGEgZGVmaW5pdGlvblxuZXhwb3J0IGNsYXNzIENvbnRleHQgZXh0ZW5kcyBDb3JlQ29udGV4dCB7XG4gICAgc3RhdGljIHN5c3RlbSgpIHtcbiAgICAgICAgcmV0dXJuIG5ldyB0aGlzKHsgdHlwZTogJ3RyYWNrJywgZXZlbnQ6ICdzeXN0ZW0nIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/dispatch-emit.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/app/dispatch-emit.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dispatchAndEmit: () => (/* binding */ dispatchAndEmit)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/analytics/dispatch.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/context.js\");\n\n\nconst normalizeDispatchCb = (cb) => (ctx) => {\n    const failedDelivery = ctx.failedDelivery();\n    return failedDelivery ? cb(failedDelivery.reason, ctx) : cb(undefined, ctx);\n};\n/* Dispatch function, but swallow promise rejections and use event emitter instead */\nconst dispatchAndEmit = async (event, queue, emitter, callback) => {\n    try {\n        const context = new _context__WEBPACK_IMPORTED_MODULE_0__.Context(event);\n        const ctx = await (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__.dispatch)(context, queue, emitter, {\n            ...(callback ? { callback: normalizeDispatchCb(callback) } : {}),\n        });\n        const failedDelivery = ctx.failedDelivery();\n        if (failedDelivery) {\n            emitter.emit('error', {\n                code: 'delivery_failure',\n                reason: failedDelivery.reason,\n                ctx: ctx,\n            });\n        }\n        else {\n            emitter.emit(event.type, ctx);\n        }\n    }\n    catch (err) {\n        emitter.emit('error', {\n            code: 'unknown',\n            reason: err,\n        });\n    }\n};\n//# sourceMappingURL=dispatch-emit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/dispatch-emit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/emitter.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/app/emitter.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeEmitter: () => (/* binding */ NodeEmitter)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n\nclass NodeEmitter extends _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_0__.Emitter {\n}\n//# sourceMappingURL=emitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vYXBwL2VtaXR0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkQ7QUFDcEQsMEJBQTBCLHFFQUFPO0FBQ3hDO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1ub2RlXFxkaXN0XFxlc21cXGFwcFxcZW1pdHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFbWl0dGVyIH0gZnJvbSAnQHNlZ21lbnQvYW5hbHl0aWNzLWdlbmVyaWMtdXRpbHMnO1xuZXhwb3J0IGNsYXNzIE5vZGVFbWl0dGVyIGV4dGVuZHMgRW1pdHRlciB7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbWl0dGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/emitter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/event-factory.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/app/event-factory.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeEventFactory: () => (/* binding */ NodeEventFactory)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/events/index.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/assertions.js\");\n/* harmony import */ var _lib_get_message_id__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/get-message-id */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/get-message-id.js\");\n\n\nclass NodeEventFactory extends _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__.CoreEventFactory {\n    constructor() {\n        super({\n            createMessageId: _lib_get_message_id__WEBPACK_IMPORTED_MODULE_0__.createMessageId,\n            onFinishedEvent: (event) => {\n                (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_2__.assertUserIdentity)(event);\n            },\n        });\n    }\n}\n//# sourceMappingURL=event-factory.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vYXBwL2V2ZW50LWZhY3RvcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErRTtBQUN2QjtBQUNqRCwrQkFBK0IscUVBQWdCO0FBQ3REO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQSxnQkFBZ0IsMkVBQWtCO0FBQ2xDLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBzZWdtZW50XFxhbmFseXRpY3Mtbm9kZVxcZGlzdFxcZXNtXFxhcHBcXGV2ZW50LWZhY3RvcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYXNzZXJ0VXNlcklkZW50aXR5LCBDb3JlRXZlbnRGYWN0b3J5IH0gZnJvbSAnQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUnO1xuaW1wb3J0IHsgY3JlYXRlTWVzc2FnZUlkIH0gZnJvbSAnLi4vbGliL2dldC1tZXNzYWdlLWlkJztcbmV4cG9ydCBjbGFzcyBOb2RlRXZlbnRGYWN0b3J5IGV4dGVuZHMgQ29yZUV2ZW50RmFjdG9yeSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKHtcbiAgICAgICAgICAgIGNyZWF0ZU1lc3NhZ2VJZCxcbiAgICAgICAgICAgIG9uRmluaXNoZWRFdmVudDogKGV2ZW50KSA9PiB7XG4gICAgICAgICAgICAgICAgYXNzZXJ0VXNlcklkZW50aXR5KGV2ZW50KTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWV2ZW50LWZhY3RvcnkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/event-factory.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/event-queue.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/app/event-queue.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeEventQueue: () => (/* binding */ NodeEventQueue)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/queue/event-queue.js\");\n\nclass NodePriorityQueue extends _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__.PriorityQueue {\n    constructor() {\n        super(1, []);\n    }\n    // do not use an internal \"seen\" map\n    getAttempts(ctx) {\n        return ctx.attempts ?? 0;\n    }\n    updateAttempts(ctx) {\n        ctx.attempts = this.getAttempts(ctx) + 1;\n        return this.getAttempts(ctx);\n    }\n}\nclass NodeEventQueue extends _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__.CoreEventQueue {\n    constructor() {\n        super(new NodePriorityQueue());\n    }\n}\n//# sourceMappingURL=event-queue.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vYXBwL2V2ZW50LXF1ZXVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3RTtBQUN4RSxnQ0FBZ0Msa0VBQWE7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sNkJBQTZCLG1FQUFjO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1ub2RlXFxkaXN0XFxlc21cXGFwcFxcZXZlbnQtcXVldWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29yZUV2ZW50UXVldWUsIFByaW9yaXR5UXVldWUgfSBmcm9tICdAc2VnbWVudC9hbmFseXRpY3MtY29yZSc7XG5jbGFzcyBOb2RlUHJpb3JpdHlRdWV1ZSBleHRlbmRzIFByaW9yaXR5UXVldWUge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcigxLCBbXSk7XG4gICAgfVxuICAgIC8vIGRvIG5vdCB1c2UgYW4gaW50ZXJuYWwgXCJzZWVuXCIgbWFwXG4gICAgZ2V0QXR0ZW1wdHMoY3R4KSB7XG4gICAgICAgIHJldHVybiBjdHguYXR0ZW1wdHMgPz8gMDtcbiAgICB9XG4gICAgdXBkYXRlQXR0ZW1wdHMoY3R4KSB7XG4gICAgICAgIGN0eC5hdHRlbXB0cyA9IHRoaXMuZ2V0QXR0ZW1wdHMoY3R4KSArIDE7XG4gICAgICAgIHJldHVybiB0aGlzLmdldEF0dGVtcHRzKGN0eCk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIE5vZGVFdmVudFF1ZXVlIGV4dGVuZHMgQ29yZUV2ZW50UXVldWUge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcihuZXcgTm9kZVByaW9yaXR5UXVldWUoKSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXZlbnQtcXVldWUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/event-queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/settings.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/app/settings.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateSettings: () => (/* binding */ validateSettings)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/validation/errors.js\");\n\nconst validateSettings = (settings) => {\n    if (!settings.writeKey) {\n        throw new _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__.ValidationError('writeKey', 'writeKey is missing.');\n    }\n};\n//# sourceMappingURL=settings.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vYXBwL3NldHRpbmdzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBEO0FBQ25EO0FBQ1A7QUFDQSxrQkFBa0Isb0VBQWU7QUFDakM7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBzZWdtZW50XFxhbmFseXRpY3Mtbm9kZVxcZGlzdFxcZXNtXFxhcHBcXHNldHRpbmdzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFZhbGlkYXRpb25FcnJvciB9IGZyb20gJ0BzZWdtZW50L2FuYWx5dGljcy1jb3JlJztcbmV4cG9ydCBjb25zdCB2YWxpZGF0ZVNldHRpbmdzID0gKHNldHRpbmdzKSA9PiB7XG4gICAgaWYgKCFzZXR0aW5ncy53cml0ZUtleSkge1xuICAgICAgICB0aHJvdyBuZXcgVmFsaWRhdGlvbkVycm9yKCd3cml0ZUtleScsICd3cml0ZUtleSBpcyBtaXNzaW5nLicpO1xuICAgIH1cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZXR0aW5ncy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/settings.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/generated/version.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/generated/version.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n// This file is generated.\nconst version = '2.2.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vZ2VuZXJhdGVkL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAc2VnbWVudFxcYW5hbHl0aWNzLW5vZGVcXGRpc3RcXGVzbVxcZ2VuZXJhdGVkXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgZmlsZSBpcyBnZW5lcmF0ZWQuXG5leHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjIuMSc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/generated/version.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/index.common.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/index.common.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* reexport safe */ _app_analytics_node__WEBPACK_IMPORTED_MODULE_0__.Analytics),\n/* harmony export */   Context: () => (/* reexport safe */ _app_context__WEBPACK_IMPORTED_MODULE_1__.Context),\n/* harmony export */   FetchHTTPClient: () => (/* reexport safe */ _lib_http_client__WEBPACK_IMPORTED_MODULE_2__.FetchHTTPClient)\n/* harmony export */ });\n/* harmony import */ var _app_analytics_node__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/analytics-node */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/analytics-node.js\");\n/* harmony import */ var _app_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app/context */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/app/context.js\");\n/* harmony import */ var _lib_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/http-client */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/http-client.js\");\n\n\n\n//# sourceMappingURL=index.common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vaW5kZXguY29tbW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFpRDtBQUNUO0FBQ2E7QUFDckQiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1ub2RlXFxkaXN0XFxlc21cXGluZGV4LmNvbW1vbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBBbmFseXRpY3MgfSBmcm9tICcuL2FwcC9hbmFseXRpY3Mtbm9kZSc7XG5leHBvcnQgeyBDb250ZXh0IH0gZnJvbSAnLi9hcHAvY29udGV4dCc7XG5leHBvcnQgeyBGZXRjaEhUVFBDbGllbnQsIH0gZnJvbSAnLi9saWIvaHR0cC1jbGllbnQnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguY29tbW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/index.common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* reexport safe */ _index_common__WEBPACK_IMPORTED_MODULE_0__.Analytics),\n/* harmony export */   Context: () => (/* reexport safe */ _index_common__WEBPACK_IMPORTED_MODULE_0__.Context),\n/* harmony export */   FetchHTTPClient: () => (/* reexport safe */ _index_common__WEBPACK_IMPORTED_MODULE_0__.FetchHTTPClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.common */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/index.common.js\");\n\n// export Analytics as both a named export and a default export (for backwards-compat. reasons)\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_index_common__WEBPACK_IMPORTED_MODULE_0__.Analytics);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0I7QUFDL0I7QUFDMkM7QUFDM0MsaUVBQWUsb0RBQVMsRUFBQztBQUN6QiIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAc2VnbWVudFxcYW5hbHl0aWNzLW5vZGVcXGRpc3RcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9pbmRleC5jb21tb24nO1xuLy8gZXhwb3J0IEFuYWx5dGljcyBhcyBib3RoIGEgbmFtZWQgZXhwb3J0IGFuZCBhIGRlZmF1bHQgZXhwb3J0IChmb3IgYmFja3dhcmRzLWNvbXBhdC4gcmVhc29ucylcbmltcG9ydCB7IEFuYWx5dGljcyB9IGZyb20gJy4vaW5kZXguY29tbW9uJztcbmV4cG9ydCBkZWZhdWx0IEFuYWx5dGljcztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/abort.js":
/*!********************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/lib/abort.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortController: () => (/* binding */ AbortController),\n/* harmony export */   AbortSignal: () => (/* binding */ AbortSignal),\n/* harmony export */   abortSignalAfterTimeout: () => (/* binding */ abortSignalAfterTimeout)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./env */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/env.js\");\n/**\n * use non-native event emitter for the benefit of non-node runtimes like CF workers.\n */\n\n\n/**\n * adapted from: https://www.npmjs.com/package/node-abort-controller\n */\nclass AbortSignal {\n    onabort = null;\n    aborted = false;\n    eventEmitter = new _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_1__.Emitter();\n    toString() {\n        return '[object AbortSignal]';\n    }\n    get [Symbol.toStringTag]() {\n        return 'AbortSignal';\n    }\n    removeEventListener(...args) {\n        this.eventEmitter.off(...args);\n    }\n    addEventListener(...args) {\n        this.eventEmitter.on(...args);\n    }\n    dispatchEvent(type) {\n        const event = { type, target: this };\n        const handlerName = `on${type}`;\n        if (typeof this[handlerName] === 'function') {\n            ;\n            this[handlerName](event);\n        }\n        this.eventEmitter.emit(type, event);\n    }\n}\n/**\n * This polyfill is only neccessary to support versions of node < 14.17.\n * Can be removed once node 14 support is dropped.\n */\nclass AbortController {\n    signal = new AbortSignal();\n    abort() {\n        if (this.signal.aborted)\n            return;\n        this.signal.aborted = true;\n        this.signal.dispatchEvent('abort');\n    }\n    toString() {\n        return '[object AbortController]';\n    }\n    get [Symbol.toStringTag]() {\n        return 'AbortController';\n    }\n}\n/**\n * @param timeoutMs - Set a request timeout, after which the request is cancelled.\n */\nconst abortSignalAfterTimeout = (timeoutMs) => {\n    if ((0,_env__WEBPACK_IMPORTED_MODULE_0__.detectRuntime)() === 'cloudflare-worker') {\n        return []; // TODO: this is broken in cloudflare workers, otherwise results in \"A hanging Promise was canceled...\" error.\n    }\n    const ac = new (globalThis.AbortController || AbortController)();\n    const timeoutId = setTimeout(() => {\n        ac.abort();\n    }, timeoutMs);\n    // Allow Node.js processes to exit early if only the timeout is running\n    timeoutId?.unref?.();\n    return [ac.signal, timeoutId];\n};\n//# sourceMappingURL=abort.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/abort.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/create-url.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/lib/create-url.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tryCreateFormattedUrl: () => (/* binding */ tryCreateFormattedUrl)\n/* harmony export */ });\nconst stripTrailingSlash = (str) => str.replace(/\\/$/, '');\n/**\n *\n * @param host e.g. \"http://foo.com\"\n * @param path e.g. \"/bar\"\n * @returns \"e.g.\" \"http://foo.com/bar\"\n */\nconst tryCreateFormattedUrl = (host, path) => {\n    return stripTrailingSlash(new URL(path || '', host).href);\n};\n//# sourceMappingURL=create-url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vbGliL2NyZWF0ZS11cmwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1ub2RlXFxkaXN0XFxlc21cXGxpYlxcY3JlYXRlLXVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzdHJpcFRyYWlsaW5nU2xhc2ggPSAoc3RyKSA9PiBzdHIucmVwbGFjZSgvXFwvJC8sICcnKTtcbi8qKlxuICpcbiAqIEBwYXJhbSBob3N0IGUuZy4gXCJodHRwOi8vZm9vLmNvbVwiXG4gKiBAcGFyYW0gcGF0aCBlLmcuIFwiL2JhclwiXG4gKiBAcmV0dXJucyBcImUuZy5cIiBcImh0dHA6Ly9mb28uY29tL2JhclwiXG4gKi9cbmV4cG9ydCBjb25zdCB0cnlDcmVhdGVGb3JtYXR0ZWRVcmwgPSAoaG9zdCwgcGF0aCkgPT4ge1xuICAgIHJldHVybiBzdHJpcFRyYWlsaW5nU2xhc2gobmV3IFVSTChwYXRoIHx8ICcnLCBob3N0KS5ocmVmKTtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcmVhdGUtdXJsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/create-url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/env.js":
/*!******************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/lib/env.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   detectRuntime: () => (/* binding */ detectRuntime)\n/* harmony export */ });\nconst detectRuntime = () => {\n    if (typeof process === 'object' &&\n        process &&\n        typeof process.env === 'object' &&\n        process.env &&\n        typeof process.version === 'string') {\n        return 'node';\n    }\n    if (typeof window === 'object') {\n        return 'browser';\n    }\n    // @ts-ignore\n    if (typeof WebSocketPair !== 'undefined') {\n        return 'cloudflare-worker';\n    }\n    // @ts-ignore\n    if (typeof EdgeRuntime === 'string') {\n        return 'vercel-edge';\n    }\n    if (\n    // @ts-ignore\n    typeof WorkerGlobalScope !== 'undefined' &&\n        // @ts-ignore\n        typeof importScripts === 'function') {\n        return 'web-worker';\n    }\n    return 'unknown';\n};\n//# sourceMappingURL=env.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vbGliL2Vudi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAc2VnbWVudFxcYW5hbHl0aWNzLW5vZGVcXGRpc3RcXGVzbVxcbGliXFxlbnYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGRldGVjdFJ1bnRpbWUgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiBwcm9jZXNzID09PSAnb2JqZWN0JyAmJlxuICAgICAgICBwcm9jZXNzICYmXG4gICAgICAgIHR5cGVvZiBwcm9jZXNzLmVudiA9PT0gJ29iamVjdCcgJiZcbiAgICAgICAgcHJvY2Vzcy5lbnYgJiZcbiAgICAgICAgdHlwZW9mIHByb2Nlc3MudmVyc2lvbiA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuICdub2RlJztcbiAgICB9XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICdvYmplY3QnKSB7XG4gICAgICAgIHJldHVybiAnYnJvd3Nlcic7XG4gICAgfVxuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBpZiAodHlwZW9mIFdlYlNvY2tldFBhaXIgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHJldHVybiAnY2xvdWRmbGFyZS13b3JrZXInO1xuICAgIH1cbiAgICAvLyBAdHMtaWdub3JlXG4gICAgaWYgKHR5cGVvZiBFZGdlUnVudGltZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuICd2ZXJjZWwtZWRnZSc7XG4gICAgfVxuICAgIGlmIChcbiAgICAvLyBAdHMtaWdub3JlXG4gICAgdHlwZW9mIFdvcmtlckdsb2JhbFNjb3BlICE9PSAndW5kZWZpbmVkJyAmJlxuICAgICAgICAvLyBAdHMtaWdub3JlXG4gICAgICAgIHR5cGVvZiBpbXBvcnRTY3JpcHRzID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHJldHVybiAnd2ViLXdvcmtlcic7XG4gICAgfVxuICAgIHJldHVybiAndW5rbm93bic7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW52LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/fetch.js":
/*!********************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/lib/fetch.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetch: () => (/* binding */ fetch)\n/* harmony export */ });\nconst fetch = async (...args) => {\n    if (globalThis.fetch) {\n        return globalThis.fetch(...args);\n    }\n    // This guard causes is important, as it causes dead-code elimination to be enabled inside this block.\n    // @ts-ignore\n    else if (typeof EdgeRuntime !== 'string') {\n        return (await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! node-fetch */ \"(rsc)/./node_modules/node-fetch/lib/index.mjs\"))).default(...args);\n    }\n    else {\n        throw new Error('Invariant: an edge runtime that does not support fetch should not exist');\n    }\n};\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vbGliL2ZldGNoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQix1SkFBb0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBzZWdtZW50XFxhbmFseXRpY3Mtbm9kZVxcZGlzdFxcZXNtXFxsaWJcXGZldGNoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBmZXRjaCA9IGFzeW5jICguLi5hcmdzKSA9PiB7XG4gICAgaWYgKGdsb2JhbFRoaXMuZmV0Y2gpIHtcbiAgICAgICAgcmV0dXJuIGdsb2JhbFRoaXMuZmV0Y2goLi4uYXJncyk7XG4gICAgfVxuICAgIC8vIFRoaXMgZ3VhcmQgY2F1c2VzIGlzIGltcG9ydGFudCwgYXMgaXQgY2F1c2VzIGRlYWQtY29kZSBlbGltaW5hdGlvbiB0byBiZSBlbmFibGVkIGluc2lkZSB0aGlzIGJsb2NrLlxuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBlbHNlIGlmICh0eXBlb2YgRWRnZVJ1bnRpbWUgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHJldHVybiAoYXdhaXQgaW1wb3J0KCdub2RlLWZldGNoJykpLmRlZmF1bHQoLi4uYXJncyk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFyaWFudDogYW4gZWRnZSBydW50aW1lIHRoYXQgZG9lcyBub3Qgc3VwcG9ydCBmZXRjaCBzaG91bGQgbm90IGV4aXN0Jyk7XG4gICAgfVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZldGNoLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/get-message-id.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/lib/get-message-id.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMessageId: () => (/* binding */ createMessageId)\n/* harmony export */ });\n/* harmony import */ var _uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uuid */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/uuid.js\");\n\n/**\n * get a unique messageId with a very low chance of collisions\n * using @lukeed/uuid/secure uses the node crypto module, which is the fastest\n * @example \"node-next-1668208232027-743be593-7789-4b74-8078-cbcc8894c586\"\n */\nconst createMessageId = () => {\n    return `node-next-${Date.now()}-${(0,_uuid__WEBPACK_IMPORTED_MODULE_0__.uuid)()}`;\n};\n//# sourceMappingURL=get-message-id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vbGliL2dldC1tZXNzYWdlLWlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLHdCQUF3QixXQUFXLEdBQUcsMkNBQUksR0FBRztBQUM3QztBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBzZWdtZW50XFxhbmFseXRpY3Mtbm9kZVxcZGlzdFxcZXNtXFxsaWJcXGdldC1tZXNzYWdlLWlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHV1aWQgfSBmcm9tICcuL3V1aWQnO1xuLyoqXG4gKiBnZXQgYSB1bmlxdWUgbWVzc2FnZUlkIHdpdGggYSB2ZXJ5IGxvdyBjaGFuY2Ugb2YgY29sbGlzaW9uc1xuICogdXNpbmcgQGx1a2VlZC91dWlkL3NlY3VyZSB1c2VzIHRoZSBub2RlIGNyeXB0byBtb2R1bGUsIHdoaWNoIGlzIHRoZSBmYXN0ZXN0XG4gKiBAZXhhbXBsZSBcIm5vZGUtbmV4dC0xNjY4MjA4MjMyMDI3LTc0M2JlNTkzLTc3ODktNGI3NC04MDc4LWNiY2M4ODk0YzU4NlwiXG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVNZXNzYWdlSWQgPSAoKSA9PiB7XG4gICAgcmV0dXJuIGBub2RlLW5leHQtJHtEYXRlLm5vdygpfS0ke3V1aWQoKX1gO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldC1tZXNzYWdlLWlkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/get-message-id.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/http-client.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/lib/http-client.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FetchHTTPClient: () => (/* binding */ FetchHTTPClient)\n/* harmony export */ });\n/* harmony import */ var _abort__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abort */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/abort.js\");\n/* harmony import */ var _fetch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fetch */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/fetch.js\");\n\n\n/**\n * Default HTTP client implementation using fetch\n */\nclass FetchHTTPClient {\n    _fetch;\n    constructor(fetchFn) {\n        this._fetch = fetchFn ?? _fetch__WEBPACK_IMPORTED_MODULE_1__.fetch;\n    }\n    async makeRequest(options) {\n        const [signal, timeoutId] = (0,_abort__WEBPACK_IMPORTED_MODULE_0__.abortSignalAfterTimeout)(options.httpRequestTimeout);\n        const requestInit = {\n            url: options.url,\n            method: options.method,\n            headers: options.headers,\n            body: options.body,\n            signal: signal,\n        };\n        return this._fetch(options.url, requestInit).finally(() => clearTimeout(timeoutId));\n    }\n}\n//# sourceMappingURL=http-client.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vbGliL2h0dHAtY2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNGO0FBQ2hEO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLGlDQUFpQyx5Q0FBWTtBQUM3QztBQUNBO0FBQ0Esb0NBQW9DLCtEQUF1QjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBzZWdtZW50XFxhbmFseXRpY3Mtbm9kZVxcZGlzdFxcZXNtXFxsaWJcXGh0dHAtY2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFib3J0U2lnbmFsQWZ0ZXJUaW1lb3V0IH0gZnJvbSAnLi9hYm9ydCc7XG5pbXBvcnQgeyBmZXRjaCBhcyBkZWZhdWx0RmV0Y2ggfSBmcm9tICcuL2ZldGNoJztcbi8qKlxuICogRGVmYXVsdCBIVFRQIGNsaWVudCBpbXBsZW1lbnRhdGlvbiB1c2luZyBmZXRjaFxuICovXG5leHBvcnQgY2xhc3MgRmV0Y2hIVFRQQ2xpZW50IHtcbiAgICBfZmV0Y2g7XG4gICAgY29uc3RydWN0b3IoZmV0Y2hGbikge1xuICAgICAgICB0aGlzLl9mZXRjaCA9IGZldGNoRm4gPz8gZGVmYXVsdEZldGNoO1xuICAgIH1cbiAgICBhc3luYyBtYWtlUmVxdWVzdChvcHRpb25zKSB7XG4gICAgICAgIGNvbnN0IFtzaWduYWwsIHRpbWVvdXRJZF0gPSBhYm9ydFNpZ25hbEFmdGVyVGltZW91dChvcHRpb25zLmh0dHBSZXF1ZXN0VGltZW91dCk7XG4gICAgICAgIGNvbnN0IHJlcXVlc3RJbml0ID0ge1xuICAgICAgICAgICAgdXJsOiBvcHRpb25zLnVybCxcbiAgICAgICAgICAgIG1ldGhvZDogb3B0aW9ucy5tZXRob2QsXG4gICAgICAgICAgICBoZWFkZXJzOiBvcHRpb25zLmhlYWRlcnMsXG4gICAgICAgICAgICBib2R5OiBvcHRpb25zLmJvZHksXG4gICAgICAgICAgICBzaWduYWw6IHNpZ25hbCxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2ZldGNoKG9wdGlvbnMudXJsLCByZXF1ZXN0SW5pdCkuZmluYWxseSgoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aHR0cC1jbGllbnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/http-client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/token-manager.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/lib/token-manager.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager)\n/* harmony export */ });\n/* harmony import */ var _uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uuid */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/uuid.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/key/import.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jwt/sign.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/callback/index.js\");\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n\n\n\n\nconst isAccessToken = (thing) => {\n    return Boolean(thing &&\n        typeof thing === 'object' &&\n        'access_token' in thing &&\n        'expires_in' in thing &&\n        typeof thing.access_token === 'string' &&\n        typeof thing.expires_in === 'number');\n};\nconst isValidCustomResponse = (response) => {\n    return typeof response.text === 'function';\n};\nfunction convertHeaders(headers) {\n    const lowercaseHeaders = {};\n    if (!headers)\n        return {};\n    if (isHeaders(headers)) {\n        for (const [name, value] of headers.entries()) {\n            lowercaseHeaders[name.toLowerCase()] = value;\n        }\n        return lowercaseHeaders;\n    }\n    for (const [name, value] of Object.entries(headers)) {\n        lowercaseHeaders[name.toLowerCase()] = value;\n    }\n    return lowercaseHeaders;\n}\nfunction isHeaders(thing) {\n    if (typeof thing === 'object' &&\n        thing !== null &&\n        'entries' in Object(thing) &&\n        typeof Object(thing).entries === 'function') {\n        return true;\n    }\n    return false;\n}\nclass TokenManager {\n    alg = 'RS256';\n    grantType = 'client_credentials';\n    clientAssertionType = 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer';\n    clientId;\n    clientKey;\n    keyId;\n    scope;\n    authServer;\n    httpClient;\n    maxRetries;\n    clockSkewInSeconds = 0;\n    accessToken;\n    tokenEmitter = new _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_1__.Emitter();\n    retryCount;\n    pollerTimer;\n    constructor(props) {\n        this.keyId = props.keyId;\n        this.clientId = props.clientId;\n        this.clientKey = props.clientKey;\n        this.authServer = props.authServer ?? 'https://oauth2.segment.io';\n        this.scope = props.scope ?? 'tracking_api:write';\n        this.httpClient = props.httpClient;\n        this.maxRetries = props.maxRetries;\n        this.tokenEmitter.on('access_token', (event) => {\n            if ('token' in event) {\n                this.accessToken = event.token;\n            }\n        });\n        this.retryCount = 0;\n    }\n    stopPoller() {\n        clearTimeout(this.pollerTimer);\n    }\n    async pollerLoop() {\n        let timeUntilRefreshInMs = 25;\n        let response;\n        try {\n            response = await this.requestAccessToken();\n        }\n        catch (err) {\n            // Error without a status code - likely networking, retry\n            return this.handleTransientError({ error: err });\n        }\n        if (!isValidCustomResponse(response)) {\n            return this.handleInvalidCustomResponse();\n        }\n        const headers = convertHeaders(response.headers);\n        if (headers['date']) {\n            this.updateClockSkew(Date.parse(headers['date']));\n        }\n        // Handle status codes!\n        if (response.status === 200) {\n            try {\n                const body = await response.text();\n                const token = JSON.parse(body);\n                if (!isAccessToken(token)) {\n                    throw new Error('Response did not contain a valid access_token and expires_in');\n                }\n                // Success, we have a token!\n                token.expires_at = Math.round(Date.now() / 1000) + token.expires_in;\n                this.tokenEmitter.emit('access_token', { token });\n                // Reset our failure count\n                this.retryCount = 0;\n                // Refresh the token after half the expiry time passes\n                timeUntilRefreshInMs = (token.expires_in / 2) * 1000;\n                return this.queueNextPoll(timeUntilRefreshInMs);\n            }\n            catch (err) {\n                // Something went really wrong with the body, lets surface an error and try again?\n                return this.handleTransientError({ error: err, forceEmitError: true });\n            }\n        }\n        else if (response.status === 429) {\n            // Rate limited, wait for the reset time\n            return await this.handleRateLimited(response, headers, timeUntilRefreshInMs);\n        }\n        else if ([400, 401, 415].includes(response.status)) {\n            // Unrecoverable errors, stops the poller\n            return this.handleUnrecoverableErrors(response);\n        }\n        else {\n            return this.handleTransientError({\n                error: new Error(`[${response.status}] ${response.statusText}`),\n            });\n        }\n    }\n    handleTransientError({ error, forceEmitError, }) {\n        this.incrementRetries({ error, forceEmitError });\n        const timeUntilRefreshInMs = (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_2__.backoff)({\n            attempt: this.retryCount,\n            minTimeout: 25,\n            maxTimeout: 1000,\n        });\n        this.queueNextPoll(timeUntilRefreshInMs);\n    }\n    handleInvalidCustomResponse() {\n        this.tokenEmitter.emit('access_token', {\n            error: new Error('HTTPClient does not implement response.text method'),\n        });\n    }\n    async handleRateLimited(response, headers, timeUntilRefreshInMs) {\n        this.incrementRetries({\n            error: new Error(`[${response.status}] ${response.statusText}`),\n        });\n        if (headers['x-ratelimit-reset']) {\n            const rateLimitResetTimestamp = parseInt(headers['x-ratelimit-reset'], 10);\n            if (isFinite(rateLimitResetTimestamp)) {\n                timeUntilRefreshInMs =\n                    rateLimitResetTimestamp - Date.now() + this.clockSkewInSeconds * 1000;\n            }\n            else {\n                timeUntilRefreshInMs = 5 * 1000;\n            }\n            // We want subsequent calls to get_token to be able to interrupt our\n            //  Timeout when it's waiting for e.g. a long normal expiration, but\n            //  not when we're waiting for a rate limit reset. Sleep instead.\n            await (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_3__.sleep)(timeUntilRefreshInMs);\n            timeUntilRefreshInMs = 0;\n        }\n        this.queueNextPoll(timeUntilRefreshInMs);\n    }\n    handleUnrecoverableErrors(response) {\n        this.retryCount = 0;\n        this.tokenEmitter.emit('access_token', {\n            error: new Error(`[${response.status}] ${response.statusText}`),\n        });\n        this.stopPoller();\n    }\n    updateClockSkew(dateInMs) {\n        this.clockSkewInSeconds = (Date.now() - dateInMs) / 1000;\n    }\n    incrementRetries({ error, forceEmitError, }) {\n        this.retryCount++;\n        if (forceEmitError || this.retryCount % this.maxRetries === 0) {\n            this.retryCount = 0;\n            this.tokenEmitter.emit('access_token', { error: error });\n        }\n    }\n    queueNextPoll(timeUntilRefreshInMs) {\n        this.pollerTimer = setTimeout(() => this.pollerLoop(), timeUntilRefreshInMs);\n        if (this.pollerTimer.unref) {\n            this.pollerTimer.unref();\n        }\n    }\n    /**\n     * Solely responsible for building the HTTP request and calling the token service.\n     */\n    async requestAccessToken() {\n        // Set issued at time to 5 seconds in the past to account for clock skew\n        const ISSUED_AT_BUFFER_IN_SECONDS = 5;\n        const MAX_EXPIRY_IN_SECONDS = 60;\n        // Final expiry time takes into account the issued at time, so need to subtract IAT buffer\n        const EXPIRY_IN_SECONDS = MAX_EXPIRY_IN_SECONDS - ISSUED_AT_BUFFER_IN_SECONDS;\n        const jti = (0,_uuid__WEBPACK_IMPORTED_MODULE_0__.uuid)();\n        const currentUTCInSeconds = Math.round(Date.now() / 1000) - this.clockSkewInSeconds;\n        const jwtBody = {\n            iss: this.clientId,\n            sub: this.clientId,\n            aud: this.authServer,\n            iat: currentUTCInSeconds - ISSUED_AT_BUFFER_IN_SECONDS,\n            exp: currentUTCInSeconds + EXPIRY_IN_SECONDS,\n            jti,\n        };\n        const key = await (0,jose__WEBPACK_IMPORTED_MODULE_4__.importPKCS8)(this.clientKey, 'RS256');\n        const signedJwt = await new jose__WEBPACK_IMPORTED_MODULE_5__.SignJWT(jwtBody)\n            .setProtectedHeader({ alg: this.alg, kid: this.keyId, typ: 'JWT' })\n            .sign(key);\n        const requestBody = `grant_type=${this.grantType}&client_assertion_type=${this.clientAssertionType}&client_assertion=${signedJwt}&scope=${this.scope}`;\n        const accessTokenEndpoint = `${this.authServer}/token`;\n        const requestOptions = {\n            method: 'POST',\n            url: accessTokenEndpoint,\n            body: requestBody,\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded',\n            },\n            httpRequestTimeout: 10000,\n        };\n        return this.httpClient.makeRequest(requestOptions);\n    }\n    async getAccessToken() {\n        // Use the cached token if it is still valid, otherwise wait for a new token.\n        if (this.isValidToken(this.accessToken)) {\n            return this.accessToken;\n        }\n        // stop poller first in order to make sure that it's not sleeping if we need a token immediately\n        // Otherwise it could be hours before the expiration time passes normally\n        this.stopPoller();\n        // startPoller needs to be called somewhere, either lazily when a token is first requested, or at instantiation.\n        // Doing it lazily currently\n        this.pollerLoop().catch(() => { });\n        return new Promise((resolve, reject) => {\n            this.tokenEmitter.once('access_token', (event) => {\n                if ('token' in event) {\n                    resolve(event.token);\n                }\n                else {\n                    reject(event.error);\n                }\n            });\n        });\n    }\n    clearToken() {\n        this.accessToken = undefined;\n    }\n    isValidToken(token) {\n        return (typeof token !== 'undefined' &&\n            token !== null &&\n            token.expires_in < Date.now() / 1000);\n    }\n}\n//# sourceMappingURL=token-manager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/token-manager.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/uuid.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/lib/uuid.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uuid: () => (/* reexport safe */ _lukeed_uuid__WEBPACK_IMPORTED_MODULE_0__.v4)\n/* harmony export */ });\n/* harmony import */ var _lukeed_uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lukeed/uuid */ \"(rsc)/./node_modules/@lukeed/uuid/dist/index.mjs\");\n\n//# sourceMappingURL=uuid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vbGliL3V1aWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEM7QUFDMUMiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQHNlZ21lbnRcXGFuYWx5dGljcy1ub2RlXFxkaXN0XFxlc21cXGxpYlxcdXVpZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyB2NCBhcyB1dWlkIH0gZnJvbSAnQGx1a2VlZC91dWlkJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV1aWQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/uuid.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/context-batch.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/context-batch.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextBatch: () => (/* binding */ ContextBatch)\n/* harmony export */ });\n/* harmony import */ var _lib_uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/uuid */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/uuid.js\");\n\nconst MAX_EVENT_SIZE_IN_KB = 32;\nconst MAX_BATCH_SIZE_IN_KB = 480; //  (500 KB is the limit, leaving some padding)\nclass ContextBatch {\n    id = (0,_lib_uuid__WEBPACK_IMPORTED_MODULE_0__.uuid)();\n    items = [];\n    sizeInBytes = 0;\n    maxEventCount;\n    constructor(maxEventCount) {\n        this.maxEventCount = Math.max(1, maxEventCount);\n    }\n    tryAdd(item) {\n        if (this.length === this.maxEventCount)\n            return {\n                success: false,\n                message: `Event limit of ${this.maxEventCount} has been exceeded.`,\n            };\n        const eventSize = this.calculateSize(item.context);\n        if (eventSize > MAX_EVENT_SIZE_IN_KB * 1024) {\n            return {\n                success: false,\n                message: `Event exceeds maximum event size of ${MAX_EVENT_SIZE_IN_KB} KB`,\n            };\n        }\n        if (this.sizeInBytes + eventSize > MAX_BATCH_SIZE_IN_KB * 1024) {\n            return {\n                success: false,\n                message: `Event has caused batch size to exceed ${MAX_BATCH_SIZE_IN_KB} KB`,\n            };\n        }\n        this.items.push(item);\n        this.sizeInBytes += eventSize;\n        return { success: true };\n    }\n    get length() {\n        return this.items.length;\n    }\n    calculateSize(ctx) {\n        return encodeURI(JSON.stringify(ctx.event)).split(/%..|i/).length;\n    }\n    getEvents() {\n        const events = this.items.map(({ context }) => context.event);\n        return events;\n    }\n    getContexts() {\n        return this.items.map((item) => item.context);\n    }\n    resolveEvents() {\n        this.items.forEach(({ resolver, context }) => resolver(context));\n    }\n}\n//# sourceMappingURL=context-batch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/context-batch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfiguredNodePlugin: () => (/* binding */ createConfiguredNodePlugin),\n/* harmony export */   createNodePlugin: () => (/* binding */ createNodePlugin)\n/* harmony export */ });\n/* harmony import */ var _publisher__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./publisher */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/publisher.js\");\n/* harmony import */ var _generated_version__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../generated/version */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/generated/version.js\");\n/* harmony import */ var _lib_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/env */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/env.js\");\n\n\n\nfunction normalizeEvent(ctx) {\n    ctx.updateEvent('context.library.name', '@segment/analytics-node');\n    ctx.updateEvent('context.library.version', _generated_version__WEBPACK_IMPORTED_MODULE_1__.version);\n    const runtime = (0,_lib_env__WEBPACK_IMPORTED_MODULE_2__.detectRuntime)();\n    if (runtime === 'node') {\n        // eslint-disable-next-line no-restricted-globals\n        ctx.updateEvent('_metadata.nodeVersion', process.version);\n    }\n    ctx.updateEvent('_metadata.jsRuntime', runtime);\n}\nfunction createNodePlugin(publisher) {\n    function action(ctx) {\n        normalizeEvent(ctx);\n        return publisher.enqueue(ctx);\n    }\n    return {\n        name: 'Segment.io',\n        type: 'destination',\n        version: '1.0.0',\n        isLoaded: () => true,\n        load: () => Promise.resolve(),\n        alias: action,\n        group: action,\n        identify: action,\n        page: action,\n        screen: action,\n        track: action,\n    };\n}\nconst createConfiguredNodePlugin = (props, emitter) => {\n    const publisher = new _publisher__WEBPACK_IMPORTED_MODULE_0__.Publisher(props, emitter);\n    return {\n        publisher: publisher,\n        plugin: createNodePlugin(publisher),\n    };\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/publisher.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/publisher.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Publisher: () => (/* binding */ Publisher)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js\");\n/* harmony import */ var _lib_create_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/create-url */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/create-url.js\");\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/@segment/analytics-generic-utils/dist/esm/create-deferred/create-deferred.js\");\n/* harmony import */ var _context_batch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context-batch */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/context-batch.js\");\n/* harmony import */ var _lib_token_manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/token-manager */ \"(rsc)/./node_modules/@segment/analytics-node/dist/esm/lib/token-manager.js\");\n\n\n\n\n\nfunction sleep(timeoutInMs) {\n    return new Promise((resolve) => setTimeout(resolve, timeoutInMs));\n}\nfunction noop() { }\n/**\n * The Publisher is responsible for batching events and sending them to the Segment API.\n */\nclass Publisher {\n    pendingFlushTimeout;\n    _batch;\n    _flushInterval;\n    _flushAt;\n    _maxRetries;\n    _url;\n    _flushPendingItemsCount;\n    _httpRequestTimeout;\n    _emitter;\n    _disable;\n    _httpClient;\n    _writeKey;\n    _tokenManager;\n    constructor({ host, path, maxRetries, flushAt, flushInterval, writeKey, httpRequestTimeout, httpClient, disable, oauthSettings, }, emitter) {\n        this._emitter = emitter;\n        this._maxRetries = maxRetries;\n        this._flushAt = Math.max(flushAt, 1);\n        this._flushInterval = flushInterval;\n        this._url = (0,_lib_create_url__WEBPACK_IMPORTED_MODULE_0__.tryCreateFormattedUrl)(host ?? 'https://api.segment.io', path ?? '/v1/batch');\n        this._httpRequestTimeout = httpRequestTimeout ?? 10000;\n        this._disable = Boolean(disable);\n        this._httpClient = httpClient;\n        this._writeKey = writeKey;\n        if (oauthSettings) {\n            this._tokenManager = new _lib_token_manager__WEBPACK_IMPORTED_MODULE_2__.TokenManager({\n                ...oauthSettings,\n                httpClient: oauthSettings.httpClient ?? httpClient,\n                maxRetries: oauthSettings.maxRetries ?? maxRetries,\n            });\n        }\n    }\n    createBatch() {\n        this.pendingFlushTimeout && clearTimeout(this.pendingFlushTimeout);\n        const batch = new _context_batch__WEBPACK_IMPORTED_MODULE_1__.ContextBatch(this._flushAt);\n        this._batch = batch;\n        this.pendingFlushTimeout = setTimeout(() => {\n            if (batch === this._batch) {\n                this._batch = undefined;\n            }\n            this.pendingFlushTimeout = undefined;\n            if (batch.length) {\n                this.send(batch).catch(noop);\n            }\n        }, this._flushInterval);\n        return batch;\n    }\n    clearBatch() {\n        this.pendingFlushTimeout && clearTimeout(this.pendingFlushTimeout);\n        this._batch = undefined;\n    }\n    flush(pendingItemsCount) {\n        if (!pendingItemsCount) {\n            // if number of pending items is 0, there will never be anything else entering the batch, since the app is closed.\n            if (this._tokenManager) {\n                this._tokenManager.stopPoller();\n            }\n            return;\n        }\n        this._flushPendingItemsCount = pendingItemsCount;\n        // if batch is empty, there's nothing to flush, and when things come in, enqueue will handle them.\n        if (!this._batch)\n            return;\n        // the number of globally pending items will always be larger or the same as batch size.\n        // Any mismatch is because some globally pending items are in plugins.\n        const isExpectingNoMoreItems = this._batch.length === pendingItemsCount;\n        if (isExpectingNoMoreItems) {\n            this.send(this._batch)\n                .catch(noop)\n                .finally(() => {\n                // stop poller so program can exit ().\n                if (this._tokenManager) {\n                    this._tokenManager.stopPoller();\n                }\n            });\n            this.clearBatch();\n        }\n    }\n    /**\n     * Enqueues the context for future delivery.\n     * @param ctx - Context containing a Segment event.\n     * @returns a promise that resolves with the context after the event has been delivered.\n     */\n    enqueue(ctx) {\n        const batch = this._batch ?? this.createBatch();\n        const { promise: ctxPromise, resolve } = (0,_segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_3__.createDeferred)();\n        const pendingItem = {\n            context: ctx,\n            resolver: resolve,\n        };\n        /*\n          The following logic ensures that a batch is never orphaned,\n          and is always sent before a new batch is created.\n    \n          Add an event to the existing batch.\n            Success: Check if batch is full or no more items are expected to come in (i.e. closing). If so, send batch.\n            Failure: Assume event is too big to fit in current batch - send existing batch.\n              Add an event to the new batch.\n                Success: Check if batch is full and send if it is.\n                Failure: Event exceeds maximum size (it will never fit), fail the event.\n        */\n        const addStatus = batch.tryAdd(pendingItem);\n        if (addStatus.success) {\n            const isExpectingNoMoreItems = batch.length === this._flushPendingItemsCount;\n            const isFull = batch.length === this._flushAt;\n            if (isFull || isExpectingNoMoreItems) {\n                this.send(batch).catch(noop);\n                this.clearBatch();\n            }\n            return ctxPromise;\n        }\n        // If the new item causes the maximimum event size to be exceeded, send the current batch and create a new one.\n        if (batch.length) {\n            this.send(batch).catch(noop);\n            this.clearBatch();\n        }\n        const fallbackBatch = this.createBatch();\n        const fbAddStatus = fallbackBatch.tryAdd(pendingItem);\n        if (fbAddStatus.success) {\n            const isExpectingNoMoreItems = fallbackBatch.length === this._flushPendingItemsCount;\n            if (isExpectingNoMoreItems) {\n                this.send(fallbackBatch).catch(noop);\n                this.clearBatch();\n            }\n            return ctxPromise;\n        }\n        else {\n            // this should only occur if max event size is exceeded\n            ctx.setFailedDelivery({\n                reason: new Error(fbAddStatus.message),\n            });\n            return Promise.resolve(ctx);\n        }\n    }\n    async send(batch) {\n        if (this._flushPendingItemsCount) {\n            this._flushPendingItemsCount -= batch.length;\n        }\n        const events = batch.getEvents();\n        const maxAttempts = this._maxRetries + 1;\n        let currentAttempt = 0;\n        while (currentAttempt < maxAttempts) {\n            currentAttempt++;\n            let requestedRetryTimeout;\n            let failureReason;\n            try {\n                if (this._disable) {\n                    return batch.resolveEvents();\n                }\n                let authString = undefined;\n                if (this._tokenManager) {\n                    const token = await this._tokenManager.getAccessToken();\n                    if (token && token.access_token) {\n                        authString = `Bearer ${token.access_token}`;\n                    }\n                }\n                const headers = {\n                    'Content-Type': 'application/json',\n                    'User-Agent': 'analytics-node-next/latest',\n                    ...(authString ? { Authorization: authString } : {}),\n                };\n                const request = {\n                    url: this._url,\n                    method: 'POST',\n                    headers: headers,\n                    body: JSON.stringify({\n                        batch: events,\n                        writeKey: this._writeKey,\n                        sentAt: new Date(),\n                    }),\n                    httpRequestTimeout: this._httpRequestTimeout,\n                };\n                this._emitter.emit('http_request', {\n                    body: request.body,\n                    method: request.method,\n                    url: request.url,\n                    headers: request.headers,\n                });\n                const response = await this._httpClient.makeRequest(request);\n                if (response.status >= 200 && response.status < 300) {\n                    // Successfully sent events, so exit!\n                    batch.resolveEvents();\n                    return;\n                }\n                else if (this._tokenManager &&\n                    (response.status === 400 ||\n                        response.status === 401 ||\n                        response.status === 403)) {\n                    // Retry with a new OAuth token if we have OAuth data\n                    this._tokenManager.clearToken();\n                    failureReason = new Error(`[${response.status}] ${response.statusText}`);\n                }\n                else if (response.status === 400) {\n                    // https://segment.com/docs/connections/sources/catalog/libraries/server/http-api/#max-request-size\n                    // Request either malformed or size exceeded - don't retry.\n                    resolveFailedBatch(batch, new Error(`[${response.status}] ${response.statusText}`));\n                    return;\n                }\n                else if (response.status === 429) {\n                    // Rate limited, wait for the reset time\n                    if (response.headers && 'x-ratelimit-reset' in response.headers) {\n                        const rateLimitResetTimestamp = parseInt(response.headers['x-ratelimit-reset'], 10);\n                        if (isFinite(rateLimitResetTimestamp)) {\n                            requestedRetryTimeout = rateLimitResetTimestamp - Date.now();\n                        }\n                    }\n                    failureReason = new Error(`[${response.status}] ${response.statusText}`);\n                }\n                else {\n                    // Treat other errors as transient and retry.\n                    failureReason = new Error(`[${response.status}] ${response.statusText}`);\n                }\n            }\n            catch (err) {\n                // Network errors get thrown, retry them.\n                failureReason = err;\n            }\n            // Final attempt failed, update context and resolve events.\n            if (currentAttempt === maxAttempts) {\n                resolveFailedBatch(batch, failureReason);\n                return;\n            }\n            // Retry after attempt-based backoff.\n            await sleep(requestedRetryTimeout\n                ? requestedRetryTimeout\n                : (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_4__.backoff)({\n                    attempt: currentAttempt,\n                    minTimeout: 25,\n                    maxTimeout: 1000,\n                }));\n        }\n    }\n}\nfunction resolveFailedBatch(batch, reason) {\n    batch.getContexts().forEach((ctx) => ctx.setFailedDelivery({ reason }));\n    batch.resolveEvents();\n}\n//# sourceMappingURL=publisher.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/publisher.js\n");

/***/ })

};
;