// route.ts
import { NextRequest, NextResponse } from "next/server";
import {
  CopilotRuntime,
  EmptyAdapter,
  copilotRuntimeNextJSAppRouterEndpoint,
} from "@copilotkit/runtime";

const REMOTE_ACTION_URL = 'https://stage.ionm.copilot.zinniax.com';
// const REMOTE_ACTION_URL =  'http://localhost:8001';

console.log("REMOTE_ACTION_URL_ROUTE2222", REMOTE_ACTION_URL);

const serviceAdapter = new EmptyAdapter();
// Use trailing slash to match your working configuration
const link = REMOTE_ACTION_URL + "/api/copilotkit";

const runtime = new CopilotRuntime({
  remoteEndpoints: [
    {
      url: link,
    },
  ],
});

export const POST = async (req: NextRequest) => {
  console.log("BACKEND REQUEST RECEIVED...", req);
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter,
    endpoint: "/api2/copilotkit",
    
  });
  console.log("POST /copilotkit", req);
  return handleRequest(req);
};
