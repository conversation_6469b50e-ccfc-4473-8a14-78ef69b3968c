import React, { useState } from "react";

interface HospitalItem {
  id: string;
  name: string;
  city: string;
  address: string;
  region: string;
}

interface HospitalListDisplayProps {
  items: HospitalItem[];
  onSubmit?: (selected: HospitalItem[]) => void;
  disabled?: boolean;
  selectedIdxs?: number[];
}

const HospitalListDisplay: React.FC<HospitalListDisplayProps> = ({
  items,
  onSubmit,
  disabled,
  selectedIdxs = [],
}) => {
  const [selectedIndices, setSelectedIndices] = useState<number[]>(selectedIdxs);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(
    selectedIdxs.length > 0 ? selectedIdxs[0] : null
  );
 const toggleSelection = (idx: number) => {
  if (disabled) return;
  setSelectedIndex(idx);
};

 const handleSubmit = () => {
  if (disabled || !onSubmit || selectedIndex === null) return;
  const selectedItem = items[selectedIndex];
  onSubmit([selectedItem]);
};

  if (!items || items.length === 0) {
    return <div>No hospitals to display.</div>;
  }

  return (
    <div
      style={{
        padding: 24,
        background: "#fff",
        borderRadius: 16,
        boxShadow: "0 4px 24px rgba(0,0,0,0.09)",
        maxWidth: 400,
        border: "1px solid #e5e7eb",
        opacity: disabled ? 0.6 : 1,
        pointerEvents: disabled ? "none" : "auto",
      }}
    >
      <h3 style={{ fontSize: 20, fontWeight: 600, marginBottom: 12 }}>
        Select Hospitals
      </h3>

      <ul
        style={{
          paddingLeft: 0,
          margin: 0,
          maxHeight: 240,
          overflowY: "auto",
        }}
      >
        {items.map((item, idx) => (
          <li
            key={item.id}
            onClick={() => toggleSelection(idx)}
            style={{
              margin: "10px 0",
              padding: "8px 12px",
              background: "#f3f4f6",
              borderRadius: 8,
              listStyle: "none",
              fontSize: 14,
              cursor: "pointer",
              fontWeight: selectedIndex === idx ? 600 : 400,
              boxShadow: selectedIndex === idx
                ? "0 2px 8px rgba(56,189,248,0.15)"
                : undefined,
            }}
          >
            <input
              type="radio"
              checked={selectedIndex === idx}
              onChange={() => toggleSelection(idx)}
              onClick={(e) => e.stopPropagation()}
              disabled={disabled}
            />
            <div style={{ marginLeft: 8 }}>
              <div style={{ fontWeight: 600 }}>{item.name}</div>
              <div style={{ fontSize: 12, color: "#555" }}>
                {item.city} | {item.region}
              </div>
              <div style={{ fontSize: 12, color: "#888" }}>{item.address}</div>
            </div>
          </li>
        ))}
      </ul>

      <button
        onClick={handleSubmit}
        style={{
          marginTop: 16,
          padding: "8px 12px",
          backgroundColor: "#38bdf8",
          border: "none",
          borderRadius: 8,
          color: "#fff",
          fontWeight: 600,
          cursor: disabled ? "not-allowed" : "pointer",
          width: "100%",
        }}
        disabled={disabled}
      >
        Submit Selected
      </button>
    </div>
  );
};

export default HospitalListDisplay;
