/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@envelop";
exports.ids = ["vendor-chunks/@envelop"];
exports.modules = {

/***/ "(rsc)/./node_modules/@envelop/core/cjs/create.js":
/*!**************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/create.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.envelop = envelop;\nconst instrumentation_1 = __webpack_require__(/*! @envelop/instrumentation */ \"(rsc)/./node_modules/@envelop/instrumentation/cjs/index.js\");\nconst orchestrator_js_1 = __webpack_require__(/*! ./orchestrator.js */ \"(rsc)/./node_modules/@envelop/core/cjs/orchestrator.js\");\nfunction notEmpty(value) {\n    return value != null;\n}\nfunction envelop(options) {\n    const plugins = options.plugins.filter(notEmpty);\n    const orchestrator = (0, orchestrator_js_1.createEnvelopOrchestrator)({\n        plugins,\n    });\n    const instrumentation = orchestrator.instrumentation;\n    const getEnveloped = (context = {}) => {\n        const instrumented = (0, instrumentation_1.getInstrumented)({ context });\n        const typedOrchestrator = orchestrator;\n        instrumented.fn(instrumentation?.init, orchestrator.init)(context);\n        return {\n            parse: instrumented.fn(instrumentation?.parse, typedOrchestrator.parse(context)),\n            validate: instrumented.fn(instrumentation?.validate, typedOrchestrator.validate(context)),\n            contextFactory: instrumented.fn(instrumentation?.context, typedOrchestrator.contextFactory(context)),\n            execute: instrumented.asyncFn(instrumentation?.execute, typedOrchestrator.execute),\n            subscribe: instrumented.asyncFn(instrumentation?.subscribe, typedOrchestrator.subscribe),\n            schema: typedOrchestrator.getCurrentSchema(),\n        };\n    };\n    getEnveloped._plugins = plugins;\n    return getEnveloped;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/create.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/document-string-map.js":
/*!***************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/document-string-map.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.documentStringMap = void 0;\nexports.getDocumentString = getDocumentString;\nexports.documentStringMap = new WeakMap();\nfunction getDocumentString(document, print) {\n    let documentSource = exports.documentStringMap.get(document);\n    if (!documentSource && print) {\n        documentSource = print(document);\n        exports.documentStringMap.set(document, documentSource);\n    }\n    return documentSource;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvZG9jdW1lbnQtc3RyaW5nLW1hcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx5QkFBeUI7QUFDekIseUJBQXlCO0FBQ3pCLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBlbnZlbG9wXFxjb3JlXFxjanNcXGRvY3VtZW50LXN0cmluZy1tYXAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRvY3VtZW50U3RyaW5nTWFwID0gdm9pZCAwO1xuZXhwb3J0cy5nZXREb2N1bWVudFN0cmluZyA9IGdldERvY3VtZW50U3RyaW5nO1xuZXhwb3J0cy5kb2N1bWVudFN0cmluZ01hcCA9IG5ldyBXZWFrTWFwKCk7XG5mdW5jdGlvbiBnZXREb2N1bWVudFN0cmluZyhkb2N1bWVudCwgcHJpbnQpIHtcbiAgICBsZXQgZG9jdW1lbnRTb3VyY2UgPSBleHBvcnRzLmRvY3VtZW50U3RyaW5nTWFwLmdldChkb2N1bWVudCk7XG4gICAgaWYgKCFkb2N1bWVudFNvdXJjZSAmJiBwcmludCkge1xuICAgICAgICBkb2N1bWVudFNvdXJjZSA9IHByaW50KGRvY3VtZW50KTtcbiAgICAgICAgZXhwb3J0cy5kb2N1bWVudFN0cmluZ01hcC5zZXQoZG9jdW1lbnQsIGRvY3VtZW50U291cmNlKTtcbiAgICB9XG4gICAgcmV0dXJuIGRvY3VtZW50U291cmNlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/document-string-map.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getDocumentString = void 0;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n// eslint-disable-next-line import/export\ntslib_1.__exportStar(__webpack_require__(/*! @envelop/types */ \"(rsc)/./node_modules/@envelop/types/cjs/index.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! @envelop/instrumentation */ \"(rsc)/./node_modules/@envelop/instrumentation/cjs/index.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./create.js */ \"(rsc)/./node_modules/@envelop/core/cjs/create.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/@envelop/core/cjs/utils.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-envelop.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-envelop.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-logger.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-logger.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-schema.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-schema.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-error-handler.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-error-handler.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-extend-context.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-extend-context.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-payload-formatter.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-payload-formatter.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-masked-errors.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-masked-errors.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-engine.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-engine.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-validation-rule.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-validation-rule.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugin-with-state.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugin-with-state.js\"), exports);\nvar document_string_map_js_1 = __webpack_require__(/*! ./document-string-map.js */ \"(rsc)/./node_modules/@envelop/core/cjs/document-string-map.js\");\nObject.defineProperty(exports, \"getDocumentString\", ({ enumerable: true, get: function () { return document_string_map_js_1.getDocumentString; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/orchestrator.js":
/*!********************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/orchestrator.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createEnvelopOrchestrator = createEnvelopOrchestrator;\nconst instrumentation_1 = __webpack_require__(/*! @envelop/instrumentation */ \"(rsc)/./node_modules/@envelop/instrumentation/cjs/index.js\");\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst document_string_map_js_1 = __webpack_require__(/*! ./document-string-map.js */ \"(rsc)/./node_modules/@envelop/core/cjs/document-string-map.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/@envelop/core/cjs/utils.js\");\nfunction throwEngineFunctionError(name) {\n    throw Error(`No \\`${name}\\` function found! Register it using \"useEngine\" plugin.`);\n}\nfunction createEnvelopOrchestrator({ plugins, }) {\n    let schema = null;\n    let initDone = false;\n    const parse = () => throwEngineFunctionError('parse');\n    const validate = () => throwEngineFunctionError('validate');\n    const execute = () => throwEngineFunctionError('execute');\n    const subscribe = () => throwEngineFunctionError('subscribe');\n    let instrumentation;\n    // Define the initial method for replacing the GraphQL schema, this is needed in order\n    // to allow setting the schema from the onPluginInit callback. We also need to make sure\n    // here not to call the same plugin that initiated the schema switch.\n    const replaceSchema = (newSchema, ignorePluginIndex = -1) => {\n        if (schema === newSchema) {\n            return;\n        }\n        schema = newSchema;\n        if (initDone) {\n            for (const [i, plugin] of plugins.entries()) {\n                if (i !== ignorePluginIndex) {\n                    plugin.onSchemaChange &&\n                        plugin.onSchemaChange({\n                            schema,\n                            replaceSchema: schemaToSet => {\n                                replaceSchema(schemaToSet, i);\n                            },\n                        });\n                }\n            }\n        }\n    };\n    const contextErrorHandlers = [];\n    // Iterate all plugins and trigger onPluginInit\n    for (let i = 0; i < plugins.length; i++) {\n        const plugin = plugins[i];\n        const pluginsToAdd = [];\n        plugin.onPluginInit?.({\n            plugins,\n            addPlugin: newPlugin => {\n                pluginsToAdd.push(newPlugin);\n            },\n            setSchema: modifiedSchema => replaceSchema(modifiedSchema, i),\n            registerContextErrorHandler: handler => contextErrorHandlers.push(handler),\n        });\n        pluginsToAdd.length && plugins.splice(i + 1, 0, ...pluginsToAdd);\n    }\n    // A set of before callbacks defined here in order to allow it to be used later\n    const beforeCallbacks = {\n        init: [],\n        parse: [],\n        validate: [],\n        subscribe: [],\n        execute: [],\n        context: [],\n    };\n    for (const { onContextBuilding, onExecute, onParse, onSubscribe, onValidate, onEnveloped, instrumentation: pluginInstrumentation, } of plugins) {\n        onEnveloped && beforeCallbacks.init.push(onEnveloped);\n        onContextBuilding && beforeCallbacks.context.push(onContextBuilding);\n        onExecute && beforeCallbacks.execute.push(onExecute);\n        onParse && beforeCallbacks.parse.push(onParse);\n        onSubscribe && beforeCallbacks.subscribe.push(onSubscribe);\n        onValidate && beforeCallbacks.validate.push(onValidate);\n        if (pluginInstrumentation) {\n            instrumentation = instrumentation\n                ? (0, instrumentation_1.chain)(instrumentation, pluginInstrumentation)\n                : pluginInstrumentation;\n        }\n    }\n    const init = initialContext => {\n        for (const [i, onEnveloped] of beforeCallbacks.init.entries()) {\n            onEnveloped({\n                context: initialContext,\n                extendContext: extension => {\n                    if (!initialContext) {\n                        return;\n                    }\n                    Object.assign(initialContext, extension);\n                },\n                setSchema: modifiedSchema => replaceSchema(modifiedSchema, i),\n            });\n        }\n    };\n    const customParse = beforeCallbacks.parse.length\n        ? initialContext => (source, parseOptions) => {\n            let result = null;\n            let parseFn = parse;\n            const context = initialContext;\n            const afterCalls = [];\n            for (const onParse of beforeCallbacks.parse) {\n                const afterFn = onParse({\n                    context,\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    params: { source, options: parseOptions },\n                    parseFn,\n                    setParseFn: newFn => {\n                        parseFn = newFn;\n                    },\n                    setParsedDocument: newDoc => {\n                        result = newDoc;\n                    },\n                });\n                if (afterFn) {\n                    afterCalls.push(afterFn);\n                }\n            }\n            if (result === null) {\n                try {\n                    result = parseFn(source, parseOptions);\n                }\n                catch (e) {\n                    result = e;\n                }\n            }\n            for (const afterCb of afterCalls) {\n                afterCb({\n                    context,\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    replaceParseResult: newResult => {\n                        result = newResult;\n                    },\n                    result,\n                });\n            }\n            if (result === null) {\n                throw new Error(`Failed to parse document.`);\n            }\n            if (result instanceof Error) {\n                throw result;\n            }\n            document_string_map_js_1.documentStringMap.set(result, source.toString());\n            return result;\n        }\n        : () => parse;\n    const customValidate = beforeCallbacks.validate\n        .length\n        ? initialContext => (schema, documentAST, rules, typeInfo, validationOptions) => {\n            let actualRules = rules ? [...rules] : undefined;\n            let validateFn = validate;\n            let result = null;\n            const context = initialContext;\n            const afterCalls = [];\n            for (const onValidate of beforeCallbacks.validate) {\n                const afterFn = onValidate({\n                    context,\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    params: {\n                        schema,\n                        documentAST,\n                        rules: actualRules,\n                        typeInfo,\n                        options: validationOptions,\n                    },\n                    validateFn,\n                    addValidationRule: rule => {\n                        if (!actualRules) {\n                            actualRules = [];\n                        }\n                        actualRules.push(rule);\n                    },\n                    setValidationFn: newFn => {\n                        validateFn = newFn;\n                    },\n                    setResult: newResults => {\n                        result = newResults;\n                    },\n                });\n                afterFn && afterCalls.push(afterFn);\n            }\n            if (!result) {\n                result = validateFn(schema, documentAST, actualRules, typeInfo, validationOptions);\n            }\n            if (!result) {\n                return;\n            }\n            const valid = result.length === 0;\n            for (const afterCb of afterCalls) {\n                afterCb({\n                    valid,\n                    result,\n                    context,\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    setResult: newResult => {\n                        result = newResult;\n                    },\n                });\n            }\n            return result;\n        }\n        : () => validate;\n    const customContextFactory = beforeCallbacks.context.length\n        ? initialContext => orchestratorCtx => {\n            const afterCalls = [];\n            // In order to have access to the \"last working\" context object we keep this outside of the try block:\n            const context = initialContext;\n            if (orchestratorCtx) {\n                Object.assign(context, orchestratorCtx);\n            }\n            let isBreakingContextBuilding = false;\n            return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(beforeCallbacks.context, (onContext, stopEarly) => onContext({\n                context,\n                extendContext: extension => {\n                    Object.assign(context, extension);\n                },\n                breakContextBuilding: () => {\n                    isBreakingContextBuilding = true;\n                    stopEarly();\n                },\n            }), afterCalls), () => {\n                if (!isBreakingContextBuilding) {\n                    return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(afterCalls, afterCb => afterCb({\n                        context,\n                        extendContext(extension) {\n                            Object.assign(context, extension);\n                        },\n                    })), () => context);\n                }\n                return context;\n            }, err => {\n                let error = err;\n                for (const errorCb of contextErrorHandlers) {\n                    errorCb({\n                        context,\n                        error,\n                        setError: err => {\n                            error = err;\n                        },\n                    });\n                }\n                throw error;\n            });\n        }\n        : initialContext => orchestratorCtx => {\n            if (orchestratorCtx) {\n                Object.assign(initialContext, orchestratorCtx);\n            }\n            return initialContext;\n        };\n    const useCustomSubscribe = beforeCallbacks.subscribe.length;\n    const customSubscribe = useCustomSubscribe\n        ? (0, utils_js_1.makeSubscribe)(args => {\n            let subscribeFn = subscribe;\n            const afterCallbacks = [];\n            const context = args.contextValue || {};\n            let result;\n            return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(beforeCallbacks.subscribe, (onSubscribe, endEarly) => onSubscribe({\n                subscribeFn,\n                setSubscribeFn: newSubscribeFn => {\n                    subscribeFn = newSubscribeFn;\n                },\n                context,\n                extendContext: extension => {\n                    Object.assign(context, extension);\n                },\n                args: args,\n                setResultAndStopExecution: stopResult => {\n                    result = stopResult;\n                    endEarly();\n                },\n            }), afterCallbacks), () => {\n                const afterCalls = [];\n                const subscribeErrorHandlers = [];\n                for (const { onSubscribeResult, onSubscribeError } of afterCallbacks) {\n                    if (onSubscribeResult) {\n                        afterCalls.push(onSubscribeResult);\n                    }\n                    if (onSubscribeError) {\n                        subscribeErrorHandlers.push(onSubscribeError);\n                    }\n                }\n                return (0, promise_helpers_1.handleMaybePromise)(() => result || subscribeFn(args), result => {\n                    const onNextHandler = [];\n                    const onEndHandler = [];\n                    for (const afterCb of afterCalls) {\n                        const hookResult = afterCb({\n                            args: args,\n                            result,\n                            setResult: newResult => {\n                                result = newResult;\n                            },\n                        });\n                        if (hookResult) {\n                            if (hookResult.onNext) {\n                                onNextHandler.push(hookResult.onNext);\n                            }\n                            if (hookResult.onEnd) {\n                                onEndHandler.push(hookResult.onEnd);\n                            }\n                        }\n                    }\n                    if (onNextHandler.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                        result = (0, utils_js_1.mapAsyncIterator)(result, (result) => (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(onNextHandler, onNext => onNext({\n                            args: args,\n                            result,\n                            setResult: newResult => (result = newResult),\n                        })), () => result));\n                    }\n                    if (onEndHandler.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                        result = (0, utils_js_1.finalAsyncIterator)(result, () => {\n                            for (const onEnd of onEndHandler) {\n                                onEnd();\n                            }\n                        });\n                    }\n                    if (subscribeErrorHandlers.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                        result = (0, utils_js_1.errorAsyncIterator)(result, err => {\n                            let error = err;\n                            for (const handler of subscribeErrorHandlers) {\n                                handler({\n                                    error,\n                                    setError: err => {\n                                        error = err;\n                                    },\n                                });\n                            }\n                            throw error;\n                        });\n                    }\n                    return result;\n                });\n            });\n        })\n        : (0, utils_js_1.makeSubscribe)(subscribe);\n    const useCustomExecute = beforeCallbacks.execute.length;\n    const customExecute = useCustomExecute\n        ? (0, utils_js_1.makeExecute)(args => {\n            let executeFn = execute;\n            let result;\n            const afterCalls = [];\n            const afterDoneCalls = [];\n            const context = args.contextValue || {};\n            return (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(beforeCallbacks.execute, (onExecute, endEarly) => onExecute({\n                executeFn,\n                setExecuteFn: newExecuteFn => {\n                    executeFn = newExecuteFn;\n                },\n                setResultAndStopExecution: stopResult => {\n                    result = stopResult;\n                    endEarly();\n                },\n                context,\n                extendContext: extension => {\n                    if (typeof extension === 'object') {\n                        Object.assign(context, extension);\n                    }\n                    else {\n                        throw new Error(`Invalid context extension provided! Expected \"object\", got: \"${JSON.stringify(extension)}\" (${typeof extension})`);\n                    }\n                },\n                args: args,\n            }), afterCalls), () => (0, promise_helpers_1.handleMaybePromise)(() => result ||\n                executeFn({\n                    ...args,\n                    contextValue: context,\n                }), result => (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsync)(afterCalls, afterCb => afterCb.onExecuteDone?.({\n                args: args,\n                result,\n                setResult: newResult => {\n                    result = newResult;\n                },\n            }), afterDoneCalls), () => {\n                const onNextHandler = [];\n                const onEndHandler = [];\n                for (const { onNext, onEnd } of afterDoneCalls) {\n                    if (onNext) {\n                        onNextHandler.push(onNext);\n                    }\n                    if (onEnd) {\n                        onEndHandler.push(onEnd);\n                    }\n                }\n                if (onNextHandler.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                    result = (0, utils_js_1.mapAsyncIterator)(result, result => (0, promise_helpers_1.handleMaybePromise)(() => (0, promise_helpers_1.iterateAsyncVoid)(onNextHandler, onNext => onNext({\n                        args: args,\n                        result: result,\n                        setResult: newResult => {\n                            result = newResult;\n                        },\n                    })), () => result));\n                }\n                if (onEndHandler.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                    result = (0, utils_js_1.finalAsyncIterator)(result, () => {\n                        for (const onEnd of onEndHandler) {\n                            onEnd();\n                        }\n                    });\n                }\n                return result;\n            })));\n        })\n        : (0, utils_js_1.makeExecute)(execute);\n    initDone = true;\n    // This is done in order to trigger the first schema available, to allow plugins that needs the schema\n    // eagerly to have it.\n    if (schema) {\n        for (const [i, plugin] of plugins.entries()) {\n            plugin.onSchemaChange?.({\n                schema,\n                replaceSchema: modifiedSchema => replaceSchema(modifiedSchema, i),\n            });\n        }\n    }\n    return {\n        getCurrentSchema() {\n            return schema;\n        },\n        init,\n        parse: customParse,\n        validate: customValidate,\n        execute: customExecute,\n        subscribe: customSubscribe,\n        contextFactory: customContextFactory,\n        instrumentation,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/orchestrator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugin-with-state.js":
/*!*************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugin-with-state.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.withState = withState;\nexports.getMostSpecificState = getMostSpecificState;\nfunction withState(pluginFactory) {\n    const states = {};\n    function getProp(scope, key) {\n        return {\n            get() {\n                if (!states[scope])\n                    states[scope] = new WeakMap();\n                let value = states[scope].get(key);\n                if (!value)\n                    states[scope].set(key, (value = {}));\n                return value;\n            },\n            enumerable: true,\n        };\n    }\n    function getState(payload) {\n        if (!payload) {\n            return undefined;\n        }\n        let { executionRequest, context, request } = payload;\n        const state = {};\n        const defineState = (scope, key) => Object.defineProperty(state, scope, getProp(scope, key));\n        if (executionRequest) {\n            defineState('forSubgraphExecution', executionRequest);\n            // ExecutionRequest can happen outside of any Graphql Operation for Gateway internal usage like Introspection queries.\n            // We check for `params` to be present, which means it's actually a GraphQL context.\n            if (executionRequest.context?.params)\n                context = executionRequest.context;\n        }\n        if (context) {\n            defineState('forOperation', context);\n            if (context.request)\n                request = context.request;\n        }\n        if (request) {\n            defineState('forRequest', request);\n        }\n        return state;\n    }\n    function addStateGetters(src) {\n        const result = {};\n        for (const [hookName, hook] of Object.entries(src)) {\n            if (typeof hook !== 'function') {\n                result[hookName] = hook;\n            }\n            else {\n                result[hookName] = {\n                    [hook.name](payload, ...args) {\n                        if (payload && (payload.request || payload.context || payload.executionRequest)) {\n                            return hook({\n                                ...payload,\n                                get state() {\n                                    return getState(payload);\n                                },\n                            }, ...args);\n                        }\n                        else {\n                            return hook(payload, ...args);\n                        }\n                    },\n                }[hook.name];\n            }\n        }\n        return result;\n    }\n    const { instrumentation, ...hooks } = pluginFactory(getState);\n    const pluginWithState = addStateGetters(hooks);\n    if (instrumentation) {\n        pluginWithState.instrumentation = addStateGetters(instrumentation);\n    }\n    return pluginWithState;\n}\nfunction getMostSpecificState(state = {}) {\n    const { forOperation, forRequest, forSubgraphExecution } = state;\n    return forSubgraphExecution ?? forOperation ?? forRequest;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugin-with-state.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-engine.js":
/*!**************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-engine.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useEngine = void 0;\nconst useEngine = (engine) => {\n    return {\n        onExecute: ({ setExecuteFn }) => {\n            if (engine.execute) {\n                setExecuteFn(engine.execute);\n            }\n        },\n        onParse: ({ setParseFn }) => {\n            if (engine.parse) {\n                setParseFn(engine.parse);\n            }\n        },\n        onValidate: ({ setValidationFn, addValidationRule }) => {\n            if (engine.validate) {\n                setValidationFn(engine.validate);\n            }\n            engine.specifiedRules?.map(addValidationRule);\n        },\n        onSubscribe: ({ setSubscribeFn }) => {\n            if (engine.subscribe) {\n                setSubscribeFn(engine.subscribe);\n            }\n        },\n    };\n};\nexports.useEngine = useEngine;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtZW5naW5lLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0Esc0JBQXNCLGNBQWM7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULG9CQUFvQixZQUFZO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCx1QkFBdUIsb0NBQW9DO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULHdCQUF3QixnQkFBZ0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGVudmVsb3BcXGNvcmVcXGNqc1xccGx1Z2luc1xcdXNlLWVuZ2luZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudXNlRW5naW5lID0gdm9pZCAwO1xuY29uc3QgdXNlRW5naW5lID0gKGVuZ2luZSkgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICAgIG9uRXhlY3V0ZTogKHsgc2V0RXhlY3V0ZUZuIH0pID0+IHtcbiAgICAgICAgICAgIGlmIChlbmdpbmUuZXhlY3V0ZSkge1xuICAgICAgICAgICAgICAgIHNldEV4ZWN1dGVGbihlbmdpbmUuZXhlY3V0ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIG9uUGFyc2U6ICh7IHNldFBhcnNlRm4gfSkgPT4ge1xuICAgICAgICAgICAgaWYgKGVuZ2luZS5wYXJzZSkge1xuICAgICAgICAgICAgICAgIHNldFBhcnNlRm4oZW5naW5lLnBhcnNlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgb25WYWxpZGF0ZTogKHsgc2V0VmFsaWRhdGlvbkZuLCBhZGRWYWxpZGF0aW9uUnVsZSB9KSA9PiB7XG4gICAgICAgICAgICBpZiAoZW5naW5lLnZhbGlkYXRlKSB7XG4gICAgICAgICAgICAgICAgc2V0VmFsaWRhdGlvbkZuKGVuZ2luZS52YWxpZGF0ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbmdpbmUuc3BlY2lmaWVkUnVsZXM/Lm1hcChhZGRWYWxpZGF0aW9uUnVsZSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uU3Vic2NyaWJlOiAoeyBzZXRTdWJzY3JpYmVGbiB9KSA9PiB7XG4gICAgICAgICAgICBpZiAoZW5naW5lLnN1YnNjcmliZSkge1xuICAgICAgICAgICAgICAgIHNldFN1YnNjcmliZUZuKGVuZ2luZS5zdWJzY3JpYmUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgIH07XG59O1xuZXhwb3J0cy51c2VFbmdpbmUgPSB1c2VFbmdpbmU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-engine.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-envelop.js":
/*!***************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-envelop.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useEnvelop = void 0;\nconst useEnvelop = (envelop) => {\n    let initialized = false;\n    return {\n        onPluginInit({ addPlugin }) {\n            if (initialized) {\n                return;\n            }\n            for (const plugin of envelop._plugins) {\n                addPlugin(plugin);\n            }\n            // Avoid double execution if envelop is extended multiple times\n            initialized = true;\n        },\n    };\n};\nexports.useEnvelop = useEnvelop;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtZW52ZWxvcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLFdBQVc7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0Esa0JBQWtCIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBlbnZlbG9wXFxjb3JlXFxjanNcXHBsdWdpbnNcXHVzZS1lbnZlbG9wLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy51c2VFbnZlbG9wID0gdm9pZCAwO1xuY29uc3QgdXNlRW52ZWxvcCA9IChlbnZlbG9wKSA9PiB7XG4gICAgbGV0IGluaXRpYWxpemVkID0gZmFsc2U7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgb25QbHVnaW5Jbml0KHsgYWRkUGx1Z2luIH0pIHtcbiAgICAgICAgICAgIGlmIChpbml0aWFsaXplZCkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZvciAoY29uc3QgcGx1Z2luIG9mIGVudmVsb3AuX3BsdWdpbnMpIHtcbiAgICAgICAgICAgICAgICBhZGRQbHVnaW4ocGx1Z2luKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIEF2b2lkIGRvdWJsZSBleGVjdXRpb24gaWYgZW52ZWxvcCBpcyBleHRlbmRlZCBtdWx0aXBsZSB0aW1lc1xuICAgICAgICAgICAgaW5pdGlhbGl6ZWQgPSB0cnVlO1xuICAgICAgICB9LFxuICAgIH07XG59O1xuZXhwb3J0cy51c2VFbnZlbG9wID0gdXNlRW52ZWxvcDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-envelop.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-error-handler.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-error-handler.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useErrorHandler = void 0;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/@envelop/core/cjs/utils.js\");\nconst use_masked_errors_js_1 = __webpack_require__(/*! ./use-masked-errors.js */ \"(rsc)/./node_modules/@envelop/core/cjs/plugins/use-masked-errors.js\");\nconst makeHandleResult = (errorHandler) => ({ result, args }) => {\n    if (result.errors?.length) {\n        errorHandler({ errors: result.errors, context: args, phase: 'execution' });\n    }\n};\nconst useErrorHandler = (errorHandler) => {\n    const handleResult = makeHandleResult(errorHandler);\n    return {\n        onParse() {\n            return function onParseEnd({ result, context }) {\n                if (result instanceof Error) {\n                    errorHandler({ errors: [result], context, phase: 'parse' });\n                }\n            };\n        },\n        onValidate() {\n            return function onValidateEnd({ valid, result, context }) {\n                if (valid === false && result.length > 0) {\n                    errorHandler({ errors: result, context, phase: 'validate' });\n                }\n            };\n        },\n        onPluginInit(context) {\n            context.registerContextErrorHandler(({ error }) => {\n                if ((0, use_masked_errors_js_1.isGraphQLError)(error)) {\n                    errorHandler({ errors: [error], context, phase: 'context' });\n                }\n                else {\n                    // @ts-expect-error its not an error at this point so we just create a new one - can we handle this better?\n                    errorHandler({ errors: [new Error(error)], context, phase: 'context' });\n                }\n            });\n        },\n        onExecute() {\n            return {\n                onExecuteDone(payload) {\n                    return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n                },\n            };\n        },\n        onSubscribe() {\n            return {\n                onSubscribeResult(payload) {\n                    return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n                },\n            };\n        },\n    };\n};\nexports.useErrorHandler = useErrorHandler;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-error-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-extend-context.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-extend-context.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useExtendContext = void 0;\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nconst useExtendContext = (contextFactory) => ({\n    onContextBuilding({ context, extendContext }) {\n        return (0, promise_helpers_1.handleMaybePromise)(() => contextFactory(context), result => extendContext(result));\n    },\n});\nexports.useExtendContext = useExtendContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtZXh0ZW5kLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsd0JBQXdCO0FBQ3hCLDBCQUEwQixtQkFBTyxDQUFDLG9HQUE4QjtBQUNoRTtBQUNBLHdCQUF3Qix3QkFBd0I7QUFDaEQ7QUFDQSxLQUFLO0FBQ0wsQ0FBQztBQUNELHdCQUF3QiIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAZW52ZWxvcFxcY29yZVxcY2pzXFxwbHVnaW5zXFx1c2UtZXh0ZW5kLWNvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVzZUV4dGVuZENvbnRleHQgPSB2b2lkIDA7XG5jb25zdCBwcm9taXNlX2hlbHBlcnNfMSA9IHJlcXVpcmUoXCJAd2hhdHdnLW5vZGUvcHJvbWlzZS1oZWxwZXJzXCIpO1xuY29uc3QgdXNlRXh0ZW5kQ29udGV4dCA9IChjb250ZXh0RmFjdG9yeSkgPT4gKHtcbiAgICBvbkNvbnRleHRCdWlsZGluZyh7IGNvbnRleHQsIGV4dGVuZENvbnRleHQgfSkge1xuICAgICAgICByZXR1cm4gKDAsIHByb21pc2VfaGVscGVyc18xLmhhbmRsZU1heWJlUHJvbWlzZSkoKCkgPT4gY29udGV4dEZhY3RvcnkoY29udGV4dCksIHJlc3VsdCA9PiBleHRlbmRDb250ZXh0KHJlc3VsdCkpO1xuICAgIH0sXG59KTtcbmV4cG9ydHMudXNlRXh0ZW5kQ29udGV4dCA9IHVzZUV4dGVuZENvbnRleHQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-extend-context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-logger.js":
/*!**************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-logger.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useLogger = void 0;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/@envelop/core/cjs/utils.js\");\nconst DEFAULT_OPTIONS = {\n    logFn: console.log,\n};\nconst useLogger = (rawOptions = DEFAULT_OPTIONS) => {\n    const options = {\n        DEFAULT_OPTIONS,\n        ...rawOptions,\n    };\n    return {\n        onParse({ extendContext, params }) {\n            if (options.skipIntrospection && (0, utils_js_1.isIntrospectionOperationString)(params.source)) {\n                extendContext({\n                    [utils_js_1.envelopIsIntrospectionSymbol]: true,\n                });\n            }\n        },\n        onExecute({ args }) {\n            if (args.contextValue[utils_js_1.envelopIsIntrospectionSymbol]) {\n                return;\n            }\n            options.logFn('execute-start', { args });\n            return {\n                onExecuteDone: ({ result }) => {\n                    options.logFn('execute-end', { args, result });\n                },\n            };\n        },\n        onSubscribe({ args }) {\n            if (args.contextValue[utils_js_1.envelopIsIntrospectionSymbol]) {\n                return;\n            }\n            options.logFn('subscribe-start', { args });\n            return {\n                onSubscribeResult: ({ result }) => {\n                    options.logFn('subscribe-end', { args, result });\n                },\n            };\n        },\n    };\n};\nexports.useLogger = useLogger;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-logger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-masked-errors.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-masked-errors.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.defaultMaskError = exports.createDefaultMaskError = exports.DEFAULT_ERROR_MESSAGE = void 0;\nexports.isGraphQLError = isGraphQLError;\nexports.isOriginalGraphQLError = isOriginalGraphQLError;\nexports.useMaskedErrors = useMaskedErrors;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/@envelop/core/cjs/utils.js\");\nexports.DEFAULT_ERROR_MESSAGE = 'Unexpected error.';\nfunction isGraphQLError(error) {\n    return error instanceof Error && error.name === 'GraphQLError';\n}\nfunction isOriginalGraphQLError(error) {\n    if (isGraphQLError(error)) {\n        if (error.originalError != null) {\n            return isOriginalGraphQLError(error.originalError);\n        }\n        return true;\n    }\n    return false;\n}\nfunction createSerializableGraphQLError(message, originalError, isDev) {\n    const error = new Error(message);\n    error.name = 'GraphQLError';\n    if (isDev) {\n        const extensions = originalError instanceof Error\n            ? { message: originalError.message, stack: originalError.stack }\n            : { message: String(originalError) };\n        Object.defineProperty(error, 'extensions', {\n            get() {\n                return extensions;\n            },\n        });\n    }\n    Object.defineProperty(error, 'toJSON', {\n        value() {\n            return {\n                message: error.message,\n                extensions: error.extensions,\n            };\n        },\n    });\n    return error;\n}\nconst createDefaultMaskError = (isDev) => (error, message) => {\n    if (isOriginalGraphQLError(error)) {\n        return error;\n    }\n    return createSerializableGraphQLError(message, error, isDev);\n};\nexports.createDefaultMaskError = createDefaultMaskError;\nconst isDev = globalThis.process?.env?.NODE_ENV === 'development';\nexports.defaultMaskError = (0, exports.createDefaultMaskError)(isDev);\nconst makeHandleResult = (maskError, message) => ({ result, setResult, }) => {\n    if (result.errors != null) {\n        setResult({ ...result, errors: result.errors.map(error => maskError(error, message)) });\n    }\n};\nfunction useMaskedErrors(opts) {\n    const maskError = opts?.maskError ?? exports.defaultMaskError;\n    const message = opts?.errorMessage || exports.DEFAULT_ERROR_MESSAGE;\n    const handleResult = makeHandleResult(maskError, message);\n    return {\n        onPluginInit(context) {\n            context.registerContextErrorHandler(({ error, setError }) => {\n                setError(maskError(error, message));\n            });\n        },\n        onExecute() {\n            return {\n                onExecuteDone(payload) {\n                    return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n                },\n            };\n        },\n        onSubscribe() {\n            return {\n                onSubscribeResult(payload) {\n                    return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n                },\n                onSubscribeError({ error, setError }) {\n                    setError(maskError(error, message));\n                },\n            };\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-masked-errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-payload-formatter.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-payload-formatter.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.usePayloadFormatter = void 0;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/@envelop/core/cjs/utils.js\");\nconst makeHandleResult = (formatter) => ({ args, result, setResult, }) => {\n    const modified = formatter(result, args);\n    if (modified !== false) {\n        setResult(modified);\n    }\n};\nconst usePayloadFormatter = (formatter) => ({\n    onExecute() {\n        const handleResult = makeHandleResult(formatter);\n        return {\n            onExecuteDone(payload) {\n                return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n            },\n        };\n    },\n});\nexports.usePayloadFormatter = usePayloadFormatter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtcGF5bG9hZC1mb3JtYXR0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsMkJBQTJCO0FBQzNCLG1CQUFtQixtQkFBTyxDQUFDLG9FQUFhO0FBQ3hDLDJDQUEyQywwQkFBMEI7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTCxDQUFDO0FBQ0QsMkJBQTJCIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBlbnZlbG9wXFxjb3JlXFxjanNcXHBsdWdpbnNcXHVzZS1wYXlsb2FkLWZvcm1hdHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudXNlUGF5bG9hZEZvcm1hdHRlciA9IHZvaWQgMDtcbmNvbnN0IHV0aWxzX2pzXzEgPSByZXF1aXJlKFwiLi4vdXRpbHMuanNcIik7XG5jb25zdCBtYWtlSGFuZGxlUmVzdWx0ID0gKGZvcm1hdHRlcikgPT4gKHsgYXJncywgcmVzdWx0LCBzZXRSZXN1bHQsIH0pID0+IHtcbiAgICBjb25zdCBtb2RpZmllZCA9IGZvcm1hdHRlcihyZXN1bHQsIGFyZ3MpO1xuICAgIGlmIChtb2RpZmllZCAhPT0gZmFsc2UpIHtcbiAgICAgICAgc2V0UmVzdWx0KG1vZGlmaWVkKTtcbiAgICB9XG59O1xuY29uc3QgdXNlUGF5bG9hZEZvcm1hdHRlciA9IChmb3JtYXR0ZXIpID0+ICh7XG4gICAgb25FeGVjdXRlKCkge1xuICAgICAgICBjb25zdCBoYW5kbGVSZXN1bHQgPSBtYWtlSGFuZGxlUmVzdWx0KGZvcm1hdHRlcik7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBvbkV4ZWN1dGVEb25lKHBheWxvYWQpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIHV0aWxzX2pzXzEuaGFuZGxlU3RyZWFtT3JTaW5nbGVFeGVjdXRpb25SZXN1bHQpKHBheWxvYWQsIGhhbmRsZVJlc3VsdCk7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH0sXG59KTtcbmV4cG9ydHMudXNlUGF5bG9hZEZvcm1hdHRlciA9IHVzZVBheWxvYWRGb3JtYXR0ZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-payload-formatter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-schema.js":
/*!**************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-schema.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useSchemaByContext = exports.useSchema = void 0;\nconst useSchema = (schema) => {\n    return {\n        onPluginInit({ setSchema }) {\n            setSchema(schema);\n        },\n    };\n};\nexports.useSchema = useSchema;\nconst useSchemaByContext = (schemaLoader) => {\n    return {\n        onEnveloped({ setSchema, context }) {\n            setSchema(schemaLoader(context));\n        },\n    };\n};\nexports.useSchemaByContext = useSchemaByContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2Utc2NoZW1hLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDBCQUEwQixHQUFHLGlCQUFpQjtBQUM5QztBQUNBO0FBQ0EsdUJBQXVCLFdBQVc7QUFDbEM7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0Esc0JBQXNCLG9CQUFvQjtBQUMxQztBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsMEJBQTBCIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBlbnZlbG9wXFxjb3JlXFxjanNcXHBsdWdpbnNcXHVzZS1zY2hlbWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVzZVNjaGVtYUJ5Q29udGV4dCA9IGV4cG9ydHMudXNlU2NoZW1hID0gdm9pZCAwO1xuY29uc3QgdXNlU2NoZW1hID0gKHNjaGVtYSkgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICAgIG9uUGx1Z2luSW5pdCh7IHNldFNjaGVtYSB9KSB7XG4gICAgICAgICAgICBzZXRTY2hlbWEoc2NoZW1hKTtcbiAgICAgICAgfSxcbiAgICB9O1xufTtcbmV4cG9ydHMudXNlU2NoZW1hID0gdXNlU2NoZW1hO1xuY29uc3QgdXNlU2NoZW1hQnlDb250ZXh0ID0gKHNjaGVtYUxvYWRlcikgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICAgIG9uRW52ZWxvcGVkKHsgc2V0U2NoZW1hLCBjb250ZXh0IH0pIHtcbiAgICAgICAgICAgIHNldFNjaGVtYShzY2hlbWFMb2FkZXIoY29udGV4dCkpO1xuICAgICAgICB9LFxuICAgIH07XG59O1xuZXhwb3J0cy51c2VTY2hlbWFCeUNvbnRleHQgPSB1c2VTY2hlbWFCeUNvbnRleHQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-schema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/plugins/use-validation-rule.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/plugins/use-validation-rule.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useValidationRule = void 0;\nconst useValidationRule = (rule) => {\n    return {\n        onValidate({ addValidationRule }) {\n            addValidationRule(rule);\n        },\n    };\n};\nexports.useValidationRule = useValidationRule;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtdmFsaWRhdGlvbi1ydWxlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EscUJBQXFCLG1CQUFtQjtBQUN4QztBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXEBlbnZlbG9wXFxjb3JlXFxjanNcXHBsdWdpbnNcXHVzZS12YWxpZGF0aW9uLXJ1bGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVzZVZhbGlkYXRpb25SdWxlID0gdm9pZCAwO1xuY29uc3QgdXNlVmFsaWRhdGlvblJ1bGUgPSAocnVsZSkgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICAgIG9uVmFsaWRhdGUoeyBhZGRWYWxpZGF0aW9uUnVsZSB9KSB7XG4gICAgICAgICAgICBhZGRWYWxpZGF0aW9uUnVsZShydWxlKTtcbiAgICAgICAgfSxcbiAgICB9O1xufTtcbmV4cG9ydHMudXNlVmFsaWRhdGlvblJ1bGUgPSB1c2VWYWxpZGF0aW9uUnVsZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/plugins/use-validation-rule.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/core/cjs/utils.js":
/*!*************************************************!*\
  !*** ./node_modules/@envelop/core/cjs/utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isPromise = exports.mapMaybePromise = exports.makeExecute = exports.mapAsyncIterator = exports.makeSubscribe = exports.envelopIsIntrospectionSymbol = void 0;\nexports.isIntrospectionOperationString = isIntrospectionOperationString;\nexports.isAsyncIterable = isAsyncIterable;\nexports.handleStreamOrSingleExecutionResult = handleStreamOrSingleExecutionResult;\nexports.finalAsyncIterator = finalAsyncIterator;\nexports.errorAsyncIterator = errorAsyncIterator;\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nexports.envelopIsIntrospectionSymbol = Symbol('ENVELOP_IS_INTROSPECTION');\nfunction isIntrospectionOperationString(operation) {\n    return (typeof operation === 'string' ? operation : operation.body).indexOf('__schema') !== -1;\n}\nfunction getSubscribeArgs(args) {\n    return args.length === 1\n        ? args[0]\n        : {\n            schema: args[0],\n            document: args[1],\n            rootValue: args[2],\n            contextValue: args[3],\n            variableValues: args[4],\n            operationName: args[5],\n            fieldResolver: args[6],\n            subscribeFieldResolver: args[7],\n        };\n}\n/**\n * Utility function for making a subscribe function that handles polymorphic arguments.\n */\nconst makeSubscribe = (subscribeFn) => ((...polyArgs) => subscribeFn(getSubscribeArgs(polyArgs)));\nexports.makeSubscribe = makeSubscribe;\nvar promise_helpers_2 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nObject.defineProperty(exports, \"mapAsyncIterator\", ({ enumerable: true, get: function () { return promise_helpers_2.mapAsyncIterator; } }));\nfunction getExecuteArgs(args) {\n    return args.length === 1\n        ? args[0]\n        : {\n            schema: args[0],\n            document: args[1],\n            rootValue: args[2],\n            contextValue: args[3],\n            variableValues: args[4],\n            operationName: args[5],\n            fieldResolver: args[6],\n            typeResolver: args[7],\n        };\n}\n/**\n * Utility function for making a execute function that handles polymorphic arguments.\n */\nconst makeExecute = (executeFn) => ((...polyArgs) => executeFn(getExecuteArgs(polyArgs)));\nexports.makeExecute = makeExecute;\n/**\n * Returns true if the provided object implements the AsyncIterator protocol via\n * implementing a `Symbol.asyncIterator` method.\n *\n * Source: https://github.com/graphql/graphql-js/blob/main/src/jsutils/isAsyncIterable.ts\n */\nfunction isAsyncIterable(maybeAsyncIterable) {\n    return (typeof maybeAsyncIterable === 'object' &&\n        maybeAsyncIterable != null &&\n        typeof maybeAsyncIterable[Symbol.asyncIterator] === 'function');\n}\n/**\n * A utility function for handling `onExecuteDone` hook result, for simplifying the handling of AsyncIterable returned from `execute`.\n *\n * @param payload The payload send to `onExecuteDone` hook function\n * @param fn The handler to be executed on each result\n * @returns a subscription for streamed results, or undefined in case of an non-async\n */\nfunction handleStreamOrSingleExecutionResult(payload, fn) {\n    if (isAsyncIterable(payload.result)) {\n        return { onNext: fn };\n    }\n    fn({\n        args: payload.args,\n        result: payload.result,\n        setResult: payload.setResult,\n    });\n    return undefined;\n}\nfunction finalAsyncIterator(source, onFinal) {\n    const iterator = source[Symbol.asyncIterator]();\n    let isDone = false;\n    const stream = {\n        [Symbol.asyncIterator]() {\n            return stream;\n        },\n        next() {\n            return iterator.next().then(result => {\n                if (result.done && isDone === false) {\n                    isDone = true;\n                    onFinal();\n                }\n                return result;\n            });\n        },\n        return() {\n            const promise = iterator.return?.();\n            if (isDone === false) {\n                isDone = true;\n                onFinal();\n            }\n            return promise || (0, promise_helpers_1.fakePromise)({ done: true, value: undefined });\n        },\n        throw(error) {\n            const promise = iterator.throw?.();\n            if (promise) {\n                return promise;\n            }\n            // if the source has no throw method we just re-throw error\n            // usually throw is not called anyways\n            throw error;\n        },\n    };\n    return stream;\n}\nfunction errorAsyncIterator(source, onError) {\n    const iterator = source[Symbol.asyncIterator]();\n    const stream = {\n        [Symbol.asyncIterator]() {\n            return stream;\n        },\n        next() {\n            return iterator.next().catch(error => {\n                onError(error);\n                return { done: true, value: undefined };\n            });\n        },\n        return() {\n            const promise = iterator.return?.();\n            return promise || (0, promise_helpers_1.fakePromise)({ done: true, value: undefined });\n        },\n        throw(error) {\n            const promise = iterator.throw?.();\n            if (promise) {\n                return promise;\n            }\n            // if the source has no throw method we just re-throw error\n            // usually throw is not called anyways\n            throw error;\n        },\n    };\n    return stream;\n}\nvar promise_helpers_3 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\nObject.defineProperty(exports, \"mapMaybePromise\", ({ enumerable: true, get: function () { return promise_helpers_3.mapMaybePromise; } }));\nObject.defineProperty(exports, \"isPromise\", ({ enumerable: true, get: function () { return promise_helpers_3.isPromise; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/core/cjs/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/instrumentation/cjs/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@envelop/instrumentation/cjs/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n// eslint-disable-next-line import/export\ntslib_1.__exportStar(__webpack_require__(/*! ./instrumentation.js */ \"(rsc)/./node_modules/@envelop/instrumentation/cjs/instrumentation.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvaW5zdHJ1bWVudGF0aW9uL2Nqcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0IsbUJBQU8sQ0FBQyx1REFBTztBQUMvQjtBQUNBLHFCQUFxQixtQkFBTyxDQUFDLGtHQUFzQiIsInNvdXJjZXMiOlsiRDpcXHppbm5pYXgtY29waWxvdFxcemlubmlheC1jb3BpbG90XFx1aVxcbm9kZV9tb2R1bGVzXFxAZW52ZWxvcFxcaW5zdHJ1bWVudGF0aW9uXFxjanNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgdHNsaWJfMSA9IHJlcXVpcmUoXCJ0c2xpYlwiKTtcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBpbXBvcnQvZXhwb3J0XG50c2xpYl8xLl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9pbnN0cnVtZW50YXRpb24uanNcIiksIGV4cG9ydHMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/instrumentation/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/instrumentation/cjs/instrumentation.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@envelop/instrumentation/cjs/instrumentation.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getInstrumented = void 0;\nexports.chain = chain;\nexports.composeInstrumentation = composeInstrumentation;\nexports.getInstrumentationAndPlugin = getInstrumentationAndPlugin;\nconst promise_helpers_1 = __webpack_require__(/*! @whatwg-node/promise-helpers */ \"(rsc)/./node_modules/@whatwg-node/promise-helpers/cjs/index.js\");\n/**\n * Composes 2 instrumentations together into one instrumentation.\n * The first one will be the outer call, the second one the inner call.\n */\nfunction chain(first, next) {\n    const merged = { ...next, ...first };\n    for (const key of Object.keys(merged)) {\n        if (key in first && key in next) {\n            merged[key] = (payload, wrapped) => first[key](payload, () => next[key](payload, wrapped));\n        }\n    }\n    return merged;\n}\n/**\n * Composes a list of instrumentation together into one instrumentation object.\n * The order of execution will respect the order of the array,\n * the first one being the outter most call, the last one the inner most call.\n */\nfunction composeInstrumentation(instrumentation) {\n    return instrumentation.length > 0 ? instrumentation.reduce(chain) : undefined;\n}\n/**\n * Extract instrumentation from a list of plugins.\n * It returns instrumentation found, and the list of plugins without their instrumentation.\n *\n * You can use this to easily customize the composition of the instrumentation if the default one\n * doesn't suits your needs.\n */\nfunction getInstrumentationAndPlugin(plugins) {\n    const pluginInstrumentation = [];\n    const newPlugins = [];\n    for (const { instrumentation, ...plugin } of plugins) {\n        if (instrumentation) {\n            pluginInstrumentation.push(instrumentation);\n        }\n        newPlugins.push(plugin);\n    }\n    return { pluginInstrumentation, plugins: newPlugins };\n}\n/**\n * A helper to instrument a function.\n *\n * @param payload: The first argument that will be passed to the instrumentation on each function call\n * @returns Function and Async Functions factories allowing to wrap a function with a given instrument.\n */\nconst getInstrumented = (payload) => ({\n    /**\n     * Wraps the `wrapped` function with the given `instrument` wrapper.\n     * @returns The wrapped function, or `undefined` if the instrument is `undefined`.\n     */\n    fn(instrument, wrapped) {\n        if (!instrument) {\n            return wrapped;\n        }\n        return (...args) => {\n            let result;\n            instrument(payload, () => {\n                result = wrapped(...args);\n            });\n            return result;\n        };\n    },\n    /**\n     * Wraps the `wrapped` function with the given `instrument` wrapper.\n     * @returns The wrapped function, or `undefined` if the instrument is `undefined`.\n     */\n    asyncFn(instrument, wrapped) {\n        if (!instrument) {\n            return wrapped;\n        }\n        return (...args) => {\n            let result;\n            return (0, promise_helpers_1.handleMaybePromise)(() => instrument(payload, () => {\n                result = wrapped(...args);\n                return (0, promise_helpers_1.isPromise)(result) ? result.then(() => undefined) : undefined;\n            }), () => {\n                return result;\n            });\n        };\n    },\n});\nexports.getInstrumented = getInstrumented;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/instrumentation/cjs/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/types/cjs/context-types.js":
/*!**********************************************************!*\
  !*** ./node_modules/@envelop/types/cjs/context-types.js ***!
  \**********************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/@envelop/types/cjs/get-enveloped.js":
/*!**********************************************************!*\
  !*** ./node_modules/@envelop/types/cjs/get-enveloped.js ***!
  \**********************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/@envelop/types/cjs/graphql.js":
/*!****************************************************!*\
  !*** ./node_modules/@envelop/types/cjs/graphql.js ***!
  \****************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/@envelop/types/cjs/hooks.js":
/*!**************************************************!*\
  !*** ./node_modules/@envelop/types/cjs/hooks.js ***!
  \**************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/@envelop/types/cjs/index.js":
/*!**************************************************!*\
  !*** ./node_modules/@envelop/types/cjs/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\ntslib_1.__exportStar(__webpack_require__(/*! ./context-types.js */ \"(rsc)/./node_modules/@envelop/types/cjs/context-types.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./hooks.js */ \"(rsc)/./node_modules/@envelop/types/cjs/hooks.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugin.js */ \"(rsc)/./node_modules/@envelop/types/cjs/plugin.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./get-enveloped.js */ \"(rsc)/./node_modules/@envelop/types/cjs/get-enveloped.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./graphql.js */ \"(rsc)/./node_modules/@envelop/types/cjs/graphql.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/@envelop/types/cjs/utils.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGVudmVsb3AvdHlwZXMvY2pzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGdCQUFnQixtQkFBTyxDQUFDLHVEQUFPO0FBQy9CLHFCQUFxQixtQkFBTyxDQUFDLG9GQUFvQjtBQUNqRCxxQkFBcUIsbUJBQU8sQ0FBQyxvRUFBWTtBQUN6QyxxQkFBcUIsbUJBQU8sQ0FBQyxzRUFBYTtBQUMxQyxxQkFBcUIsbUJBQU8sQ0FBQyxvRkFBb0I7QUFDakQscUJBQXFCLG1CQUFPLENBQUMsd0VBQWM7QUFDM0MscUJBQXFCLG1CQUFPLENBQUMsb0VBQVkiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcQGVudmVsb3BcXHR5cGVzXFxjanNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgdHNsaWJfMSA9IHJlcXVpcmUoXCJ0c2xpYlwiKTtcbnRzbGliXzEuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2NvbnRleHQtdHlwZXMuanNcIiksIGV4cG9ydHMpO1xudHNsaWJfMS5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vaG9va3MuanNcIiksIGV4cG9ydHMpO1xudHNsaWJfMS5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vcGx1Z2luLmpzXCIpLCBleHBvcnRzKTtcbnRzbGliXzEuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2dldC1lbnZlbG9wZWQuanNcIiksIGV4cG9ydHMpO1xudHNsaWJfMS5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vZ3JhcGhxbC5qc1wiKSwgZXhwb3J0cyk7XG50c2xpYl8xLl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi91dGlscy5qc1wiKSwgZXhwb3J0cyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@envelop/types/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@envelop/types/cjs/plugin.js":
/*!***************************************************!*\
  !*** ./node_modules/@envelop/types/cjs/plugin.js ***!
  \***************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/@envelop/types/cjs/utils.js":
/*!**************************************************!*\
  !*** ./node_modules/@envelop/types/cjs/utils.js ***!
  \**************************************************/
/***/ (() => {



/***/ })

};
;