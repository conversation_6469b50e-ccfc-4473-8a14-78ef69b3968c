import React, { useState } from "react";

interface DoctorItem {
    id: string;
    name: string;
}

interface DoctorListDisplayProps {
    items: DoctorItem[];
    onSubmit?: (selected: DoctorItem[]) => void;
    disabled?: boolean;
    selectedIdxs?: number[];
}

const DoctorListDisplay: React.FC<DoctorListDisplayProps> = ({
    items,
    onSubmit,
    disabled,
    selectedIdxs = [],
}) => {
    const [selectedIndices, setSelectedIndices] = useState<number[]>(selectedIdxs);
    const [selectedIndex, setSelectedIndex] = useState<number | null>(
        selectedIdxs.length > 0 ? selectedIdxs[0] : null
    );
    const toggleSelection = (idx: number) => {
        if (disabled) return;
        setSelectedIndex(idx);
    };

    const handleSubmit = () => {
        if (disabled || !onSubmit || selectedIndex === null) return;
        const selectedItem = items[selectedIndex];
        onSubmit([selectedItem]);
    };

    const formatName = (name: string) => {
        // Input: "<PERSON>, <PERSON>"
        const [lastName, firstName] = name.split(",").map(part => part.trim());

        if (!firstName || !lastName) return name; // fallback

        return `Dr. ${firstName} ${lastName}`;
    };

    if (!items || items.length === 0) {
        return <div>No Doctors to display.</div>;
    }

    return (
        <div
            style={{
                padding: 24,
                background: "#fff",
                borderRadius: 16,
                boxShadow: "0 4px 24px rgba(0,0,0,0.09)",
                maxWidth: 400,
                border: "1px solid #e5e7eb",
                opacity: disabled ? 0.6 : 1,
                pointerEvents: disabled ? "none" : "auto",
            }}
        >
            <h3 style={{ fontSize: 20, fontWeight: 600, marginBottom: 12 }}>
                Select Doctor
            </h3>

            <ul
                style={{
                    paddingLeft: 0,
                    margin: 0,
                    maxHeight: 240,
                    overflowY: "auto",
                }}
            >
                {items.map((item, idx) => (
                    <li
                        key={item.id}
                        onClick={() => toggleSelection(idx)}
                        style={{
                            margin: "10px 0",
                            padding: "8px 12px",
                            background: "#f3f4f6",
                            borderRadius: 8,
                            listStyle: "none",
                            fontSize: 14,
                            cursor: "pointer",
                            fontWeight: selectedIndex === idx ? 600 : 400,
                            boxShadow: selectedIndex === idx
                                ? "0 2px 8px rgba(56,189,248,0.15)"
                                : undefined,
                            display: "flex",
                            alignItems: "center",
                        }}
                    >
                        <input
                            type="radio"
                            checked={selectedIndex === idx}
                            onChange={() => toggleSelection(idx)}
                            onClick={(e) => e.stopPropagation()}
                            disabled={disabled}
                        />
                        <div style={{ fontWeight: 600, marginLeft: 3 }}>{formatName(item.name)}</div>
                    </li>
                ))}
            </ul>

            <button
                onClick={handleSubmit}
                style={{
                    marginTop: 16,
                    padding: "8px 12px",
                    backgroundColor: "#38bdf8",
                    border: "none",
                    borderRadius: 8,
                    color: "#fff",
                    fontWeight: 600,
                    cursor: disabled ? "not-allowed" : "pointer",
                    width: "100%",
                }}
                disabled={disabled}
            >
                Submit Selected
            </button>
        </div>
    );
};

export default DoctorListDisplay;
