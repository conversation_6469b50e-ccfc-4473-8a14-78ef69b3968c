import type { IncomingMessage, ServerResponse } from 'node:http';
import type { Http2ServerRequest, Http2ServerResponse } from 'node:http2';
import type { Socket } from 'node:net';
import type { Readable } from 'node:stream';
import { createDeferredPromise, isPromise, Maybe<PERSON>romise } from '@whatwg-node/promise-helpers';
import type { FetchAPI, FetchEvent, WaitUntilFn } from './types.js';
export { isPromise, createDeferredPromise };
export declare function isAsyncIterable(body: any): body is AsyncIterable<any>;
export interface NodeRequest {
    protocol?: string | undefined;
    hostname?: string | undefined;
    body?: any | undefined;
    url?: string | undefined;
    originalUrl?: string | undefined;
    method?: string | undefined;
    headers?: any | undefined;
    req?: IncomingMessage | Http2ServerRequest | undefined;
    raw?: IncomingMessage | Http2ServerRequest | undefined;
    socket?: Socket | undefined;
    query?: any | undefined;
    once?(event: string, listener: (...args: any[]) => void): void;
    aborted?: boolean | undefined;
}
export type NodeResponse = ServerResponse | Http2ServerResponse;
export declare function normalizeNodeRequest(nodeRequest: NodeRequest, fetchAPI: FetchAPI, nodeResponse?: NodeResponse, __useCustomAbortCtrl?: boolean): Request;
export declare function isReadable(stream: any): stream is Readable;
export declare function isNodeRequest(request: any): request is NodeRequest;
export declare function isServerResponse(stream: any): stream is NodeResponse;
export declare function isReadableStream(stream: any): stream is ReadableStream;
export declare function isFetchEvent(event: any): event is FetchEvent;
export declare function sendNodeResponse(fetchResponse: Response, serverResponse: NodeResponse, nodeRequest: NodeRequest, __useSingleWriteHead: boolean): MaybePromise<void>;
export declare function isRequestInit(val: unknown): val is RequestInit;
export declare function completeAssign(...args: any[]): any;
export { iterateAsyncVoid } from '@whatwg-node/promise-helpers';
export declare function handleErrorFromRequestHandler(error: any, ResponseCtor: typeof Response): Response;
export declare function isolateObject<TIsolatedObject extends object>(originalCtx: TIsolatedObject, waitUntilFn?: WaitUntilFn): TIsolatedObject;
export declare function handleAbortSignalAndPromiseResponse(response$: MaybePromise<Response>, abortSignal: AbortSignal): MaybePromise<Response>;
export declare const decompressedResponseMap: WeakMap<Response, Response>;
export declare function getSupportedEncodings(fetchAPI: FetchAPI): CompressionFormat[];
export declare function handleResponseDecompression(response: Response, fetchAPI: FetchAPI): Response;
export declare function ensureDisposableStackRegisteredForTerminateEvents(disposableStack: AsyncDisposableStack): void;
export declare function createCustomAbortControllerSignal(): AbortController;
