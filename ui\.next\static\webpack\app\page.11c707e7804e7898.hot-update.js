"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _copilotkit_react_ui_styles_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/react-ui/styles.css */ \"(app-pages-browser)/./node_modules/@copilotkit/react-ui/dist/index.css\");\n/* harmony import */ var _copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @copilotkit/react-ui */ \"(app-pages-browser)/./node_modules/@copilotkit/react-ui/dist/chunk-RT4HE74K.mjs\");\n/* harmony import */ var _components_ChatArea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ChatArea */ \"(app-pages-browser)/./components/ChatArea.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helper */ \"(app-pages-browser)/./app/helper.ts\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(app-pages-browser)/./node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/react-core */ \"(app-pages-browser)/./node_modules/@copilotkit/react-core/dist/chunk-4DVPRMVH.mjs\");\n// page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst prompts = [\n    \"Cancel the case for Dr.Anderson Richard\",\n    \"Reschedule case 106258\",\n    \"Schedule a case for Dr.Davis Lucas\",\n    \"Reopen case 106188\",\n    \"Dr. Johnson has a spine surgery rescheduled\"\n];\nfunction PromptButtons(param) {\n    let { onUserInteracted, animateOut, onAnimationEnd } = param;\n    _s();\n    const { appendMessage } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCopilotChat)();\n    function handlePromptClick(prompt) {\n        appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.TextMessage({\n            content: prompt,\n            role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.Role.User\n        }));\n        onUserInteracted();\n    }\n    const buttonClass = \"rounded-full px-4 py-3 text-blue-700 shadow-sm border-[1.5px] border-blue-300 transition-all duration-200 max-w-xs\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-3 items-center transition-opacity duration-500 \".concat(animateOut ? 'opacity-0 pointer-events-none' : 'opacity-100'),\n        onTransitionEnd: onAnimationEnd,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap gap-3 justify-center\",\n            children: prompts.map((prompt, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: buttonClass,\n                    onClick: ()=>handlePromptClick(prompt),\n                    children: prompt\n                }, idx, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s(PromptButtons, \"FoCHNEKApZ3v/GfLFmdeF+VBf6I=\", false, function() {\n    return [\n        _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCopilotChat\n    ];\n});\n_c = PromptButtons;\nfunction Greeting(param) {\n    let { animateOut, onAnimationEnd } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center text-center transition-opacity duration-500 \".concat(animateOut ? 'opacity-0 pointer-events-none' : 'opacity-100'),\n        onTransitionEnd: onAnimationEnd,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-4xl  font-bold text-blue-600 text-center\",\n                children: \"Hello there \\uD83D\\uDC4B\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl text-gray-700 mt-2 mb-3\",\n                children: \"How can I assist you today ?\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Greeting;\nfunction Home() {\n    _s1();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userInteracted, setUserInteracted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hideSuggestions, setHideSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [suggestionAnimationDone, setSuggestionAnimationDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const copilotChatRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Listen for manual chat input to trigger suggestion removal\n    const { appendMessage } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCopilotChat)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Patch CopilotChat input to detect manual user message\n            const handler = {\n                \"Home.useEffect.handler\": (e)=>{\n                    var _e_detail;\n                    if (!userInteracted && (e === null || e === void 0 ? void 0 : (_e_detail = e.detail) === null || _e_detail === void 0 ? void 0 : _e_detail.role) === 'user') {\n                        setUserInteracted(true);\n                    }\n                }\n            }[\"Home.useEffect.handler\"];\n            window.addEventListener('copilotkit-user-message', handler);\n            return ({\n                \"Home.useEffect\": ()=>window.removeEventListener('copilotkit-user-message', handler)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userInteracted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const observer = new MutationObserver({\n                \"Home.useEffect\": ()=>{\n                    const inputEl = document.querySelector('.copilotKitInputContainer textarea');\n                    if (inputEl) {\n                        const handleInput = {\n                            \"Home.useEffect.handleInput\": ()=>{\n                                if (!userInteracted) {\n                                    setUserInteracted(true);\n                                    const customEvent = new CustomEvent(\"copilotkit-user-message\", {\n                                        detail: {\n                                            role: 'user'\n                                        }\n                                    });\n                                    window.dispatchEvent(customEvent);\n                                }\n                            }\n                        }[\"Home.useEffect.handleInput\"];\n                        inputEl.addEventListener('keydown', handleInput);\n                        observer.disconnect(); // stop observing once found\n                    }\n                }\n            }[\"Home.useEffect\"]);\n            observer.observe(document.body, {\n                childList: true,\n                subtree: true\n            });\n            return ({\n                \"Home.useEffect\": ()=>observer.disconnect()\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userInteracted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const REMOTE_ACTION_URL = 'https://stage.ionm.copilot.zinniax.com';\n            // const REMOTE_ACTION_URL = 'http://localhost:8001'\n            console.log(\"NEXT_PUBLIC_REMOTE_ACTION_URL\", REMOTE_ACTION_URL);\n            // --- Session ID Management ---\n            let sessionId = localStorage.getItem(\"session_id\");\n            if (!sessionId) {\n                sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n                localStorage.setItem(\"session_id\", sessionId);\n                document.cookie = \"session_id=\".concat(sessionId, \"; path=/;\");\n            }\n            const params = new URLSearchParams(window.location.search);\n            window.history.replaceState({}, document.title, \"/\");\n            const token = params.get(\"token\");\n            if (!token) {\n                window.location.href = \"https://stage.ionm.zinniax.com/\";\n                setLoading(false);\n                return;\n            }\n            if (token) {\n                const result = (0,_helper__WEBPACK_IMPORTED_MODULE_4__.decodeJwt)(token);\n                if (result) {\n                    const email = result.payload['sub'];\n                    if (email) {\n                        let username = email.split('@')[0];\n                        let words = username.replace(/\\./g, ' ').split(' ');\n                        username = words.map({\n                            \"Home.useEffect\": (word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()\n                        }[\"Home.useEffect\"]).join(' ');\n                        localStorage.setItem(\"copilotkit_username\", username);\n                        setUsername(username);\n                    }\n                }\n            }\n            if (token) {\n                localStorage.setItem(\"copilotkit_token\", token);\n                const link = REMOTE_ACTION_URL + '/api/store-token';\n                fetch(link, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        token,\n                        sessionId\n                    }),\n                    credentials: \"include\"\n                }).then({\n                    \"Home.useEffect\": ()=>setLoading(false)\n                }[\"Home.useEffect\"]);\n            }\n            const storedUsername = localStorage.getItem(\"copilotkit_username\");\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"Home.useEffect\"], []);\n    // When user interacts, start fade-out\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (userInteracted) setHideSuggestions(true);\n        }\n    }[\"Home.useEffect\"], [\n        userInteracted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    const footer = document.querySelector(\".copilotKitInputContainer .poweredBy\");\n                    const container = document.querySelector(\".copilotKitInputContainer\");\n                    if (footer && container) {\n                        // Prevent adding multiple overlays\n                        if (!container.querySelector(\".poweredByCover\")) {\n                            var _footer_parentElement;\n                            const overlay = document.createElement(\"div\");\n                            overlay.className = \"poweredByCover\";\n                            Object.assign(overlay.style, {\n                                position: \"absolute\",\n                                top: \"0\",\n                                left: \"0\",\n                                right: \"0\",\n                                bottom: \"0\",\n                                background: \"white\",\n                                zIndex: \"2\"\n                            });\n                            const wrapper = document.createElement(\"div\");\n                            wrapper.style.position = \"relative\";\n                            wrapper.appendChild(overlay);\n                            (_footer_parentElement = footer.parentElement) === null || _footer_parentElement === void 0 ? void 0 : _footer_parentElement.insertBefore(wrapper, footer);\n                            wrapper.appendChild(footer);\n                        }\n                        clearInterval(interval); // once done, stop checking\n                    }\n                }\n            }[\"Home.useEffect.interval\"], 300); // Wait for CopilotChat to fully mount\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    // When fade-out animation ends, unmount suggestions\n    function handleSuggestionAnimationEnd() {\n        if (hideSuggestions) setSuggestionAnimationDone(true);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-10 w-10 border-t-4 border-blue-500 border-solid\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-600 text-sm\",\n                    children: \"Verifying session, please wait...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen w-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: \"/logo.png\",\n                alt: \"ZinniaX Logo\",\n                className: \"fixed top-4 left-4 h-10 w-auto \"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center pt-8 pb-2 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-blue-600 text-center\",\n                            children: \"ZinniaX Copilot\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-md text-gray-500 mb-2 text-center\",\n                        children: \"Your Everyday Copilot\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    !suggestionAnimationDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex flex-col items-center justify-center gap-4 px-4\",\n                        style: {\n                            paddingTop: '150px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Greeting, {\n                                animateOut: hideSuggestions,\n                                onAnimationEnd: handleSuggestionAnimationEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PromptButtons, {\n                                onUserInteracted: ()=>setUserInteracted(true),\n                                animateOut: hideSuggestions,\n                                onAnimationEnd: handleSuggestionAnimationEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex flex-col min-h-0 overflow-hidden items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-1 min-h-0 overflow-hidden w-full max-w-4xl\",\n                    style: {\n                        width: '60vw',\n                        minWidth: 320\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_8__.CopilotChat, {\n                            labels: {\n                                title: \"ZinniaX Copilot\",\n                                initial: \"\",\n                                placeholder: \"Ask Zinniax Copilot....\"\n                            },\n                            className: \"flex flex-col flex-1 min-h-0 overflow-hidden copilot-chat-with-suggestions\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\app\\\\page.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s1(Home, \"aeuXuI+suq4qfQixxadIOa9WntM=\", false, function() {\n    return [\n        _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCopilotChat\n    ];\n});\n_c2 = Home;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PromptButtons\");\n$RefreshReg$(_c1, \"Greeting\");\n$RefreshReg$(_c2, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ChatArea.tsx":
/*!*********************************!*\
  !*** ./components/ChatArea.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatArea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @copilotkit/react-core */ \"(app-pages-browser)/./node_modules/@copilotkit/react-core/dist/chunk-4DVPRMVH.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @copilotkit/react-core */ \"(app-pages-browser)/./node_modules/@copilotkit/react-core/dist/chunk-B5UA5G3E.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(app-pages-browser)/./node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CaseListDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CaseListDisplay */ \"(app-pages-browser)/./components/CaseListDisplay.tsx\");\n/* harmony import */ var _static_ConfirmationButtons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./static/ConfirmationButtons */ \"(app-pages-browser)/./components/static/ConfirmationButtons.tsx\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _HospitalListDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HospitalListDisplay */ \"(app-pages-browser)/./components/HospitalListDisplay.tsx\");\n/* harmony import */ var _DoctorListDisplay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./DoctorListDisplay */ \"(app-pages-browser)/./components/DoctorListDisplay.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatArea() {\n    _s();\n    const [selectedCases, setSelectedCases] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedHospitals, setSelectedHospitals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDoctors, setSelectedDoctors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedItems, setProcessedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const { appendMessage } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__.useCopilotChat)();\n    async function handleCancelCases(selected, caseKey) {\n        try {\n            const caseIds = selected.map((caseStr)=>{\n                var _caseStr_split_pop;\n                return (_caseStr_split_pop = caseStr.split(\" - \").pop()) === null || _caseStr_split_pop === void 0 ? void 0 : _caseStr_split_pop.trim();\n            }).filter(Boolean);\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: \"\".concat(caseIds),\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n            setSelectedCases([]);\n            setProcessedItems((prev)=>new Set([\n                    ...prev,\n                    caseKey\n                ]));\n        } catch (err) {\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: \"Error: \".concat(err.message),\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n        }\n    }\n    async function handleSelectedHospitals(selected, hospitalKey) {\n        try {\n            const selectedHospital = selected[0];\n            const displayMessage = \"hospitalId=\".concat(selectedHospital.id, \" \\ncity=\").concat(selectedHospital.city);\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: displayMessage,\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n            setSelectedHospitals(selected.map((item)=>item.id));\n            setProcessedItems((prev)=>new Set([\n                    ...prev,\n                    hospitalKey\n                ]));\n        } catch (err) {\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: \"Error: \".concat(err.message),\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n        }\n    }\n    async function handleSelectedDoctor(selected, DoctorKey) {\n        try {\n            const selectedDoctor = selected[0];\n            const displayMessage = \"doctorId = \".concat(selectedDoctor.id, \" \\nDoctor name = \").concat(selectedDoctor.name);\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: displayMessage,\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n            setSelectedDoctors(selected.map((item)=>item.id));\n            setProcessedItems((prev)=>new Set([\n                    ...prev,\n                    DoctorKey\n                ]));\n        } catch (err) {\n            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                content: \"Error: \".concat(err.message),\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n            }));\n        }\n    }\n    const rendered = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_8__.useCoAgentStateRender)({\n        name: \"scheduling_agent\",\n        render: {\n            \"ChatArea.useCoAgentStateRender[rendered]\": (param)=>{\n                let { state, status } = param;\n                var _state_showHospitals, _state_showDoctors;\n                if (status === \"inProgress\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 43\n                }, this);\n                // Create unique keys for current items\n                const hospitalKey = state.showHospitals ? \"hospitals_\".concat(state.showHospitals.map({\n                    \"ChatArea.useCoAgentStateRender[rendered]\": (h)=>h.id\n                }[\"ChatArea.useCoAgentStateRender[rendered]\"]).join('_')) : '';\n                const caseKey = state.showCase ? \"cases_\".concat(state.showCase.map({\n                    \"ChatArea.useCoAgentStateRender[rendered]\": (c)=>c.caseNo\n                }[\"ChatArea.useCoAgentStateRender[rendered]\"]).join('_')) : '';\n                const confirmationKey = state.confirmation_needed ? \"confirmation_\".concat(state.confirmation_message) : '';\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        state.frontend_tool == \"showHospitals\" && ((_state_showHospitals = state.showHospitals) === null || _state_showHospitals === void 0 ? void 0 : _state_showHospitals.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown, {\n                                    children: state.confirmation_message || \"Please select the hospital.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HospitalListDisplay__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    items: state.showHospitals,\n                                    selectedIdxs: selectedHospitals.map({\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (id)=>state.showHospitals.findIndex({\n                                                \"ChatArea.useCoAgentStateRender[rendered]\": (item)=>item.id === id\n                                            }[\"ChatArea.useCoAgentStateRender[rendered]\"])\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"]),\n                                    onSubmit: {\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (selected)=>handleSelectedHospitals(selected, hospitalKey)\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                                    disabled: processedItems.has(hospitalKey)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this) : null,\n                        state.frontend_tool == \"showDoctors\" && ((_state_showDoctors = state.showDoctors) === null || _state_showDoctors === void 0 ? void 0 : _state_showDoctors.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown, {\n                                    children: state.confirmation_message || \"Please select the hospital.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DoctorListDisplay__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    items: state.showDoctors,\n                                    selectedIdxs: selectedDoctors.map({\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (id)=>state.showDoctors.findIndex({\n                                                \"ChatArea.useCoAgentStateRender[rendered]\": (item)=>item.id === id\n                                            }[\"ChatArea.useCoAgentStateRender[rendered]\"])\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"]),\n                                    onSubmit: {\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (selected)=>handleSelectedDoctor(selected, hospitalKey)\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                                    disabled: processedItems.has(hospitalKey)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this) : null,\n                        state.frontend_tool == \"showCase\" && state.showCase ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CaseListDisplay__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            items: state.showCase.map({\n                                \"ChatArea.useCoAgentStateRender[rendered]\": (item)=>\"\".concat(item.doctorName, \" - \").concat(item.caseNo)\n                            }[\"ChatArea.useCoAgentStateRender[rendered]\"]),\n                            selectedIdxs: selectedCases.map({\n                                \"ChatArea.useCoAgentStateRender[rendered]\": (caseStr)=>state.showCase.findIndex({\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": (item)=>\"\".concat(item.doctorName, \" - \").concat(item.caseNo) === caseStr\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"])\n                            }[\"ChatArea.useCoAgentStateRender[rendered]\"]),\n                            onSubmit: {\n                                \"ChatArea.useCoAgentStateRender[rendered]\": async (selected)=>{\n                                    setSelectedCases(selected);\n                                    await handleCancelCases(selected, caseKey);\n                                }\n                            }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                            disabled: processedItems.has(caseKey)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this) : null,\n                        state.frontend_tool == \"confirmation\" && state.confirmation_needed == true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: 24\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown, {\n                                    children: state.confirmation_message\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_static_ConfirmationButtons__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onYes: {\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": ()=>{\n                                            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                                                content: \"yes\",\n                                                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n                                            }));\n                                            setProcessedItems({\n                                                \"ChatArea.useCoAgentStateRender[rendered]\": (prev)=>new Set([\n                                                        ...prev,\n                                                        confirmationKey\n                                                    ])\n                                            }[\"ChatArea.useCoAgentStateRender[rendered]\"]);\n                                        }\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                                    onNo: {\n                                        \"ChatArea.useCoAgentStateRender[rendered]\": ()=>{\n                                            appendMessage(new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                                                content: \"no\",\n                                                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.User\n                                            }));\n                                            setProcessedItems({\n                                                \"ChatArea.useCoAgentStateRender[rendered]\": (prev)=>new Set([\n                                                        ...prev,\n                                                        confirmationKey\n                                                    ])\n                                            }[\"ChatArea.useCoAgentStateRender[rendered]\"]);\n                                        }\n                                    }[\"ChatArea.useCoAgentStateRender[rendered]\"],\n                                    disabled: processedItems.has(confirmationKey)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\ChatArea.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this);\n            }\n        }[\"ChatArea.useCoAgentStateRender[rendered]\"]\n    });\n    return rendered !== null && rendered !== void 0 ? rendered : null;\n}\n_s(ChatArea, \"WyB7h38PYswftCvX0ycNeA/74gg=\", false, function() {\n    return [\n        _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__.useCopilotChat,\n        _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_8__.useCoAgentStateRender\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ChatArea.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/HospitalListDisplay.tsx":
/*!********************************************!*\
  !*** ./components/HospitalListDisplay.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst HospitalListDisplay = (param)=>{\n    let { items, onSubmit, disabled, selectedIdxs = [] } = param;\n    _s();\n    const [selectedIndices, setSelectedIndices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedIdxs);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedIdxs.length > 0 ? selectedIdxs[0] : null);\n    const toggleSelection = (idx)=>{\n        if (disabled) return;\n        setSelectedIndex(idx);\n    };\n    const handleSubmit = ()=>{\n        if (disabled || !onSubmit || selectedIndex === null) return;\n        const selectedItem = items[selectedIndex];\n        onSubmit([\n            selectedItem\n        ]);\n    };\n    if (!items || items.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"No hospitals to display.\"\n        }, void 0, false, {\n            fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n            lineNumber: 40,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: 24,\n            background: \"#fff\",\n            borderRadius: 16,\n            boxShadow: \"0 4px 24px rgba(0,0,0,0.09)\",\n            maxWidth: 400,\n            border: \"1px solid #e5e7eb\",\n            opacity: disabled ? 0.6 : 1,\n            pointerEvents: disabled ? \"none\" : \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                style: {\n                    fontSize: 20,\n                    fontWeight: 600,\n                    marginBottom: 12\n                },\n                children: \"Select Hospitals\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                style: {\n                    paddingLeft: 0,\n                    margin: 0,\n                    maxHeight: 240,\n                    overflowY: \"auto\"\n                },\n                children: items.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        onClick: ()=>toggleSelection(idx),\n                        style: {\n                            margin: \"10px 0\",\n                            padding: \"8px 12px\",\n                            background: \"#f3f4f6\",\n                            borderRadius: 8,\n                            listStyle: \"none\",\n                            fontSize: 14,\n                            cursor: \"pointer\",\n                            fontWeight: selectedIndex === idx ? 600 : 400,\n                            boxShadow: selectedIndex === idx ? \"0 2px 8px rgba(56,189,248,0.15)\" : undefined\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                checked: selectedIndex === idx,\n                                onChange: ()=>toggleSelection(idx),\n                                onClick: (e)=>e.stopPropagation(),\n                                disabled: disabled\n                            }, void 0, false, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginLeft: 8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontWeight: 600\n                                        },\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: 12,\n                                            color: \"#555\"\n                                        },\n                                        children: [\n                                            item.city,\n                                            \" | \",\n                                            item.region\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: 12,\n                                            color: \"#888\"\n                                        },\n                                        children: item.address\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, item.id, true, {\n                        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSubmit,\n                style: {\n                    marginTop: 16,\n                    padding: \"8px 12px\",\n                    backgroundColor: \"#38bdf8\",\n                    border: \"none\",\n                    borderRadius: 8,\n                    color: \"#fff\",\n                    fontWeight: 600,\n                    cursor: disabled ? \"not-allowed\" : \"pointer\",\n                    width: \"100%\"\n                },\n                disabled: disabled,\n                children: \"Submit Selected\"\n            }, void 0, false, {\n                fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\zinniax-copilot\\\\zinniax-copilot\\\\ui\\\\components\\\\HospitalListDisplay.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HospitalListDisplay, \"rP3lJ3QjFPKvCVqiCFyaVc6Tn6A=\");\n_c = HospitalListDisplay;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HospitalListDisplay);\nvar _c;\n$RefreshReg$(_c, \"HospitalListDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HospitalListDisplay.tsx\n"));

/***/ })

});