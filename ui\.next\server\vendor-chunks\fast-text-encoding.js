/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-text-encoding";
exports.ids = ["vendor-chunks/fast-text-encoding"];
exports.modules = {

/***/ "(rsc)/./node_modules/fast-text-encoding/text.min.js":
/*!*****************************************************!*\
  !*** ./node_modules/fast-text-encoding/text.min.js ***!
  \*****************************************************/
/***/ (function() {

eval("(function(scope) {'use strict';\nfunction B(r,e){var f;return r instanceof Buffer?f=r:f=Buffer.from(r.buffer,r.byteOffset,r.byteLength),f.toString(e)}var w=function(r){return Buffer.from(r)};function h(r){for(var e=0,f=Math.min(256*256,r.length+1),n=new Uint16Array(f),i=[],o=0;;){var t=e<r.length;if(!t||o>=f-1){var s=n.subarray(0,o),m=s;if(i.push(String.fromCharCode.apply(null,m)),!t)return i.join(\"\");r=r.subarray(e),e=0,o=0}var a=r[e++];if((a&128)===0)n[o++]=a;else if((a&224)===192){var d=r[e++]&63;n[o++]=(a&31)<<6|d}else if((a&240)===224){var d=r[e++]&63,l=r[e++]&63;n[o++]=(a&31)<<12|d<<6|l}else if((a&248)===240){var d=r[e++]&63,l=r[e++]&63,R=r[e++]&63,c=(a&7)<<18|d<<12|l<<6|R;c>65535&&(c-=65536,n[o++]=c>>>10&1023|55296,c=56320|c&1023),n[o++]=c}}}function F(r){for(var e=0,f=r.length,n=0,i=Math.max(32,f+(f>>>1)+7),o=new Uint8Array(i>>>3<<3);e<f;){var t=r.charCodeAt(e++);if(t>=55296&&t<=56319){if(e<f){var s=r.charCodeAt(e);(s&64512)===56320&&(++e,t=((t&1023)<<10)+(s&1023)+65536)}if(t>=55296&&t<=56319)continue}if(n+4>o.length){i+=8,i*=1+e/r.length*2,i=i>>>3<<3;var m=new Uint8Array(i);m.set(o),o=m}if((t&4294967168)===0){o[n++]=t;continue}else if((t&4294965248)===0)o[n++]=t>>>6&31|192;else if((t&4294901760)===0)o[n++]=t>>>12&15|224,o[n++]=t>>>6&63|128;else if((t&4292870144)===0)o[n++]=t>>>18&7|240,o[n++]=t>>>12&63|128,o[n++]=t>>>6&63|128;else continue;o[n++]=t&63|128}return o.slice?o.slice(0,n):o.subarray(0,n)}var u=\"Failed to \",p=function(r,e,f){if(r)throw new Error(\"\".concat(u).concat(e,\": the '\").concat(f,\"' option is unsupported.\"))};var x=typeof Buffer==\"function\"&&Buffer.from;var A=x?w:F;function v(){this.encoding=\"utf-8\"}v.prototype.encode=function(r,e){return p(e&&e.stream,\"encode\",\"stream\"),A(r)};function U(r){var e;try{var f=new Blob([r],{type:\"text/plain;charset=UTF-8\"});e=URL.createObjectURL(f);var n=new XMLHttpRequest;return n.open(\"GET\",e,!1),n.send(),n.responseText}finally{e&&URL.revokeObjectURL(e)}}var O=!x&&typeof Blob==\"function\"&&typeof URL==\"function\"&&typeof URL.createObjectURL==\"function\",S=[\"utf-8\",\"utf8\",\"unicode-1-1-utf-8\"],T=h;x?T=B:O&&(T=function(r){try{return U(r)}catch(e){return h(r)}});var y=\"construct 'TextDecoder'\",E=\"\".concat(u,\" \").concat(y,\": the \");function g(r,e){p(e&&e.fatal,y,\"fatal\"),r=r||\"utf-8\";var f;if(x?f=Buffer.isEncoding(r):f=S.indexOf(r.toLowerCase())!==-1,!f)throw new RangeError(\"\".concat(E,\" encoding label provided ('\").concat(r,\"') is invalid.\"));this.encoding=r,this.fatal=!1,this.ignoreBOM=!1}g.prototype.decode=function(r,e){p(e&&e.stream,\"decode\",\"stream\");var f;return r instanceof Uint8Array?f=r:r.buffer instanceof ArrayBuffer?f=new Uint8Array(r.buffer):f=new Uint8Array(r),T(f,this.encoding)};scope.TextEncoder=scope.TextEncoder||v;scope.TextDecoder=scope.TextDecoder||g;\n}(typeof window !== 'undefined' ? window : (typeof global !== 'undefined' ? global : this)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmFzdC10ZXh0LWVuY29kaW5nL3RleHQubWluLmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQjtBQUNsQixnQkFBZ0IsTUFBTSwrRkFBK0Ysa0JBQWtCLHVCQUF1QixjQUFjLDBFQUEwRSxFQUFFLGlCQUFpQixlQUFlLDBCQUEwQixrRUFBa0Usd0JBQXdCLGFBQWEsd0JBQXdCLHVCQUF1QixnQkFBZ0IsbUJBQW1CLHVCQUF1Qiw0QkFBNEIseUJBQXlCLHVCQUF1QixpRUFBaUUsdUVBQXVFLGNBQWMsaUZBQWlGLElBQUksRUFBRSx3QkFBd0IsdUJBQXVCLFFBQVEsc0JBQXNCLHlEQUF5RCwrQkFBK0IsaUJBQWlCLGtDQUFrQyx3QkFBd0IsYUFBYSx1QkFBdUIsU0FBUyxTQUFTLCtDQUErQyxvRUFBb0Usd0ZBQXdGLGNBQWMsZ0JBQWdCLDRDQUE0QyxxQ0FBcUMsNkZBQTZGLDZDQUE2QyxZQUFZLGFBQWEsc0JBQXNCLGlDQUFpQyw4Q0FBOEMsY0FBYyxNQUFNLElBQUksb0JBQW9CLGlCQUFpQixlQUFlLEVBQUUseUJBQXlCLHlCQUF5QixrREFBa0QsUUFBUSwyQkFBMkIsNklBQTZJLHdCQUF3QixJQUFJLFlBQVksU0FBUyxhQUFhLEVBQUUsc0VBQXNFLGdCQUFnQixxQ0FBcUMsTUFBTSw2SkFBNkosZ0RBQWdELGlDQUFpQyxpQ0FBaUMsTUFBTSxzSUFBc0ksdUNBQXVDO0FBQ3hvRixDQUFDIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGZhc3QtdGV4dC1lbmNvZGluZ1xcdGV4dC5taW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiKGZ1bmN0aW9uKHNjb3BlKSB7J3VzZSBzdHJpY3QnO1xuZnVuY3Rpb24gQihyLGUpe3ZhciBmO3JldHVybiByIGluc3RhbmNlb2YgQnVmZmVyP2Y9cjpmPUJ1ZmZlci5mcm9tKHIuYnVmZmVyLHIuYnl0ZU9mZnNldCxyLmJ5dGVMZW5ndGgpLGYudG9TdHJpbmcoZSl9dmFyIHc9ZnVuY3Rpb24ocil7cmV0dXJuIEJ1ZmZlci5mcm9tKHIpfTtmdW5jdGlvbiBoKHIpe2Zvcih2YXIgZT0wLGY9TWF0aC5taW4oMjU2KjI1NixyLmxlbmd0aCsxKSxuPW5ldyBVaW50MTZBcnJheShmKSxpPVtdLG89MDs7KXt2YXIgdD1lPHIubGVuZ3RoO2lmKCF0fHxvPj1mLTEpe3ZhciBzPW4uc3ViYXJyYXkoMCxvKSxtPXM7aWYoaS5wdXNoKFN0cmluZy5mcm9tQ2hhckNvZGUuYXBwbHkobnVsbCxtKSksIXQpcmV0dXJuIGkuam9pbihcIlwiKTtyPXIuc3ViYXJyYXkoZSksZT0wLG89MH12YXIgYT1yW2UrK107aWYoKGEmMTI4KT09PTApbltvKytdPWE7ZWxzZSBpZigoYSYyMjQpPT09MTkyKXt2YXIgZD1yW2UrK10mNjM7bltvKytdPShhJjMxKTw8NnxkfWVsc2UgaWYoKGEmMjQwKT09PTIyNCl7dmFyIGQ9cltlKytdJjYzLGw9cltlKytdJjYzO25bbysrXT0oYSYzMSk8PDEyfGQ8PDZ8bH1lbHNlIGlmKChhJjI0OCk9PT0yNDApe3ZhciBkPXJbZSsrXSY2MyxsPXJbZSsrXSY2MyxSPXJbZSsrXSY2MyxjPShhJjcpPDwxOHxkPDwxMnxsPDw2fFI7Yz42NTUzNSYmKGMtPTY1NTM2LG5bbysrXT1jPj4+MTAmMTAyM3w1NTI5NixjPTU2MzIwfGMmMTAyMyksbltvKytdPWN9fX1mdW5jdGlvbiBGKHIpe2Zvcih2YXIgZT0wLGY9ci5sZW5ndGgsbj0wLGk9TWF0aC5tYXgoMzIsZisoZj4+PjEpKzcpLG89bmV3IFVpbnQ4QXJyYXkoaT4+PjM8PDMpO2U8Zjspe3ZhciB0PXIuY2hhckNvZGVBdChlKyspO2lmKHQ+PTU1Mjk2JiZ0PD01NjMxOSl7aWYoZTxmKXt2YXIgcz1yLmNoYXJDb2RlQXQoZSk7KHMmNjQ1MTIpPT09NTYzMjAmJigrK2UsdD0oKHQmMTAyMyk8PDEwKSsocyYxMDIzKSs2NTUzNil9aWYodD49NTUyOTYmJnQ8PTU2MzE5KWNvbnRpbnVlfWlmKG4rND5vLmxlbmd0aCl7aSs9OCxpKj0xK2Uvci5sZW5ndGgqMixpPWk+Pj4zPDwzO3ZhciBtPW5ldyBVaW50OEFycmF5KGkpO20uc2V0KG8pLG89bX1pZigodCY0Mjk0OTY3MTY4KT09PTApe29bbisrXT10O2NvbnRpbnVlfWVsc2UgaWYoKHQmNDI5NDk2NTI0OCk9PT0wKW9bbisrXT10Pj4+NiYzMXwxOTI7ZWxzZSBpZigodCY0Mjk0OTAxNzYwKT09PTApb1tuKytdPXQ+Pj4xMiYxNXwyMjQsb1tuKytdPXQ+Pj42JjYzfDEyODtlbHNlIGlmKCh0JjQyOTI4NzAxNDQpPT09MClvW24rK109dD4+PjE4Jjd8MjQwLG9bbisrXT10Pj4+MTImNjN8MTI4LG9bbisrXT10Pj4+NiY2M3wxMjg7ZWxzZSBjb250aW51ZTtvW24rK109dCY2M3wxMjh9cmV0dXJuIG8uc2xpY2U/by5zbGljZSgwLG4pOm8uc3ViYXJyYXkoMCxuKX12YXIgdT1cIkZhaWxlZCB0byBcIixwPWZ1bmN0aW9uKHIsZSxmKXtpZihyKXRocm93IG5ldyBFcnJvcihcIlwiLmNvbmNhdCh1KS5jb25jYXQoZSxcIjogdGhlICdcIikuY29uY2F0KGYsXCInIG9wdGlvbiBpcyB1bnN1cHBvcnRlZC5cIikpfTt2YXIgeD10eXBlb2YgQnVmZmVyPT1cImZ1bmN0aW9uXCImJkJ1ZmZlci5mcm9tO3ZhciBBPXg/dzpGO2Z1bmN0aW9uIHYoKXt0aGlzLmVuY29kaW5nPVwidXRmLThcIn12LnByb3RvdHlwZS5lbmNvZGU9ZnVuY3Rpb24ocixlKXtyZXR1cm4gcChlJiZlLnN0cmVhbSxcImVuY29kZVwiLFwic3RyZWFtXCIpLEEocil9O2Z1bmN0aW9uIFUocil7dmFyIGU7dHJ5e3ZhciBmPW5ldyBCbG9iKFtyXSx7dHlwZTpcInRleHQvcGxhaW47Y2hhcnNldD1VVEYtOFwifSk7ZT1VUkwuY3JlYXRlT2JqZWN0VVJMKGYpO3ZhciBuPW5ldyBYTUxIdHRwUmVxdWVzdDtyZXR1cm4gbi5vcGVuKFwiR0VUXCIsZSwhMSksbi5zZW5kKCksbi5yZXNwb25zZVRleHR9ZmluYWxseXtlJiZVUkwucmV2b2tlT2JqZWN0VVJMKGUpfX12YXIgTz0heCYmdHlwZW9mIEJsb2I9PVwiZnVuY3Rpb25cIiYmdHlwZW9mIFVSTD09XCJmdW5jdGlvblwiJiZ0eXBlb2YgVVJMLmNyZWF0ZU9iamVjdFVSTD09XCJmdW5jdGlvblwiLFM9W1widXRmLThcIixcInV0ZjhcIixcInVuaWNvZGUtMS0xLXV0Zi04XCJdLFQ9aDt4P1Q9QjpPJiYoVD1mdW5jdGlvbihyKXt0cnl7cmV0dXJuIFUocil9Y2F0Y2goZSl7cmV0dXJuIGgocil9fSk7dmFyIHk9XCJjb25zdHJ1Y3QgJ1RleHREZWNvZGVyJ1wiLEU9XCJcIi5jb25jYXQodSxcIiBcIikuY29uY2F0KHksXCI6IHRoZSBcIik7ZnVuY3Rpb24gZyhyLGUpe3AoZSYmZS5mYXRhbCx5LFwiZmF0YWxcIikscj1yfHxcInV0Zi04XCI7dmFyIGY7aWYoeD9mPUJ1ZmZlci5pc0VuY29kaW5nKHIpOmY9Uy5pbmRleE9mKHIudG9Mb3dlckNhc2UoKSkhPT0tMSwhZil0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIlwiLmNvbmNhdChFLFwiIGVuY29kaW5nIGxhYmVsIHByb3ZpZGVkICgnXCIpLmNvbmNhdChyLFwiJykgaXMgaW52YWxpZC5cIikpO3RoaXMuZW5jb2Rpbmc9cix0aGlzLmZhdGFsPSExLHRoaXMuaWdub3JlQk9NPSExfWcucHJvdG90eXBlLmRlY29kZT1mdW5jdGlvbihyLGUpe3AoZSYmZS5zdHJlYW0sXCJkZWNvZGVcIixcInN0cmVhbVwiKTt2YXIgZjtyZXR1cm4gciBpbnN0YW5jZW9mIFVpbnQ4QXJyYXk/Zj1yOnIuYnVmZmVyIGluc3RhbmNlb2YgQXJyYXlCdWZmZXI/Zj1uZXcgVWludDhBcnJheShyLmJ1ZmZlcik6Zj1uZXcgVWludDhBcnJheShyKSxUKGYsdGhpcy5lbmNvZGluZyl9O3Njb3BlLlRleHRFbmNvZGVyPXNjb3BlLlRleHRFbmNvZGVyfHx2O3Njb3BlLlRleHREZWNvZGVyPXNjb3BlLlRleHREZWNvZGVyfHxnO1xufSh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdyA6ICh0eXBlb2YgZ2xvYmFsICE9PSAndW5kZWZpbmVkJyA/IGdsb2JhbCA6IHRoaXMpKSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-text-encoding/text.min.js\n");

/***/ })

};
;