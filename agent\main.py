from fastapi import <PERSON><PERSON><PERSON>, Request, Response, <PERSON><PERSON>
from typing import Annotated, Dict, Any, Literal, Optional
import uuid
from fastapi.middleware.cors import CORSMiddleware
from copilotkit.integrations.fastapi import add_fastapi_endpoint
from copilotkit import CopilotKitRemoteEndpoint, LangGraphAgent
import uvicorn
from datetime import datetime
import os
import asyncio
import re
import json
import logging
from typing import Literal, Optional, Dict, Any
from langchain_openai import ChatOpenA<PERSON>
from langchain_core.messages import SystemMessage, AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.prebuilt import Tool<PERSON>ode
from copilotkit import CopilotKitState
from apis import ZinniaXAPI
from langchain_ollama import ChatOllama
from langgraph.checkpoint.memory import MemorySaver
from tools import (
    get_schedule_details, get_cancel_details_id, get_uncancel_details,
    validate_schedule_details, validate_cancel_details, get_cancel_details_doctorname_date,
    validate_cancel_reschedule_doctor_details, validate_uncancel_details,
    get_reschedule_case_details, validate_reschedule_details, get_reschedule_details_doctorname_date
)
from helper import decode_jwt_no_secret, bind_thread_to_user, get_username_for_thread, add_token_usage_for_user, load_json_file


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Configuration ---
STAGE_URL = os.environ.get("STAGE_URL")


async def get_user_token_from_config(config: RunnableConfig) -> Optional[str]:
    """Safely get or store user token using thread_id as the key in a JSON file."""
    try:
        print("\n==============================\n🤖 Starting FUNCTION: get_user_token_from_config\n==============================\n")           
        
        print('⚙️'*100)
        print('CONFIG', config)
        print('⚙️'*100)


        data = config["metadata"]
        thread_id = data.get("thread_id")
        if not thread_id:
            print("Warning: thread_id not found in metadata.")
            return None, None
            

        # Load existing thread_id to token mapping
        tokens_path = "tokens.json"
        tokens = {}
        if os.path.exists(tokens_path):
            try:
                with open(tokens_path, "r") as f:
                    tokens = json.load(f)
            except Exception as e:
                print(f"Error reading tokens.json: {e}")

        # If token already exists for this thread_id, return it
        if thread_id in tokens.keys():
            return None, tokens[thread_id]
            

        # Extract session_id and user_token from metadata
        # Assumes the first item (after 'thread_id') is the session_id: token pair

        print('⚙️'*100)
        # print('CONFIG DATA', data)
        print('⚙️'*100)
        
        session_id, user_token = next(
            ((k, v) for k, v in data.items() if k != "thread_id"), (None, None)
        )

        if not user_token:
            print("Warning: user_token not found in metadata.")
            return None, None
            

        username = decode_jwt_no_secret(user_token)
        if not username:
            print("No UserName found with this token.")
            return None, None
            
        print('THREAD ID',thread_id) 
        # Store the thread_id: user_token mapping
        tokens[thread_id] = user_token
        with open(tokens_path, "w") as f:
            json.dump(tokens, f, indent=2)

        # Bind thread_id to username
        bind_thread_to_user(thread_id, username)

        return username, user_token

        

    except KeyError:
        print("Warning: Could not find metadata in config.")
        return None, None

class AgentState(CopilotKitState):
    """
    State management for the scheduling agent
    """

    confirmation_needed: bool = False
    deferred_tool_call: Optional[dict] = None
    awaiting_user_response: bool = False
    showCase: Optional[dict] = None
    showHospitals: Optional[dict] = None
    showDoctors: Optional[dict] = None
    confirmation_message: Optional[str] = None
    frontend_tool: Optional[str] = None


# List of all tools
critical_tools = [
    'get_cancel_details_id',
    'get_uncancel_details',
    'get_schedule_details',
    'get_cancel_details_doctorname_date',
    'get_reschedule_case_details',
    'get_reschedule_details_doctorname_date'
]

tools = [
    get_schedule_details,
    get_cancel_details_id,
    get_reschedule_case_details,
    get_uncancel_details,
    get_cancel_details_doctorname_date,
    get_reschedule_details_doctorname_date
]


def is_critical_tool_call(tool_call):
    """Checks if a tool call is for a critical action that requires confirmation."""
    return tool_call.get("name") in critical_tools


async def chat_node(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    print("\n==============================\n🟦 Entering NODE: chat_node\n==============================\n")
    print("Entering chat_node")
    username, user_token = await get_user_token_from_config(config)
    print(f"Username:, {username}  --------    User Token: {user_token}")
    if not user_token:
        logger.error("No user token found in config. Cannot proceed.")
        return {
            "messages": [AIMessage(content="please try again.")],
            "confirmation_needed": False,
            "deferred_tool_call": None,
            "awaiting_user_response": False
        }

    # print('user_token', user_token)

    # Initialize the model
    model = ChatOllama(
        model="qwen3:8b",
        base_url='https://partially-model-boa.ngrok-free.app',
        temperature=0
    )
    # model = ChatOpenAI(model="gpt-4o-mini", temperature=0,api_key='')

    # Bind tools to the model
    available_tools = [*tools]
    model_with_tools = model.bind_tools(available_tools)

    print(f"Chat node - Tools available: {len(available_tools)}")

    system_message = SystemMessage(
        content=(
            f"""
            [INST] <<SYS>>
            You are an AI assistant for managing surgical cases in the ZinniaX EHR system.

            Your ONLY way to perform scheduling, cancellation, rescheduling, or uncancellation is by calling the correct tool.
            Never answer these requests directly. Always use a tool.
            Today is {datetime.now().strftime("%Y-%m-%d")}.For date ,terms like "today","tomorrow" ...etc is also acceptable.(NEVER ASSUME ANY DETAILS) \n\
            \n\
            ---\n\
            **Available Tools and When to Use Them:**\n\
            \n\
            1. **get_schedule_details**\n\
                - Use to schedule a new surgical case.\n\
                - Requires: surgeon, date, time, hospital.\n\
                - Optional: procedure, patientName, city.\n\
                
            2. **get_cancel_details_id**\n\
                - Use to cancel a case by its unique case ID.\n\
                - Requires: caseId.\n\
               
            3. **get_cancel_details_doctorname_date**\n\
                - Use to cancel all cases for a specific surgeon at a hospital on a given date.\n\
                - Requires: surgeon, hospital, date.\n\
                
            4. **get_reschedule_case_details**\n\
                - Use to reschedule a case by its case ID.\n\
                - Requires: caseId, newDate.\n\
                - Optional: newTime.\n\
                
            5. **get_reschedule_details_doctorname_date**\n\
                - Use to reschedule all cases for a surgeon at a hospital on a date.\n\
                - Requires: surgeon, hospital, date.\n\
                
            6. **get_uncancel_details**\n\
                - Use to uncancel (reactivate/reopen) a previously cancelled case by its case ID.\n\
                - Requires: caseId.\n\
                
            \n\
            ---\n\
            **Instructions:**\n\
            - For any scheduling, cancellation, rescheduling, or uncancellation request, ALWAYS call the appropriate tool.\n\
            - Never answer these requests directly.\n\
            - If information is missing, ask the user for the required details.\n\
            - Never combine actions. Handle one request at a time.\n\
            - If a user asks for something outside these actions, you may answer directly.\n\
            \n\
            **Examples:**\n\
            - User: "Schedule a case for Dr. Lee at General Hospital on June 12 at 8am."\n\
              -> Call get_schedule_details with surgeon="Dr. Lee", hospital="General Hospital", date="2024-06-12", time="08:00"\n\
            - User: "Cancel case 78910." or "106418 - AHMC - cancelled\n\
              -> Call get_cancel_details_id with caseId="78910"\n\
            - User: "Reschedule Dr. Patel's cases at Mercy Hospital on July 1."\n\
              -> Call get_reschedule_details_doctorname_date with surgeon="Dr. Patel", hospital="Mercy Hospital", date="2024-07-01"\n\
            \n\
            ---\n\
            **Remember:**\n\
            - You must always use a tool for any scheduling, cancellation, rescheduling, or uncancellation.\n\
            - If you are unsure which tool to use, ask the user for clarification.\n\
            """
        )
    )

    # Get the response from the model
    response = await model_with_tools.ainvoke([system_message, *state["messages"]], config)

    # Handle tool calls
    if isinstance(response, AIMessage) and response.tool_calls:
        print('RESPONSE MAIN --> ', response.usage_metadata['total_tokens'])
        tokens_used = response.usage_metadata.get('total_tokens', 0)
        thread_id = config["metadata"].get("thread_id", "unknown")
        username = get_username_for_thread(thread_id)

        print('👾'*100)        
        print('USERNAME', username)
        print('THREAD ID', thread_id)
        print('👾'*100)

        if username != None:
            add_token_usage_for_user(username, tokens_used)
        tool_call = response.tool_calls[0]
        print(f"Model selected tool: {tool_call['name']}")

        # For critical tools, initiate confirmation workflow
        if is_critical_tool_call(tool_call):
            args = tool_call.get("args", {})
            tool_name = tool_call["name"]

            # Validation and confirmation logic
            if tool_name == "get_schedule_details":
                flag, details = await validate_schedule_details(**args, token=user_token)
                if flag == False:
                    if isinstance(details, list):
                        return {

                            "messages": [],
                            "showHospitals": details,
                            "frontend_tool": "showHospitals",
                            "confirmation_message": "Multiple Hospitals Found with Same Name."

                        }

                    else:
                        return {
                            "messages": [*state["messages"], AIMessage(content=details)]
                        }
                
                elif flag == "MutliDotorName":
                    if isinstance(details, list):
                        return {

                            "messages": [],
                            "showDoctors": details,
                            "frontend_tool": "showDoctors",
                            "confirmation_message": "Multiple Doctors Found with Same Name."

                        }

                    else:
                        return {
                            "messages": [*state["messages"], AIMessage(content=details)]
                        }
                    

                tool_call["args"] = details
                confirmation_message = await generate_confirmation_message(tool_call, user_token)
                return {

                    "messages": [],
                    "confirmation_needed": True,
                    "showHospitals": [],
                    "showDoctors": [],
                    "deferred_tool_call": tool_call,
                    "frontend_tool": "confirmation",
                    "awaiting_user_response": True,
                    "confirmation_message": confirmation_message
                }

            elif tool_name == "get_cancel_details_id":
                flag, details = await validate_cancel_details(args.get('caseId'), token=user_token)
                if not flag:
                    return {

                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                tool_call["args"] = details

                confirmation_message = await generate_confirmation_message(tool_call, user_token)

                return {

                    "messages": [],
                    "confirmation_needed": True,
                    "deferred_tool_call": tool_call,
                    "frontend_tool": "confirmation",
                    "awaiting_user_response": True,
                    "confirmation_message": confirmation_message
                }

            elif tool_name in ["get_cancel_details_doctorname_date", 'get_reschedule_details_doctorname_date']:
                flag, details = await validate_cancel_reschedule_doctor_details(
                    args.get('surgeon'), args.get('hospital'), args.get('date'), token=user_token
                )

                if flag == False:
                    # Multiple cases found - show them for selection

                    return {
                        "messages": [AIMessage(content=details)],
                        "confirmation_needed": False,
                        "deferred_tool_call": None,
                        "awaiting_user_response": False,
                        "showCase": []

                    }
                if flag == True:
                    # Single case found - proceed to confirmation
                    tool_call["args"] = details
                    if tool_name == "get_cancel_details_doctorname_date":
                        confirmation_message = await generate_confirmation_message(tool_call, user_token)
                        return {
                            "messages": [*state["messages"], AIMessage(content=confirmation_message)],
                            "confirmation_needed": True,
                            "deferred_tool_call": {"name": "get_cancel_details_id", "args": details},
                            "awaiting_user_response": True,
                            "showCase": details,
                            "frontend_tool": "showCase",

                            "confirmation_message": confirmation_message

                        }
                    else:
                        confirmation_message = await generate_confirmation_message(tool_call, user_token)
                        return {
                            "messages": [*state["messages"], AIMessage(content=confirmation_message)],
                            "confirmation_needed": True,
                            "deferred_tool_call": {"name": "get_reschedule_case_details", "args": details},
                            "awaiting_user_response": True,
                            "showCase": details,
                            "frontend_tool": "showCase",
                            "confirmation_message": confirmation_message

                        }
            elif tool_name == 'get_reschedule_case_details':
                flag, details = await validate_reschedule_details(
                    args.get('caseId'), args.get('newDate'), args.get('newTime'), token=user_token
                )
                if not flag:
                    return {

                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                confirmation_message = await generate_confirmation_message(tool_call, user_token)
                return {

                    "messages": [],
                    "confirmation_needed": True,
                    "deferred_tool_call": tool_call,
                    "awaiting_user_response": True,
                    "frontend_tool": "confirmation",
                    "confirmation_message": confirmation_message
                }

            elif tool_name == "get_uncancel_details":
                flag, details = await validate_uncancel_details(args.get('caseId'), token=user_token)
                if not flag:
                    return {

                        "messages": [*state["messages"], AIMessage(content=details)]
                    }
                tool_call["args"] = details
                confirmation_message = await generate_confirmation_message(tool_call, user_token)
                return {

                    "messages": [],
                    "confirmation_needed": True,
                    "deferred_tool_call": tool_call,
                    "awaiting_user_response": True,
                    "frontend_tool": "confirmation",
                    "confirmation_message": confirmation_message
                }
        else:
            # For non-critical tools, proceed normally
            return {

                "messages": [*state["messages"], response]
            }

    # If no tool call, just return the model's response
    # print('RESPONSE --> ', response.usage_metadata['total_tokens'])

    tokens_used = response.usage_metadata.get('total_tokens', 0)
    thread_id = config["metadata"].get("thread_id", "unknown")
    username = get_username_for_thread(thread_id)
    if username:
        add_token_usage_for_user(username, tokens_used)
    cleaned = re.sub(r'<think>.*?</think>\s*', '',
                     response.content, flags=re.DOTALL)
    print('CLEANED RESPONSE --> ', cleaned)

    result = {
        "messages": [*state["messages"], AIMessage(content=cleaned)],
    }
    print(f"🔍 CHAT_NODE RETURNING: {result}")
    return result


async def generate_confirmation_message(tool_call: dict, user_token) -> str:

    print("\n==============================\n🟦 Entering FUNCTION: generate_confirmation_message\n==============================\n")
    """Generate a user-friendly confirmation message for a given tool call."""
    args = tool_call.get("args", {})
    tool_name = tool_call["name"]

    # Generate confirmation messages based on tool type
    prompts = {
        "get_cancel_details_doctorname_date": "Multiple cases found. Please select which case to cancel.",
        "get_reschedule_details_doctorname_date": "Multiple cases found. Please select which case to reschedule."
    }
    if tool_name == 'get_schedule_details':
        details = {
                        'Hospital Name': args.get('hospital', 'N/A'),
                        'Surgeon Name': args.get('surgeon', 'N/A'),
                        'Procedure': args.get('procedure', 'N/A'),
                        'Scheduled on': args.get('date', 'N/A') + " at " + args.get('time', 'N/A'),                       
                        'Patient Name': args.get('patientName', 'N/A'),
                        
                    }
        details_text = "\n".join([
            f"- **{key}**: {val}"
            for key, val in details.items()
            if val and str(val).strip().upper() != 'N/A'
        ])
        return (
            "Are you sure you want to Schedule a case with following details:\n\n\n"
            "📋 **Case Details**:\n"
            f"{details_text}"
        )

    if tool_name in prompts:
        return prompts[tool_name]

    if tool_name in ["get_cancel_details_id", "get_reschedule_case_details", "get_uncancel_details"]:
        case_id = args.get("caseId")
        try:
            async with ZinniaXAPI() as api:
                case_details = await api.fetch_case_details(case_id, token=user_token)
            if not case_details:
                return f"I couldn't find details for case #{case_id}. Are you sure you want to proceed?"

            if tool_name == "get_cancel_details_id":
                print('CASE DETAILS', case_details)
                details = {
                'caseID': case_id,
                'Hospital Name': case_details.get('hospital', 'N/A'),
                'Surgeon Name': case_details.get('surgeonName', 'N/A'),
                'Procedure': case_details.get('procedure', 'N/A'),
                'Date-Time': case_details.get('dateOfSurgeryHospitalTime', 'N/A'),
                'Patient Name': case_details.get('patientName', 'N/A'),
            }
                details_text = "\n".join([
                    f"- **{key}**: {val}"
                    for key, val in details.items()
                    if val and str(val).strip().upper() != 'N/A'
                ])
                # print('DETAILS TEXT', details_text)
                return (
                    "Are you sure you want to cancel a case with following details:\n\n\n"
                    "📋 **Case Details**:\n"
                    f"{details_text}"
                )

            elif tool_name == "get_reschedule_case_details":
                print('RESCHEDULE ARGS: ', case_details)
                if 'newTime' not in args.keys():
                    args['newTime']  = case_details.get('dateOfSurgeryHospitalTime', 'N/A').split(',')[1].strip()

                details = {
                'caseID': case_id,
                'Hospital Name': case_details.get('hospital', 'N/A'),
                'Surgeon Name': case_details.get('surgeonName', 'N/A'),
                'Procedure': case_details.get('procedure', 'N/A'),
                'Scheduled On': args.get('newDate' , 'N/A') + " at " + args.get('newTime', 'N/A'),               
                'Patient Name': case_details.get('patientName', 'N/A')
                
            }
                details_text = "\n".join([
                    f"- **{key}**: {val}"
                    for key, val in details.items()
                    if val and str(val).strip().upper() != 'N/A'
                ])

                print('DETAILS TEXT', details)
                return (
                    "Are you sure you want to Reschedule a case with following details:\n\n\n"
                    "📋 **Case Details**:\n"
                    f"{details_text}"
                )

            elif tool_name == "get_uncancel_details":
                details = {
                'caseID': case_id,
                'Hospital Name': case_details.get('hospital', 'N/A'),
                'Surgeon Name': case_details.get('surgeonName', 'N/A'),
                'Procedure': case_details.get('procedure', 'N/A'),
                'Date-Time': case_details.get('dateOfSurgeryHospitalTime', 'N/A'),
                'Patient Name': case_details.get('patientName', 'N/A')
                
            }
                details_text = "\n".join([
                    f"- **{key}**: {val}"
                    for key, val in details.items()
                    if val and str(val).strip().upper() != 'N/A'
                ])

                # print('DETAILS TEXT', details_text)
                return (
                    "Are you sure you want to Reopen a case with following details:\n\n\n"
                    "📋 **Case Details**:\n"
                    f"{details_text}"
                )
        except Exception as e:
            logger.error(f"Error fetching case details for confirmation: {e}")
            return "I had trouble retrieving case details. Are you sure you want to proceed?"

    return "Sorry,can you please confirm the action you want to take? I need more details to proceed."


async def confirmation_node(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    print("\n==============================\n🟨 Entering NODE: confirmation_node\n==============================\n")
    print("Entering confirmation_node")
    # print('state', state)
    username, user_token = await get_user_token_from_config(config)
    last_message = state["messages"][-1]
    if not isinstance(last_message, HumanMessage):
        return {
            **dict(state),
            "messages": [*state["messages"], AIMessage(content="I'm waiting for your confirmation. Please respond with 'yes' or 'no'.")],
            "confirmation_needed": True,
            "awaiting_user_response": True,
        }

    user_response = last_message.content.lower().strip()
    print(f"User confirmation response: '{user_response}'")

    # Handle case selection
    if state.get("showCase") and isinstance(state["showCase"], list):
        print("Handling case selection from multiple options")

        try:
            selected_case_id = last_message.content.strip()
            deferred_tool_call = state.get("deferred_tool_call", {})
            tool_name = deferred_tool_call.get("name", "")

            if tool_name == 'get_cancel_details_id':
                tool_call = {
                    "name": "get_cancel_details_id",
                    "args": {"caseId": selected_case_id}
                }
                flag, details = await validate_cancel_details(selected_case_id, token=user_token)
                if flag:
                    tool_call["args"] = details
                    confirmation_message = await generate_confirmation_message(tool_call, user_token=user_token)
                    return {
                        **dict(state),
                        "messages": [],
                        "confirmation_needed": True,
                        "deferred_tool_call": tool_call,
                        "awaiting_user_response": True,
                        "showCase": None,
                        "frontend_tool": "confirmation",
                        "confirmation_message": confirmation_message
                    }
                else:
                    return {

                        "messages": [*state["messages"], AIMessage(content=details)]
                    }

            elif tool_name == 'get_reschedule_case_details':
                return {
                    **dict(state),
                    "messages": [*state["messages"], AIMessage(content="Please provide the new date and time for rescheduling (format: YYYY-MM-DD HH:MM):")],
                    "confirmation_needed": False,
                    "deferred_tool_call": {"name": "get_reschedule_case_details", "args": {"caseId": selected_case_id}},
                    "awaiting_user_response": True,
                    "showCase": None,

                }
            else:
                return {

                    "messages": [*state["messages"], AIMessage(content="Invalid action. Please try again.")]
                }

        except Exception as e:
            logger.error(f"Error handling case selection: {e}")
            return {
                **dict(state),
                "messages": [*state["messages"], AIMessage(content="Invalid selection. Please enter a valid case ID from the list.")],
                "showCase": state["showCase"]
            }

    # Handle positive confirmation
    if any(word in user_response for word in ["yes", "confirm", "proceed", "correct", "do it", "y"]):
        print("User confirmed. Executing deferred tool call.")
        deferred_tool_call = state.get("deferred_tool_call")
        if not deferred_tool_call:
            return {

                "messages": [*state["messages"], AIMessage(content="My apologies, there was no action to confirm. Let's try again.")]
            }

        try:
            result_message = await execute_tool_call(deferred_tool_call, user_token)
            return {

                "messages": [*state["messages"], AIMessage(content=result_message)],
                "deferred_tool_call": None,
                "awaiting_user_response": False,
                "confirmation_needed": False
            }
        except Exception as e:
            logger.error(f"Error executing confirmed tool call: {e}")
            return {

                "messages": [*state["messages"], AIMessage(content=f"I encountered an error while performing the action: {e}")]
            }

    # Handle negative confirmation
    elif any(word in user_response for word in ["no", "stop", "cancel"]):
        print("User declined. Cancelling operation.")
        return {

            "messages": [*state["messages"], AIMessage(content="Okay, Do you want to edit the details or start over?")],
            "deferred_tool_call": None,
            "awaiting_user_response": False,
            "confirmation_needed": False
        }

    # Unclear response
    else:
        print("User response unclear. Asking for clarification.")
        return {
            **dict(state),
            "messages": [*state["messages"], AIMessage(content="can you please clarify your response? start Again.")],
            "confirmation_needed": False,
            "awaiting_user_response": False,
        }


async def execute_tool_call(tool_call, user_token):
    """Execute a tool call and return the result."""
    async with ZinniaXAPI() as api:
        tool_name = tool_call["name"]
        args = tool_call.get("args", {})

        if tool_name == "get_schedule_details":
            details = {
                'hospital': args['hospital'],
                'surgeon': args['surgeon'],
                'procedure': args.get('procedure', ''),
                'date': args['date'],
                'time': args['time'],
                'patientName': args.get('patientName', ''),
                'city': args.get('city', ''),
                'hospitalId': args.get('hospitalId', ''),
                'doctorId' : args.get('doctorId', '')
            }
            print("Detailsaa", details)
            success, result, caseId = await api.schedule_new_case(details, user_token)
            if success:
                case_link = f"https://stage.ionm.zinniax.com/case/{caseId}/summary"
                details.pop('hospitalId')
                details.pop('doctorId')
                print("Details here", details)
                details_text = "\n".join([
                    f"- **{key}**: {val}"
                    for key, val in details.items()
                    if val and str(val).strip().upper() != 'N/A'
                ])

                return (f"Successfully scheduled new case with ID: "
                        f"[{result}]({case_link}).\n\n"
                        f"📋 **Case Details**:\n{details_text}"
                        )
            else:
                return f"Failed to schedule case: {result}"

        elif tool_name == "get_cancel_details_id":
            print("FINALLY CALLING CANCELLATION FUNCTION")
            print(args)
            details = {
                'hospital': args['hospital'],
                'surgeon': args['surgeonName'],
                'procedure': args.get('procedure', ''),
                'date and time': args['dateOfSurgeryHospitalTime'],
                'patientName': args.get('patientName', ''),
            }
            caseNo = args['caseId']
            success, result = await api.cancel_case(caseNo, user_token)
            if success:
                details_text = "\n".join(
                    [f"- {key}: {val}" for key, val in details.items()])

                return (f"Successfully Cancelled the case with ID: "
                        f"{caseNo}.\n\n"
                        )
            else:
                return f"{result}"

        elif tool_name == "get_reschedule_case_details":
            print("FINALLY CALLING RESCHEDULE FUNCTION")
            print(args)
            case_id = args['caseId']
            new_date = args['newDate']
            new_time = args['newTime']
            details = {
                'caseId': case_id,
                'newDate': new_date,
                'newTime': new_time
            }
            print(case_id, new_date, new_time)
            success, result = await api.reschedule_case(case_id, new_date, new_time, user_token)
            if success:
                return f"Successfully Rescheduled case with ID: {case_id} to {new_date} at {new_time}."
            else:
                return f"{result}"

        elif tool_name == "get_uncancel_details":
            caseNo = args['caseId']
            details = {
                'caseId': caseNo,
                'hospital': args['hospital'],
                'surgeon': args['surgeonName'],
                'procedure': args.get('procedure', ''),
                'date and time': args['dateOfSurgeryHospitalTime'],
                'patientName': args.get('patientName', ''),
            }
            success, result = await api.uncancel_case(caseNo, user_token)

            if success:
                details = "\n".join(
                    [f"- {key}: {val}" for key, val in details.items()])
                return (f"Successfully Un-cancelled the case with ID: "
                        f"{caseNo}.\n\n")
            else:
                return f"Failed to Reopen case: {result}"

        else:
            return f"Unknown tool: {tool_name}"

    return "I'm sorry, I don't know how to handle that action. Please contact support."


def should_continue(state: AgentState) -> Literal["confirmation_node", "tool_node", "__end__"]:
    print("\n==============================\n🟧 Entering ROUTER: should_continue\n==============================\n")
    print("Executing should_continue router")
    # print("router should continue, ", state)
    # If awaiting user response, end the turn
    if state.get("awaiting_user_response", False):
        print("Awaiting user response - ending turn")
        return "__end__"

    # Check if the last message has tool calls
    last_message = state["messages"][-1] if state["messages"] else None
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        # If it's a critical tool call, we should have already handled it in chat_node
        # So if we get here with tool calls, they should be non-critical
        print("Non-critical tool call detected - routing to tool_node")
        return "tool_node"

    # Otherwise, end the turn
    print("No tool calls or awaiting response - ending turn")
    return "__end__"


def route_entry(state: AgentState) -> Literal["chat_node", "confirmation_node"]:
    print("\n==============================\n🟪 Entering ROUTER: route_entry\n==============================\n")
    print('royter_state', state)
    # print("router states, ", state)
    print("Executing route_entry")

    if state.get("awaiting_user_response", False):
        print("Awaiting user response - routing to confirmation_node")
        return "confirmation_node"

    print("New request - routing to chat_node")
    return "chat_node"


# --- Build the workflow graph ---
workflow = StateGraph(AgentState)

# Add nodes
workflow.add_node("chat_node", chat_node)
workflow.add_node("tool_node", ToolNode(tools=tools))
workflow.add_node("confirmation_node", confirmation_node)

# Set up routing
workflow.add_conditional_edges(
    START,
    route_entry,
    {
        "chat_node": "chat_node",
        "confirmation_node": "confirmation_node",
    }
)

workflow.add_conditional_edges(
    "chat_node",
    should_continue,
    {
        "confirmation_node": "confirmation_node",
        "tool_node": "tool_node",
        "__end__": END,
    }
)

workflow.add_edge("confirmation_node", END)
workflow.add_edge("tool_node", "chat_node")

# Compile the graph
is_langgraph_api = (
    os.environ.get("LANGGRAPH_API", "false").lower() == "true" or
    os.environ.get("LANGGRAPH_API_DIR") is not None
)

if is_langgraph_api:
    graph = workflow.compile()
else:
    memory = MemorySaver()
    graph = workflow.compile(checkpointer=memory,interrupt_after=['tool_node'])

  
async def run_chatbot():
    print("\n==============================\n🤖 Starting FUNCTION: run_chatbot\n==============================\n")
    """Run an interactive chatbot in the terminal."""
    print("🏥 Welcome to the Scheduling Assistant!")
    print("I can help you schedule, cancel, or reschedule medical cases.")
    print("Type 'exit' to quit.\n")

    config = {"configurable": {"thread_id": "main_conversation"}}

    # Initialize state properly
    current_state = {
        "messages": [],
        "copilotkit": {"actions": []},
        "confirmation_needed": False,
        "deferred_tool_call": None,
        "awaiting_user_response": False,
        "showCase": None,
    }

    while True:
        try:
            user_input = input("You: ").strip()
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print("👋 Goodbye! Have a great day!")
                break
            if not user_input:
                continue

            # Add user message to state
            current_state["messages"].append(HumanMessage(content=user_input))

            # Run the workflow
            result = await graph.ainvoke(current_state, config)

            if result:
                # Preserve copilotkit context
                if not result.get("copilotkit"):
                    result["copilotkit"] = {"actions": []}

                current_state = result

                print(
                    f"State after workflow: confirmation_needed={result.get('confirmation_needed')}, awaiting_user_response={result.get('awaiting_user_response')}")

            # Display the bot's response
            if result and result.get("messages"):
                last_ai_message = next(
                    (msg for msg in reversed(result["messages"])
                     if isinstance(msg, AIMessage) and msg.content),
                    None
                )
                if last_ai_message:
                    print(f"🤖 Bot: {last_ai_message.content}")

        except KeyboardInterrupt:
            print("\n👋 Goodbye! Have a great day!")
            break
        except Exception as e:
            logger.error(f"Error in chatbot loop: {e}", exc_info=True)
            print(
                f"🚨 Sorry, I encountered an error: {str(e)}. Let's start over.")
            current_state = {
                "messages": [],
                "copilotkit": {"actions": []},
                "confirmation_needed": False,
                "deferred_tool_call": None,
                "awaiting_user_response": False,
                "showCase": None,
            }
            continue


# FastAPI setup
import uvicorn
from fastapi import FastAPI,Request, Response, Cookie
from copilotkit import CopilotKitRemoteEndpoint, LangGraphAgent
from copilotkit.integrations.fastapi import add_fastapi_endpoint
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from typing import Annotated, Dict, Any, Literal, Optional

app = FastAPI()


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For production, restrict this!
    # allow_origins=["*"],  # For production, restrict this!
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory session-token store (use Redis/DB for production)
session_tokens = {}




import os
import json
sdk = CopilotKitRemoteEndpoint(
    agents=[
        LangGraphAgent(
            name="scheduling_agent",
            description="A professional assistant for scheduling, canceling, and managing medical cases.",
            graph=graph,
            config=session_tokens,


        )
    ],
)

add_fastapi_endpoint(app, sdk, "/api/copilotkit/", use_thread_pool=False)


# Add middleware to log requests
@app.middleware("http")
async def normalize_url(request: Request, call_next):
    # Normalize CopilotKit URLs
    if request.url.path == "/api/copilotkit":
        request.scope["path"] = "/api/copilotkit/"
    elif request.url.path == "/api/copilotkit/":
        request.scope["path"] = "/api/copilotkit"
    
    response = await call_next(request)
    return response

@app.middleware("http")
async def debug_routes(request: Request, call_next):
    print(f"🛣️ Route called: {request.method} {request.url.path}")
    response = await call_next(request)
    print(f"🛣️ Route response: {response.status_code}")
    return response
@app.get("/api/health")
def health():
    print("\n==============================\n🟩 Entering ENDPOINT: health\n==============================\n")
    """Health check endpoint."""
    return {"status": "okkkkkkkk"}

@app.get("/api/test")
def health():
    print("\n==============================\n🟩 Entering ENDPOINT: health\n==============================\n")
    """Health check endpoint."""
    return {"status": "okkkkkkk"}

@app.post("/api/store-token")
async def store_token(request: Request, response: Response):
    print("\n==============================\n🟩 Entering ENDPOINT: store_token\n==============================\n")
    data = await request.json()
    session_id = data.get("sessionId")
    token = data.get("token")
    
    if not token or not session_id:
        return {"status": "error", "message": "No token or session_id provided"}

    # Store token against the session_id
    print(f"Storing token for session_id: {session_id}")
    print(f"Storing token : {token}")
    session_tokens.clear()
    session_tokens[session_id] = token
    # bind_thread_to_user(session_id, token)
    return {"status": "success", "session_id": session_id}


USER_USAGE_FILE = "user_token_usage.json"
@app.get("/api/user-token-usage")
def get_user_token_usage():
    print("\n==============================\n🟩 Entering ENDPOINT: get_user_token_usage\n==============================\n")
    file_path = "user_token_usage.json"
    if not os.path.exists(file_path):
        return {"status": "error", "message": "user_token_usage.json not found"}
    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        return data
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/api/tokens")
def get_tokens():
    print("\n==============================\n🟩 Entering ENDPOINT: get_tokens\n==============================\n")
    file_path = "tokens.json"
    if not os.path.exists(file_path):
        return {"status": "error", "message": "tokens.json not found"}
    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        return data
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/api/thread-to-user")
def get_thread_to_user():
    print("\n==============================\n🟩 Entering ENDPOINT: get_thread_to_user\n==============================\n")
    file_path = "thread_to_user.json"
    if not os.path.exists(file_path):
        return {"status": "error", "message": "thread_to_user.json not found"}
    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        return data
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.post("/api/clean-files")
def clean_files():
    print("\n==============================\n🟩 Entering ENDPOINT: clean_files\n==============================\n")
    files = [
        ("thread_to_user.json", "thread_to_user"),
        ("user_token_usage.json", "user_token_usage"),
        ("tokens.json", "tokens")
    ]
    results = {}
    for file_path, key in files:
        try:
            if os.path.exists(file_path):
                with open(file_path, "w") as f:
                    json.dump({}, f, indent=2)
                results[key] = "cleared"
            else:
                results[key] = "file not found"
        except Exception as e:
            results[key] = f"error: {str(e)}"
    return {"status": "success", "results": results}


def main():
    """Run the uvicorn server."""
    port = int(os.getenv("PORT", "8001"))

    # Check if SSL certificates are available
    # ssl_keyfile = '../privkey.key'
    # ssl_certfile = '../cert.crt'

    uvicorn_config = {
        "app": "app:app",
        "host": "0.0.0.0",
        "port": port,
        "reload": True,
        "forwarded_allow_ips": '*',
        "proxy_headers": True,
        
    }

    # Add SSL configuration if certificates are provided
    # if ssl_keyfile and ssl_certfile:
    #     uvicorn_config.update({
    #         "ssl_keyfile": ssl_keyfile,
    #         "ssl_certfile": ssl_certfile
    #     })

    uvicorn.run(**uvicorn_config)


if __name__ == "__main__":

    main()
    # asyncio.run(run_chatbot())
