import asyncio
from typing import Optional
from langchain.tools import tool
from apis import ZinniaXAPI
from helper import normalize_time, normalize_name,get_hospital_list,find_hospitals_by_name,find_hospital_by_initials,get_hospital_accurately,get_doctor_name_accurately,fetch_doctors_list,find_closest_doctor,_fetch_cases_for_filters,fetch_hospital_details,parse_time_string
import logging
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, AIMessage,HumanMessage
from datetime import datetime
import re




# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from langgraph.types import interrupt

def parse_flexible_date(date_str: str) -> datetime:
    """
    Parse date string in various formats and return datetime object.
    Handles formats like: 15/8/25, 2024-08-15, 08/15/2024, Aug 15 2024, etc.
    """
    if not date_str or not isinstance(date_str, str):
        raise ValueError(f"Invalid date string: {date_str}")

    date_str = date_str.strip()

    # Common date formats to try
    date_formats = [
        "%Y-%m-%d",           # 2024-08-15
        "%m/%d/%Y",           # 08/15/2024
        "%d/%m/%Y",           # 15/08/2024
        "%m/%d/%y",           # 8/15/24
        "%d/%m/%y",           # 15/8/24
        "%Y/%m/%d",           # 2024/08/15
        "%B %d, %Y",          # August 15, 2024
        "%b %d, %Y",          # Aug 15, 2024
        "%B %d %Y",           # August 15 2024
        "%b %d %Y",           # Aug 15 2024
        "%d %B %Y",           # 15 August 2024
        "%d %b %Y",           # 15 Aug 2024
        "%m-%d-%Y",           # 08-15-2024
        "%d-%m-%Y",           # 15-08-2024
        "%m-%d-%y",           # 8-15-24
        "%d-%m-%y",           # 15-8-24
    ]

    # Handle special keywords
    today = datetime.now()
    date_str_lower = date_str.lower()

    if date_str_lower in ['today', 'now']:
        return today
    elif date_str_lower in ['tomorrow', 'tmrw']:
        from datetime import timedelta
        return today + timedelta(days=1)
    elif date_str_lower in ['yesterday']:
        from datetime import timedelta
        return today - timedelta(days=1)

    # Try each format
    for fmt in date_formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt)

            # Handle 2-digit years (assume 20xx for years 00-30, 19xx for 31-99)
            if parsed_date.year < 100:
                if parsed_date.year <= 30:
                    parsed_date = parsed_date.replace(year=parsed_date.year + 2000)
                else:
                    parsed_date = parsed_date.replace(year=parsed_date.year + 1900)

            return parsed_date

        except ValueError:
            continue

    # If no format worked, try to handle some edge cases with regex
    # Handle formats like "15/8/25" where day/month might be single digits
    date_patterns = [
        (r'^(\d{1,2})/(\d{1,2})/(\d{2,4})$', lambda m: f"{m.group(1).zfill(2)}/{m.group(2).zfill(2)}/{m.group(3)}"),
        (r'^(\d{1,2})-(\d{1,2})-(\d{2,4})$', lambda m: f"{m.group(1).zfill(2)}-{m.group(2).zfill(2)}-{m.group(3)}"),
    ]

    for pattern, formatter in date_patterns:
        match = re.match(pattern, date_str)
        if match:
            formatted_date = formatter(match)
            # Try parsing the formatted date
            for fmt in ["%d/%m/%Y", "%d/%m/%y", "%m/%d/%Y", "%m/%d/%y", "%d-%m-%Y", "%d-%m-%y", "%m-%d-%Y", "%m-%d-%y"]:
                try:
                    parsed_date = datetime.strptime(formatted_date, fmt)
                    # Handle 2-digit years
                    if parsed_date.year < 100:
                        if parsed_date.year <= 30:
                            parsed_date = parsed_date.replace(year=parsed_date.year + 2000)
                        else:
                            parsed_date = parsed_date.replace(year=parsed_date.year + 1900)
                    return parsed_date
                except ValueError:
                    continue

    # If all else fails, raise an error with helpful message
    raise ValueError(f"Unable to parse date '{date_str}'. Please use formats like: YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY, or words like 'today', 'tomorrow'.")

# Test function for the date parser (can be removed in production)
def test_date_parsing():
    """Test the flexible date parsing function with various formats"""
    test_dates = [
        "15/8/25",      # The problematic format
        "2024-08-15",   # Standard format
        "08/15/2024",   # US format
        "15/08/2024",   # European format
        "today",        # Keyword
        "tomorrow",     # Keyword
        "Aug 15, 2024", # Month name format
    ]

    print("Testing date parsing function:")
    for date_str in test_dates:
        try:
            parsed = parse_flexible_date(date_str)
            print(f"✓ '{date_str}' -> {parsed.strftime('%Y-%m-%d')}")
        except ValueError as e:
            print(f"✗ '{date_str}' -> Error: {e}")

# Uncomment the line below to test the function
# test_date_parsing()
# --- Tool Definitions ---
@tool
def get_schedule_details(
    surgeon: str,
    date: str,
    time: str,
    hospital: str,
    procedure: Optional[str] = "",
    patientName: Optional[str] = "",
    city: Optional[str] = "",
    id:Optional[int]="",
) -> str:
    """Schedule a new surgical case in ZinniaX EHR system after validating all details.
    
    .
    Automatically handles date/time format conversions.
    Never prompts for optional fields (procedure, patientName, city) if not provided initially.
    
    Args:
        surgeon: Full surgeon name , take as it is , dont clarify.
        hospital: Hospital/facility name , take as it is , dont clarify.
        date: Procedure date in any recognizable format(convert to YYYY-MM-DD). 
        time: Procedure time in any common format (convert to HH:MM 24-hour format)
        procedure: (Optional) Medical procedure description
        patientName: (Optional) Patient full name
        city: (Optional) Hospital city/location
        hospitalId:(Optional) Hospital ID will be send in case of multiple hospital with same name occures. do no ask directly.
        doctorId:(Optional) Doctor ID will be send in case of multiple doctor with same name occures. do no ask directly.
        
    example :  Dr. Oderich wants to add a case at Baylor St. Lukes today for around 12pm , Dr. Stark THORACIC 12-LUMBAR 1 LAMINECTOMY 8:30am start , 4/23 7 am 1 level ALIF  Dr Bashir at Townsen humble , dr. chang has a 7:30am cervical revision case
    
    """
    return None
            
async def validate_schedule_details(surgeon,hospital,date,time,procedure=None,patientName=None,city=None,token=None,hospitalId=None,doctorId=None):
            print("doctorIDs",doctorId)
            print(  "hospitalIdss",hospitalId)
            print(f"Starting hospital validation for input: '{hospital}' (city: '{city}')")
            hospital_list = get_hospital_list(token)
            initial_hospital = find_hospital_by_initials(hospital_list, hospital)
            
            final_hospital_name = None
        
            if initial_hospital and final_hospital_name == None:
                final_hospital_name = initial_hospital
                print(f"Hospital after initials match: {final_hospital_name}")
                flag, final_hospital_name = get_hospital_accurately(final_hospital_name, hospital_list, city)
                print(f"Hospital after accurate match: {final_hospital_name}, flag: {flag}")
                if not flag:
                    logger.error(f"Hospital name '{hospital}' is invalid after all checks.")
                    return False,f"The provided hospital name '{hospital}' seems invalid. Please provide a valid hospital's name."
            print(f"Fetched {len(hospital_list)} hospitals from API.")
            matched_hospitals = find_hospitals_by_name(hospital_list, hospital)
            print(f"Matched hospitals: {matched_hospitals}")

            # If no or only one match, try initials/accurate match
            if len(matched_hospitals) == 0 or len(matched_hospitals) >= 1:
                if hospitalId :
                    if city:
                        final_hospital_name = fetch_hospital_details(hospitalId,token)['name']  + ';' + city
                    else:
                        final_hospital_name = fetch_hospital_details(hospitalId,token)['name']
                    # final_hospital_name = fetch_hospital_details(hospitalId,token)['name']  + ';' + city
                    print("final_hospital_nameee",final_hospital_name)
                else :
                    final_hospital_name = find_hospital_by_initials(hospital_list, hospital)
                    print(f"Hospital after initials match: {final_hospital_name}")
                    flag, final_hospital_name = get_hospital_accurately(final_hospital_name, hospital_list, city)   
                    if flag[0] ==  True:
                        hospitalId = flag[1]
                        print("hospitalId",hospitalId)

                    print(f"Hospital after accurate match: {final_hospital_name}, flag: {flag}")
                    if flag == 'Multiple':
                        return False,final_hospital_name
                    if not flag:
                        logger.error(f"Hospital name '{hospital}' is invalid after all checks.")
                        return False,f"The provided hospital name '{hospital}' seems invalid. Please provide a valid hospital's name."
            # else:
            #     # If multiple matches, ask user to clarify
            #     options = ", ".join([h['name'] for h in matched_hospitals])
            #     print(f"Multiple hospitals matched: {options}")
            #     return False,f"Multiple hospitals match '{hospital}': {options}. Please specify which one you mean."

            # Doctor validation
            print(f"Starting doctor validation for input: '{surgeon}' at hospital: '{final_hospital_name}' (city: '{city}')")
            final_doctor_name = get_doctor_name_accurately(surgeon, final_hospital_name,token, city=city)
            print(f"Doctor match result: {final_doctor_name}")
            if not doctorId:
                if final_doctor_name is not None:
                    if len(final_doctor_name) > 1:
                        doc_string = ", ".join([item['name'] for item in final_doctor_name])
                        print(f"Multiple doctors matched: {doc_string}")
                        return "MutliDotorName",final_doctor_name
                    elif len(final_doctor_name) == 1:
                        print(final_doctor_name)
                        # print(type(final_doctor_name[0]))

                        final_doctor_name_done = final_doctor_name[0]['name']
                        doctor_id = final_doctor_name[0]['id']
                        print(f"Unique doctor found: {final_doctor_name_done}")
                    else:
                        logger.error(f"No doctor found matching '{surgeon}' at '{final_hospital_name}'.")
                        return False,f"No doctor found matching '{surgeon}' at '{final_hospital_name}'. Please provide a valid doctor name."
                else:
                    logger.error(f"No doctor found matching '{surgeon}' at '{final_hospital_name}'.")
                    return False,f"No doctor found matching '{surgeon}' at '{final_hospital_name}'. Please provide a valid doctor name."

                print(f"Validated1: surgeon={final_doctor_name_done}, date={date}, time={time}, hospital={final_hospital_name}, procedure={procedure}, patientName={patientName}, city={city}")
                details = {
                    'surgeon': final_doctor_name_done,
                    'date': date,
                    'time': time,
                    'hospital': final_hospital_name,
                    'procedure': procedure,
                    'patientName': patientName,
                    'city': city,
                    'doctorId':doctor_id,
                    'hospitalId':hospitalId
                }
            else :
                surgeon = final_doctor_name[0]['name']
                doctorId = final_doctor_name[0]['id']
                print(f"Validated2: surgeon={surgeon}, date={date}, time={time}, hospital={final_hospital_name}, procedure={procedure}, patientName={patientName}, city={city}")
                details = {
                    'surgeon': surgeon,
                    'date': date,
                    'time': time,
                    'hospital': final_hospital_name,
                    'procedure': procedure,
                    'patientName': patientName,
                    'city': city,
                    'doctorId': doctorId,
                    'hospitalId': hospitalId
                }
            print("details tools",details)
            return True,details

    
   

@tool
def get_cancel_details_id(caseId: str) -> str:
    """Cancel a specific surgical case when the user provides a case ID number.

    **USE THIS TOOL ONLY WHEN:** The user mentions a specific case ID/number in their request.

    Verifies case exists, checks cancellation status, and presents full case details
    for user confirmation before proceeding with cancellation.

    Args:
        caseId: The exact case ID number provided by the user

    Examples of when to use this tool:
    - "Cancel case 106418"
    - "106418 - AHMC - cancelled"
    - "Cancel case #193402"
    - "192776 - Adu Lartey - cancelled for tomorrow"
    - "Can you cancel case 12345?"

    """

    return None


async def validate_cancel_details(caseId: str,token=None):
    async with ZinniaXAPI() as api:
        case_details = await api.fetch_case_details_pro(caseId,action='cancel',token=token)
        if not case_details:
            return False,f"Error: Case #{caseId} not found in ZinniaX. Please verify the case number."

        if case_details['status'].lower() in ['cancelled', 'canceled']:
            return False,f"Case #{caseId} is already cancelled. No action needed."
        
        return True,case_details
    


@tool
def get_cancel_details_doctorname_date(surgeon:str,hospital: str,date:str,hospitalId:str,doctorId:str) -> str:
    """Cancel cases when the user does NOT provide a case ID, but provides surgeon, hospital, and date.

    **USE THIS TOOL ONLY WHEN:** The user wants to cancel but does NOT mention a specific case ID.
    Instead, they provide surgeon name, hospital, and date information.

    Validates surgeon-hospital relationship and presents list of affected cases
    for user confirmation before cancellation.

    Args:
        surgeon: Full surgeon name
        hospital: Hospital name
        date: Procedure date in any recognizable format, convert is to valid '%Y-%m-%d' format.
        hospitalId: (Optional) Hospital ID will be send in case of multiple hospital with same name occures. do no ask directly.
        doctorId: (Optional) Doctor ID will be send in case of multiple doctors. do no ask directly.
        
    

    Examples of when to use this tool:
    - "Cancel Dr. Smith's cases at General Hospital today"
    - "Cancel all Dr. Lee's cases at Mercy Hospital tomorrow"
    - "Cancel Dr. Johnson's surgery at City Hospital on June 15"
    - "Cancel all cases for Dr. Patel at Memorial Hospital on Friday"

    """
    return None


async def validate_cancel_reschedule_doctor_details(surgeon,hospital,date,city=None,hospitalId=None,doctorId=None,token=None):
    print("doctorIDs",doctorId)
    print( "hospitalIdss",hospitalId)
    print("Inside validate_cancel_reschedule_doctor_details")
    try:
        hospital_list = get_hospital_list(token=token)
        initial_hospital = find_hospital_by_initials(hospital_list, hospital)
        final_hospital_name = None
        if initial_hospital:
            final_hospital_name = initial_hospital
            print(f"Hospital after initials match1: {final_hospital_name}")
            flag, final_hospital_name = get_hospital_accurately(final_hospital_name, hospital_list, city)
            print(f"Hospital after accurate match1: {final_hospital_name}, flag: {flag}")
            if not flag:
                logger.error(f"Hospital name '{hospital}' is invalid after all checks.")
                return False,f"The provided hospital name '{hospital}' seems invalid. Please provide a valid hospital's name."
        print(f"Fetched {len(hospital_list)} hospitals from API.")
        matched_hospitals = find_hospitals_by_name(hospital_list, hospital)
        print(f"Matched hospitals: {matched_hospitals}")

        # If no or only one match, try initials/accurate match
        if len(matched_hospitals) == 0 or len(matched_hospitals) >= 1:
                if hospitalId :
                    if city:
                            final_hospital_name = fetch_hospital_details(hospitalId,token)['name']  + ';' + city
                    else:
                            final_hospital_name = fetch_hospital_details(hospitalId,token)['name']
                    print("final_hospital_nameee",final_hospital_name)
                else :
                    final_hospital_name = find_hospital_by_initials(hospital_list, hospital)
                    print(f"Hospital after initials match: {final_hospital_name}")
                    flag, final_hospital_name = get_hospital_accurately(final_hospital_name, hospital_list, city)   
                    if flag[0] ==  True:
                        hospitalId = flag[1]
                        print("hospitalId",hospitalId)

                    print(f"Hospital after accurate match: {final_hospital_name}, flag: {flag}")
                    if flag == 'Multiple':
                        return "MutliHospitalName",final_hospital_name
                    if not flag:
                        logger.error(f"Hospital name '{hospital}' is invalid after all checks.")
                        return False,f"The provided hospital name '{hospital}' seems invalid. Please provide a valid hospital's name."

        # Doctor validation
        print(f"Starting doctor validation for input: '{surgeon}' at hospital: '{final_hospital_name}' (city: '{city}')")
        final_doctor_name = get_doctor_name_accurately(surgeon, final_hospital_name,token, city=city)
        print(f"Doctor match result: {final_doctor_name}")
        if not doctorId:
            if final_doctor_name is not None:
                if len(final_doctor_name) > 1:
                    doc_string = ", ".join([item['name'] for item in final_doctor_name])
                    print(f"Multiple doctors matched: {doc_string}")
                    return "MutliDotorName",final_doctor_name
                elif len(final_doctor_name) == 1:
                    print(final_doctor_name)
                    # print(type(final_doctor_name[0]))

                    final_doctor_name_done = final_doctor_name[0]['name']
                    doctor_id = final_doctor_name[0]['id']
                    print(f"Unique doctor found: {final_doctor_name_done}")
                else:
                    logger.error(f"No doctor found matching '{surgeon}' at '{final_hospital_name}'.")
                    return False,f"No doctor found matching '{surgeon}' at '{final_hospital_name}'. Please provide a valid doctor name."
            else:
                logger.error(f"No doctor found matching '{surgeon}' at '{final_hospital_name}'.")
                return False,f"No doctor found matching '{surgeon}' at '{final_hospital_name}'. Please provide a valid doctor name."
            date = parse_time_string(date,token,"%Y-%m-%d")
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            date_formatted = date_obj.strftime("%m/%d/%Y")
            cases = _fetch_cases_for_filters( hospitalId=hospitalId,doctor_id=doctor_id, hospital_name_str=final_hospital_name,date_str=date_formatted,token=token)
            
            print("Debug :new :",hospitalId,doctorId)
            return True,cases
        else :
            date = parse_time_string(date,token,"%Y-%m-%d")
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            date_formatted = date_obj.strftime("%m/%d/%Y")
            cases = _fetch_cases_for_filters(hospitalId=hospitalId , doctor_id=doctorId, hospital_name_str=final_hospital_name,date_str=date_formatted,token=token)
            return True,cases
    except:
        return False,"Error in fetching cases"


@tool
def get_reschedule_case_details(caseId: str, newDate: str, newTime: Optional[str] = "07:30 AM") -> str:
    """Reschedule a specific surgical case when the user provides a case ID number.

    **USE THIS TOOL ONLY WHEN:** The user mentions a specific case ID/number in their reschedule request.

    Compares old/new schedule, checks for conflicts, and requires explicit
    user confirmation before updating. If user doesn't provide new time, it defaults to 07:30 AM.

    Args:
        caseId: Valid existing case ID provided by the user
        newDate: New date in any recognizable format OR "same date" to keep original date
        newTime: (Optional) New time in any common format (defaults to 07:30am)

    Examples of when to use this tool:
    - "Reschedule case 194297 to 04/30"
    - "194297 - lee - rescheduled to 04/30"
    - "Reschedule case #186783 to 4/22"
    - "reschedule 106543 to 4am same date"
    - "Move case 12345 to tomorrow at 10am"
   """

    return None


@tool
def get_reschedule_details_doctorname_date(surgeon:str,hospital: str,date:str,hospitalId:str,doctorId:str) -> str:
    """Reschedule cases when the user does NOT provide a case ID, but provides surgeon, hospital, and date.

    **USE THIS TOOL ONLY WHEN:** The user wants to reschedule but does NOT mention a specific case ID.
    Instead, they provide surgeon name, hospital, and date information.

    Validates surgeon-hospital relationship and presents list of affected cases
    for user confirmation before rescheduling.

    Args:
        surgeon: Full surgeon name
        hospital: Hospital name
        date: Procedure date in any recognizable format , convert is to valid '%Y-%m-%d' format.
        hospitalId: (Optional) Hospital ID will be send in case of multiple hospital with same name occures. do no ask directly.
        doctorId: (Optional) Doctor ID will be send in case of multiple doctors. do no ask directly.
        

    Examples of when to use this tool:
    - "Reschedule Dr. Patel's cases at Mercy Hospital on July 1"
    - "Move all Dr. Johnson's cases at City Hospital tomorrow"
    - "Reschedule Dr. Smith's surgeries at General Hospital on Friday"
    - "Move all cases for Dr. Lee at Memorial Hospital on June 15"

    """
    return None

async def validate_reschedule_details(caseId: str, newDate: str, newTime: Optional[str] = "07:30 AM", token=None):
    async with ZinniaXAPI() as api:
        existing_case = await api.fetch_case_details_pro(caseId, action='reschedule', token=token)
        if not existing_case:
            return False, f"Error: Case #{caseId} not found in ZinniaX. Please verify the case number."

        if existing_case['status'].lower() in ['cancelled', 'canceled']:
            return False, f"Case #{caseId} is currently '{existing_case['status']}', can not be rescheduled."
        
        # NEW: Handle "same date" or similar keywords by preserving original date
        if (newDate and any(keyword in newDate.lower() for keyword in ['same', 'current', 'existing', 'original'])) or newDate == 'None':
            # Extract original date from existing case
            original_datetime = existing_case.get('dateOfSurgeryHospitalTime', '')
            print('original date' , original_datetime)
            if original_datetime:
                # Parse the original date with flexible parsing
                try:
                    dt = datetime.strptime(original_datetime, '%m/%d/%Y, %I:%M %p')
                    newDate = dt.strftime('%Y-%m-%d')
                except ValueError:
                    # Try alternative formats for the original datetime
                    try:
                        dt = parse_flexible_date(original_datetime.split(',')[0])  # Take just the date part
                        newDate = dt.strftime('%Y-%m-%d')
                    except ValueError as e:
                        logger.error(f"Could not parse original date '{original_datetime}': {e}")
                        return False, f"Could not parse original case date. Please provide a specific new date."
        else:
            # Parse the provided newDate with flexible parsing
            try:
                parsed_new_date = parse_flexible_date(newDate)
                newDate = parsed_new_date.strftime('%Y-%m-%d')
            except ValueError as e:
                logger.error(f"Invalid newDate format '{newDate}': {e}")
                return False, f"Invalid date format '{newDate}'. Please use formats like: YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY, or words like 'today', 'tomorrow'."
                
        
        # Add original case details to the response for context
        result = {
            'caseId': caseId,
            'newDate': newDate,
            'newTime': newTime,
            'originalDate': existing_case.get('dateOfSurgeryHospitalTime', ''),
            'hospital': existing_case.get('hospital', ''),
            'surgeonName': existing_case.get('surgeonName', ''),
            'procedure': existing_case.get('procedure', ''),
            'patientName': existing_case.get('patientName', '')
        }
        
        return True, result
@tool
def get_uncancel_details(caseId: str) -> str:
    """Reactivate a previously cancelled surgical case using its case ID.

    **USE THIS TOOL WHEN:** The user wants to uncancel/reopen a case and provides a case ID.

    Verifies case is in cancelled state, presents original details, and requires
    explicit reconfirmation before reactivation.

    Args:
        caseId: Valid cancelled case ID provided by the user

    Examples of when to use this tool:
    - "Can you uncancel case #192438"
    - "Uncancel case 12345 - going to go today"
    - "Reopen case 98765"
    - "Reactivate case #54321"
    """
    return None

async def validate_uncancel_details(caseId: str,token=None):

        async with ZinniaXAPI() as api:
            existing_case = await api.fetch_case_details_pro(caseId, "uncancel",token=token)
            if not existing_case:
                return False,f"Error: Case #{caseId} not found in ZinniaX. Please verify the case number."

            if existing_case['status'].lower() == 'pending':
                return False,f"Case #{caseId} is currently '{existing_case['status']}', not cancelled. Cannot uncancel."
            
        return True,existing_case
            
            

           

           




        
        
