"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-json-patch";
exports.ids = ["vendor-chunks/fast-json-patch"];
exports.modules = {

/***/ "(rsc)/./node_modules/fast-json-patch/index.mjs":
/*!************************************************!*\
  !*** ./node_modules/fast-json-patch/index.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonPatchError: () => (/* reexport safe */ _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.PatchError),\n/* harmony export */   _areEquals: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__._areEquals),\n/* harmony export */   applyOperation: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.applyOperation),\n/* harmony export */   applyPatch: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.applyPatch),\n/* harmony export */   applyReducer: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.applyReducer),\n/* harmony export */   compare: () => (/* reexport safe */ _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__.compare),\n/* harmony export */   deepClone: () => (/* reexport safe */ _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__._deepClone),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   escapePathComponent: () => (/* reexport safe */ _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.escapePathComponent),\n/* harmony export */   generate: () => (/* reexport safe */ _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__.generate),\n/* harmony export */   getValueByPointer: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.getValueByPointer),\n/* harmony export */   observe: () => (/* reexport safe */ _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__.observe),\n/* harmony export */   unescapePathComponent: () => (/* reexport safe */ _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.unescapePathComponent),\n/* harmony export */   unobserve: () => (/* reexport safe */ _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__.unobserve),\n/* harmony export */   validate: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.validate),\n/* harmony export */   validator: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.validator)\n/* harmony export */ });\n/* harmony import */ var _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./module/core.mjs */ \"(rsc)/./node_modules/fast-json-patch/module/core.mjs\");\n/* harmony import */ var _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./module/duplex.mjs */ \"(rsc)/./node_modules/fast-json-patch/module/duplex.mjs\");\n/* harmony import */ var _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./module/helpers.mjs */ \"(rsc)/./node_modules/fast-json-patch/module/helpers.mjs\");\n\n\n\n\n\n/**\n * Default export for backwards compat\n */\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.assign({}, _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__, _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__, {\n    JsonPatchError: _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.PatchError,\n    deepClone: _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__._deepClone,\n    escapePathComponent: _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.escapePathComponent,\n    unescapePathComponent: _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.unescapePathComponent\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmFzdC1qc29uLXBhdGNoL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtDO0FBQ0U7QUFNTjs7O0FBRzlCO0FBQ0E7QUFDQTs7QUFFMEM7QUFDSTtBQU1oQjs7QUFFOUIsaUVBQWUsZ0JBQWdCLEVBQUUsNkNBQUksRUFBRSwrQ0FBTTtBQUM3QyxrQkFBa0I7QUFDbEIsYUFBYTtBQUNiLHVCQUF1QjtBQUN2Qix5QkFBeUI7QUFDekIsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJEOlxcemlubmlheC1jb3BpbG90XFx6aW5uaWF4LWNvcGlsb3RcXHVpXFxub2RlX21vZHVsZXNcXGZhc3QtanNvbi1wYXRjaFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vbW9kdWxlL2NvcmUubWpzJztcbmV4cG9ydCAqIGZyb20gJy4vbW9kdWxlL2R1cGxleC5tanMnO1xuZXhwb3J0IHtcbiAgICBQYXRjaEVycm9yIGFzIEpzb25QYXRjaEVycm9yLFxuICAgIF9kZWVwQ2xvbmUgYXMgZGVlcENsb25lLFxuICAgIGVzY2FwZVBhdGhDb21wb25lbnQsXG4gICAgdW5lc2NhcGVQYXRoQ29tcG9uZW50XG59IGZyb20gJy4vbW9kdWxlL2hlbHBlcnMubWpzJztcblxuXG4vKipcbiAqIERlZmF1bHQgZXhwb3J0IGZvciBiYWNrd2FyZHMgY29tcGF0XG4gKi9cblxuaW1wb3J0ICogYXMgY29yZSBmcm9tICcuL21vZHVsZS9jb3JlLm1qcyc7XG5pbXBvcnQgKiBhcyBkdXBsZXggZnJvbSAnLi9tb2R1bGUvZHVwbGV4Lm1qcyc7XG5pbXBvcnQge1xuICAgIFBhdGNoRXJyb3IgYXMgSnNvblBhdGNoRXJyb3IsXG4gICAgX2RlZXBDbG9uZSBhcyBkZWVwQ2xvbmUsXG4gICAgZXNjYXBlUGF0aENvbXBvbmVudCxcbiAgICB1bmVzY2FwZVBhdGhDb21wb25lbnRcbn0gZnJvbSAnLi9tb2R1bGUvaGVscGVycy5tanMnO1xuXG5leHBvcnQgZGVmYXVsdCBPYmplY3QuYXNzaWduKHt9LCBjb3JlLCBkdXBsZXgsIHtcbiAgICBKc29uUGF0Y2hFcnJvcixcbiAgICBkZWVwQ2xvbmUsXG4gICAgZXNjYXBlUGF0aENvbXBvbmVudCxcbiAgICB1bmVzY2FwZVBhdGhDb21wb25lbnRcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-json-patch/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-json-patch/module/core.mjs":
/*!******************************************************!*\
  !*** ./node_modules/fast-json-patch/module/core.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonPatchError: () => (/* binding */ JsonPatchError),\n/* harmony export */   _areEquals: () => (/* binding */ _areEquals),\n/* harmony export */   applyOperation: () => (/* binding */ applyOperation),\n/* harmony export */   applyPatch: () => (/* binding */ applyPatch),\n/* harmony export */   applyReducer: () => (/* binding */ applyReducer),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   getValueByPointer: () => (/* binding */ getValueByPointer),\n/* harmony export */   validate: () => (/* binding */ validate),\n/* harmony export */   validator: () => (/* binding */ validator)\n/* harmony export */ });\n/* harmony import */ var _helpers_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers.mjs */ \"(rsc)/./node_modules/fast-json-patch/module/helpers.mjs\");\n\nvar JsonPatchError = _helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.PatchError;\nvar deepClone = _helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone;\n/* We use a Javascript hash to store each\n function. Each hash entry (property) uses\n the operation identifiers specified in rfc6902.\n In this way, we can map each patch operation\n to its dedicated function in efficient way.\n */\n/* The operations applicable to an object */\nvar objOps = {\n    add: function (obj, key, document) {\n        obj[key] = this.value;\n        return { newDocument: document };\n    },\n    remove: function (obj, key, document) {\n        var removed = obj[key];\n        delete obj[key];\n        return { newDocument: document, removed: removed };\n    },\n    replace: function (obj, key, document) {\n        var removed = obj[key];\n        obj[key] = this.value;\n        return { newDocument: document, removed: removed };\n    },\n    move: function (obj, key, document) {\n        /* in case move target overwrites an existing value,\n        return the removed value, this can be taxing performance-wise,\n        and is potentially unneeded */\n        var removed = getValueByPointer(document, this.path);\n        if (removed) {\n            removed = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(removed);\n        }\n        var originalValue = applyOperation(document, { op: \"remove\", path: this.from }).removed;\n        applyOperation(document, { op: \"add\", path: this.path, value: originalValue });\n        return { newDocument: document, removed: removed };\n    },\n    copy: function (obj, key, document) {\n        var valueToCopy = getValueByPointer(document, this.from);\n        // enforce copy by value so further operations don't affect source (see issue #177)\n        applyOperation(document, { op: \"add\", path: this.path, value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(valueToCopy) });\n        return { newDocument: document };\n    },\n    test: function (obj, key, document) {\n        return { newDocument: document, test: _areEquals(obj[key], this.value) };\n    },\n    _get: function (obj, key, document) {\n        this.value = obj[key];\n        return { newDocument: document };\n    }\n};\n/* The operations applicable to an array. Many are the same as for the object */\nvar arrOps = {\n    add: function (arr, i, document) {\n        if ((0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.isInteger)(i)) {\n            arr.splice(i, 0, this.value);\n        }\n        else { // array props\n            arr[i] = this.value;\n        }\n        // this may be needed when using '-' in an array\n        return { newDocument: document, index: i };\n    },\n    remove: function (arr, i, document) {\n        var removedList = arr.splice(i, 1);\n        return { newDocument: document, removed: removedList[0] };\n    },\n    replace: function (arr, i, document) {\n        var removed = arr[i];\n        arr[i] = this.value;\n        return { newDocument: document, removed: removed };\n    },\n    move: objOps.move,\n    copy: objOps.copy,\n    test: objOps.test,\n    _get: objOps._get\n};\n/**\n * Retrieves a value from a JSON document by a JSON pointer.\n * Returns the value.\n *\n * @param document The document to get the value from\n * @param pointer an escaped JSON pointer\n * @return The retrieved value\n */\nfunction getValueByPointer(document, pointer) {\n    if (pointer == '') {\n        return document;\n    }\n    var getOriginalDestination = { op: \"_get\", path: pointer };\n    applyOperation(document, getOriginalDestination);\n    return getOriginalDestination.value;\n}\n/**\n * Apply a single JSON Patch Operation on a JSON document.\n * Returns the {newDocument, result} of the operation.\n * It modifies the `document` and `operation` objects - it gets the values by reference.\n * If you would like to avoid touching your values, clone them:\n * `jsonpatch.applyOperation(document, jsonpatch._deepClone(operation))`.\n *\n * @param document The document to patch\n * @param operation The operation to apply\n * @param validateOperation `false` is without validation, `true` to use default jsonpatch's validation, or you can pass a `validateOperation` callback to be used for validation.\n * @param mutateDocument Whether to mutate the original document or clone it before applying\n * @param banPrototypeModifications Whether to ban modifications to `__proto__`, defaults to `true`.\n * @return `{newDocument, result}` after the operation\n */\nfunction applyOperation(document, operation, validateOperation, mutateDocument, banPrototypeModifications, index) {\n    if (validateOperation === void 0) { validateOperation = false; }\n    if (mutateDocument === void 0) { mutateDocument = true; }\n    if (banPrototypeModifications === void 0) { banPrototypeModifications = true; }\n    if (index === void 0) { index = 0; }\n    if (validateOperation) {\n        if (typeof validateOperation == 'function') {\n            validateOperation(operation, 0, document, operation.path);\n        }\n        else {\n            validator(operation, 0);\n        }\n    }\n    /* ROOT OPERATIONS */\n    if (operation.path === \"\") {\n        var returnValue = { newDocument: document };\n        if (operation.op === 'add') {\n            returnValue.newDocument = operation.value;\n            return returnValue;\n        }\n        else if (operation.op === 'replace') {\n            returnValue.newDocument = operation.value;\n            returnValue.removed = document; //document we removed\n            return returnValue;\n        }\n        else if (operation.op === 'move' || operation.op === 'copy') { // it's a move or copy to root\n            returnValue.newDocument = getValueByPointer(document, operation.from); // get the value by json-pointer in `from` field\n            if (operation.op === 'move') { // report removed item\n                returnValue.removed = document;\n            }\n            return returnValue;\n        }\n        else if (operation.op === 'test') {\n            returnValue.test = _areEquals(document, operation.value);\n            if (returnValue.test === false) {\n                throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n            }\n            returnValue.newDocument = document;\n            return returnValue;\n        }\n        else if (operation.op === 'remove') { // a remove on root\n            returnValue.removed = document;\n            returnValue.newDocument = null;\n            return returnValue;\n        }\n        else if (operation.op === '_get') {\n            operation.value = document;\n            return returnValue;\n        }\n        else { /* bad operation */\n            if (validateOperation) {\n                throw new JsonPatchError('Operation `op` property is not one of operations defined in RFC-6902', 'OPERATION_OP_INVALID', index, operation, document);\n            }\n            else {\n                return returnValue;\n            }\n        }\n    } /* END ROOT OPERATIONS */\n    else {\n        if (!mutateDocument) {\n            document = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(document);\n        }\n        var path = operation.path || \"\";\n        var keys = path.split('/');\n        var obj = document;\n        var t = 1; //skip empty element - http://jsperf.com/to-shift-or-not-to-shift\n        var len = keys.length;\n        var existingPathFragment = undefined;\n        var key = void 0;\n        var validateFunction = void 0;\n        if (typeof validateOperation == 'function') {\n            validateFunction = validateOperation;\n        }\n        else {\n            validateFunction = validator;\n        }\n        while (true) {\n            key = keys[t];\n            if (key && key.indexOf('~') != -1) {\n                key = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.unescapePathComponent)(key);\n            }\n            if (banPrototypeModifications &&\n                (key == '__proto__' ||\n                    (key == 'prototype' && t > 0 && keys[t - 1] == 'constructor'))) {\n                throw new TypeError('JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README');\n            }\n            if (validateOperation) {\n                if (existingPathFragment === undefined) {\n                    if (obj[key] === undefined) {\n                        existingPathFragment = keys.slice(0, t).join('/');\n                    }\n                    else if (t == len - 1) {\n                        existingPathFragment = operation.path;\n                    }\n                    if (existingPathFragment !== undefined) {\n                        validateFunction(operation, 0, document, existingPathFragment);\n                    }\n                }\n            }\n            t++;\n            if (Array.isArray(obj)) {\n                if (key === '-') {\n                    key = obj.length;\n                }\n                else {\n                    if (validateOperation && !(0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.isInteger)(key)) {\n                        throw new JsonPatchError(\"Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index\", \"OPERATION_PATH_ILLEGAL_ARRAY_INDEX\", index, operation, document);\n                    } // only parse key when it's an integer for `arr.prop` to work\n                    else if ((0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.isInteger)(key)) {\n                        key = ~~key;\n                    }\n                }\n                if (t >= len) {\n                    if (validateOperation && operation.op === \"add\" && key > obj.length) {\n                        throw new JsonPatchError(\"The specified index MUST NOT be greater than the number of elements in the array\", \"OPERATION_VALUE_OUT_OF_BOUNDS\", index, operation, document);\n                    }\n                    var returnValue = arrOps[operation.op].call(operation, obj, key, document); // Apply patch\n                    if (returnValue.test === false) {\n                        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n                    }\n                    return returnValue;\n                }\n            }\n            else {\n                if (t >= len) {\n                    var returnValue = objOps[operation.op].call(operation, obj, key, document); // Apply patch\n                    if (returnValue.test === false) {\n                        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n                    }\n                    return returnValue;\n                }\n            }\n            obj = obj[key];\n            // If we have more keys in the path, but the next value isn't a non-null object,\n            // throw an OPERATION_PATH_UNRESOLVABLE error instead of iterating again.\n            if (validateOperation && t < len && (!obj || typeof obj !== \"object\")) {\n                throw new JsonPatchError('Cannot perform operation at the desired path', 'OPERATION_PATH_UNRESOLVABLE', index, operation, document);\n            }\n        }\n    }\n}\n/**\n * Apply a full JSON Patch array on a JSON document.\n * Returns the {newDocument, result} of the patch.\n * It modifies the `document` object and `patch` - it gets the values by reference.\n * If you would like to avoid touching your values, clone them:\n * `jsonpatch.applyPatch(document, jsonpatch._deepClone(patch))`.\n *\n * @param document The document to patch\n * @param patch The patch to apply\n * @param validateOperation `false` is without validation, `true` to use default jsonpatch's validation, or you can pass a `validateOperation` callback to be used for validation.\n * @param mutateDocument Whether to mutate the original document or clone it before applying\n * @param banPrototypeModifications Whether to ban modifications to `__proto__`, defaults to `true`.\n * @return An array of `{newDocument, result}` after the patch\n */\nfunction applyPatch(document, patch, validateOperation, mutateDocument, banPrototypeModifications) {\n    if (mutateDocument === void 0) { mutateDocument = true; }\n    if (banPrototypeModifications === void 0) { banPrototypeModifications = true; }\n    if (validateOperation) {\n        if (!Array.isArray(patch)) {\n            throw new JsonPatchError('Patch sequence must be an array', 'SEQUENCE_NOT_AN_ARRAY');\n        }\n    }\n    if (!mutateDocument) {\n        document = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(document);\n    }\n    var results = new Array(patch.length);\n    for (var i = 0, length_1 = patch.length; i < length_1; i++) {\n        // we don't need to pass mutateDocument argument because if it was true, we already deep cloned the object, we'll just pass `true`\n        results[i] = applyOperation(document, patch[i], validateOperation, true, banPrototypeModifications, i);\n        document = results[i].newDocument; // in case root was replaced\n    }\n    results.newDocument = document;\n    return results;\n}\n/**\n * Apply a single JSON Patch Operation on a JSON document.\n * Returns the updated document.\n * Suitable as a reducer.\n *\n * @param document The document to patch\n * @param operation The operation to apply\n * @return The updated document\n */\nfunction applyReducer(document, operation, index) {\n    var operationResult = applyOperation(document, operation);\n    if (operationResult.test === false) { // failed test\n        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n    }\n    return operationResult.newDocument;\n}\n/**\n * Validates a single operation. Called from `jsonpatch.validate`. Throws `JsonPatchError` in case of an error.\n * @param {object} operation - operation object (patch)\n * @param {number} index - index of operation in the sequence\n * @param {object} [document] - object where the operation is supposed to be applied\n * @param {string} [existingPathFragment] - comes along with `document`\n */\nfunction validator(operation, index, document, existingPathFragment) {\n    if (typeof operation !== 'object' || operation === null || Array.isArray(operation)) {\n        throw new JsonPatchError('Operation is not an object', 'OPERATION_NOT_AN_OBJECT', index, operation, document);\n    }\n    else if (!objOps[operation.op]) {\n        throw new JsonPatchError('Operation `op` property is not one of operations defined in RFC-6902', 'OPERATION_OP_INVALID', index, operation, document);\n    }\n    else if (typeof operation.path !== 'string') {\n        throw new JsonPatchError('Operation `path` property is not a string', 'OPERATION_PATH_INVALID', index, operation, document);\n    }\n    else if (operation.path.indexOf('/') !== 0 && operation.path.length > 0) {\n        // paths that aren't empty string should start with \"/\"\n        throw new JsonPatchError('Operation `path` property must start with \"/\"', 'OPERATION_PATH_INVALID', index, operation, document);\n    }\n    else if ((operation.op === 'move' || operation.op === 'copy') && typeof operation.from !== 'string') {\n        throw new JsonPatchError('Operation `from` property is not present (applicable in `move` and `copy` operations)', 'OPERATION_FROM_REQUIRED', index, operation, document);\n    }\n    else if ((operation.op === 'add' || operation.op === 'replace' || operation.op === 'test') && operation.value === undefined) {\n        throw new JsonPatchError('Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)', 'OPERATION_VALUE_REQUIRED', index, operation, document);\n    }\n    else if ((operation.op === 'add' || operation.op === 'replace' || operation.op === 'test') && (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.hasUndefined)(operation.value)) {\n        throw new JsonPatchError('Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)', 'OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED', index, operation, document);\n    }\n    else if (document) {\n        if (operation.op == \"add\") {\n            var pathLen = operation.path.split(\"/\").length;\n            var existingPathLen = existingPathFragment.split(\"/\").length;\n            if (pathLen !== existingPathLen + 1 && pathLen !== existingPathLen) {\n                throw new JsonPatchError('Cannot perform an `add` operation at the desired path', 'OPERATION_PATH_CANNOT_ADD', index, operation, document);\n            }\n        }\n        else if (operation.op === 'replace' || operation.op === 'remove' || operation.op === '_get') {\n            if (operation.path !== existingPathFragment) {\n                throw new JsonPatchError('Cannot perform the operation at a path that does not exist', 'OPERATION_PATH_UNRESOLVABLE', index, operation, document);\n            }\n        }\n        else if (operation.op === 'move' || operation.op === 'copy') {\n            var existingValue = { op: \"_get\", path: operation.from, value: undefined };\n            var error = validate([existingValue], document);\n            if (error && error.name === 'OPERATION_PATH_UNRESOLVABLE') {\n                throw new JsonPatchError('Cannot perform the operation from a path that does not exist', 'OPERATION_FROM_UNRESOLVABLE', index, operation, document);\n            }\n        }\n    }\n}\n/**\n * Validates a sequence of operations. If `document` parameter is provided, the sequence is additionally validated against the object document.\n * If error is encountered, returns a JsonPatchError object\n * @param sequence\n * @param document\n * @returns {JsonPatchError|undefined}\n */\nfunction validate(sequence, document, externalValidator) {\n    try {\n        if (!Array.isArray(sequence)) {\n            throw new JsonPatchError('Patch sequence must be an array', 'SEQUENCE_NOT_AN_ARRAY');\n        }\n        if (document) {\n            //clone document and sequence so that we can safely try applying operations\n            applyPatch((0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(document), (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(sequence), externalValidator || true);\n        }\n        else {\n            externalValidator = externalValidator || validator;\n            for (var i = 0; i < sequence.length; i++) {\n                externalValidator(sequence[i], i, document, undefined);\n            }\n        }\n    }\n    catch (e) {\n        if (e instanceof JsonPatchError) {\n            return e;\n        }\n        else {\n            throw e;\n        }\n    }\n}\n// based on https://github.com/epoberezkin/fast-deep-equal\n// MIT License\n// Copyright (c) 2017 Evgeny Poberezkin\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nfunction _areEquals(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a == 'object' && typeof b == 'object') {\n        var arrA = Array.isArray(a), arrB = Array.isArray(b), i, length, key;\n        if (arrA && arrB) {\n            length = a.length;\n            if (length != b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!_areEquals(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (arrA != arrB)\n            return false;\n        var keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!b.hasOwnProperty(keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            key = keys[i];\n            if (!_areEquals(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    return a !== a && b !== b;\n}\n;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-json-patch/module/core.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-json-patch/module/duplex.mjs":
/*!********************************************************!*\
  !*** ./node_modules/fast-json-patch/module/duplex.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compare: () => (/* binding */ compare),\n/* harmony export */   generate: () => (/* binding */ generate),\n/* harmony export */   observe: () => (/* binding */ observe),\n/* harmony export */   unobserve: () => (/* binding */ unobserve)\n/* harmony export */ });\n/* harmony import */ var _helpers_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers.mjs */ \"(rsc)/./node_modules/fast-json-patch/module/helpers.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core.mjs */ \"(rsc)/./node_modules/fast-json-patch/module/core.mjs\");\n/*!\n * https://github.com/Starcounter-Jack/JSON-Patch\n * (c) 2017-2021 Joachim Wester\n * MIT license\n */\n\n\nvar beforeDict = new WeakMap();\nvar Mirror = /** @class */ (function () {\n    function Mirror(obj) {\n        this.observers = new Map();\n        this.obj = obj;\n    }\n    return Mirror;\n}());\nvar ObserverInfo = /** @class */ (function () {\n    function ObserverInfo(callback, observer) {\n        this.callback = callback;\n        this.observer = observer;\n    }\n    return ObserverInfo;\n}());\nfunction getMirror(obj) {\n    return beforeDict.get(obj);\n}\nfunction getObserverFromMirror(mirror, callback) {\n    return mirror.observers.get(callback);\n}\nfunction removeObserverFromMirror(mirror, observer) {\n    mirror.observers.delete(observer.callback);\n}\n/**\n * Detach an observer from an object\n */\nfunction unobserve(root, observer) {\n    observer.unobserve();\n}\n/**\n * Observes changes made to an object, which can then be retrieved using generate\n */\nfunction observe(obj, callback) {\n    var patches = [];\n    var observer;\n    var mirror = getMirror(obj);\n    if (!mirror) {\n        mirror = new Mirror(obj);\n        beforeDict.set(obj, mirror);\n    }\n    else {\n        var observerInfo = getObserverFromMirror(mirror, callback);\n        observer = observerInfo && observerInfo.observer;\n    }\n    if (observer) {\n        return observer;\n    }\n    observer = {};\n    mirror.value = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(obj);\n    if (callback) {\n        observer.callback = callback;\n        observer.next = null;\n        var dirtyCheck = function () {\n            generate(observer);\n        };\n        var fastCheck = function () {\n            clearTimeout(observer.next);\n            observer.next = setTimeout(dirtyCheck);\n        };\n        if (typeof window !== 'undefined') { //not Node\n            window.addEventListener('mouseup', fastCheck);\n            window.addEventListener('keyup', fastCheck);\n            window.addEventListener('mousedown', fastCheck);\n            window.addEventListener('keydown', fastCheck);\n            window.addEventListener('change', fastCheck);\n        }\n    }\n    observer.patches = patches;\n    observer.object = obj;\n    observer.unobserve = function () {\n        generate(observer);\n        clearTimeout(observer.next);\n        removeObserverFromMirror(mirror, observer);\n        if (typeof window !== 'undefined') {\n            window.removeEventListener('mouseup', fastCheck);\n            window.removeEventListener('keyup', fastCheck);\n            window.removeEventListener('mousedown', fastCheck);\n            window.removeEventListener('keydown', fastCheck);\n            window.removeEventListener('change', fastCheck);\n        }\n    };\n    mirror.observers.set(callback, new ObserverInfo(callback, observer));\n    return observer;\n}\n/**\n * Generate an array of patches from an observer\n */\nfunction generate(observer, invertible) {\n    if (invertible === void 0) { invertible = false; }\n    var mirror = beforeDict.get(observer.object);\n    _generate(mirror.value, observer.object, observer.patches, \"\", invertible);\n    if (observer.patches.length) {\n        (0,_core_mjs__WEBPACK_IMPORTED_MODULE_1__.applyPatch)(mirror.value, observer.patches);\n    }\n    var temp = observer.patches;\n    if (temp.length > 0) {\n        observer.patches = [];\n        if (observer.callback) {\n            observer.callback(temp);\n        }\n    }\n    return temp;\n}\n// Dirty check if obj is different from mirror, generate patches and update mirror\nfunction _generate(mirror, obj, patches, path, invertible) {\n    if (obj === mirror) {\n        return;\n    }\n    if (typeof obj.toJSON === \"function\") {\n        obj = obj.toJSON();\n    }\n    var newKeys = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._objectKeys)(obj);\n    var oldKeys = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._objectKeys)(mirror);\n    var changed = false;\n    var deleted = false;\n    //if ever \"move\" operation is implemented here, make sure this test runs OK: \"should not generate the same patch twice (move)\"\n    for (var t = oldKeys.length - 1; t >= 0; t--) {\n        var key = oldKeys[t];\n        var oldVal = mirror[key];\n        if ((0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.hasOwnProperty)(obj, key) && !(obj[key] === undefined && oldVal !== undefined && Array.isArray(obj) === false)) {\n            var newVal = obj[key];\n            if (typeof oldVal == \"object\" && oldVal != null && typeof newVal == \"object\" && newVal != null && Array.isArray(oldVal) === Array.isArray(newVal)) {\n                _generate(oldVal, newVal, patches, path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), invertible);\n            }\n            else {\n                if (oldVal !== newVal) {\n                    changed = true;\n                    if (invertible) {\n                        patches.push({ op: \"test\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(oldVal) });\n                    }\n                    patches.push({ op: \"replace\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(newVal) });\n                }\n            }\n        }\n        else if (Array.isArray(mirror) === Array.isArray(obj)) {\n            if (invertible) {\n                patches.push({ op: \"test\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(oldVal) });\n            }\n            patches.push({ op: \"remove\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key) });\n            deleted = true; // property has been deleted\n        }\n        else {\n            if (invertible) {\n                patches.push({ op: \"test\", path: path, value: mirror });\n            }\n            patches.push({ op: \"replace\", path: path, value: obj });\n            changed = true;\n        }\n    }\n    if (!deleted && newKeys.length == oldKeys.length) {\n        return;\n    }\n    for (var t = 0; t < newKeys.length; t++) {\n        var key = newKeys[t];\n        if (!(0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.hasOwnProperty)(mirror, key) && obj[key] !== undefined) {\n            patches.push({ op: \"add\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(obj[key]) });\n        }\n    }\n}\n/**\n * Create an array of patches from the differences in two objects\n */\nfunction compare(tree1, tree2, invertible) {\n    if (invertible === void 0) { invertible = false; }\n    var patches = [];\n    _generate(tree1, tree2, patches, '', invertible);\n    return patches;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-json-patch/module/duplex.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-json-patch/module/helpers.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/fast-json-patch/module/helpers.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PatchError: () => (/* binding */ PatchError),\n/* harmony export */   _deepClone: () => (/* binding */ _deepClone),\n/* harmony export */   _getPathRecursive: () => (/* binding */ _getPathRecursive),\n/* harmony export */   _objectKeys: () => (/* binding */ _objectKeys),\n/* harmony export */   escapePathComponent: () => (/* binding */ escapePathComponent),\n/* harmony export */   getPath: () => (/* binding */ getPath),\n/* harmony export */   hasOwnProperty: () => (/* binding */ hasOwnProperty),\n/* harmony export */   hasUndefined: () => (/* binding */ hasUndefined),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   unescapePathComponent: () => (/* binding */ unescapePathComponent)\n/* harmony export */ });\n/*!\n * https://github.com/Starcounter-Jack/JSON-Patch\n * (c) 2017-2022 Joachim Wester\n * MIT licensed\n */\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwnProperty(obj, key) {\n    return _hasOwnProperty.call(obj, key);\n}\nfunction _objectKeys(obj) {\n    if (Array.isArray(obj)) {\n        var keys_1 = new Array(obj.length);\n        for (var k = 0; k < keys_1.length; k++) {\n            keys_1[k] = \"\" + k;\n        }\n        return keys_1;\n    }\n    if (Object.keys) {\n        return Object.keys(obj);\n    }\n    var keys = [];\n    for (var i in obj) {\n        if (hasOwnProperty(obj, i)) {\n            keys.push(i);\n        }\n    }\n    return keys;\n}\n;\n/**\n* Deeply clone the object.\n* https://jsperf.com/deep-copy-vs-json-stringify-json-parse/25 (recursiveDeepCopy)\n* @param  {any} obj value to clone\n* @return {any} cloned obj\n*/\nfunction _deepClone(obj) {\n    switch (typeof obj) {\n        case \"object\":\n            return JSON.parse(JSON.stringify(obj)); //Faster than ES5 clone - http://jsperf.com/deep-cloning-of-objects/5\n        case \"undefined\":\n            return null; //this is how JSON.stringify behaves for array items\n        default:\n            return obj; //no need to clone primitives\n    }\n}\n//3x faster than cached /^\\d+$/.test(str)\nfunction isInteger(str) {\n    var i = 0;\n    var len = str.length;\n    var charCode;\n    while (i < len) {\n        charCode = str.charCodeAt(i);\n        if (charCode >= 48 && charCode <= 57) {\n            i++;\n            continue;\n        }\n        return false;\n    }\n    return true;\n}\n/**\n* Escapes a json pointer path\n* @param path The raw pointer\n* @return the Escaped path\n*/\nfunction escapePathComponent(path) {\n    if (path.indexOf('/') === -1 && path.indexOf('~') === -1)\n        return path;\n    return path.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n/**\n * Unescapes a json pointer path\n * @param path The escaped pointer\n * @return The unescaped path\n */\nfunction unescapePathComponent(path) {\n    return path.replace(/~1/g, '/').replace(/~0/g, '~');\n}\nfunction _getPathRecursive(root, obj) {\n    var found;\n    for (var key in root) {\n        if (hasOwnProperty(root, key)) {\n            if (root[key] === obj) {\n                return escapePathComponent(key) + '/';\n            }\n            else if (typeof root[key] === 'object') {\n                found = _getPathRecursive(root[key], obj);\n                if (found != '') {\n                    return escapePathComponent(key) + '/' + found;\n                }\n            }\n        }\n    }\n    return '';\n}\nfunction getPath(root, obj) {\n    if (root === obj) {\n        return '/';\n    }\n    var path = _getPathRecursive(root, obj);\n    if (path === '') {\n        throw new Error(\"Object not found in root\");\n    }\n    return \"/\" + path;\n}\n/**\n* Recursively checks whether an object has any undefined values inside.\n*/\nfunction hasUndefined(obj) {\n    if (obj === undefined) {\n        return true;\n    }\n    if (obj) {\n        if (Array.isArray(obj)) {\n            for (var i_1 = 0, len = obj.length; i_1 < len; i_1++) {\n                if (hasUndefined(obj[i_1])) {\n                    return true;\n                }\n            }\n        }\n        else if (typeof obj === \"object\") {\n            var objKeys = _objectKeys(obj);\n            var objKeysLength = objKeys.length;\n            for (var i = 0; i < objKeysLength; i++) {\n                if (hasUndefined(obj[objKeys[i]])) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\nfunction patchErrorMessageFormatter(message, args) {\n    var messageParts = [message];\n    for (var key in args) {\n        var value = typeof args[key] === 'object' ? JSON.stringify(args[key], null, 2) : args[key]; // pretty print\n        if (typeof value !== 'undefined') {\n            messageParts.push(key + \": \" + value);\n        }\n    }\n    return messageParts.join('\\n');\n}\nvar PatchError = /** @class */ (function (_super) {\n    __extends(PatchError, _super);\n    function PatchError(message, name, index, operation, tree) {\n        var _newTarget = this.constructor;\n        var _this = _super.call(this, patchErrorMessageFormatter(message, { name: name, index: index, operation: operation, tree: tree })) || this;\n        _this.name = name;\n        _this.index = index;\n        _this.operation = operation;\n        _this.tree = tree;\n        Object.setPrototypeOf(_this, _newTarget.prototype); // restore prototype chain, see https://stackoverflow.com/a/48342359\n        _this.message = patchErrorMessageFormatter(message, { name: name, index: index, operation: operation, tree: tree });\n        return _this;\n    }\n    return PatchError;\n}(Error));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmFzdC1qc29uLXBhdGNoL21vZHVsZS9oZWxwZXJzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixTQUFJLElBQUksU0FBSTtBQUM3QjtBQUNBO0FBQ0EsZUFBZSxnQkFBZ0Isc0NBQXNDLGtCQUFrQjtBQUN2Riw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLHdCQUF3QixtQkFBbUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLEtBQUs7QUFDaEIsV0FBVyxLQUFLO0FBQ2hCO0FBQ087QUFDUDtBQUNBO0FBQ0Esb0RBQW9EO0FBQ3BEO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsV0FBVztBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLG1CQUFtQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0dBQW9HO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEVBQTRFLDREQUE0RDtBQUN4STtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RDtBQUM1RCw4REFBOEQsNERBQTREO0FBQzFIO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDcUIiLCJzb3VyY2VzIjpbIkQ6XFx6aW5uaWF4LWNvcGlsb3RcXHppbm5pYXgtY29waWxvdFxcdWlcXG5vZGVfbW9kdWxlc1xcZmFzdC1qc29uLXBhdGNoXFxtb2R1bGVcXGhlbHBlcnMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIVxuICogaHR0cHM6Ly9naXRodWIuY29tL1N0YXJjb3VudGVyLUphY2svSlNPTi1QYXRjaFxuICogKGMpIDIwMTctMjAyMiBKb2FjaGltIFdlc3RlclxuICogTUlUIGxpY2Vuc2VkXG4gKi9cbnZhciBfX2V4dGVuZHMgPSAodGhpcyAmJiB0aGlzLl9fZXh0ZW5kcykgfHwgKGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgZXh0ZW5kU3RhdGljcyA9IGZ1bmN0aW9uIChkLCBiKSB7XG4gICAgICAgIGV4dGVuZFN0YXRpY3MgPSBPYmplY3Quc2V0UHJvdG90eXBlT2YgfHxcbiAgICAgICAgICAgICh7IF9fcHJvdG9fXzogW10gfSBpbnN0YW5jZW9mIEFycmF5ICYmIGZ1bmN0aW9uIChkLCBiKSB7IGQuX19wcm90b19fID0gYjsgfSkgfHxcbiAgICAgICAgICAgIGZ1bmN0aW9uIChkLCBiKSB7IGZvciAodmFyIHAgaW4gYikgaWYgKGIuaGFzT3duUHJvcGVydHkocCkpIGRbcF0gPSBiW3BdOyB9O1xuICAgICAgICByZXR1cm4gZXh0ZW5kU3RhdGljcyhkLCBiKTtcbiAgICB9O1xuICAgIHJldHVybiBmdW5jdGlvbiAoZCwgYikge1xuICAgICAgICBleHRlbmRTdGF0aWNzKGQsIGIpO1xuICAgICAgICBmdW5jdGlvbiBfXygpIHsgdGhpcy5jb25zdHJ1Y3RvciA9IGQ7IH1cbiAgICAgICAgZC5wcm90b3R5cGUgPSBiID09PSBudWxsID8gT2JqZWN0LmNyZWF0ZShiKSA6IChfXy5wcm90b3R5cGUgPSBiLnByb3RvdHlwZSwgbmV3IF9fKCkpO1xuICAgIH07XG59KSgpO1xudmFyIF9oYXNPd25Qcm9wZXJ0eSA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHk7XG5leHBvcnQgZnVuY3Rpb24gaGFzT3duUHJvcGVydHkob2JqLCBrZXkpIHtcbiAgICByZXR1cm4gX2hhc093blByb3BlcnR5LmNhbGwob2JqLCBrZXkpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIF9vYmplY3RLZXlzKG9iaikge1xuICAgIGlmIChBcnJheS5pc0FycmF5KG9iaikpIHtcbiAgICAgICAgdmFyIGtleXNfMSA9IG5ldyBBcnJheShvYmoubGVuZ3RoKTtcbiAgICAgICAgZm9yICh2YXIgayA9IDA7IGsgPCBrZXlzXzEubGVuZ3RoOyBrKyspIHtcbiAgICAgICAgICAgIGtleXNfMVtrXSA9IFwiXCIgKyBrO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBrZXlzXzE7XG4gICAgfVxuICAgIGlmIChPYmplY3Qua2V5cykge1xuICAgICAgICByZXR1cm4gT2JqZWN0LmtleXMob2JqKTtcbiAgICB9XG4gICAgdmFyIGtleXMgPSBbXTtcbiAgICBmb3IgKHZhciBpIGluIG9iaikge1xuICAgICAgICBpZiAoaGFzT3duUHJvcGVydHkob2JqLCBpKSkge1xuICAgICAgICAgICAga2V5cy5wdXNoKGkpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBrZXlzO1xufVxuO1xuLyoqXG4qIERlZXBseSBjbG9uZSB0aGUgb2JqZWN0LlxuKiBodHRwczovL2pzcGVyZi5jb20vZGVlcC1jb3B5LXZzLWpzb24tc3RyaW5naWZ5LWpzb24tcGFyc2UvMjUgKHJlY3Vyc2l2ZURlZXBDb3B5KVxuKiBAcGFyYW0gIHthbnl9IG9iaiB2YWx1ZSB0byBjbG9uZVxuKiBAcmV0dXJuIHthbnl9IGNsb25lZCBvYmpcbiovXG5leHBvcnQgZnVuY3Rpb24gX2RlZXBDbG9uZShvYmopIHtcbiAgICBzd2l0Y2ggKHR5cGVvZiBvYmopIHtcbiAgICAgICAgY2FzZSBcIm9iamVjdFwiOlxuICAgICAgICAgICAgcmV0dXJuIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkob2JqKSk7IC8vRmFzdGVyIHRoYW4gRVM1IGNsb25lIC0gaHR0cDovL2pzcGVyZi5jb20vZGVlcC1jbG9uaW5nLW9mLW9iamVjdHMvNVxuICAgICAgICBjYXNlIFwidW5kZWZpbmVkXCI6XG4gICAgICAgICAgICByZXR1cm4gbnVsbDsgLy90aGlzIGlzIGhvdyBKU09OLnN0cmluZ2lmeSBiZWhhdmVzIGZvciBhcnJheSBpdGVtc1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgcmV0dXJuIG9iajsgLy9ubyBuZWVkIHRvIGNsb25lIHByaW1pdGl2ZXNcbiAgICB9XG59XG4vLzN4IGZhc3RlciB0aGFuIGNhY2hlZCAvXlxcZCskLy50ZXN0KHN0cilcbmV4cG9ydCBmdW5jdGlvbiBpc0ludGVnZXIoc3RyKSB7XG4gICAgdmFyIGkgPSAwO1xuICAgIHZhciBsZW4gPSBzdHIubGVuZ3RoO1xuICAgIHZhciBjaGFyQ29kZTtcbiAgICB3aGlsZSAoaSA8IGxlbikge1xuICAgICAgICBjaGFyQ29kZSA9IHN0ci5jaGFyQ29kZUF0KGkpO1xuICAgICAgICBpZiAoY2hhckNvZGUgPj0gNDggJiYgY2hhckNvZGUgPD0gNTcpIHtcbiAgICAgICAgICAgIGkrKztcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59XG4vKipcbiogRXNjYXBlcyBhIGpzb24gcG9pbnRlciBwYXRoXG4qIEBwYXJhbSBwYXRoIFRoZSByYXcgcG9pbnRlclxuKiBAcmV0dXJuIHRoZSBFc2NhcGVkIHBhdGhcbiovXG5leHBvcnQgZnVuY3Rpb24gZXNjYXBlUGF0aENvbXBvbmVudChwYXRoKSB7XG4gICAgaWYgKHBhdGguaW5kZXhPZignLycpID09PSAtMSAmJiBwYXRoLmluZGV4T2YoJ34nKSA9PT0gLTEpXG4gICAgICAgIHJldHVybiBwYXRoO1xuICAgIHJldHVybiBwYXRoLnJlcGxhY2UoL34vZywgJ34wJykucmVwbGFjZSgvXFwvL2csICd+MScpO1xufVxuLyoqXG4gKiBVbmVzY2FwZXMgYSBqc29uIHBvaW50ZXIgcGF0aFxuICogQHBhcmFtIHBhdGggVGhlIGVzY2FwZWQgcG9pbnRlclxuICogQHJldHVybiBUaGUgdW5lc2NhcGVkIHBhdGhcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHVuZXNjYXBlUGF0aENvbXBvbmVudChwYXRoKSB7XG4gICAgcmV0dXJuIHBhdGgucmVwbGFjZSgvfjEvZywgJy8nKS5yZXBsYWNlKC9+MC9nLCAnficpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIF9nZXRQYXRoUmVjdXJzaXZlKHJvb3QsIG9iaikge1xuICAgIHZhciBmb3VuZDtcbiAgICBmb3IgKHZhciBrZXkgaW4gcm9vdCkge1xuICAgICAgICBpZiAoaGFzT3duUHJvcGVydHkocm9vdCwga2V5KSkge1xuICAgICAgICAgICAgaWYgKHJvb3Rba2V5XSA9PT0gb2JqKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVzY2FwZVBhdGhDb21wb25lbnQoa2V5KSArICcvJztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKHR5cGVvZiByb290W2tleV0gPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICAgICAgZm91bmQgPSBfZ2V0UGF0aFJlY3Vyc2l2ZShyb290W2tleV0sIG9iaik7XG4gICAgICAgICAgICAgICAgaWYgKGZvdW5kICE9ICcnKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBlc2NhcGVQYXRoQ29tcG9uZW50KGtleSkgKyAnLycgKyBmb3VuZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuICcnO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldFBhdGgocm9vdCwgb2JqKSB7XG4gICAgaWYgKHJvb3QgPT09IG9iaikge1xuICAgICAgICByZXR1cm4gJy8nO1xuICAgIH1cbiAgICB2YXIgcGF0aCA9IF9nZXRQYXRoUmVjdXJzaXZlKHJvb3QsIG9iaik7XG4gICAgaWYgKHBhdGggPT09ICcnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIk9iamVjdCBub3QgZm91bmQgaW4gcm9vdFwiKTtcbiAgICB9XG4gICAgcmV0dXJuIFwiL1wiICsgcGF0aDtcbn1cbi8qKlxuKiBSZWN1cnNpdmVseSBjaGVja3Mgd2hldGhlciBhbiBvYmplY3QgaGFzIGFueSB1bmRlZmluZWQgdmFsdWVzIGluc2lkZS5cbiovXG5leHBvcnQgZnVuY3Rpb24gaGFzVW5kZWZpbmVkKG9iaikge1xuICAgIGlmIChvYmogPT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgaWYgKG9iaikge1xuICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShvYmopKSB7XG4gICAgICAgICAgICBmb3IgKHZhciBpXzEgPSAwLCBsZW4gPSBvYmoubGVuZ3RoOyBpXzEgPCBsZW47IGlfMSsrKSB7XG4gICAgICAgICAgICAgICAgaWYgKGhhc1VuZGVmaW5lZChvYmpbaV8xXSkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiBvYmogPT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgICAgIHZhciBvYmpLZXlzID0gX29iamVjdEtleXMob2JqKTtcbiAgICAgICAgICAgIHZhciBvYmpLZXlzTGVuZ3RoID0gb2JqS2V5cy5sZW5ndGg7XG4gICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IG9iaktleXNMZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgIGlmIChoYXNVbmRlZmluZWQob2JqW29iaktleXNbaV1dKSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufVxuZnVuY3Rpb24gcGF0Y2hFcnJvck1lc3NhZ2VGb3JtYXR0ZXIobWVzc2FnZSwgYXJncykge1xuICAgIHZhciBtZXNzYWdlUGFydHMgPSBbbWVzc2FnZV07XG4gICAgZm9yICh2YXIga2V5IGluIGFyZ3MpIHtcbiAgICAgICAgdmFyIHZhbHVlID0gdHlwZW9mIGFyZ3Nba2V5XSA9PT0gJ29iamVjdCcgPyBKU09OLnN0cmluZ2lmeShhcmdzW2tleV0sIG51bGwsIDIpIDogYXJnc1trZXldOyAvLyBwcmV0dHkgcHJpbnRcbiAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIG1lc3NhZ2VQYXJ0cy5wdXNoKGtleSArIFwiOiBcIiArIHZhbHVlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gbWVzc2FnZVBhcnRzLmpvaW4oJ1xcbicpO1xufVxudmFyIFBhdGNoRXJyb3IgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoX3N1cGVyKSB7XG4gICAgX19leHRlbmRzKFBhdGNoRXJyb3IsIF9zdXBlcik7XG4gICAgZnVuY3Rpb24gUGF0Y2hFcnJvcihtZXNzYWdlLCBuYW1lLCBpbmRleCwgb3BlcmF0aW9uLCB0cmVlKSB7XG4gICAgICAgIHZhciBfbmV3VGFyZ2V0ID0gdGhpcy5jb25zdHJ1Y3RvcjtcbiAgICAgICAgdmFyIF90aGlzID0gX3N1cGVyLmNhbGwodGhpcywgcGF0Y2hFcnJvck1lc3NhZ2VGb3JtYXR0ZXIobWVzc2FnZSwgeyBuYW1lOiBuYW1lLCBpbmRleDogaW5kZXgsIG9wZXJhdGlvbjogb3BlcmF0aW9uLCB0cmVlOiB0cmVlIH0pKSB8fCB0aGlzO1xuICAgICAgICBfdGhpcy5uYW1lID0gbmFtZTtcbiAgICAgICAgX3RoaXMuaW5kZXggPSBpbmRleDtcbiAgICAgICAgX3RoaXMub3BlcmF0aW9uID0gb3BlcmF0aW9uO1xuICAgICAgICBfdGhpcy50cmVlID0gdHJlZTtcbiAgICAgICAgT2JqZWN0LnNldFByb3RvdHlwZU9mKF90aGlzLCBfbmV3VGFyZ2V0LnByb3RvdHlwZSk7IC8vIHJlc3RvcmUgcHJvdG90eXBlIGNoYWluLCBzZWUgaHR0cHM6Ly9zdGFja292ZXJmbG93LmNvbS9hLzQ4MzQyMzU5XG4gICAgICAgIF90aGlzLm1lc3NhZ2UgPSBwYXRjaEVycm9yTWVzc2FnZUZvcm1hdHRlcihtZXNzYWdlLCB7IG5hbWU6IG5hbWUsIGluZGV4OiBpbmRleCwgb3BlcmF0aW9uOiBvcGVyYXRpb24sIHRyZWU6IHRyZWUgfSk7XG4gICAgICAgIHJldHVybiBfdGhpcztcbiAgICB9XG4gICAgcmV0dXJuIFBhdGNoRXJyb3I7XG59KEVycm9yKSk7XG5leHBvcnQgeyBQYXRjaEVycm9yIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-json-patch/module/helpers.mjs\n");

/***/ })

};
;